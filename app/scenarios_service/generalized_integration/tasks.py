import csv
import io
import json
from datetime import datetime, UTC

import elasticapm
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from gcloud_client.storage.upload import upload_object
from scenarios_service.generalized_integration import fetch_events_for_simulations
from scenarios_service.generalized_integration.explore_integration import (
    ExploreIntegration,
)
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryIntegration,
)
from scenarios_service.generalized_integration.measure_integration import (
    MeasureIntegration,
)
from scenarios_service.generalized_integration.schema import (
    ExploreRequestBody,
)

settings = get_settings()


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def submit_project_fields_to_explore_task(
    self: DBTask,
    *,
    body: ExploreRequestBody,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await ExploreIntegration(request=request).submit_project_fields_to_explore(body.project_id, body.field_ids)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def approve_outcomes_for_program(
    self: DBTask,
    *,
    program_id: int,
    task_id: str,
    is_test_run: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    # avoid circular import
    from scenarios_service.generalized_integration.outcome_approval_handler import (
        process_outcome_approval,
    )

    await process_outcome_approval(request, program_id, task_id, is_test_run)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def revoke_approval_for_program(
    self: DBTask,
    *,
    program_id: int,
    is_test_run: bool,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    from scenarios_service.generalized_integration.outcome_approval_handler import (
        revoke_existing_approval,
    )

    await revoke_existing_approval(request, program_id, is_test_run)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
async def get_adjusted_cropping_events_task(
    self: DBTask,
    *,
    program_id: int,
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    adjusted_cropping_events = await fetch_events_for_simulations.get_adjusted_cropping_events(
        request=request, program_id=program_id
    )

    csv_obj = io.StringIO()
    if adjusted_cropping_events:
        data_dicts = [json.loads(e.json()) for e in adjusted_cropping_events]
        writer = csv.DictWriter(csv_obj, fieldnames=data_dicts[0].keys())
        writer.writeheader()
        writer.writerows(data_dicts)
        csv_obj.seek(0)

    time_now = datetime.now(tz=UTC).strftime("%Y-%m-%dT%H:%M:%S")
    await upload_object(
        request=request,
        bucket_name=settings.GCLOUD_STORAGE_PRIVATE_BUCKET,
        data=csv_obj.read(),
        object_name=f"adjusted-cropping-events/{program_id}_{time_now}.csv",
        content_type="text/csv",
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def submit_fields_for_measure_project_task(
    self: DBTask,
    *,
    dndc_task_id: str,
    field_ids: list[int],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await MeasureIntegration(request).submit_fields_for_measure_project(dndc_task_id=dndc_task_id, field_ids=field_ids)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def finalize_measure_project_task(
    self: DBTask,
    *,
    dndc_task_id: str,
    field_ids: list[int],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await MeasureIntegration(request).finalize_measure_project(dndc_task_id=dndc_task_id, field_ids=field_ids)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def submit_fields_for_inventory_project_task(
    self: DBTask,
    *,
    dndc_task_id: str,
    field_ids: list[int],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await InventoryIntegration(request).submit_fields_for_inventory_project(
        dndc_task_id=dndc_task_id, field_ids=field_ids
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def finalize_inventory_project_task(
    self: DBTask,
    *,
    dndc_task_id: str,
    field_ids: list[int],
    fs_user_id: int,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await InventoryIntegration(request).finalize_inventory_project(dndc_task_id=dndc_task_id, field_ids=field_ids)
