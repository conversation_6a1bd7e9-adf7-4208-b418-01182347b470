from ui.projects.completion.calculation.calculate_project_completion_percentages import (
    _determine_percentage_complete,
)


def test_determine_percentage_complete():
    assert _determine_percentage_complete(0, 10000) == 0
    assert _determine_percentage_complete(2500, 10000) == 25
    assert _determine_percentage_complete(5000, 10000) == 50
    assert _determine_percentage_complete(7500, 10000) == 75
    assert _determine_percentage_complete(9999, 10000) == 99
    assert _determine_percentage_complete(10000, 10000) == 100
