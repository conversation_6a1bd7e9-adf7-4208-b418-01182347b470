import asyncio
from datetime import datetime, timezone
from typing import Union

from fastapi import Request, Response, status
from sqlalchemy import and_, delete, insert, or_, Row, select, Sequence, update

from helper.helper import run_query
from programs.db import does_program_exist, get_program_by_id
from programs.enums import ProgramType
from programs.model import Programs
from projects.db import does_project_exist
from users.constants import MAX_RECENT_PROGRAMS
from users.model import UserRecentPrograms
from users.schema import CreateUserRecentProgramRequest, UserRecentProgramItem


async def get_user_recent_programs(request: Request) -> list[UserRecentProgramItem]:
    recent_programs = await get_filtered_user_recent_programs(request=request)
    return [
        UserRecentProgramItem(
            program_name=row.name,
            program_id=row.program_id,
            project_id=row.project_id,
        )
        for row in recent_programs
    ]


async def does_user_recent_program_exist(request: Request, program_id: int, project_id: int | None) -> bool:
    async with request.state.sql_session() as s:
        select_query = select(UserRecentPrograms).where(
            UserRecentPrograms.user_id == request.state.fs_user_id,
            UserRecentPrograms.program_id == program_id,
            or_(
                UserRecentPrograms.project_id == project_id,
                and_(UserRecentPrograms.project_id.is_(None), project_id is None),
            ),
        )
        return (await run_query(s=s, query=select_query)).scalar_one_or_none() is not None


async def _select_user_recent_programs(request: Request) -> Sequence[Row]:
    async with request.state.sql_session() as s:
        select_query = (
            select(
                UserRecentPrograms.id,
                UserRecentPrograms.program_id,
                UserRecentPrograms.project_id,
                Programs.name,
                Programs.program_type,
            )
            .join(Programs, UserRecentPrograms.program_id == Programs.id)
            .where(UserRecentPrograms.user_id == request.state.fs_user_id)
            .order_by(UserRecentPrograms.created_at.desc())
        )
        return (await run_query(s=s, query=select_query)).all()


VALID_RECENT_PROGRAM_TYPES = [ProgramType.live, ProgramType.demo]


async def get_filtered_user_recent_programs(request: Request) -> Sequence[Row]:
    result = await _select_user_recent_programs(request=request)
    return [row for row in result if row.program_type in VALID_RECENT_PROGRAM_TYPES]


async def create_user_recent_program(
    request: Request, data: CreateUserRecentProgramRequest
) -> Union[list[UserRecentProgramItem], Response]:
    """
    Create an entry in the mrv_user_recent_programs table.
    If the entry already exists, update the created_at timestamp.
    If the total recent programs for the user exceeds the limit, delete the oldest ones.
    """
    async with request.state.sql_session() as s:
        existence_checks = [
            does_user_recent_program_exist(request=request, program_id=data.program_id, project_id=data.project_id),
            does_program_exist(request, data.program_id),
            get_program_by_id(request, data.program_id),
        ]

        if data.project_id is None:
            existence_checks.append(asyncio.create_task(asyncio.sleep(0)))
        else:
            existence_checks.append(does_project_exist(request, data.project_id))

        duplicate_exists: bool
        program_exists: bool
        found_program: Programs | None
        project_exists: bool
        duplicate_exists, program_exists, found_program, project_exists = await asyncio.gather(*existence_checks)

        if program_exists is False or found_program is None:
            return Response(status_code=status.HTTP_404_NOT_FOUND, content="Program not found")
        if project_exists is False:
            return Response(status_code=status.HTTP_404_NOT_FOUND, content="Project not found")

        if found_program.program_type not in VALID_RECENT_PROGRAM_TYPES:
            return Response(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=f"Invalid program type '{found_program.program_type}'. Only {', '.join(VALID_RECENT_PROGRAM_TYPES)} programs can be added to recent programs.",
            )

        if duplicate_exists:
            # Update the created_at timestamp
            update_query = (
                update(UserRecentPrograms)
                .where(
                    UserRecentPrograms.user_id == request.state.fs_user_id,
                    UserRecentPrograms.program_id == data.program_id,
                    or_(
                        UserRecentPrograms.project_id == data.project_id,
                        and_(UserRecentPrograms.project_id.is_(None), data.project_id is None),
                    ),
                )
                .values(created_at=datetime.now(tz=timezone.utc))
            )
            await s.execute(update_query)
            await s.commit()

            recent_programs = await get_filtered_user_recent_programs(request=request)

            return [
                UserRecentProgramItem(
                    program_name=row.name,
                    program_id=row.program_id,
                    project_id=row.project_id,
                )
                for row in recent_programs
            ]

        # Otherwise insert the new entry
        insert_query = insert(UserRecentPrograms).values(
            user_id=request.state.fs_user_id,
            program_id=data.program_id,
            project_id=data.project_id,
        )
        await s.execute(insert_query)
        await s.commit()

        recent_programs = await get_filtered_user_recent_programs(request=request)

        if len(recent_programs) > MAX_RECENT_PROGRAMS:
            # Delete the oldest entries
            ids_to_delete = [row.id for row in recent_programs[MAX_RECENT_PROGRAMS:]]
            await s.execute(delete(UserRecentPrograms).where(UserRecentPrograms.id.in_(ids_to_delete)))
            await s.commit()
            recent_programs = recent_programs[:MAX_RECENT_PROGRAMS]

        return [
            UserRecentProgramItem(
                program_name=row.name,
                program_id=row.program_id,
                project_id=row.project_id,
            )
            for row in recent_programs
        ]


async def delete_user_recent_program_by_program_id(request: Request, program_id: int) -> None:
    """
    This will remove all UserRecentProgram associated with the program_id.
    """
    async with request.state.sql_session() as s:
        delete_query = delete(UserRecentPrograms).where(UserRecentPrograms.program_id == program_id)
        await s.execute(delete_query)
        await s.commit()


async def delete_user_recent_program_by_project_id(request: Request, project_id: int) -> None:
    """
    This will remove all UserRecentProgram associated with the project_id.
    """
    async with request.state.sql_session() as s:
        delete_query = delete(UserRecentPrograms).where(UserRecentPrograms.project_id == project_id)
        await s.execute(delete_query)
        await s.commit()


async def delete_user_recent_program_by_user_id(request: Request, user_id: int) -> None:
    """
    This will remove all UserRecentProgram associated with the user_id.
    """
    async with request.state.sql_session() as s:
        delete_query = delete(UserRecentPrograms).where(UserRecentPrograms.user_id == user_id)
        await s.execute(delete_query)
        await s.commit()
