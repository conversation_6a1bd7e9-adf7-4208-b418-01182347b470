import json
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

import pytest
from regrow.ses.crop.v1 import crop_id_enum_pb2, crop_pb2
from regrow.ses.tillage.v1 import tillage_pb2
from regrow.ses.application.v1 import application_pb2
from regrow.ses.termination.v1 import termination_pb2
from ses_client import event, pb_value
from ses_client.application import ApplicationBroadcast
from ses_client.boundaries import BoundariesClient
from ses_client.client import Client, EventNotFoundError
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from regrow.ses.event.v1.event_pb2 import Event
from ses_client.crop import (
    HarvestedCrop,
    PlantedCrop,
    SownCrop,
    TerminatedCrop,
    crop_id_from_label,
)
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.irrigation import IrrigationActivity
from ses_client.planting import PlantingActivity
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from tests.health import health_check
from tests.testdata import (
    TEST_USER_ID,
    event_srvc_addr,
    random_datetime,
    random_polygon,
    random_md5,
)


def create_mock_upsert_response(event_id: str) -> UpsertEventResponse:
    """Helper function to create a mock UpsertEventResponse with the given event ID."""

    # Create a mock UpsertEventResponse
    mock_response = MagicMock(spec=UpsertEventResponse)
    mock_response.event = MagicMock(spec=Event)
    mock_response.event.id = event_id
    mock_response.context = MagicMock()
    return mock_response


@pytest.mark.integration
class TestCopyEventToField:
    """Test cases for the copy_event_to_field functionality."""

    @pytest.mark.asyncio
    async def test_copy_event_to_field_no_boundaries_service(self):
        """Test that copy_event_to_field raises error when boundaries service is not initialized."""
        client = Client("localhost:50051")

        with pytest.raises(ValueError, match="Boundaries service not initialized"):
            await client.copy_event_to_field("test_event_id", "test_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_basic_flow(self):
        """Test the basic flow of copying an event to a different field."""
        # Setup
        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock a simple source event and context (we'll mock the protobuf objects)

        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"

        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}

        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        # Mock fetch_event_with_context to return our source event with context
        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to return a mock structured event
        mock_new_event = MagicMock()
        mock_new_event.pb_event.id = "new_event_id"
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return a response with a unique UUID
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("unique_uuid_123")
        )

        # Test
        field_id = "test_field_md5_hash"
        new_event_id_result = await client.copy_event_to_field(
            "source_event_id", field_id
        )

        # Verify boundaries service was called
        mock_boundaries.fetch_geometry_str.assert_called_once_with(field_id)

        # Verify fetch_event_with_context was called
        client.fetch_event_with_context.assert_called_once_with("source_event_id")

        # Verify _create_event_copy was called with correct parameters (UUID will be unique)
        assert client._create_event_copy.call_count == 1
        call_args = client._create_event_copy.call_args[0]
        assert call_args[0] == mock_source_event
        assert call_args[1] == mock_source_context
        # call_args[2] is the generated UUID - we just verify it's a string
        assert isinstance(call_args[2], str)
        assert call_args[3] == target_geometry

        # Verify upsert_event was called
        client.upsert_event.assert_called_once_with(mock_new_event)

        # Verify the returned event ID
        assert new_event_id_result == "unique_uuid_123"

    @pytest.mark.asyncio
    async def test_copy_event_to_field_boundary_service_error(self):
        """Test that copy_event_to_field handles boundary service errors properly."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event first (since it's fetched before boundaries service)
        mock_source_event = MagicMock()
        mock_source_event.id = "test_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock the boundaries service to raise an exception
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ValueError("Field not found")
        client.boundaries_service = mock_boundaries

        # Test that the exception is propagated
        with pytest.raises(ValueError, match="Field not found"):
            await client.copy_event_to_field("test_event_id", "invalid_field_id")

        # Verify both services were called in the correct order
        client.fetch_event_with_context.assert_called_once_with("test_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("invalid_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_boundary_service_network_error(self):
        """Test that copy_event_to_field handles network errors from boundary service."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event first (since it's fetched before boundaries service)
        mock_source_event = MagicMock()
        mock_source_event.id = "test_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock the boundaries service to raise a connection error
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ConnectionError(
            "Network timeout"
        )
        client.boundaries_service = mock_boundaries

        # Test that the exception is propagated
        with pytest.raises(ConnectionError, match="Network timeout"):
            await client.copy_event_to_field("test_event_id", "test_field_id")

        # Verify both services were called
        client.fetch_event_with_context.assert_called_once_with("test_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")

    @pytest.mark.asyncio
    async def test_copy_event_to_field_source_event_not_found(self):
        """Test that copy_event_to_field handles EventNotFoundError properly."""
        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to return valid geometry
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock fetch_event_with_context to raise EventNotFoundError
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("nonexistent_event_id")
        )

        # Test that EventNotFoundError is propagated
        with pytest.raises(
            EventNotFoundError, match="No result for event_id: nonexistent_event_id"
        ):
            await client.copy_event_to_field("nonexistent_event_id", "test_field_id")

        # Verify only fetch_event_with_context was called (boundaries service not called due to early failure)
        client.fetch_event_with_context.assert_called_once_with("nonexistent_event_id")
        mock_boundaries.fetch_geometry_str.assert_not_called()

    @pytest.mark.asyncio
    async def test_copy_event_to_field_invalid_geometry(self):
        """Test that copy_event_to_field handles invalid/malformed geometry gracefully."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to return invalid geometry
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        invalid_geometry = "invalid json geometry"
        mock_boundaries.fetch_geometry_str.return_value = invalid_geometry
        client.boundaries_service = mock_boundaries

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to potentially fail with invalid geometry
        client._create_event_copy = MagicMock(
            side_effect=ValueError("Invalid geometry format")
        )

        # Test that the error is propagated when _create_event_copy fails
        with pytest.raises(ValueError, match="Invalid geometry format"):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify all services were called in the correct order
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        # Verify _create_event_copy was called with a generated UUID
        assert client._create_event_copy.call_count == 1
        call_args = client._create_event_copy.call_args[0]
        assert call_args[0] == mock_source_event
        assert call_args[1] == mock_source_context
        assert isinstance(call_args[2], str)  # Generated UUID
        assert call_args[3] == invalid_geometry

    @pytest.mark.asyncio
    async def test_copy_event_to_field_upsert_failure(self):
        """Test that copy_event_to_field handles upsert failures properly."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to succeed
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to fail
        client.upsert_event = AsyncMock(
            side_effect=RuntimeError("Database connection failed")
        )

        # Test that the upsert error is propagated
        with pytest.raises(RuntimeError, match="Database connection failed"):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify all services were called up to the upsert failure
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        # Verify _create_event_copy was called with a generated UUID
        assert client._create_event_copy.call_count == 1
        call_args = client._create_event_copy.call_args[0]
        assert call_args[0] == mock_source_event
        assert call_args[1] == mock_source_context
        assert isinstance(call_args[2], str)  # Generated UUID
        assert call_args[3] == target_geometry
        client.upsert_event.assert_called_once_with(mock_new_event)

    @pytest.mark.asyncio
    async def test_copy_event_to_field_no_partial_state_on_error(self):
        """Test that no partial state changes occur when errors happen during copy operation."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock fetch_event_with_context to fail early
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("source_event_id")
        )

        # Mock other services that should NOT be called due to early failure
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        client.boundaries_service = mock_boundaries
        client._create_event_copy = MagicMock()
        client.upsert_event = AsyncMock()

        # Test that the event not found error is propagated
        with pytest.raises(EventNotFoundError):
            await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify that only fetch_event_with_context was called, no other operations occurred
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        mock_boundaries.fetch_geometry_str.assert_not_called()
        client._create_event_copy.assert_not_called()
        client.upsert_event.assert_not_called()

    @pytest.mark.asyncio
    async def test_copy_event_to_field_error_messages_contain_context(self):
        """Test that error messages contain relevant context information."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Test 1: Boundary service error with field ID context
        # First mock a valid event fetch
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        mock_boundaries = AsyncMock(spec=BoundariesClient)
        mock_boundaries.fetch_geometry_str.side_effect = ValueError(
            "Field 'test_field_123' not found"
        )
        client.boundaries_service = mock_boundaries

        with pytest.raises(ValueError) as exc_info:
            await client.copy_event_to_field("source_event_id", "test_field_123")

        # Verify error message contains field ID context
        assert "test_field_123" in str(exc_info.value)

        # Test 2: Event not found error with event ID context
        client.fetch_event_with_context = AsyncMock(
            side_effect=EventNotFoundError("missing_event_456")
        )

        with pytest.raises(EventNotFoundError) as exc_info:
            await client.copy_event_to_field("missing_event_456", "test_field_id")

        # Verify error message contains event ID context
        assert "missing_event_456" in str(exc_info.value)
        assert exc_info.value.event_id == "missing_event_456"

    @pytest.mark.asyncio
    async def test_copy_event_to_field_returns_actual_upserted_event_id(self):
        """Test that copy_event_to_field returns the actual event ID from upsert response, not the deterministic UUID."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock a valid source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock _create_event_copy
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return a specific event ID
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("existing_event_456")
        )

        # Test
        result = await client.copy_event_to_field("source_event_id", "test_field_id")

        # Verify the result is the actual event ID from upsert response
        assert result == "existing_event_456"

        # Verify all services were called correctly
        client.fetch_event_with_context.assert_called_once_with("source_event_id")
        mock_boundaries.fetch_geometry_str.assert_called_once_with("test_field_id")
        # Verify _create_event_copy was called with a generated UUID
        assert client._create_event_copy.call_count == 1
        call_args = client._create_event_copy.call_args[0]
        assert call_args[0] == mock_source_event
        assert call_args[1] == mock_source_context
        assert isinstance(call_args[2], str)  # Generated UUID
        assert call_args[3] == target_geometry
        client.upsert_event.assert_called_once_with(mock_new_event)


@pytest.mark.integration
class TestCopyEventToFieldIntegration:
    """Integration tests for the copy_event_to_field functionality."""

    def _get_mock_target_geometry(self):
        """Helper to get consistent mock target geometry."""
        return json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )

    async def _setup_client_with_mock_boundaries(self):
        """Helper to setup client with mocked boundaries service."""
        # Use a dummy URL since we'll mock the boundaries service
        boundaries_service_url = "http://mock-boundaries-service"
        client = Client(event_srvc_addr, boundaries_service_url=boundaries_service_url)
        return client

    async def test_copy_tillage_activity_to_field(self):
        """Test copying a tillage activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source tillage activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_tillage = (
                TillageActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .depth(pb_value.depth_inches(2.5))
                .soil_inversion(True)
                .strip_tillage_percent(55.5)
                .implement(tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW)
                .tillage_practice(tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_tillage)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_tillage.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")

            # Verify tillage-specific data was copied
            assert copied_event.tillage_activity.depth.value == 2.5
            assert copied_event.tillage_activity.soil_inversion is True
            assert copied_event.tillage_activity.strip_tillage_fraction.percent == 55.5
            assert (
                copied_event.tillage_activity.implement
                == tillage_pb2.TillageImplement.TILLAGE_IMPLEMENT_PLOW
            )
            assert (
                copied_event.tillage_activity.tillage_practice
                == tillage_pb2.TillagePractice.TILLAGE_PRACTICE_REDUCED
            )

            # Verify geometry is different (should be from target field)
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_sowing_activity_to_field(self):
        """Test copying a sowing activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source sowing activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            cropping_period_id = event.new_event_id()

            source_sowing = (
                SowingActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    SownCrop(crop_id=crop_id_from_label("rye")).sowing_rate_kg_per_ha(
                        100
                    )
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_sowing)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_sowing.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")
            # Verify copied event has NO cropping_period_identifier (individual copies don't get them)
            assert copied_event.cropping_period_identifier == ""

            # Verify sowing-specific data was copied
            assert len(copied_event.sowing_activity.crops) == 1
            assert (
                copied_event.sowing_activity.crops[0].seed_mass_rate.mass.value == 100
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_planting_activity_to_field(self):
        """Test copying a planting activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source planting activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            crop_id = crop_id_from_label("rice")
            cropping_period_id = event.new_event_id()

            source_planting = (
                PlantingActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    PlantedCrop(crop_id=crop_id)
                    .planting_rate_plants_per_sqm(64)
                    .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_planting)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_planting.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")
            # Verify copied event has NO cropping_period_identifier (individual copies don't get them)
            assert copied_event.cropping_period_identifier == ""

            # Verify planting-specific data was copied
            assert len(copied_event.planting_activity.crops) == 1
            assert copied_event.planting_activity.crops[0].crop.id == crop_id
            assert (
                copied_event.planting_activity.crops[0].planting_rate.count.value == 64
            )
            assert (
                copied_event.planting_activity.crops[0].crop.purpose[0]
                == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_application_activity_to_field(self):
        """Test copying an application activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source application activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_application = (
                ApplicationBroadcast(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .input(
                    BasicFertiliser(input_name="npk")
                    .mass_rate(pb_value.pounds_per_acre(150))
                    .pb()
                )
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_application)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_application.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")

            # Verify application-specific data was copied
            assert (
                copied_event.application_activity.method
                == application_pb2.APPLICATION_METHOD_BROADCAST
            )
            assert len(copied_event.application_activity.inputs) == 1
            assert (
                copied_event.application_activity.inputs[0].basic_fertiliser.name
                == "npk"
            )
            assert (
                copied_event.application_activity.inputs[0].mass_rate.mass.value == 150
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_harvest_activity_to_field(self):
        """Test copying a harvest activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source harvest activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()
            crop_id = crop_id_from_label("rye")
            cropping_period_id = event.new_event_id()

            source_harvest = (
                HarvestActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(
                    HarvestedCrop(crop_id=crop_id)
                    .purpose(crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST)
                    .mass_yield(pb_value.tonnes_per_hectare(1.5))
                )
                .cropping_period_identifier(cropping_period_id)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_harvest)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_harvest.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")
            # Verify copied event has NO cropping_period_identifier (individual copies don't get them)
            assert copied_event.cropping_period_identifier == ""

            # Verify harvest-specific data was copied
            assert len(copied_event.harvest_activity.crops) == 1
            assert copied_event.harvest_activity.crops[0].crop.id == crop_id
            assert (
                copied_event.harvest_activity.crops[0].crop.purpose[0]
                == crop_pb2.CropPurpose.CROP_PURPOSE_COMMODITY_HARVEST
            )
            assert copied_event.harvest_activity.crops[0].mass_yield.mass.value == 1.5

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_irrigation_activity_to_field(self):
        """Test copying an irrigation activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source irrigation activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_irrigation = (
                IrrigationActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .method_sprinkler()
                .water_applied(total_depth=pb_value.depth_millimetres(10))
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_irrigation)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_irrigation.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")

            # Verify irrigation-specific data was copied
            assert copied_event.irrigation_activity.water_depth_total.value == 10

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_fallow_period_to_field(self):
        """Test copying a fallow period to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source fallow period
            source_field_geom = random_polygon()
            start_datetime = random_datetime()
            end_datetime = datetime(
                start_datetime.year + 1, start_datetime.month, start_datetime.day
            )
            source_event_id = event.new_event_id()

            source_fallow = (
                FallowPeriod(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(start_datetime)
                .end(end_datetime)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_fallow)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Use a larger tolerance for timestamp comparison due to potential timezone differences
            # Allow up to 24 hours difference to account for timezone handling
            assert (
                abs(
                    copied_event.interval.start_time.seconds
                    - source_fallow.pb_event.interval.start_time.seconds
                )
                <= 86400
            )
            assert (
                abs(
                    copied_event.interval.end_time.seconds
                    - source_fallow.pb_event.interval.end_time.seconds
                )
                <= 86400
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    async def test_copy_termination_activity_to_field(self):
        """Test copying a termination activity to a different field."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source termination activity
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_termination = (
                TerminationActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .crop(terminated_crop=TerminatedCrop(crop_id_enum_pb2.CROP_ID_ALFALFA))
                .reason(
                    termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST
                )
                .method(termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING)
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_termination)

            # Copy to new field
            target_field_id = random_md5()
            new_event_id = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called
            mock_fetch.assert_called_once_with(target_field_id)

            # Verify new event was created
            assert new_event_id != source_event_id

            # Fetch and verify copied event
            copied_event = await client.fetch_event(new_event_id)

            # Verify basic properties
            assert copied_event.id == new_event_id
            # Verify timestamp matches original exactly (no more random offset)
            assert (
                copied_event.interval.start_time.seconds
                == source_termination.pb_event.interval.start_time.seconds
            )
            # Verify copy tracking fields are set correctly
            assert copied_event.copied_from_event_id == source_event_id
            # The revision should match the persisted source event (fetched during copy)
            fetched_source_event = await client.fetch_event(source_event_id)
            assert (
                copied_event.copied_from_event_revision == fetched_source_event.revision
            )
            # Verify copied_at field is set by the service (should not be empty)
            assert copied_event.HasField("copied_at")

            # Verify termination-specific data was copied
            assert len(copied_event.termination_activity.crops) == 1
            assert (
                copied_event.termination_activity.crops[0].crop.id
                == crop_id_enum_pb2.CROP_ID_ALFALFA
            )
            assert (
                copied_event.termination_activity.reason
                == termination_pb2.TerminationReason.TERMINATION_REASON_FORAGE_HARVEST
            )
            assert (
                copied_event.termination_activity.method
                == termination_pb2.TerminationMethod.TERMINATION_METHOD_GRAZING
            )

            # Verify geometry is different
            assert copied_event.geojson != source_field_geom
            # Parse both geometries as JSON to compare structure, not formatting
            import json

            copied_geom = json.loads(copied_event.geojson)
            expected_geom = json.loads(target_geometry)
            assert copied_geom == expected_geom

    @pytest.mark.integration
    async def test_multiple_copies_create_separate_events(self):
        """Test that copying the same event multiple times creates separate independent events."""
        await health_check(event_srvc_addr)

        client = await self._setup_client_with_mock_boundaries()
        target_geometry = self._get_mock_target_geometry()

        with patch.object(
            client.boundaries_service, "fetch_geometry_str", new_callable=AsyncMock
        ) as mock_fetch:
            mock_fetch.return_value = target_geometry

            # Create source event
            source_field_geom = random_polygon()
            event_datetime = random_datetime()
            source_event_id = event.new_event_id()

            source_event = (
                TillageActivity(event_id=source_event_id, user_id=TEST_USER_ID)
                .geom(source_field_geom)
                .start(event_datetime)
                .depth(pb_value.depth_inches(3))
                .validate()
            )

            # Upsert source event
            await client.upsert_event(source_event)

            # Copy to same field three times
            target_field_id = random_md5()
            new_event_id_1 = await client.copy_event_to_field(
                source_event_id, target_field_id
            )
            new_event_id_2 = await client.copy_event_to_field(
                source_event_id, target_field_id
            )
            new_event_id_3 = await client.copy_event_to_field(
                source_event_id, target_field_id
            )

            # Verify boundaries service was called three times
            assert mock_fetch.call_count == 3

            # Should generate different UUIDs each time
            assert new_event_id_1 != new_event_id_2
            assert new_event_id_2 != new_event_id_3
            assert new_event_id_1 != new_event_id_3

            # Verify all three events exist and are separate
            copied_event_1 = await client.fetch_event(new_event_id_1)
            copied_event_2 = await client.fetch_event(new_event_id_2)
            copied_event_3 = await client.fetch_event(new_event_id_3)

            # All should have the same content (except ID) but be separate events
            assert copied_event_1.id != copied_event_2.id
            assert copied_event_2.id != copied_event_3.id
            assert copied_event_1.id != copied_event_3.id

            # All should have the target geometry
            for copied_event in [copied_event_1, copied_event_2, copied_event_3]:
                copied_geom = json.loads(copied_event.geojson)
                expected_geom = json.loads(target_geometry)
                assert copied_geom == expected_geom
