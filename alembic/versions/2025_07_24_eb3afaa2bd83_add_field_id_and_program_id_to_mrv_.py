"""Add field_id and program_id to mrv_soils_override.

Revision ID: eb3afaa2bd83
Revises: 42702cd5aa43
Create Date: 2025-07-24 17:21:06.086749

"""

from collections import defaultdict

import sqlalchemy as sa
from sqlalchemy import desc, select, update
from sqlalchemy.orm.session import Session

from alembic import op
from fields.model import Fields
from logger import get_logger
from permissions.alembic_helpers import (
    create_all_regular_roles_with_permissions,
    delete_roles_permissions_records,
)
from permissions.enums import DefaultRoles, Permission
from projects.model import Projects
from soils.model import SoilsOverride

logger = get_logger(__name__)

# revision identifiers, used by Alembic.
revision = "eb3afaa2bd83"
down_revision = "42702cd5aa43"
branch_labels = None
depends_on = None

# uncomment in case new permissions were added
regular_roles = {role.value for role in DefaultRoles}

roles_to_permissions_mapping = {
    DefaultRoles.SUPER_ADMIN.value: {
        Permission.UPSERT_SOIL_PROPERTIES.value,
    },
    DefaultRoles.PRODUCER.value: {},
    DefaultRoles.PROGRAM_ADMIN.value: {},
    DefaultRoles.EDIT_PROGRAM_ADMIN.value: {},
    DefaultRoles.RESTRICTED_USER_ADMIN.value: {},
}


def upgrade():
    op.add_column("mrv_soils_override", sa.Column("field_id", sa.Integer(), nullable=True))
    op.add_column("mrv_soils_override", sa.Column("program_id", sa.Integer(), nullable=True))
    op.add_column("mrv_soils_override", sa.Column("deleted_at", sa.DateTime(), nullable=True))

    op.create_foreign_key("mrv_soils_override_program_id", "mrv_soils_override", "mrv_programs", ["program_id"], ["id"])
    op.create_index(
        "mrv_soils_override_unique_undeleted_field_id",
        table_name="mrv_soils_override",
        columns=["field_id", sa.text("(IF(deleted_at IS NULL, 1, NULL))")],
        unique=True,
    )

    session = Session(bind=op.get_bind())
    create_all_regular_roles_with_permissions(session, regular_roles, roles_to_permissions_mapping)

    sampling_date_to_possible_program_ids = {
        "2021-10-01": {21},
        "2022-11-01": {68},
        "2023-10-23": {155},
        "2023-11-28": {116, 117},
        "2024-10-13": {253, 254, 257, 258, 259},
        "2024-02-21": {116, 117},
        "2025-04-22": {1226},
    }
    program_ids = set()
    for program_id_set in sampling_date_to_possible_program_ids.values():
        program_ids.update(program_id_set)

    try:
        # Create a mapping from md5 to field_id and program_id for all fields in the programs
        fields = session.execute(
            select(Fields.md5, Fields.id, Projects.program_id)
            .join(Projects, Projects.id == Fields.parent_project_id)
            .where(Projects.program_id.in_(program_ids))
            # Sort by deleted_at so any undeleted fields come last and overwrite deleted fields
            .order_by(desc(Fields.deleted_at))
        )
        md5_to_program_to_field = defaultdict(dict)
        for md5, field_id, program_id in fields:
            md5_to_program_to_field[md5][program_id] = field_id

        # Get all the mrv_soils_override records. Iterate over them and update the field_id and program_id. Look through
        # the program_ids for an md5 and only accept one if it's in the possible program_ids for that sampling date.

        mrv_soils_overrides = session.execute(select(SoilsOverride.id, SoilsOverride.md5, SoilsOverride.sampling_date))

        # Build mapping: soil_id -> (field_id, program_id)
        soil_id_to_field_program = {}
        soil_ids_to_delete = []

        for soil_id, md5, sampling_date in mrv_soils_overrides:
            possible_program_ids = sampling_date_to_possible_program_ids.get(sampling_date.strftime("%Y-%m-%d"))
            if not possible_program_ids:
                raise ValueError(
                    f"No possible program IDs found for sampling date: {sampling_date.strftime('%Y-%m-%d')}"
                )
            soil_field_id = None
            soil_program_id = None
            for program_id_using_field_md5 in md5_to_program_to_field[md5]:
                if program_id_using_field_md5 in possible_program_ids:
                    soil_field_id = md5_to_program_to_field[md5][program_id_using_field_md5]
                    soil_program_id = program_id_using_field_md5
                    break
            if soil_field_id and soil_program_id:
                soil_id_to_field_program[soil_id] = (soil_field_id, soil_program_id)
            else:
                soil_ids_to_delete.append(soil_id)

        if soil_id_to_field_program:
            # Bulk update for field_id and program_id
            session.execute(
                update(SoilsOverride)
                .where(SoilsOverride.id.in_(list(soil_id_to_field_program.keys())))
                .values(
                    field_id=sa.case(
                        *[(SoilsOverride.id == sid, fp[0]) for sid, fp in soil_id_to_field_program.items()]
                    ),
                    program_id=sa.case(
                        *[(SoilsOverride.id == sid, fp[1]) for sid, fp in soil_id_to_field_program.items()]
                    ),
                )
            )

        # Bulk mark as deleted
        if soil_ids_to_delete:
            session.execute(
                update(SoilsOverride).where(SoilsOverride.id.in_(soil_ids_to_delete)).values(deleted_at=sa.func.now())
            )
    except Exception as e:
        if "MockConnection" not in str(e):
            raise e


def downgrade():
    op.drop_constraint("mrv_soils_override_unique_undeleted_field_id", "mrv_soils_override", type_="unique")
    op.drop_constraint("mrv_soils_override_program_id", "mrv_soils_override", type_="foreignkey")
    op.drop_column("mrv_soils_override", "program_id")
    op.drop_column("mrv_soils_override", "field_id")
    op.drop_column("mrv_soils_override", "deleted_at")

    session = Session(bind=op.get_bind())
    delete_roles_permissions_records(session, regular_roles, roles_to_permissions_mapping)
