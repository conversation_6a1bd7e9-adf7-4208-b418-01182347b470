from abc import ABC, abstractmethod
from datetime import datetime

from fastapi import Request
from sqlalchemy import update

from domain_event_bus.domain_events import (
    DomainEvent,
    FieldBoundaryUpdatedEvent,
    FieldsCreatedEvent,
    FieldsDeleted,
)
from domain_event_bus.enums import DomainEventType
from domain_event_bus.tasks import run_field_status_change_handling
from field_lineage import tasks as field_lineage_tasks
from fields.model import Fields
from logger import get_logger
from projects.eligibility.measurement import tasks as measurement_elig_tasks
from projects.model import Projects
from ses_integration.reconcile_event_associations import (
    reconcile_event_associations_for_field_with_updated_md5,
)
from ui.projects.completion.domain_handler_processor import (
    process_field_completion_domain_event,
)
from ui.projects.completion.handle_bulk_field_completion_updates import (
    handle_multiple_fields_changing_completion,
    update_completion_for_projects_in_shared_program,
)

logger = get_logger(__name__)


class DomainEventHandler(ABC):
    @abstractmethod
    def get_supported_event_types(self) -> list[DomainEventType]:
        """Returns a list of DomainEventTypes that can be handled by handle_event."""

    @abstractmethod
    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        """runs handling code in response to the event"""


class MeasurementEligibilityEventHandler(DomainEventHandler):

    def __init__(self) -> None:
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.field_boundary_updated,
            DomainEventType.phase_completed,
            DomainEventType.project_contract_updated,
            DomainEventType.fields_deleted,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True
        measurement_elig_tasks.measurement_eligibility_task.delay(
            program_id=event.program_id,
            project_ids=[event.project_id],
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )


measurement_eligibility_event_handler = MeasurementEligibilityEventHandler()


class FieldCompletionEventHandler(DomainEventHandler):

    def __init__(self) -> None:
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.field_events_changed,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True
        # avoid too many completion internals being exposed into the domain bus
        await process_field_completion_domain_event(request, event.field_id)


field_completion_event_handler = FieldCompletionEventHandler()


class BulkFieldCompletionEventHandler(DomainEventHandler):

    def __init__(self) -> None:
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.bulk_field_events_changed,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True
        await handle_multiple_fields_changing_completion(
            request=request, project_id=event.project_id, field_ids=event.field_ids
        )


bulk_field_completion_event_handler = BulkFieldCompletionEventHandler()


class BulkProjectCompletionEventHandler(DomainEventHandler):
    def __init__(self) -> None:
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.bulk_project_events_changed,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True
        await update_completion_for_projects_in_shared_program(request=request, project_ids=event.project_ids)


bulk_project_completion_event_handler = BulkProjectCompletionEventHandler()


class EntityValueLastUpdatedAtEventHandler(DomainEventHandler):

    def __init__(self) -> None:
        self.called = False

    def get_supported_event_types(self) -> list[DomainEventType]:
        return [
            DomainEventType.field_events_changed,
            DomainEventType.field_values_changed,
        ]

    async def handle_event(
        self,
        request: Request,
        event: DomainEvent,
    ) -> None:
        self.called = True
        now = datetime.now()
        async with request.state.sql_session() as session:
            await session.execute(update(Fields).where(Fields.id == event.field_id).values(value_last_updated_at=now))
            await session.execute(
                update(Projects).where(Projects.id == event.project_id).values(value_last_updated_at=now)
            )
            await session.commit()


entity_value_last_updated_at_event_handler = EntityValueLastUpdatedAtEventHandler()


class FieldLineageEventHandler(DomainEventHandler):
    def get_supported_event_types(self) -> list[DomainEventType]:
        return [DomainEventType.fields_created, DomainEventType.field_boundary_updated]

    async def handle_event(self, request: Request, event: DomainEvent) -> None:
        if isinstance(event, FieldBoundaryUpdatedEvent) or isinstance(event, FieldsCreatedEvent):
            field_ids = (
                event.field_ids if isinstance(event, FieldsCreatedEvent) else list(event.field_id_to_old_md5.keys())
            )
            set_field_lineage_and_relationship = (
                event.set_field_lineage_and_relationship if isinstance(event, FieldsCreatedEvent) else False
            )
            field_lineage_tasks.set_field_baselines_task.delay(
                program_id=event.program_id,
                project_id=event.project_id,
                field_ids=field_ids,
                set_field_lineage=set_field_lineage_and_relationship,
                set_field_relationship=set_field_lineage_and_relationship,
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            )


field_lineage_event_handler = FieldLineageEventHandler()


class ReconcileEventAssociationsHandler(DomainEventHandler):
    def get_supported_event_types(self) -> list[DomainEventType]:
        return [DomainEventType.field_boundary_updated]

    async def handle_event(self, request: Request, event: DomainEvent) -> None:
        if isinstance(event, FieldBoundaryUpdatedEvent):
            logger.debug(
                f"Kicking off reconcile event associations for updated field MD5s: {event.field_id_to_old_md5}"
            )
            for field_id, old_field_md5 in event.field_id_to_old_md5.items():
                reconcile_event_associations_for_field_with_updated_md5.delay(
                    field_id=field_id,
                    old_field_md5=old_field_md5,
                    from_time=event.update_time,
                    fs_user_id=request.state.fs_user_id,
                    fs_impersonator_user_id=request.state.fs_impersonator_user_id,
                )


reconcile_event_associations_handler = ReconcileEventAssociationsHandler()


class FieldStatusChangeHandler(DomainEventHandler):
    def get_supported_event_types(self) -> list[DomainEventType]:
        return [DomainEventType.fields_created, DomainEventType.fields_deleted]

    async def handle_event(self, request: Request, event: DomainEvent) -> None:
        self.called = True
        if isinstance(event, FieldsCreatedEvent):
            project_id = event.project_id
        elif isinstance(event, FieldsDeleted):
            project_id = event.project_id
        else:
            raise AssertionError(f"Unexpected event {event} for FieldStatusChangeHandler")
        run_field_status_change_handling.delay(
            project_id=project_id,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )


field_status_change_handler = FieldStatusChangeHandler()
