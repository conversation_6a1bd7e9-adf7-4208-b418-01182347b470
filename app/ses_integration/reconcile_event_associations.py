import asyncio
from datetime import datetime, UTC
from uuid import UUID

import elasticapm
from fastapi import Request
from regrow.ses.event.v1 import event_pb2
from ses_client.client import Client
from ses_client.search import Filter

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from fields.db import (
    get_field_by_id,
    get_fields_by_project_id,
    get_project_ids_by_field_ids,
)
from helper.list_helper import group_by
from logger import get_logger
from projects.db import get_owner_user_id_for_project, get_projects_by_program_id
from projects.schema import Project
from ses_integration.constants import MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE
from ses_integration.db import get_user_owned_field_event_associations
from ses_integration.event_associations import (
    associate_events_with_fields_of_md5,
    delete_event_associations,
)
from ses_integration.schema import EventRevision

logger = get_logger(__name__)
settings = get_settings()

ses_client = Client(
    grpc_host_addr=settings.SES_INTERNAL_URL_BASE, search_host_addr=settings.SES_SEARCH_INTERNAL_URL_BASE
)


async def reconcile_event_associations_for_program(
    request: Request,
    program_id: int,
    project_ids: list[int] | None,
    field_ids: list[int] | None,
    include_completed_phases: bool = False,
) -> None:
    """
    Refresh event associations for the given program from SES. It will add any missing associations,
    update existing ones, and remove any that are no longer present in SES.

    May optionally be filtered by specific projects and/or fields.
    If include_completed_phases is True, completed phases will be included in the reconciliation.

    Returns nothing, but reconciled event associations will be available once this completes.
    """
    project_id_set = set(project_ids) if project_ids else set()
    if field_ids:
        project_ids_for_fields = set(await get_project_ids_by_field_ids(request, field_ids))
        project_id_set = project_ids_for_fields if not project_ids else set(project_ids) & project_ids_for_fields
    projects = await get_projects_by_program_id(request, program_id)
    if project_id_set:
        projects = [project for project in projects if project.id in project_id_set]
    logger.info(f"Refreshing event associations for program {program_id} with {len(projects)} projects")

    _reconcile_event_associations_for_projects.delay(
        projects=projects,
        field_ids=field_ids,
        include_completed_phases=include_completed_phases,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


async def reconcile_event_associations_for_field(
    request: Request,
    field_id: int,
) -> None:
    """
    Refresh event associations for the given field from SES. It will add any missing associations,
    update existing ones, and remove any that are no longer present in SES.

    Returns nothing, but reconciled event associations will be available once this completes.
    """
    field = await get_field_by_id(request, field_id)
    owner_user_id = await get_owner_user_id_for_project(request, field.parent_project_id)
    logger.info(f"Refreshing event associations for field md5 {field.md5} in project {field.parent_project_id}")

    # get all events from SES for owner
    field_md5_to_events = await ses_client.fetch_events_for_fields(
        field_ids=[field.md5],
        search_filter=Filter(owner_ids=[owner_user_id], min_overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE),
    )

    # upsert and delete existing associations
    for field_md5, events in field_md5_to_events.items():
        await _reconcile_event_associations_for_md5(
            request=request,
            owner_user_id=owner_user_id,
            project_id=field.parent_project_id,
            field_md5=field_md5,
            events=events,
        )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def _reconcile_event_associations_for_projects(
    self: DBTask,
    *,
    projects: list[Project],
    field_ids: list[int] | None = None,
    include_completed_phases: bool = False,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    for project in projects:
        await _reconcile_event_associations_for_project(
            request=request,
            project=project,
            field_ids=field_ids,
            include_completed_phases=include_completed_phases,
        )


async def _reconcile_event_associations_for_project(
    request: Request,
    project: Project,
    field_ids: list[int] | None = None,
    include_completed_phases: bool = False,
) -> None:
    owner_user_id = await get_owner_user_id_for_project(request, project.id)
    fields = await get_fields_by_project_id(request=request, project_id=project.id)
    fields = [field for field in fields if (not field_ids or field.id in field_ids)]
    field_md5_to_fields = group_by(fields, lambda field: field.md5)
    field_md5s = list(field_md5_to_fields.keys())
    if not field_md5s:
        logger.info(f"No fields found for project {project.id}, skipping event association reconcile.")
        return
    logger.info(
        f"Refreshing event associations for project {project.id} with {len(field_md5s)} field md5s {field_md5s}"
    )

    # get all events from SES for owner
    field_md5_to_events = await ses_client.fetch_events_for_fields(
        field_md5s,
        Filter(
            owner_ids=[owner_user_id],
            exclude_archived=False,
            min_overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
        ),
    )
    logger.debug(f"Found {len(field_md5_to_events)} events for project {project.id}")

    # upsert and delete existing associations
    for field_md5, events in field_md5_to_events.items():
        await _reconcile_event_associations_for_md5(
            request=request,
            owner_user_id=owner_user_id,
            project_id=project.id,
            field_md5=field_md5,
            events=events,
            include_completed_phases=include_completed_phases,
        )


async def _reconcile_event_associations_for_md5(
    request: Request,
    owner_user_id: str,
    project_id: int,
    field_md5: str,
    events: list[event_pb2.Event],
    include_completed_phases: bool = False,
) -> None:
    logger.info(f"Reconciling {len(events)} events for field {field_md5}: {[ev.id for ev in events]}")

    await _delete_orphaned_event_associations(
        request=request, owner_user_id=owner_user_id, project_id=project_id, field_md5=field_md5, events=events
    )

    event_revisions = [
        EventRevision(event_id=UUID(ev.id), revision=ev.revision) for ev in events if not ev.HasField("archived")
    ]
    await associate_events_with_fields_of_md5(
        request=request,
        owner_user_id=owner_user_id,
        project_id=project_id,
        field_md5=field_md5,
        event_revisions=event_revisions,
        include_completed_phases=include_completed_phases,
    )


async def _delete_orphaned_event_associations(
    request: Request, owner_user_id: str, project_id: int, field_md5: str, events: list[event_pb2.Event]
) -> None:
    all_field_event_associations = await get_user_owned_field_event_associations(
        request=request, owner_user_id=owner_user_id, project_ids=[project_id], field_md5=field_md5
    )

    # delete associations for events that are no longer present in SES - these are force deleted as they are wholly
    # deleted, not just archived
    deleted_event_ids = [
        UUID(fea.ses_event_id)
        for fea in all_field_event_associations
        if fea.ses_event_id not in {ev.id for ev in events}
    ]
    logger.info(f"Deleting field event associations for {len(deleted_event_ids)} deleted` events: {deleted_event_ids}")
    await delete_event_associations(
        request=request, event_ids=deleted_event_ids, owner_user_id=owner_user_id, force_delete_in_completed_phases=True
    )

    # delete associations archived events, delete_event_associations will ensure that only associations for open phases are deleted
    archived_event_ids = [UUID(ev.id) for ev in events if ev.HasField("archived")]
    logger.info(
        f"Potentially deleting field event associations for {len(archived_event_ids)} archived events: {archived_event_ids}"
    )
    await delete_event_associations(request=request, event_ids=archived_event_ids, owner_user_id=owner_user_id)


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def reconcile_event_associations_for_field_with_updated_md5(
    self: DBTask,
    *,
    field_id: int,
    old_field_md5: str | None = None,
    from_time: datetime,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    field = await get_field_by_id(request, field_id)
    new_field_md5 = field.md5

    logger.info(f"Polling SES for field md5 from {old_field_md5} to {new_field_md5} for Field {field_id}")

    while True:
        old_md5_updated_at = await ses_client.get_last_update_for_field(old_field_md5) if old_field_md5 else None
        new_md5_updated_at = await ses_client.get_last_update_for_field(new_field_md5) if new_field_md5 else None
        logger.debug(
            f"Field {field_id} md5s {old_field_md5}, {new_field_md5} last updated at {old_md5_updated_at}, {new_md5_updated_at}"
        )
        if (old_md5_updated_at and old_md5_updated_at > from_time) or (
            new_md5_updated_at and new_md5_updated_at > from_time
        ):
            logger.info(
                f"Field {field_id} md5s {old_field_md5}, {new_field_md5} have been updated since {from_time}, "
                f"reconciling event associations."
            )
            break

        if (datetime.now(tz=UTC) - from_time).seconds >= settings.SES_RECONCILIATION_POLLING_INTERVAL_TIMEOUT_SECONDS:
            logger.info(
                f"Field {field_id} md5s {old_field_md5}, {new_field_md5} have not apparently been updated since "
                f"{from_time}, reconciling event associations in case."
            )
            break
        logger.debug(
            f"Field {field_id} md5s {old_field_md5}, {new_field_md5} have not been updated since "
            f"{old_md5_updated_at}, {new_md5_updated_at}, polling in {settings.SES_RECONCILIATION_POLLING_INTERVAL_SECONDS}s."
        )
        await asyncio.sleep(settings.SES_RECONCILIATION_POLLING_INTERVAL_SECONDS)

    owner_user_id = await get_owner_user_id_for_project(request=request, project_id=field.parent_project_id)

    field_md5_to_events = await ses_client.fetch_events_for_fields(
        field_ids=[new_field_md5],
        search_filter=Filter(owner_ids=[owner_user_id], min_overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE),
    )

    events = field_md5_to_events.get(new_field_md5, [])
    if events:
        await _reconcile_event_associations_for_md5(
            request=request,
            owner_user_id=owner_user_id,
            field_md5=new_field_md5,
            project_id=field.parent_project_id,
            events=events,
        )

    if old_field_md5 is not None:
        logger.info(f"Deleting event associations for old field md5 {old_field_md5} for Field {field_id}")
        await delete_event_associations(
            request=request, owner_user_id=owner_user_id, field_md5=old_field_md5, field_id=field_id
        )
