from fastapi import Request

from field_lineage.db import get_fields_by_field_lineage_id_asc
from fields.db import get_fields_by_md5s_and_program_id
from fields.model import Fields
from fields.schema import Field, SoilPropertiesData, SoilPropertiesResponse
from logger import capture_exception, get_logger
from root_crud import create, delete, get
from soils.model import SoilsOverride

logger = get_logger(__name__)


async def upsert_soil_properties(
    request: Request, program_id: int, soil_properties_data: list[SoilPropertiesData]
) -> list[SoilPropertiesResponse]:
    """
    Upsert soil properties for a given program. The soil properties only contain a field md5, so the field_id has to be
    looked up.
    """
    fields: list[Fields] = await get_fields_by_md5s_and_program_id(
        request=request,
        md5s=[item.md5 for item in soil_properties_data],
        program_id=program_id,
    )

    # If any fields are not found, log an error
    if len(fields) != len(soil_properties_data):
        missing_md5s = {item.md5 for item in soil_properties_data} - {field.md5 for field in fields}
        capture_exception(
            logger,
            ValueError(
                f"Fields with md5s {missing_md5s} not found for program_id {program_id}. Soil properties will"
                "not be created for these fields."
            ),
        )

    # Assign the program_id
    soils_to_save = []
    for soil_properties in soil_properties_data:
        soil_properties.program_id = program_id
        soil_properties.field_id = next((field.id for field in fields if field.md5 == soil_properties.md5), None)
        if soil_properties.field_id is not None:
            soils_to_save.append(soil_properties)

    # First soft delete any existing soils with provided field_ids
    if soils_to_save:
        await delete.soft(
            request=request,
            orm_type=SoilsOverride,
            id_field=SoilsOverride.field_id,
            ids=[soil.field_id for soil in soils_to_save],
        )
    return await create.create(
        request=request, instances=soils_to_save, orm_type=SoilsOverride, type=SoilPropertiesResponse
    )


async def get_soil_properties_by_field_lineage(request: Request, field: Field) -> SoilsOverride:
    """
    Fetches soil properties for a given field lineage. If multiple records are found, the most recently sampled soil
    properties are used.

    This function is not fully implemented because the 155 Inventory rerun only needs to look soils up by the current
    lineage, but doesn't yet need to trace lineage relationships to find soils farther back. Nor is it clear that
    we'd want to do that from a protocol perspective. Also the lineage data for Cargill US is not in the lineage tables
    that are under development, so writing the full lineage-tracing implementation against the legacy data model would
    be premature.

    For now, this function will continue to fall back to field md5 to look up soils. Once we are confident that we have
    field lineage populated for all fields, we can remove the md5 lookup. This lookup is not user-controlled since
    fields can change hands.

    Returns the most recently sampled SoilsOverride object for an mrv_field in the current lineage.
    """
    # Note that we do not filter out fields from future program years. Such fields would only have new soils data if
    # they experience a lineage change / baseline reset.
    fields = (
        await get_fields_by_field_lineage_id_asc(
            request=request, field_lineage_id=field.field_lineage_id, enrolled_only=True
        )
        if field.field_lineage_id
        else [field]
    )
    soil_overrides: list[SoilsOverride] = await get.get(
        request=request,
        orm_type=SoilsOverride,
        id_field=SoilsOverride.field_id,
        ids=[fld.id for fld in fields],
        empty_return=True,
        order_by_cols=[SoilsOverride.sampling_date],
        order_by_desc=True,
    )
    if not soil_overrides:
        # Look up soil_overrides by field md5
        soil_overrides = await get.get(
            request=request,
            orm_type=SoilsOverride,
            id_field=SoilsOverride.md5,
            ids=[fld.md5 for fld in fields],
            empty_return=True,
            order_by_cols=[SoilsOverride.sampling_date],
            order_by_desc=True,
        )
    return soil_overrides[0] if soil_overrides else None
