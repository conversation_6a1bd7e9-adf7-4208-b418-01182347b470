from typing import Union

import httpx
from scenarios_service_client.inventory_api_client import InventoryApiAsync<PERSON>lient

from config import get_settings
from logger import get_logger
from scenarios_service.generalized_integration.schema import (
    ConsumerConfiguration,
)

settings = get_settings()
logger = get_logger(__name__)

inventory_client = InventoryApiAsyncClient(settings.SCENARIOS_SERVICE_INTERNAL_URL, timeout=600)


async def get_inventory_consumer_configurations(
    consumer_config_id: str | None = None,
) -> Union[list[ConsumerConfiguration], dict[str, str]]:

    try:
        response = (await inventory_client.list_inventory_consumer_configurations(consumer_config_id)).json()
    except httpx.HTTPStatusError as exc:
        response = {
            "error": "Scenarios Service threw error: "
            + exc.response.text
            + " with status code: "
            + str(exc.response.status_code)
        }
    return response


async def get_dndc_version_for_inventory_config(consumer_config_id: str) -> str | None:
    consumer_configs = await get_inventory_consumer_configurations(consumer_config_id)
    if not consumer_configs or isinstance(consumer_configs, dict):
        logger.error(f"No inventory consumer configuration found for consumer_id {consumer_config_id}")
        return None

    for config in consumer_configs:
        if config["x_consumer_id"] == consumer_config_id:
            return config.get("dndc_version", None)

    logger.error(f"No dndc_version found for consumer_id {consumer_config_id}")
    return None
