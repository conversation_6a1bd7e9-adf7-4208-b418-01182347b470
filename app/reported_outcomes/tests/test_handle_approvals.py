# from unittest.mock import ANY

from reported_outcomes.handle_approvals import handle_storing_program_outcomes
from reported_outcomes.schema import (  # ReportedBiofuelsProgramOutcomes,; ReportedInventoryProgramOutcomes,
    ReportedProgramOutcomes,
)


# include mocker in future work
async def test_handle_storing_program_outcomes(app_request) -> None:
    program_id = 5
    program_outcomes = ReportedProgramOutcomes(
        program_id=program_id,
        field_outcomes={},
        crop_outcomes={},
        program_outcomes=None,
        task_id="mock_task_id",
        is_test_run=True,
    )
    # mock_run_storage_build = mocker.patch("reported_outcomes.tasks.run_build_of_combined_zip_for_program")
    await handle_storing_program_outcomes(app_request, program_outcomes, program_outcomes.task_id)
    # mock_run_storage_build.delay.assert_called_once_with(
    #    fs_impersonator_user_id=ANY, fs_user_id=ANY, program_id=program_id, task_id=program_outcomes.task_id
    # )
