from fastapi import Request

# commented-out code is future work
# import reported_outcomes.tasks as reported_outcome_tasks
from reported_outcomes.db import (
    revoke_program_outcome_approval,
    store_biofuels_program_outcomes,
    store_inventory_program_outcomes,
    store_program_outcomes,
)
from reported_outcomes.schema import (
    ReportedBiofuelsProgramOutcomes,
    ReportedInventoryProgramOutcomes,
    ReportedProgramOutcomes,
)

# from reported_outcomes.storage import delete_task_combined_outcomes

# It was always kind of weird that the scenario_service layer called
# the reported_outcomes database layer directly, but now we also want
# to managed the storage of combined outcomes, so it was better to have
# handlers for all those interactions


async def handle_storing_program_outcomes(
    request: Request, program_outcomes: ReportedProgramOutcomes, task_id: str
) -> None:
    # await delete_task_combined_outcomes(request, program_outcomes.program_id, task_id)
    await store_program_outcomes(request, program_outcomes)
    # reported_outcome_tasks.run_build_of_combined_zip_for_program.delay(
    #    program_id=program_outcomes.program_id,
    #    task_id=task_id,
    #    fs_user_id=request.state.fs_user_id,
    #    fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    # )


async def handle_storing_inventory_program_outcomes(
    request: Request, program_outcomes: ReportedInventoryProgramOutcomes, task_id: str
) -> None:
    # await delete_task_combined_outcomes(request, program_outcomes.program_id, program_outcomes.task_id)
    await store_inventory_program_outcomes(request, program_outcomes)
    # reported_outcome_tasks.run_build_of_combined_zip_for_program.delay(
    #    program_id=program_outcomes.program_id,
    #    task_id=task_id,
    #    fs_user_id=request.state.fs_user_id,
    #    fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    # )


async def handle_storing_biofuels_program_outcomes(
    request: Request, program_outcomes: ReportedBiofuelsProgramOutcomes, task_id: str
) -> None:
    # await delete_task_combined_outcomes(request, program_outcomes.program_id, task_id)
    await store_biofuels_program_outcomes(request, program_outcomes)
    # reported_outcome_tasks.run_build_of_combined_zip_for_program.delay(
    #    program_id=program_outcomes.program_id,
    #    task_id=task_id,
    #    fs_user_id=request.state.fs_user_id,
    #    fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    # )


async def handle_revoking_program_outcome_approval(request: Request, program_id: int, task_id: str) -> None:
    # await delete_task_combined_outcomes(request, program_id, task_id)
    await revoke_program_outcome_approval(request, program_id, task_id)
