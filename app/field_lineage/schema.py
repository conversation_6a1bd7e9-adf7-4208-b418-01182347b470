from dataclasses import dataclass

from pydantic import BaseModel

from field_lineage.enums import FieldRelationshipType


@dataclass
class MatchedFields:
    """
    field_id: The ID of the field
    md5: The MD5 of the field
    previous_field_id: The ID of the field in the previous program that the field was matched to
    previous_md5: The MD5 of the field in the previous program that the field was matched to
    percent_intersection_of_field_by_previous: The percent of the field intersected by the previous field
    percent_intersection_of_previous_by_field: The percent of the previous field intersected by the field
    """

    field_id: int
    md5: str
    previous_field_id: int
    previous_md5: str
    previous_field_lineage_id: int
    percent_intersection_of_field_by_previous: float
    percent_intersection_of_previous_by_field: float


class SetFieldBaselinesRequest(BaseModel):
    program_id: int
    project_id: int
    field_ids: list[int]
    set_field_lineage: bool = False
    set_field_relationship: bool = False
    set_field_baseline: bool = True


class FieldLineageRequest(BaseModel):
    baseline_field_id: int
    previous_lineage_ids: list[int]

    class Config:
        orm_mode = True


class FieldRelationshipRequest(BaseModel):
    field_id: int
    previous_field_id: int
    percent_intersection: float
    area_intersection: float
    relationship: FieldRelationshipType | None

    class Config:
        orm_mode = True


class FieldRelationshipUpdate(BaseModel):
    percent_intersection: float | None = None
    area_intersection: float | None = None
    relationship: FieldRelationshipType | None = None
