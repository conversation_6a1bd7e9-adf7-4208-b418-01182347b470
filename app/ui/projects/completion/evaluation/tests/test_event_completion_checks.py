import datetime
from uuid import UUID

from defaults.attribute_options import (
    CropUsage,
    IrrigationMethods,
    ResidueHarvested,
    TerminationMethods,
    TillagePractice,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from entity_events.units import LengthUnit
from fields.enums import FieldStatus
from logger import get_logger
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ui.projects.completion.constants import (
    RESIDUE_HARVESTED_MISSING_MSG,
    TERMINATION_METHOD_MISSING_MSG,
)
from ui.projects.completion.dataclasses import EvaluationContext
from ui.projects.completion.evaluation.event_completion_checks import (
    _find_cropping_specific_failures,
    check_cropping_event_completion,
    check_event_completion,
    check_irrigation_event_completion,
)
from ui.projects.completion.specification.validation_specification import (
    generate_validation_specification_for_program,
)
from values.enums import EntityTypeChoices

logger = get_logger(__name__)


async def test_check_missing_residue_harvested_fails_cropping_event_completion(
    app_request, mdl, create_cropping_event_data
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = create_cropping_event_data(residue_harvested=None)
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_cropping_event_completion(app_request, crop_event, ec, vs)
    assert not result.passed_evaluation
    assert RESIDUE_HARVESTED_MISSING_MSG in result.failed_rules


async def test_check_missing_termination_method_fails_cropping_event_completion(
    app_request, mdl, create_cropping_event_data
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = create_cropping_event_data(termination_method=None, crop_usage=CropUsage.COVER)
    logger.info(f"cropping_event_data: {cropping_event_data}")
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_cropping_event_completion(app_request, crop_event, ec, vs)
    assert not result.passed_evaluation
    assert TERMINATION_METHOD_MISSING_MSG in result.failed_rules


async def test_check_event_completion_for_cropping_event(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = create_cropping_event_data()
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert result.passed_evaluation


async def test_check_cover_does_not_require_yields_in_cropping_event(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        is_single_phase_data_collection=True,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = create_cropping_event_data(crop_usage=CropUsage.COVER, crop_yield=None)
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert result.passed_evaluation


async def test_check_invalid_crop_for_cropping_event(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = create_cropping_event_data(crop_type="chickpea")
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert not result.passed_evaluation


async def test_check_event_non_completion_for_cropping_event(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    crop_event = CroppingEvent(
        id=CroppingIDs(
            planting_id=None,
            sowing_id=UUID("ddc709e1-662a-53ba-83e3-3a5fa3d422d2"),
            harvesting_id=UUID("695e3470-2716-57c1-a653-45eb0c6068dc"),
            termination_id=None,
            fallow_id=None,
        ),
        entity_id=275490,
        entity_type=EntityTypeChoices.field,
        is_intended=False,
        interval=Interval(
            start=datetime.datetime(2022, 4, 20, 0, 0, tzinfo=datetime.timezone.utc),
            end=datetime.datetime(2022, 10, 23, 0, 0, tzinfo=datetime.timezone.utc),
        ),
        crop_usage=CropUsage.COMMODITY,
        crop_yield=None,
        planting_rate=None,
        reductions=[],
        residue_harvested=None,
        termination_method=None,
        planting_method=None,
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert not result.passed_evaluation


def test_find_cropping_specific_failures(create_cropping_event_data):
    event = CroppingEvent.parse_obj(create_cropping_event_data())
    for usage in CropUsage:
        if usage in (CropUsage.BASIC_COVER_CROP, CropUsage.PREMIUM_COVER_CROP, CropUsage.GRAZING):
            continue

        event.crop_usage = usage

        if usage in (CropUsage.COMMODITY, CropUsage.FORAGE_HARVEST, CropUsage.FORAGE_HARVEST_AND_GRAZING):
            event.residue_harvested = None
            failures = _find_cropping_specific_failures(event)
            assert RESIDUE_HARVESTED_MISSING_MSG in failures
            assert TERMINATION_METHOD_MISSING_MSG not in failures
            event.residue_harvested = ResidueHarvested.percent_50
            failures = _find_cropping_specific_failures(event)
            assert RESIDUE_HARVESTED_MISSING_MSG not in failures
            assert TERMINATION_METHOD_MISSING_MSG not in failures
        elif usage in (CropUsage.COVER, CropUsage.CATCH_CROP, CropUsage.COMPANION_CROP):
            event.termination_method = None
            failures = _find_cropping_specific_failures(event)
            assert RESIDUE_HARVESTED_MISSING_MSG not in failures
            assert TERMINATION_METHOD_MISSING_MSG in failures
            event.termination_method = TerminationMethods.winterkill
            failures = _find_cropping_specific_failures(event)
            assert RESIDUE_HARVESTED_MISSING_MSG not in failures
            assert TERMINATION_METHOD_MISSING_MSG not in failures
        else:
            raise AssertionError(f"Unexpected usage: {usage}")

    event.crop_usage = None
    failures = _find_cropping_specific_failures(event)


async def test_check_no_yield_passes_enrollment(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = {
        "id": {
            "sowing_id": UUID("052e6b89-9ebb-4a0d-a6eb-d64e778fe11f"),
            "harvesting_id": UUID("42157f99-9245-4da5-be91-a80dceb38ced"),
        },
        "entity_id": 123,
        "entity_type": EntityTypeChoices.field,
        "interval": {"start": "2023-03-15T07:00:00+00:00", "end": "2023-03-18T07:00:00+00:00"},
        "crop_type": "corn",
        "crop_usage": CropUsage.COMMODITY,
        "crop_yield": None,
        "residue_harvested": ResidueHarvested.percent_50,
        "reductions": [
            {
                "occurred_at": "2023-03-18T07:00:00+00:00",
                "root_removed_fraction": 0.25,
                "root_residue_fraction": 0.5,
                "stem_removed_fraction": 0.25,
                "stem_residue_fraction": 0.5,
                "leaf_removed_fraction": 0.5,
                "leaf_residue_fraction": 0.5,
                "grain_removed_fraction": 0.75,
                "grain_residue_fraction": 0.05,
            }
        ],
    }
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert result.passed_evaluation


async def test_check_no_yield_fails_monitoring(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = {
        "id": {
            "sowing_id": UUID("052e6b89-9ebb-4a0d-a6eb-d64e778fe11f"),
            "harvesting_id": UUID("42157f99-9245-4da5-be91-a80dceb38ced"),
        },
        "entity_id": 123,
        "entity_type": EntityTypeChoices.field,
        "interval": {"start": "2023-03-15T07:00:00+00:00", "end": "2023-03-18T07:00:00+00:00"},
        "crop_type": "corn",
        "crop_usage": CropUsage.COMMODITY,
        "crop_yield": None,
        "residue_harvested": ResidueHarvested.percent_50,
        "reductions": [
            {
                "occurred_at": "2023-03-18T07:00:00+00:00",
                "root_removed_fraction": 0.25,
                "root_residue_fraction": 0.5,
                "stem_removed_fraction": 0.25,
                "stem_residue_fraction": 0.5,
                "leaf_removed_fraction": 0.5,
                "leaf_residue_fraction": 0.5,
                "grain_removed_fraction": 0.75,
                "grain_residue_fraction": 0.05,
            }
        ],
    }
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert not result.passed_evaluation


async def test_too_long_event(app_request, mdl, yield_rate_data, create_interval_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    cropping_event_data = {
        "entity_id": 123,
        "entity_type": EntityTypeChoices.field,
        "interval": create_interval_data(start="2023-12-26T07:00:00+00:00", end="2025-06-27T07:00:00+00:00"),
        "crop_type": "camelina",
        "crop_yield": yield_rate_data,
        "yield_rate_unit": "kg/ha",
        "termination_method": None,
        "residue_harvested": ResidueHarvested.percent_50,
        "crop_usage": "Commodity",
    }
    crop_event = CroppingEvent.parse_obj(cropping_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, crop_event, ec, vs)
    assert result.passed_evaluation is False
    assert "A cropping event cannot be longer than 12 months." in result.failed_rules
    assert len(result.failed_rules) == 1
    assert len(result.unevaluated_rules) == 0


async def test_check_event_completion_for_fallow_period(app_request, mdl, create_fallow_period_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.CROP_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    fallow_period_data = create_fallow_period_data()
    fallow_period = FallowPeriod.parse_obj(fallow_period_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, fallow_period, ec, vs)
    assert result.passed_evaluation


async def test_check_event_completion_for_tillage_event(app_request, mdl, create_tillage_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event_data = create_tillage_event_data()
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, tillage_event, ec, vs)
    assert result.passed_evaluation


async def test_check_missing_soil_inversion_fails_event_completion_for_tillage_event(
    app_request, mdl, create_tillage_event_data
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event_data = {
        "id": "052e6b89-9ebb-4a0d-a6eb-d64e778fe11f",
        "entity_id": 123,
        "entity_type": EntityTypeChoices.field,
        "occurred_at": "2023-03-15T07:00:00+00:00",
        "depth": {"value": 4.5, "unit": LengthUnit.CENTIMETRE},
        "strip_frac": None,
        "tillage_practice": TillagePractice.conventional_till,
    }
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, tillage_event, ec, vs)
    assert not result.passed_evaluation


async def test_check_missing_soil_inversion_passes_event_completion_for_reduced_tillage(
    app_request, mdl, create_tillage_event_data
):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    tillage_event_data = {
        "id": "052e6b89-9ebb-4a0d-a6eb-d64e778fe11f",
        "entity_id": 123,
        "entity_type": EntityTypeChoices.field,
        "occurred_at": "2023-03-15T07:00:00+00:00",
        "depth": {"value": 4.5, "unit": LengthUnit.CENTIMETRE},
        "strip_frac": None,
        "tillage_practice": TillagePractice.reduced_till,
        "soil_inversion": False,
    }
    tillage_event = TillageEvent.parse_obj(tillage_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, tillage_event, ec, vs)
    assert result.passed_evaluation


async def test_check_event_non_completion_for_tillage_event(app_request, mdl, create_cropping_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    tillage_event = TillageEvent.parse_obj(
        {"entity_id": 5, "entity_type": EntityTypeChoices.field, "occurred_at": "2023-12-26T07:00:00+00:00"}
    )
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, tillage_event, ec, vs)
    assert not result.passed_evaluation


async def test_check_event_completion_for_irrigation_event(app_request, mdl, create_irrigation_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.IRRIGATION_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    irrigation_event_data = create_irrigation_event_data()
    irrigation_event = IrrigationEvent.parse_obj(irrigation_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_irrigation_event_completion(app_request, irrigation_event, ec, vs)
    assert result.passed_evaluation


async def test_check_missing_flood_percent_for_irrigation_event(app_request, mdl, create_irrigation_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.IRRIGATION_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    irrigation_event_data = create_irrigation_event_data(method=IrrigationMethods.furrow, flood_percentage=None)
    irrigation_event = IrrigationEvent.parse_obj(irrigation_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_irrigation_event_completion(app_request, irrigation_event, ec, vs)
    assert not result.passed_evaluation


async def test_check_event_completion_for_application_event(app_request, mdl, create_application_event_data):
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS, phase_id=phase.id, deleted_at=None, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    application_event_data = create_application_event_data()
    application_event = ApplicationEvent.parse_obj(application_event_data)
    ec = EvaluationContext(program=program, field=field, phase=phase, completion_end_date=datetime.date(2025, 10, 10))
    vs = await generate_validation_specification_for_program(app_request, ec)
    result = await check_event_completion(app_request, application_event, ec, vs)
    assert result.passed_evaluation
