from datetime import datetime, timezone

from scenarios_service_schema.internal.soil import Soil

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from fields.schema import Field
from programs.model import Programs
from scenarios_service.generalized_integration.session_inputs_helpers import (
    filter_non_required_historical_cultivation_cycles,
    get_ss_soil_for_field,
)


async def test_filter_non_required_historical_cultivation_cycles_does_not_filter_when_no_required_historical_years():
    program = Programs()
    cultivation_cycles = [
        _cc(datetime(2022, 1, 1), datetime(2022, 12, 31)),
        _cc(datetime(2023, 1, 1), datetime(2023, 12, 31)),
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles


async def test_filter_non_required_historical_cultivation_cycles_when_ccs_are_less_than_required_years():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles


async def test_filter_non_required_historical_cultivation_cycles_filters_when_required_historical_years():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2022, 1, 1), datetime(2022, 12, 31)),
        _cc(datetime(2023, 1, 1), datetime(2023, 12, 31)),
        _cc(datetime(2024, 1, 1), datetime(2024, 12, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


async def test_filter_non_required_historical_cultivation_cycles_filters_when_ccs_years_are_offset():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2021, 11, 1), datetime(2022, 10, 31)),
        _cc(datetime(2022, 11, 1), datetime(2023, 10, 31)),
        _cc(datetime(2023, 11, 1), datetime(2024, 10, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


async def test_filter_non_required_historical_cultivation_cycles_filters_when_ccs_are_less_than_a_year():
    program = Programs()
    program.required_years_of_history = 2
    cultivation_cycles = [
        _cc(datetime(2020, 2, 1), datetime(2021, 10, 31)),
        _cc(datetime(2021, 11, 1), datetime(2022, 1, 31)),
        _cc(datetime(2022, 2, 1), datetime(2022, 10, 31)),
        _cc(datetime(2022, 11, 1), datetime(2023, 1, 31)),
        _cc(datetime(2023, 2, 1), datetime(2023, 10, 31)),
    ]
    filtered_ccs = filter_non_required_historical_cultivation_cycles(program, cultivation_cycles)
    assert filtered_ccs == cultivation_cycles[1:]


def _cc(start: datetime, end: datetime) -> CultivationCycle:
    return CultivationCycle(
        id=CultivationCycleId(start_date=start, end_date=end),
        start=start.replace(tzinfo=timezone.utc),
        end=end.replace(tzinfo=timezone.utc),
    )


async def test_get_ss_soil_for_field(mdl, app_request):
    program = await mdl.Programs(
        crediting_year=2023,
        reporting_period_start_date=datetime(2024, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2024, 12, 31, 23, 59, 59),
    )
    _ = await mdl.ProgramModelingConfigurations(program_id=program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    _ = await mdl.SoilsOverride(
        md5=field.md5, pred_bd=0.1, pred_soc=0.1, ph=3.0, clay_fraction=0.0, sampling_date=datetime(2022, 1, 1)
    )
    _ = await mdl.SoilsOverride(
        field_id=field.id,
        program_id=program.id,
        md5=field.md5,
        pred_bd=0.5,
        pred_soc=1.0,
        ph=5.0,
        clay_fraction=0.1,
        sampling_date=datetime(2023, 1, 1),
    )

    soil = await get_ss_soil_for_field(app_request, Field.from_orm(field))
    expected_soil = Soil(
        bulk_density=0.5,
        soc=0.01,
        ph=5.0,
        clay_fraction=0.1,
    )
    assert soil == expected_soil
