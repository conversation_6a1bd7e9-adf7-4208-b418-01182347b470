from datetime import datetime, timedelta, timezone
from uuid import uuid4

import pytest

from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.fire_event import FireEvent
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import ApplicationInput
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from ui.projects.field_events.event_overlap import (
    do_events_overlap,
    get_hypothetical_interval,
)
from values.enums import EntityTypeChoices


def test_cropping_event_with_both_dates():
    """Test cropping event with complete interval returns as-is."""
    start = datetime(2023, 4, 15, 10, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 10, 20, 15, 0, 0, tzinfo=timezone.utc)
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
        crop_type="corn",
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    assert result_end == end


def test_cropping_event_planting_only():
    """Test cropping event with planting only generates hypothetical harvest +180 days."""
    start = datetime(2023, 4, 15, 10, 0, 0, tzinfo=timezone.utc)
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start),
        crop_type="corn",
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    expected_end = start + timedelta(days=180)
    assert result_end == expected_end


def test_cropping_event_planting_only_year_boundary():
    """Test cropping event planting late in year caps harvest at year end."""
    start = datetime(2023, 8, 15, 10, 0, 0, tzinfo=timezone.utc)  # Late planting
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start),
        crop_type="corn",
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    # Should cap at end of same year
    expected_end = datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
    assert result_end == expected_end


def test_cropping_event_harvest_only():
    """Test cropping event with harvest only generates hypothetical planting -180 days."""
    end = datetime(2023, 10, 20, 15, 0, 0, tzinfo=timezone.utc)
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(end=end),
        crop_type="corn",
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    expected_start = end - timedelta(days=180)
    assert result_start == expected_start


def test_cropping_event_harvest_only_year_boundary():
    """Test cropping event harvest early in year caps planting at year start."""
    end = datetime(2023, 3, 15, 15, 0, 0, tzinfo=timezone.utc)  # Early harvest
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(end=end),
        crop_type="corn",
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    # Should cap at beginning of same year
    expected_start = datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    assert result_start == expected_start


def test_cropping_event_no_interval_raises_error():
    """Test cropping event without interval raises ValueError."""
    event = CroppingEvent(
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        crop_type="corn",
    )

    with pytest.raises(ValueError, match="Event must have an interval"):
        get_hypothetical_interval(event)


def test_cropping_event_empty_interval_raises_error():
    """Test cropping event with empty interval raises ValueError."""
    # Note: Can't actually create Interval with both None due to Pydantic validation
    # This test simulates the case where get_hypothetical_cropping_interval receives
    # an event with an interval that somehow has both start and end as None
    event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=datetime(2023, 4, 1, tzinfo=timezone.utc)),
        crop_type="corn",
    )
    # Manually set both to None to test the error case
    event.interval.start = None
    event.interval.end = None

    with pytest.raises(ValueError, match="Event must have at least start or end date"):
        get_hypothetical_interval(event)


def test_irrigation_event_with_both_dates():
    """Test irrigation event with complete interval returns as-is."""
    start = datetime(2023, 6, 1, 8, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 6, 15, 18, 0, 0, tzinfo=timezone.utc)
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    assert result_end == end


def test_irrigation_event_start_only():
    """Test irrigation event with start only generates hypothetical end +30 days."""
    start = datetime(2023, 6, 1, 8, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 6, 15, 8, 0, 0, tzinfo=timezone.utc)
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )
    # Manually set end to None to test hypothetical logic
    event.interval.end = None

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    expected_end = start + timedelta(days=30)
    assert result_end == expected_end


def test_irrigation_event_start_only_year_boundary():
    """Test irrigation event start late in year caps end at year end."""
    start = datetime(2023, 12, 15, 8, 0, 0, tzinfo=timezone.utc)  # Late start
    end = datetime(2023, 12, 20, 8, 0, 0, tzinfo=timezone.utc)
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )
    # Manually set end to None to test hypothetical logic
    event.interval.end = None

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    # Should cap at end of same year
    expected_end = datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
    assert result_end == expected_end


def test_irrigation_event_end_only():
    """Test irrigation event with end only generates hypothetical start -30 days."""
    start = datetime(2023, 6, 15, 18, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 6, 30, 18, 0, 0, tzinfo=timezone.utc)
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )
    # Manually set start to None to test hypothetical logic
    event.interval.start = None

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    expected_start = end - timedelta(days=30)
    assert result_start == expected_start


def test_irrigation_event_end_only_year_boundary():
    """Test irrigation event end early in year caps start at year start."""
    start = datetime(2023, 1, 10, 18, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 1, 15, 18, 0, 0, tzinfo=timezone.utc)  # Early end
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )
    # Manually set start to None to test hypothetical logic
    event.interval.start = None

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    # Should cap at beginning of same year
    expected_start = datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    assert result_start == expected_start


def test_irrigation_event_no_interval_raises_error():
    """Test irrigation event without interval raises ValueError."""
    # Create a valid event first, then remove interval to test error case
    event = IrrigationEvent(
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )
    # Manually set interval to None to test the error case
    event.interval = None

    with pytest.raises(ValueError, match="Event must have an interval"):
        get_hypothetical_interval(event)


def test_irrigation_event_empty_interval_raises_error():
    """Test irrigation event with empty interval raises ValueError."""
    # Note: Can't actually create Interval with both None due to Pydantic validation
    # This test simulates the case where get_hypothetical_irrigation_interval receives
    # an event with an interval that somehow has both start and end as None
    event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )
    # Manually set both to None to test the error case
    event.interval.start = None
    event.interval.end = None

    with pytest.raises(ValueError, match="Event must have at least start or end date"):
        get_hypothetical_interval(event)


def test_cropping_events_overlap():
    """Test cropping events that overlap."""
    source_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    target_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
        crop_type="soy",
    )

    assert do_events_overlap(source_event, target_event) is True


def test_cropping_events_no_overlap():
    """Test cropping events that don't overlap."""
    source_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    target_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 8, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
        crop_type="soy",
    )

    assert do_events_overlap(source_event, target_event) is False


def test_cropping_events_partial_intervals():
    """Test cropping events with partial intervals using hypothetical dates."""
    # Source: planting only
    source_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=datetime(2023, 4, 1, tzinfo=timezone.utc)),
        crop_type="corn",
    )
    # Target: harvest only
    target_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(end=datetime(2023, 8, 15, tzinfo=timezone.utc)),
        crop_type="soy",
    )

    # Should overlap because:
    # Source: 2023-04-01 to 2023-09-28 (planting + 180 days)
    # Target: 2023-02-16 to 2023-08-15 (harvest - 180 days)
    assert do_events_overlap(source_event, target_event) is True


def test_irrigation_events_overlap():
    """Test irrigation events that overlap."""
    source_event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )
    target_event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 10, tzinfo=timezone.utc), end=datetime(2023, 6, 25, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(source_event, target_event) is True


def test_irrigation_events_no_overlap():
    """Test irrigation events that don't overlap."""
    source_event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )
    target_event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 20, tzinfo=timezone.utc), end=datetime(2023, 6, 30, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(source_event, target_event) is False


def test_tillage_events_same_date_overlap():
    """Test tillage events on same date overlap."""
    date = datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc)
    source_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
    )
    target_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
    )

    assert do_events_overlap(source_event, target_event) is True


def test_tillage_events_different_date_no_overlap():
    """Test tillage events on different dates don't overlap."""
    source_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc),
    )
    target_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 16, 10, 0, 0, tzinfo=timezone.utc),
    )

    assert do_events_overlap(source_event, target_event) is False


def test_application_events_same_date_overlap():
    """Test application events on same date overlap."""
    date = datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc)
    source_event = ApplicationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        products=[ApplicationInput(product_name="fertilizer")],
    )
    target_event = ApplicationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        products=[ApplicationInput(product_name="fertilizer")],
    )

    assert do_events_overlap(source_event, target_event) is True


def test_different_event_types_no_overlap():
    """Test different event types never overlap."""
    cropping_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    irrigation_event = IrrigationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 10, tzinfo=timezone.utc), end=datetime(2023, 6, 20, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(cropping_event, irrigation_event) is False


def test_different_practice_event_types_no_overlap():
    """Test different practice event types never overlap even on same date."""
    date = datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc)
    tillage_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
    )
    application_event = ApplicationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        products=[ApplicationInput(product_name="fertilizer")],
    )

    assert do_events_overlap(tillage_event, application_event) is False


def test_cropping_event_with_invalid_interval_no_overlap():
    """Test cropping event with invalid interval returns False (logged warning)."""
    source_event = CroppingEvent(
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        crop_type="corn",
    )
    target_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
        crop_type="soy",
    )

    # Should return False and log warning instead of raising exception
    assert do_events_overlap(source_event, target_event) is False


def test_fallow_period_with_both_dates():
    """Test fallow period with complete interval returns as-is."""
    start = datetime(2023, 4, 15, 10, 0, 0, tzinfo=timezone.utc)
    end = datetime(2023, 10, 20, 15, 0, 0, tzinfo=timezone.utc)
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start, end=end),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    assert result_end == end


def test_fallow_period_start_only():
    """Test fallow period with start only generates hypothetical end +180 days."""
    start = datetime(2023, 4, 15, 10, 0, 0, tzinfo=timezone.utc)
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    expected_end = start + timedelta(days=180)
    assert result_end == expected_end


def test_fallow_period_start_only_year_boundary():
    """Test fallow period start late in year caps end at year end."""
    start = datetime(2023, 8, 15, 10, 0, 0, tzinfo=timezone.utc)  # Late start
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=start),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_start == start
    # Should cap at end of same year
    expected_end = datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc)
    assert result_end == expected_end


def test_fallow_period_end_only():
    """Test fallow period with end only generates hypothetical start -180 days."""
    end = datetime(2023, 10, 20, 15, 0, 0, tzinfo=timezone.utc)
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(end=end),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    expected_start = end - timedelta(days=180)
    assert result_start == expected_start


def test_fallow_period_end_only_year_boundary():
    """Test fallow period end early in year caps start at year start."""
    end = datetime(2023, 3, 15, 15, 0, 0, tzinfo=timezone.utc)  # Early end
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(end=end),
    )

    result_start, result_end = get_hypothetical_interval(event)

    assert result_end == end
    # Should cap at beginning of same year
    expected_start = datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    assert result_start == expected_start


def test_fallow_period_no_interval_raises_error():
    """Test fallow period without interval raises ValueError."""
    event = FallowPeriod(
        entity_id=1,
        entity_type=EntityTypeChoices.field,
    )

    with pytest.raises(ValueError, match="Event must have an interval"):
        get_hypothetical_interval(event)


def test_fallow_period_empty_interval_raises_error():
    """Test fallow period with empty interval raises ValueError."""
    event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(start=datetime(2023, 4, 1, tzinfo=timezone.utc)),
    )
    # Manually set both to None to test the error case
    event.interval.start = None
    event.interval.end = None

    with pytest.raises(ValueError, match="Event must have at least start or end date"):
        get_hypothetical_interval(event)


def test_fallow_periods_overlap():
    """Test fallow periods that overlap."""
    source_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
    )
    target_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(source_event, target_event) is True


def test_fallow_periods_no_overlap():
    """Test fallow periods that don't overlap."""
    source_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )
    target_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 8, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(source_event, target_event) is False


def test_cropping_and_fallow_overlap():
    """Test cropping event and fallow period that overlap."""
    cropping_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    fallow_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(cropping_event, fallow_event) is True
    assert do_events_overlap(fallow_event, cropping_event) is True


def test_cropping_and_fallow_no_overlap():
    """Test cropping event and fallow period that don't overlap."""
    cropping_event = CroppingEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    fallow_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 8, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    assert do_events_overlap(cropping_event, fallow_event) is False
    assert do_events_overlap(fallow_event, cropping_event) is False


def test_fire_events_same_date_overlap():
    """Test fire events on same date overlap."""
    date = datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc)
    source_event = FireEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        burn_area_fraction=0.5,
        combusted_fraction=0.3,
    )
    target_event = FireEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        burn_area_fraction=0.8,
        combusted_fraction=0.6,
    )

    assert do_events_overlap(source_event, target_event) is True


def test_fire_events_different_date_no_overlap():
    """Test fire events on different dates don't overlap."""
    source_event = FireEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc),
        burn_area_fraction=0.5,
        combusted_fraction=0.3,
    )
    target_event = FireEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 16, 10, 0, 0, tzinfo=timezone.utc),
        burn_area_fraction=0.8,
        combusted_fraction=0.6,
    )

    assert do_events_overlap(source_event, target_event) is False


def test_different_practice_event_types_including_fire_no_overlap():
    """Test different practice event types never overlap even on same date."""
    date = datetime(2023, 5, 15, 10, 0, 0, tzinfo=timezone.utc)
    fire_event = FireEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        burn_area_fraction=0.5,
        combusted_fraction=0.3,
    )
    tillage_event = TillageEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
    )
    application_event = ApplicationEvent(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=date,
        products=[ApplicationInput(product_name="fertilizer")],
    )

    assert do_events_overlap(fire_event, tillage_event) is False
    assert do_events_overlap(fire_event, application_event) is False
    assert do_events_overlap(tillage_event, fire_event) is False
    assert do_events_overlap(application_event, fire_event) is False


def test_fallow_period_with_invalid_interval_no_overlap():
    """Test fallow period with invalid interval returns False (logged warning)."""
    source_event = FallowPeriod(
        entity_id=1,
        entity_type=EntityTypeChoices.field,
    )
    target_event = FallowPeriod(
        id=uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 6, 15, tzinfo=timezone.utc)
        ),
    )

    # Should return False and log warning instead of raising exception
    assert do_events_overlap(source_event, target_event) is False
