import json
import os
from functools import lru_cache
from pathlib import Path

# these can be overridden by setting ENV vars
from pydantic import BaseSettings


def choose_env_file() -> Path:
    """Depending on environment variable `ENV` value return name of dotenv file in the
    `app` directory. Cut numbers from filename if any. If no `ENV` or empty value
    consider "local".
    For example:
        - "dev6" -> ".env.dev"
        - "prod" -> ".env.prod"
        - "" -> ".env.local"
    """
    current_env = os.getenv("ENV") or "local"
    current_dir = Path(__file__).parent
    return current_dir / f".env.{''.join([x for x in current_env if not x.isdigit()])}"


class Settings(BaseSettings):
    app_name: str = "mrv-service"
    env: str = "local"
    ELASTIC_APM_SERVER_URL: str = ""
    ELASTIC_APM_ENABLED: bool = False
    ELASTIC_APM_LOG_LEVEL: str = "INFO"
    ELASTIC_APM_ENVIRONMENT: str = "dev?"
    db_url: str = "mysql+aiomysql://root:my-secret-pw@127.0.0.1:3307/mrv_service"

    DEBUG_DB_QUERIES: bool = False
    ELASTIC_APM_DEBUG: bool = False
    PROJECT_PATH: Path = Path(__file__).parent  # app/
    LOCALE_PATH: Path = PROJECT_PATH / "locale"
    TEST_DATA_PATH: Path = PROJECT_PATH / "test_data"
    WINTER_CROP_IGNORE_FIELD_JSON_PATH: Path = PROJECT_PATH / "projects" / "conflicts" / "ignore_field_md5.json"
    PINT_CUSTOM_UNITS_PATH: Path = PROJECT_PATH / "pint_custom_units.txt"
    sync_db_url: str = "mysql://root:my-secret-pw@127.0.0.1:3307/mrv_service"
    SENTRY_ENABLED: bool = False
    SENTRY_AVAILABLE_ENVS: list = ["dev", "prod"]
    SENTRY_DSN: str = ""
    # The percentage chance a given transaction will be sent to Sentry
    SENTRY_TRACES_SAMPLE_RATE: int | float = 0
    root_path: str = ""
    LOGGING_LEVEL: str = "CRITICAL"
    CORE_EXTERNAL_URL: str = "https://dev.flurosat.com"
    CORE_INTERNAL_URL: str = "http://fluro-core.int.dev.regrow.cloud"  # NOSONAR
    CORE_INTERNAL_PASSWORD = "ZqzJACH9"
    # CORE_INTERNAL_URL: str = "http://localhost:8081"
    CORE_MRV_PATH: str = "api/v1/mrv"
    CORE_API_PATH: str = "api/v1"
    CORE_FIELDS_AREA_PATH: str = "api/v1/fields/area"
    CORE_FIELDS_GEOM_PATH: str = "api/fields/geometries"
    CORE_ADMIN_PATH: str = "/admin"
    CORE_REGION_PATH: str = "/regions"
    CORE_FIELDS_SEASONS_PATH: str = "api/v1/fields"
    CORE_KML_PATH: str = "api/v1/kml"
    CORE_UPLOAD_KMLS = "files/kml"
    CORE_UPLOAD_KML_FILE = "kmlfiles"
    CORE_FIELDS_SEASONS_SUFFIX: str = "seasons"
    CORE_FIELDS_STATS_SUFFIX: str = "stats"
    CORE_FIELDS_OPERATIONS_SUFFIX: str = "operations"
    CORE_MD5_PATH = "kml/md5"
    CORE_USERS_PATH: str = "profile"
    CORE_MULTI_USERS_PATH: str = "profiles"
    CORE_USERS_SEARCH_PATH: str = "users/search"
    CORE_USERS_CREATE_PATH: str = "mrv-sign-up/program"
    CORE_FIELDS_REGIONS: str = "regions"
    CORE_FIELDS_COUNTIES: str = "counties"
    CORE_CROPS_PATH: str = "crops"
    CORE_CROPS_ICONS_PATH: str = "icon"
    CORE_USER_ID: str = "13"
    CORE_BOUNDARY_INTERSECTION = "api/v1/fields/intersecting/arbitrary"
    ELASTIC_APM_CAPTURE_BODY: str = "all"
    CORE_FERTILIZERS_PATH: str = "fertilizers"
    CORE_CARGILL_PDF: str = "https://flurosense.com/api/v1/cargill/pdf?envelope_id="
    MRV_INTERNAL_URL: str = "http://fluro-core.int.dev.regrow.cloud/mrv-service"  # NOSONAR
    # We could potentially make MRV_INTERNAL_URL the same as OPEN_API_SERVER_URL, but typically MRV_INTERNAL_URL
    # will be a `traefik.service.consul` address which is faster to reach for services within the cluster, whereas we
    # need OPEN_API_SERVER_URL to be available externally, e.g. from within our VPN (like from our laptops)
    OPEN_API_SERVER_URL: str = "http://localhost:8000"
    FIELD_BOUNDARY_CHECK_INTERNAL_URL: str = "http://field-boundary-check-service-qa.int.dev.regrow.cloud"  # NOSONAR
    FIELD_BOUNDARY_CHECK_PATH: str = "processes/field-boundary-check/execution"
    # Maximum number of MD5s we can send to field-boundary-check endpoint
    FIELD_BOUNDARY_CHECK_MAX_FIELDS = 100
    FIELD_BOUNDARY_CHECK_BATCH_LIMIT = 2
    CHECK_FIELD_BOUNDARIES_FOR_PROJECT_BATCH_LIMIT = 2
    MRV_PROFILE_PAGE: str = "/app/profile?tab=profile"
    CROP_HISTORY_NUMBER_OF_YEARS: int = 4
    MONITOR_INTERNAL_URL: str = "http://monitor-api-qa.int.dev.regrow.cloud"  # NOSONAR
    MONITOR_FIELDS_URL: str = "processes/monitor-fields-boundary-id/execution"
    MONITOR_JOB_URL_SUFFIX: str = "jobs"
    MONITOR_RESULT_URL_SUFFIX: str = "results"
    MONITOR_RETRY_TIME_LIMIT: int = 60 * 60  # 1 hour
    MONITOR_RETRY_LIMIT: int = 3
    MONITOR_MD5_LIMIT: int = 100
    OPTIS_ENABLE: bool = True
    OPTIS_MRV_USER: str = "mrv-dev"
    # optis retry batch size for retry cron.
    OPTIS_RETRY_BATCH_SIZE: int = 100
    NASS_SERVICE_INTERNAL_URL: str = "http://nass-service.int.dev.regrow.cloud/"  # NOSONAR
    CUBEJS_INTERNAL_URL: str = "http://cubejs.int.dev.regrow.cloud/"  # NOSONAR
    CUBEJS_LOAD_PATH: str = "cubejs-api/v1/load"
    CUBEJS_QUERY_TIMEOUT: float = 30.0
    GEOJSON_REGIONS_INTERNAL_URL: str = "https://flurosense.com/api/v1/geojson_regions"
    DEFAULTS_SERVICE_INTERNAL_URL_BASE: str = "http://defaults-service.int.dev.regrow.cloud/"  # NOSONAR
    DEFAULTS_SERVICE_DB_PATH: str = "db/"
    DEFAULTS_SERVICE_BASIC_FERTILIZER_PATH: str = "tables/fertilizer_basic"
    DEFAULTS_SERVICE_ADDITIVE_PATH: str = "tables/fertilizer_additive"
    DEFAULTS_SERVICE_EENF_PATH: str = "tables/fertilizer_eenf"
    DEFAULTS_SERVICE_ORGANIC_AMENDMENT_PATH: str = "tables/organic_amendment"
    DEFAULTS_SERVICE_CROP_TRANSLATION_MONITOR_PATH: str = "tables/crop_translation_monitor"
    DEFAULTS_SERVICE_CROP_TRANSLATION_PATH: str = "tables/crop_translation"

    REDIS_URL: str = "redis://localhost:6380/0"
    REDIS_RESULT_EXPIRATION_IN_SECONDS: int = 600
    NUM_DNDC_ATTEMPTS: int = 3
    # Limit number of core field IDs per request to core Endpoint api/v1/fields because of URL length limit
    # Assuming approximately 10 chars per field ID as a rough heuristic: hopefully max 5000 fields per request is ok
    MAX_CORE_FIELDS_PER_REQUEST: int = 5000

    CARGILL_ACCOUNT_ID_VALIDATION: bool = True
    CARGILL_GET_AUTH_TOKEN_URL: str = ""
    CARGILL_VALIDATE_ACCOUNT_ID_URL: str = ""
    PAPERFORM_API_KEY: str | None = None
    PAPERFORM_SUMBISSIONS_URL: str = "https://api.paperform.co/v1/submissions/{submission_id}"

    # Slack Config
    SlACK_WEBHOOK_URL_CONSUMER_ALERTS = ""
    SLACK_WEBHOOK_URL_CRON_ALERTS = ""
    SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS = ""
    SLACK_WEBHOOK_URL_EXPLORE_ALERTS = ""
    SLACK_WEBHOOK_URL_MEASURE_ALERTS = ""
    SLACK_WEBHOOK_URL_MEASUREMENT_ELIGIBILITY = ""
    SLACK_WEBHOOK_URL_MONITOR_ALERTS = ""
    MAX_RETRY_SLACK_MESSAGE: int = 5

    # Carbon Copy(CC) config for docusign contract
    CC_EMAIL: str = ""
    CC_NAME: str = ""

    # Url where the user is redirected to after signing the contract.
    # when running in local set this to http://localhost:8000
    MRV_EXTERNAL_URL: str = "http://localhost:8000"

    # Url where the user is redirected to after signing the consent.
    # This needs to be frontend url.
    DOCUSIGN_CONSENT_REDIRECT_URL: str = "http://localhost:8000/docusign/callback"

    # space delimited list of secrets from Docusign CONNECT
    DOCUSIGN_CONNECT_KEYS: str = ""

    NUMBER_SENT_MAX: int = 400

    # Email client
    MTA_SERVICE_HOST: str | None
    MTA_SERVICE_PORT: int | None
    NO_REPLY_EMAIL: str = "<EMAIL>"
    EXTERNAL_EMAIL_ENABLED: bool = False

    # Google Cloud
    GCLOUD_SERVICE_ACCOUNT: str = "sa.json"
    GCLOUD_STORAGE_BUCKET: str = "flurosense-us-west1-mrv-service-dev"
    GCLOUD_STORAGE_PRIVATE_BUCKET: str = "flurosense-us-west1-mrv-service-private-dev"
    GCLOUD_PRESIGN_EXPIRY_SECONDS: int = 60 * 5

    SERVICE_TYPE: str = "service"  # "service | worker"

    # DNDC/Scenarios Service/Measure API/Explore API Configs
    SCENARIOS_SERVICE_INTERNAL_URL: str = "http://dndc-scenarios-service.int.dev.regrow.cloud/"  # NOSONAR
    DNDC_OVERRIDE_PATH: str = "../dndc_override.json"
    EXPLORE_API_POLLING_INTERVAL_SECONDS = 1
    DNDC_TA_LOWER_BOUND: float = 0.1
    # MEASURE_INTEGRATION_FIELD_BATCH_SIZE_PER_WORKER = batch size of fields to process per celery task,
    # MEASURE_INTEGRATION_FIELD_BATCH_SIZE_TO_SUBMIT = batch size of fields to submit to Measure API in one request
    # MEASURE_INTEGRATION_FIELD_BATCH_SIZE_PER_WORKER/MEASURE_INTEGRATION_FIELD_BATCH_SIZE_TO_SUBMIT = max # of
    # concurrent executions per celery worker
    MEASURE_INTEGRATION_FIELD_BATCH_SIZE_PER_WORKER = 400
    MEASURE_INTEGRATION_FIELD_BATCH_SIZE_TO_SUBMIT = 40

    MAX_USER_DNDC_RUNS: int = 5  # Deprecated/Legacy
    ENABLE_DNDC: bool = False  # Deprecated/Legacy
    ENABLE_DNDC_SLACK_INTEGRATION: bool = False  # Deprecated/Legacy

    # DNDC cron job
    DNDC_TASK_MIN_INTERVAL_MINUTES: int = 5
    DNDC_TASK_MAX_INTERVAL_MINUTES: int = 60

    EXTERNAL_HTTP_REQUEST_TIMEOUT: int = 10

    MYSQL_POOL_SIZE: int = 20
    MYSQL_POOL_MAX_OVERFLOW: int = 20

    # A list of product names for which import of application operations will be skipped - ie non-fertilisers
    # Read in from consul as string list "SONIC\nGLYPHOSATE\n2,4-D\nDICAMBA\nATRAZINE"
    # Note: 'real' values here may break some tests.
    FMI_IMPORT_PRODUCT_SKIP_LIST = ""
    # app_version is defined in nomad - we should get rid of it once we get rid of nomad entirely
    app_version: str = "some_version"
    # APP_VERSION is defined in k8s
    APP_VERSION: str = ""  # NOSONAR
    COMMIT_HASH: str = "D3ADB33F"

    # DATASERVICE CONFIG
    DATASERVICE_BASE_URL: str = "http://api.dev.internal:9089/data-service-api"  # NOSONAR
    DATASERVICE_AVAILABLE_IMAGES: str = "/available_images/{md5}/{start_date}/{end_date}"
    DATASERVICE_FALSE_COLOUR: str = "/indices/false_colour/{md5}/{timestamp}"
    DATASERVICE_METRICS: str = "/metrics"
    # we have limit of sql queuepool in dev of 50, not sure about prod.
    MAX_LIMIT_OF_TASKS: int = 40
    # constant number to increment row_id by for updating and bypassing
    # row_id constraint.
    INCREMENT_ROW_ID_BY: int = 100

    SIMPLELOCALIZE_API_URL = "https://api.simplelocalize.io/api/v3"
    SIMPLELOCALIZE_API_TOKEN = ""  # nosec
    SIMPLELOCALIZE_GET_TRANSLATIONS = False  # should we download translations from GCP bucket

    # feature flag for field boundary checking
    CHECK_FIELD_BOUNDARIES = False

    # Boundaries Service.
    #
    # In Consul, please don't use the dev URL. GE have said that other apps (such as mrv-service) need to use qa,
    # not dev
    BOUNDARIES_SERVICE_INTERNAL_URL_BASE = "http://boundaries-service-qa.int.dev.regrow.cloud/"  # NOSONAR
    BOUNDARIES_SERVICE_FERTILIZER_APPLICATION_PATH = "datasets/fertilizer_application/2.0.0/data"
    BOUNDARIES_SERVICE_FERTILIZER_QUANTITY_PATH = "datasets/fertilizer_quantity/1.0.0/data"
    BOUNDARIES_SERVICE_COLLECTION_FEATURES_PATH = "collections/{collection_id}/items"
    BOUNDARIES_SERVICE_SEARCH_PATH = "search"
    BOUNDARIES_SERVICE_SEARCH_BYPASS_CACHE = False
    BOUNDARIES_SERVICE_CLU_COLLECTION_ID: str = "common_land_units"
    BOUNDARIES_SERVICE_CLU_COLLECTION_POLAND = "fields_government_poland_2024"
    BOUNDARIES_SERVICE_CLU_COLLECTION_PARCELID = "fields_parcelid_conus_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_CANADA = "fields_parcelid_canada_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_GERMANY = "fields_parcelid_germany_2017_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_US = "fields_parcelid_conus_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_FRANCE = "france_benelux_parcelid"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_POLAND = "fields_parcelid_poland_romania_2017_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_ROMANIA = "fields_parcelid_poland_romania_2017_2023"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_HUNGARY = "fields_parcelid_hungary_2018_2024"
    BOUNDARIES_SERVICE_COLLECTION_PARCELID_UKRAINE = "fields_parcelid_ukr_2018_2024"
    BOUNDARIES_SERVICE_UNION_AREAS_PATH = "processes/union-areas/execution"
    BOUNDARIES_SERVICE_FEATURE_INTERSECTIONS_PATH = "processes/feature-intersections/execution"
    BOUNDARIES_SERVICE_JOB_PATH = "jobs/{job_uuid}"
    BOUNDARIES_SERVICE_JOB_RESULTS_PATH = "jobs/{job_uuid}/results"

    # Structured Events Service
    SES_INTERNAL_URL_BASE: str = "localhost:50052"  # NOSONAR
    SES_SEARCH_INTERNAL_URL_BASE: str = "http://fieldevent-srvc.int.dev.regrow.cloud"  # NOSONAR
    SES_MIGRATION_FIELD_BATCH_SIZE = 50
    SES_RECONCILIATION_POLLING_INTERVAL_SECONDS: int = 5
    SES_RECONCILIATION_POLLING_INTERVAL_TIMEOUT_SECONDS: int = 300

    # Retention job
    #
    # We cache results from boundaries service used for retention calculation. If these results aren't used for this
    # many days or more, they will be deleted
    BOUNDARIES_SERVICE_CACHE_AGE_DAYS = 7
    # Only keep this many retention calculation task results, otherwise DB will be quickly filled with old results
    NUM_OVERLAP_TASK_RESULTS_TO_KEEP = 2
    # Number of concurrent async tasks to run when generating overlaps for a program
    GENERATE_OVERLAPS_FOR_PROGRAM_BATCH_LIMIT = 2
    # Number of concurrent tasks for each entity type within a program, e.g. fields, projects, regions each get their
    # own stask
    GENERATE_OVERLAPS_ENTITY_TYPE_BATCH_LIMIT = 4
    # Number of concurrent tasks to get md5s for projects/previous projects
    GENERATE_OVERLAPS_PROJECT_GET_MD5S_BATCH_LIMIT = 40
    # Number of concurrent tasks to call boundaries service union areas for projects
    GENERATE_OVERLAPS_PROJECT_CALL_BS_BATCH_LIMIT = 40
    # Number of concurrent tasks to get md5s for regions
    GENERATE_OVERLAPS_REGION_GET_MD5S_BATCH_LIMIT = 40
    # Number of concurrent tasks to call boundaries service union areas for regions
    GENERATE_OVERLAPS_REGION_CALL_BS_BATCH_LIMIT = 40
    # If we don't get all the callbacks from boundaries service within this time, we'll mark the task as having
    # timed out
    OVERLAP_TASK_TIMEOUT_SECONDS = 60 * 60 * 3  # 3 hours
    # Wait this long before checking to see how many overlap requests are still waiting for boundaries service callbacks
    CHECK_OVERLAP_REQUESTS_WAIT_PERIOD_SECONDS = 30

    # Hubspot
    HUBSPOT_API_ACCESS_TOKEN: str = ""
    HUBSPOT_API_BASE_URL: str = "https://api.hubapi.com"

    # Number of contract to get in memory.
    MAX_CONTRACTS_IN_MEMORY: int = 500

    # Page size for dashboard csv export
    DASHBOARD_CSV_EXPORT_PAGE_SIZE: int = 2000

    # Number of retries for getting data from cube js, when encountering a "Continue wait" response
    GET_FROM_CUBE_JS_RETRY_LIMIT: int = 8
    # Enable request profiling, True is on
    PROFILER: bool = False

    BIGQUERY_PROJECT: str = "dev-data-2c3e"

    # Kafka
    KAFKA_CONSUMER_GROUP_ID: str = "mrv-service"
    KAFKA_CONSUMER_HEALTH_CHECK_PATH: str | None = None
    KAFKA_CONSUMER_HEALTH_CHECK_INTERVAL: int = 5
    # Topics
    IMPORT_REQUEST_COMPLETED_TOPIC: str = "push.fm_integration.import_request_completed"
    FIELD_EVENT_NOTIFICATION_TOPIC: str = "notify.ses.field_event"

    # Celery
    # Only set one of these two options:
    CELERY_WORKER_PROCESSES_PER_CPU: float | None = 0.5  # Number of worker processes per CPU core
    CELERY_WORKER_PROCESSES: int | None = None  # Total number of worker processes

    class Config:
        env_file = choose_env_file()


@lru_cache()
def get_settings() -> Settings:
    return Settings()


@lru_cache()
def get_dndc_override() -> dict:
    settings = get_settings()
    with open(settings.DNDC_OVERRIDE_PATH) as f:
        return json.load(f)
