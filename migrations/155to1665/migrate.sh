#!/bin/bash
# migrate a set of projects from one program to another using the project/migrate endpoint

# get current folder of script
script_folder="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
# show DIR

host=""

if [ -z "${1}" ]; then
  echo "provide environment for 1st parameter [local | dev | prod]"
  exit 1
fi

if [ "${1}" == "local" ]; then
  host="http://localhost:8000"
elif [ "${1}" == "dev" ]; then
  host="http://mrv-service.int.dev.regrow.cloud"
elif [ "${1}" == "prod" ]; then
  host="http://mrv-service.int.prod.regrow.cloud"
else
  echo "provide environme4nt for 1st parameter [local | dev | prod]"
  exit 1
fi

echo "host = ${host}"

transformer_name="155to1665"

# This is the prod program
 target_program_id=1665

# Dummy dev program for testing
#target_program_id=71143
echo "target_program_id = ${target_program_id}"


folder="${script_folder}/results"

# create results folder if it does not exist
if [ ! -d ${folder} ]; then
  mkdir ${folder}
fi


migrated_project_id=""

function inspect_project {
   projectid=$1
   type=$2

   # if projectid is empty then return
   if [ -z "${projectid}" ]; then
     return
   fi

   project_folder="${folder}/${projectid}"
   if [ ! -d ${project_folder} ]; then
     mkdir ${project_folder}
     if [ $? -ne 0 ]; then
       echo "ERROR: Failed to create directory ${project_folder}" >&2
       return 1
     fi
   fi

   endpoint="${host}/projects/migration/inspect/${projectid}"

   output_json="${project_folder}/${projectid}_${type}.json"
   curl -s -H 'accept: application/json' -H 'fs-user-id: 13' -H 'Content-Type: application/json' ${endpoint} | jq > ${output_json}
   if [ $? -ne 0 ]; then
     echo "ERROR: curl or jq failed for inspect_project ${projectid} (${type})" >&2
     return 1
   fi
   echo "$(date -Iseconds) Inspect [${projectid}][${type} ] -> ${output_json}"

   output_text="${project_folder}/${projectid}_${type}.txt"
   cat ${output_json} | jq -r '.text_block' > ${output_text}
   if [ $? -ne 0 ]; then
     echo "ERROR: Failed to extract text_block for inspect_project ${projectid} (${type})" >&2
     return 1
   fi
   echo "$(date -Iseconds) Inspect [${projectid}][${type} ] -> ${output_text}"
}


function migrate_project {
   projectid=$1

   project_folder="${folder}/${projectid}"
   if [ ! -d "${project_folder}" ]; then
     mkdir "${project_folder}"
     if [ $? -ne 0 ]; then
       echo "ERROR: Failed to create directory ${project_folder}" >&2
       return 1
     fi
   fi

   endpoint="${host}/projects/migration"

   request_filename="${project_folder}/${projectid}_request.json"
   echo "{" > "${request_filename}"
   echo "  \"project_id\": ${projectid}," >> "${request_filename}"
   echo "  \"transformer_name\": \"${transformer_name}\"," >> "${request_filename}"
   echo "  \"target_program_id\": ${target_program_id}, " >> "${request_filename}"
   echo "  \"update_existing\": true, " >> "${request_filename}"
   echo "  \"remove_existing\": false, " >> "${request_filename}"
   echo "  \"overwrite_value\": true, " >> "${request_filename}"
   echo "  \"repair_target_program\": true " >> "${request_filename}"
   echo "}" >> "${request_filename}"
   echo "" >> "${request_filename}"

   if [ ! -f "${request_filename}" ]; then
     echo "ERROR: Failed to create request file ${request_filename}" >&2
     return 1
   fi

   output_json="${project_folder}/${projectid}_migrate.json"
   curl -s -X POST \
   -H 'accept: application/json' \
   -H 'fs-user-id: 13' \
   -H 'Content-Type: application/json' \
   -d @"${request_filename}" ${endpoint} | \
   jq > "${output_json}"
   if [ $? -ne 0 ]; then
     echo "ERROR: curl or jq failed for migrate_project ${projectid}" >&2
     return 1
   fi

   echo "$(date -Iseconds) Migrate [${projectid}][migrate] -> ${output_json}"

   output_text="${project_folder}/${projectid}_migrate.txt"
   cat "${output_json}" | jq -r '.text_block' > "${output_text}"
   if [ $? -ne 0 ]; then
     echo "ERROR: Failed to extract text_block for migrate_project ${projectid}" >&2
     return 1
   fi
   echo "$(date -Iseconds) Migrate [${projectid}][migrate] -> ${output_text}"

   # need to get the created project id from output_file_name
   migrated_project_id=$(cat "${output_json}" | jq -r '.project | .destination')
   if [ -z "${migrated_project_id}" ] || [ "${migrated_project_id}" == "null" ]; then
     echo "ERROR: Failed to extract migrated_project_id for ${projectid}" >&2
     return 1
   fi

   #  remove request_filename
   rm "${request_filename}"

}

function main() {

  for source_project_id in $(cat ${script_folder}/migrate.txt)
  do
    echo "$(date -Iseconds) Migrate [${source_project_id}][start  ] using ${transformer_name}"
    inspect_project ${source_project_id} "source"

    migrated_project_id=""
    migrate_project ${source_project_id}
    echo "TEST: Migrated project ID: ${migrated_project_id}"

    inspect_project ${migrated_project_id} "target"

    # we also want to track source and destination project ids
    echo "${source_project_id} -> ${migrated_project_id}" >> ${folder}/migrated_projects.txt
    echo "$(date -Iseconds) Migrate [${source_project_id}][done   ] using ${transformer_name}  ${source_project_id} to ${migrated_project_id}"

  done
}

main
