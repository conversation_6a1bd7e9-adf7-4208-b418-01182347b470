from sqlalchemy import (
    Column,
    DateTime,
    DECIMAL,
    Enum,
    Grouping,
    Index,
    Integer,
    JSON,
    TIMESTAMP,
)
from sqlalchemy.sql import func
from sqlalchemy.sql.schema import UniqueConstraint

from db.mysql import Base
from field_lineage.enums import FieldRelationshipType


# TODO: make baseline_field_id and previous_lineage_ids non-nullable
# after backfilling for existing records.
class FieldLineage(Base):
    __tablename__ = "mrv_fields_lineage"
    __table_args__ = (
        UniqueConstraint(
            "baseline_field_id",
            name="unique_baseline_field_id",
        ),
    )
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # to be deprecated
    created_year = Column(Integer, nullable=True)
    # to be deprecated
    created_at = Column(DateTime, nullable=False, server_default=func.current_timestamp())
    # the mrv_fields.id of the baseline field associated with this lineage ID
    baseline_field_id = Column(Integer, nullable=True)
    # a list of the lineage IDs prior to this lineage ID
    previous_lineage_ids = Column(JSON, nullable=True)


class FieldRelationship(Base):
    __tablename__ = "mrv_field_relationship"

    id = Column(Integer, nullable=False, primary_key=True, index=True)
    # the mrv_fields.id of the field
    field_id = Column(Integer, nullable=False)
    # the mrv_fields.id of the previous field
    previous_field_id = Column(Integer, nullable=False)
    # the percent of the field that intersects with the previous field
    percent_intersection = Column(DECIMAL(5, 2), nullable=False)
    # the area (ha) of the field that intersects with the previous field
    area_intersection = Column(DECIMAL(14, 6), nullable=False)
    # the relationship of the field to the previous field
    relationship = Column(Enum(FieldRelationshipType), nullable=True)
    deleted_at = Column(TIMESTAMP, nullable=True, index=True)

    __table_args__ = (
        Index(
            "unique_undeleted_field_id_previous_field_id",
            field_id,
            previous_field_id,
            Grouping(func.IF(deleted_at.is_(None), 1, None)),
            unique=True,
            info={"skip_autogenerate": True},
        ),
    )
