"""update to store submitted field ids

Revision ID: a77d33cfd4fc
Revises: 042e4a6b53c4
Create Date: 2025-08-06 16:23:28.172109
"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

revision = "a77d33cfd4fc"
down_revision = "042e4a6b53c4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_dndc_tasks", sa.Column("submitted_field_ids", sa.JSON(), nullable=True))
    op.drop_column("mrv_dndc_tasks", "submitted_field_count")


def downgrade():
    op.add_column(
        "mrv_dndc_tasks", sa.Column("submitted_field_count", mysql.INTEGER(), autoincrement=False, nullable=True)
    )
    op.drop_column("mrv_dndc_tasks", "submitted_field_ids")
