from defaults.consts import Reg<PERSON><PERSON><PERSON>Name

MEASUREMENT_ELIGIBILITY_PROJECT_BATCH_SIZE = 40

PREMIUM_COVER_CROPS = [
    RegrowCropName.alternative_blend_cover_crop,
    RegrowCropName.alternative_single_species_cover_crop,
    RegrowCropName.legume_cover_crop,
    RegrowCropName.premium_cover_crop_mix,
    RegrowCropName.traditional_blend_cover_crop,
]

CARGILL_US_24_25_PROGRAM_IDS = [1119, 1649]
CARGILL_US_24_25_COMMODITY_AS_COVER_CROP_TYPES = [
    RegrowCropName.barley,
    RegrowCropName.buckwheat,
    RegrowCropName.clover,
    RegrowCropName.oat,
    RegrowCropName.radish,
    RegrowCropName.rye,
    RegrowCropName.triticale,
    RegrowCropName.turnip,
    RegrowCropName.vetch,
    RegrowCropName.wheat_winter,
]
