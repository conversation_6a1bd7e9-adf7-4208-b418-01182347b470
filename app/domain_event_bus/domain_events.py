from abc import ABC, abstractmethod
from datetime import datetime

from pydantic import BaseModel

from domain_event_bus.enums import DomainEventType
from phases.enums import PhaseTypes


class DomainEvent(BaseModel, ABC):
    @abstractmethod
    def get_event_type(self) -> DomainEventType:
        """Returns the type of the event."""


class FieldBoundaryUpdatedEvent(DomainEvent):
    program_id: int
    project_id: int
    field_id_to_old_md5: dict[int, str]
    update_time: datetime

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.field_boundary_updated


class PhaseCompletedEvent(DomainEvent):
    program_id: int
    project_id: int
    phase_id: int
    phase_type: PhaseTypes

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.phase_completed


class ProjectContractUpdatedEvent(DomainEvent):
    program_id: int
    project_id: int

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.project_contract_updated


class FieldsCreatedEvent(DomainEvent):
    program_id: int
    project_id: int
    field_ids: list[int]
    set_field_lineage_and_relationship: bool

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.fields_created


class FieldsDeleted(DomainEvent):
    program_id: int
    project_id: int
    field_ids: list[int]

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.fields_deleted


class FieldEventsChanged(DomainEvent):
    """Emitted when an SES event on a field changes."""

    program_id: int
    phase_id: int
    project_id: int
    field_id: int

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.field_events_changed


class BulkFieldEventsChanged(DomainEvent):
    """Emitted when SES events across multiple fields in the same project change."""

    project_id: int
    field_ids: list[int]

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.bulk_field_events_changed


class BulkProjectEventsChanged(DomainEvent):
    """Emitted when SES events across multiple projects in the same program change."""

    project_ids: list[int]

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.bulk_project_events_changed


class FieldValuesChanged(DomainEvent):
    """Emitted when an MRV value on a field changes."""

    program_id: int
    project_id: int
    field_id: int

    def get_event_type(self) -> DomainEventType:
        return DomainEventType.field_values_changed
