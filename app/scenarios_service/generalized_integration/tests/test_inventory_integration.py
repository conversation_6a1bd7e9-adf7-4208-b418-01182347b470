import json
from unittest.mock import patch

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from fields.enums import FieldStatus
from phases.enums import PhaseTypes
from programs.enums import AccountingMethod
from reported_outcomes.db import (
    get_crop_inventory_outcomes_for_program,
    get_field_inventory_outcomes_for_program_task,
)
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration import helpers
from scenarios_service.generalized_integration.db import (
    get_dndc_task_by_id,
    get_simulation_requests_for_task,
)
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryIntegration,
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceSubmissionResult,
)
from scenarios_service.generalized_integration.tests.conftest import (
    TEST_INVENTORY_RESPONSE,
)
from scenarios_service.model import (
    DndcSimulationRequests,
    ScenariosServiceApi,
)


@pytest.mark.parametrize("dry_run", [True, False])
async def test_prepare_inventory_project_succeeds(
    dry_run,
    mocker,
    mock_inventory_api_async_client,
    mock_inventory_inputs_translator,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_api_methods.get_inventory_consumer_configurations",
        return_value=[
            {
                "x_consumer_id": "test_consumer",
                "dndc_version": "v1.5.0",
                "versions_by_protocol": {},
                "created_at": "2020-01-01",
                "updated_at": "2020-01-01",
            }
        ],
    )
    mock_validate_fields = mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, status=FieldStatus.enrolled)

    integration = InventoryIntegration(
        request=app_request,
        inventory_client=mock_inventory_api_async_client,
        inventory_inputs_translator=mock_inventory_inputs_translator,
    )
    dndc_task = await integration.prepare_inventory_project(
        program.id,
        [field.id],
        dry_run=dry_run,
    )
    assert dndc_task.status == DndcTaskStatusChoices.in_progress
    assert dndc_task.accounting_method == AccountingMethod.inventory
    assert dndc_task.is_dry_run is dry_run

    mock_inventory_api_async_client.create_project.assert_called_with(
        project_name=helpers.build_ss_project_name(program, dndc_task.id),
        x_consumer_id="test_consumer",
    )

    mock_validate_fields.assert_called_once()
    assert mock_validate_fields.call_args_list[0].kwargs["request"] == app_request
    assert mock_validate_fields.call_args_list[0].kwargs["program_id"] == program.id
    assert mock_validate_fields.call_args_list[0].kwargs["submitted_field_ids"] == [field.id]


async def test_prepare_inventory_outcomes_task_field_id_validation_failures(
    app_request,
    mocker,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    """
    Submitting fields that are nonexistent, deleted, not enrolled, or in a different program causes validation errors.
    """
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )

    program = await mdl.Programs(id=275)
    project = await mdl.Projects(program_id=program.id)
    nonexistent_field_id = -1
    deleted_field_id = (await mdl.Fields(parent_project_id=project.id, status=FieldStatus.deleted)).id
    unenrolled_field_id = (await mdl.Fields(parent_project_id=project.id, status=FieldStatus.registered)).id
    contract_voided_field_id = (await mdl.Fields(parent_project_id=project.id, status=FieldStatus.contract_voided)).id

    other_program_id = (await mdl.Programs()).id
    other_project_id = (await mdl.Projects(program_id=other_program_id)).id
    other_program_field_id = (await mdl.Fields(parent_project_id=other_project_id, status=FieldStatus.enrolled)).id

    integration = InventoryIntegration(request=app_request)

    with pytest.raises(HTTPException) as http_exc:
        await integration.prepare_inventory_project(
            program_id=program.id,
            field_ids=[
                nonexistent_field_id,
                deleted_field_id,
                unenrolled_field_id,
                other_program_field_id,
                contract_voided_field_id,
            ],
            dry_run=False,
        )
    assert http_exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert (
        f"Nonexistent field IDs: {{{nonexistent_field_id}}}, Deleted field IDs: {{{deleted_field_id}}}, Fields without enrolled+ status: {{{unenrolled_field_id}}}, Fields from a different program: {{{other_program_field_id}}}"
        == http_exc.value.detail["message"]
    )


async def test_submit_fields_for_inventory_project_succeeds(
    mocker,
    mock_inventory_api_async_client,
    mock_inventory_inputs_translator,
    mock_boundary_features,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_store_session_input_in_bucket = mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.helpers.store_session_input_in_bucket"
    )

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, status=FieldStatus.enrolled)

    dndc_task = await mdl.DndcTasks(program_id=program.id, is_dry_run=False, status=DndcTaskStatusChoices.in_progress)

    mocker.patch("boundaries_service.client.get_features_for_fields", return_value={field.id: mock_boundary_features})

    integration = InventoryIntegration(
        request=app_request,
        inventory_client=mock_inventory_api_async_client,
        inventory_inputs_translator=mock_inventory_inputs_translator,
    )
    await integration.submit_fields_for_inventory_project(
        dndc_task.id,
        [field.id],
    )

    # Persisted DndcSimulationRequest assertions:
    simulation_request = (
        await orm_select(DndcSimulationRequests, where=[DndcSimulationRequests.field_id == field.id])
    )[0]
    assert simulation_request.error_message is None
    assert simulation_request.is_error is False
    assert simulation_request.field_id == field.id
    assert simulation_request.ss_api == ScenariosServiceApi.inventory_api
    assert simulation_request.ss_field_request_id == 1234567
    assert field.id == simulation_request.field_id
    assert simulation_request.task_id == dndc_task.id

    # Submit Inventory field assertions:
    mock_inventory_api_async_client.submit_field.assert_called_once()
    assert dndc_task.id in mock_inventory_api_async_client.submit_field.call_args.kwargs["project_name"]
    assert mock_inventory_api_async_client.submit_field.call_args.kwargs["x_consumer_id"] == "test_consumer"
    assert mock_inventory_api_async_client.submit_field.call_args.kwargs["inventory_input"].field_name == str(field.id)

    # Store InventoryInput in bucket assertions:
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["request"] is app_request
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["session_input"].field_name == str(field.id)
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["field_id"] == field.id
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["task_id"] == dndc_task.id


@patch("scenarios_service.generalized_integration.explore_integration.helpers.store_session_input_in_bucket")
@patch(
    "scenarios_service.generalized_integration.inventory_integration.InventoryInputsTranslator.get_inventory_input_for_field"
)
@patch("scenarios_service.generalized_integration.inventory_integration.InventoryIntegration.submit_inventory_inputs")
async def test_submit_fields_for_inventory_project_errors(
    mock_submit_inventory_inputs,
    mock_get_inventory_input_for_field,
    mock_store_session_input_in_bucket,
    mock_inventory_api_async_client,
    mock_boundary_features,
    app_request,
    mocker,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    mock_get_inventory_input_for_field.return_value = ({}, [])

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mocker.patch("boundaries_service.client.get_features_for_fields", return_value={field.id: mock_boundary_features})

    dndc_task = await mdl.DndcTasks(
        program_id=program.id,
        is_dry_run=False,
        status=DndcTaskStatusChoices.in_progress,
    )

    mock_submit_inventory_inputs.return_value = [
        ScenariosServiceSubmissionResult(field_id=field.id, is_error=True, error_message="An error occurred")
    ]

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)

    await integration.submit_fields_for_inventory_project(
        dndc_task_id=dndc_task.id,
        field_ids=[field.id],
    )

    mock_store_session_input_in_bucket.assert_not_called()


@patch("scenarios_service.generalized_integration.explore_integration.helpers.store_session_input_in_bucket")
@patch(
    "scenarios_service.generalized_integration.inventory_integration.InventoryInputsTranslator.get_inventory_input_for_field"
)
async def test_submit_program_fails_generating_input(
    mock_get_inventory_input_for_field,
    mock_store_session_input_in_bucket,
    mock_inventory_api_async_client,
    mock_boundary_features,
    app_request,
    mocker,
    mdl,
    mdl_factory_ignore_ids,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_get_inventory_input_for_field.side_effect = ValueError("An error occurred")
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    mocker.patch("boundaries_service.client.get_features_for_fields", return_value={field.id: mock_boundary_features})

    dndc_task = await mdl.DndcTasks(
        program_id=program.id,
        is_dry_run=False,
        status=DndcTaskStatusChoices.in_progress,
    )

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)

    await integration.submit_fields_for_inventory_project(
        dndc_task_id=dndc_task.id,
        field_ids=[field.id],
    )

    sim_reqs = await get_simulation_requests_for_task(app_request, dndc_task.id, ScenariosServiceApi.inventory_api)
    assert len(sim_reqs) == 1
    assert sim_reqs[0].field_id == field.id
    assert sim_reqs[0].ss_field_request_id is None
    assert sim_reqs[0].is_error is True
    assert sim_reqs[0].error_message is not None

    mock_store_session_input_in_bucket.assert_not_called()


@patch("scenarios_service.generalized_integration.inventory_integration.db.get_program_modeling_consumer_id")
async def test_finalize_inventory_project_no_errors(
    mock_get_program_modeling_consumer_id,
    mock_inventory_api_async_client,
    app_request,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    mock_get_program_modeling_consumer_id.return_value = "test_consumer"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    dndc_task = await mdl.DndcTasks(program_id=program.id, is_dry_run=False, status=DndcTaskStatusChoices.in_progress)
    await mdl.DndcSimulationRequests(task_id=dndc_task.id, field_id=field.id, is_error=False)

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)
    await integration.finalize_inventory_project(dndc_task_id=dndc_task.id, field_ids=[field.id])

    updated_dndc_task = await get_dndc_task_by_id(app_request, dndc_task.id)
    assert updated_dndc_task.status == DndcTaskStatusChoices.finalized

    mock_inventory_api_async_client.finalize_project.assert_called_with(
        project_name=helpers.build_ss_project_name(program, dndc_task.id), x_consumer_id="test_consumer"
    )


@patch("scenarios_service.generalized_integration.inventory_integration.db.get_program_modeling_consumer_id")
async def test_finalize_inventory_project_dry_run_do_not_finalize(
    mock_get_program_modeling_consumer_id,
    mock_inventory_api_async_client,
    app_request,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    mock_get_program_modeling_consumer_id.return_value = "test_consumer"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    # Create a dry run task
    dndc_task = await mdl.DndcTasks(program_id=program.id, is_dry_run=True, status=DndcTaskStatusChoices.in_progress)
    await mdl.DndcSimulationRequests(task_id=dndc_task.id, field_id=field.id, is_error=False)

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)
    await integration.finalize_inventory_project(dndc_task_id=dndc_task.id, field_ids=[field.id])

    updated_dndc_task = await get_dndc_task_by_id(app_request, dndc_task.id)
    assert updated_dndc_task.status == DndcTaskStatusChoices.finished

    mock_inventory_api_async_client.finalize_project.assert_not_called()


@patch("scenarios_service.generalized_integration.inventory_integration.db.get_program_modeling_consumer_id")
async def test_finalize_inventory_project_no_field_request_aborts_finalize(
    mock_get_program_modeling_consumer_id,
    mock_inventory_api_async_client,
    app_request,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    mock_get_program_modeling_consumer_id.return_value = "test_consumer"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    dndc_task = await mdl.DndcTasks(program_id=program.id, is_dry_run=False, status=DndcTaskStatusChoices.in_progress)
    # no simulation requests for the field

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)
    await integration.finalize_inventory_project(dndc_task_id=dndc_task.id, field_ids=[field.id])

    updated_dndc_task = await get_dndc_task_by_id(app_request, dndc_task.id)
    assert updated_dndc_task.status == DndcTaskStatusChoices.finished

    mock_inventory_api_async_client.finalize_project.assert_not_called()


@patch("scenarios_service.generalized_integration.inventory_integration.db.get_program_modeling_consumer_id")
async def test_finalize_inventory_project_failed_field_request_aborts_finalize(
    mock_get_program_modeling_consumer_id,
    mock_inventory_api_async_client,
    app_request,
    mdl,
    mdl_factory_ignore_ids,
    comprehensive_mrv_values_lookup,
):
    mock_get_program_modeling_consumer_id.return_value = "test_consumer"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    dndc_task = await mdl.DndcTasks(program_id=program.id, is_dry_run=False, status=DndcTaskStatusChoices.in_progress)

    # Simulate a failed request
    await mdl.DndcSimulationRequests(task_id=dndc_task.id, field_id=field.id, is_error=True)

    integration = InventoryIntegration(request=app_request, inventory_client=mock_inventory_api_async_client)
    await integration.finalize_inventory_project(dndc_task_id=dndc_task.id, field_ids=[field.id])

    updated_dndc_task = await get_dndc_task_by_id(app_request, dndc_task.id)
    assert updated_dndc_task.status == DndcTaskStatusChoices.finished

    mock_inventory_api_async_client.finalize_project.assert_not_called()


async def test_approve_outcomes_for_inventory_program_succeeds(
    mocker, app_request, mock_inventory_api_async_client, mdl, mdl_factory_ignore_ids
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )

    task = await mdl.DndcTasks(
        program_id=program.id,
        status=DndcTaskStatusChoices.finished,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
        accounting_method=AccountingMethod.inventory,
        protocol=program.protocol,
        baseline_method=program.baseline_method,
    )

    for ss_field_response in TEST_INVENTORY_RESPONSE["field_level"]:
        field_id = int(ss_field_response["field_name"])
        await mdl.Fields(parent_project_id=project.id, id=field_id)

    is_test_run = False
    selected_task_id = task.id
    inventory_output_integration = InventoryOutputIntegration(
        request=app_request, inventory_client=mock_inventory_api_async_client
    )
    with patch(
        "scenarios_service.generalized_integration.inventory_integration.read_url_result",
        return_value=json.dumps(TEST_INVENTORY_RESPONSE),
    ):
        await inventory_output_integration.approve_outcomes_for_program(program.id, selected_task_id, is_test_run)

    field_outcomes = await get_field_inventory_outcomes_for_program_task(app_request, program.id, selected_task_id)
    assert len(field_outcomes) == 1
    assert field_outcomes[0].ghg_emissions_factor is not None
    crop_outcomes = await get_crop_inventory_outcomes_for_program(app_request, program.id, selected_task_id)
    assert len(crop_outcomes) == 1
