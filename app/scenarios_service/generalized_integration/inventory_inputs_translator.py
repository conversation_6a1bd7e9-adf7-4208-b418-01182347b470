from datetime import datetime, timezone

import elasticapm
from fastapi import Request
from pydantic import ValidationError
from scenarios_service_schema.internal.soil import Soil
from scenarios_service_schema.inventory import InventoryInput

from cultivation_cycles.methods import (
    get_cultivation_cycles,
    get_unbinned_events_time_delta_from_cultivation_cycles,
)
from cultivation_cycles.schema import (
    CultivationCycle,
    CultivationCycleError,
    CultivationCycleGenerationConfig,
)
from logger import get_logger
from programs.enums import ProgramTemplate
from programs.model import Programs
from scenarios_service.generalized_integration import (
    fetch_events_for_simulations,
    helpers,
    input_translator_helpers,
)
from scenarios_service.generalized_integration.helpers import FieldWithBoundary
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.generalized_integration.session_inputs_helpers import (
    get_ss_soil_for_field,
)
from scenarios_service.model import (
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)

logger = get_logger(__name__)


class InventoryInputsTranslator:
    def __init__(self, request: Request):
        self.request = request

    @elasticapm.async_capture_span()
    async def get_inventory_input_for_field(
        self, field_and_boundary: FieldWithBoundary, program: Programs
    ) -> InventoryInput:
        """
        Take a single field and derive the InventoryInput based on the cultivation cycle that ends during the reporting
        year.

        For now, events must be read via the MRV Structured Events Facade (SEF), where we use entity_events.methods to
        that interpret mrv_values and format them as event instances.
        """
        field = field_and_boundary.field

        events = await fetch_events_for_simulations.get_events(self.request, field, ScenariosServiceApi.inventory_api)

        start_year = (
            await helpers.get_historical_data_start_year_for_field(self.request, field.id)
            if program.program_template == ProgramTemplate.legacy
            else None
        )
        # Group the events into cultivation cycles, splitting irrigation events that cross CC boundaries
        try:
            cultivation_cycles, unbinned_events = get_cultivation_cycles(
                events=events,
                generation_config=CultivationCycleGenerationConfig(
                    start_year=start_year,
                    should_split_irrigation_events=True,
                ),
            )
        except CultivationCycleError as cc_err:
            raise ScenariosServiceIntegrationError(
                "Unable to group MRV Structured Events into Cultivation Cycles",
                ScenariosServiceIntegrationErrorCode.invalid_management_data,
                extra=cc_err.extra,
            )
        if unbinned_events:
            # Log to keep an eye out for unbinnable events.
            unbinned_events_time_delta = get_unbinned_events_time_delta_from_cultivation_cycles(
                cultivation_cycles=cultivation_cycles, unbinned_events=unbinned_events
            )
            logger.exception(
                f"Field {field.id} has events {unbinned_events_time_delta[0].days} days before"
                + f" and {unbinned_events_time_delta[1].days} days after cultivation cycles."
            )

        location_data = field_and_boundary.get_location_data(allow_multipolygons=False)
        cultivation_cycles = input_translator_helpers.select_cultivation_cycles_for_simulation(
            cultivation_cycles, program.get_reporting_period()
        )
        # InventoryInput data is handled as dicts to delay validation
        ss_cultivation_cycles = await input_translator_helpers.convert_cultivation_cycles_to_ss_phases(
            self.request, cultivation_cycles, field, field_and_boundary.area_hectares
        )
        reporting_info = await input_translator_helpers.get_reporting_info(
            self.request, cultivation_cycles, field.id, program, ScenariosServiceApi.inventory_api
        )

        soil: Soil = await get_ss_soil_for_field(self.request, field)

        try:
            events_min_year = self.get_cultivation_cycles_min_event_date(cultivation_cycles).year
            return InventoryInput(
                field_name=str(field.id),
                location=location_data,
                soil=soil,
                cultivation_cycles=ss_cultivation_cycles,
                reporting_information=reporting_info,
                start_year=min(start_year, events_min_year) if start_year else events_min_year,
            )
        except ValidationError as validation_err:
            errors = validation_err.json()
            error_code = ScenariosServiceIntegrationErrorCode.scenarios_service_validation_failure

            if "Self-intersection" in errors:
                error_code = ScenariosServiceIntegrationErrorCode.field_boundary_self_intersection
            raise ScenariosServiceIntegrationError(validation_err.json(), error_code)

    @staticmethod
    def get_cultivation_cycles_min_event_date(cultivation_cycles: list[CultivationCycle]) -> datetime:
        return min(
            [
                event.get_interval_start_or_occurred_at()
                for cc in cultivation_cycles
                for event in cc.events
                if event.get_interval_start_or_occurred_at()
            ],
            default=datetime.max.replace(tzinfo=timezone.utc),
        )
