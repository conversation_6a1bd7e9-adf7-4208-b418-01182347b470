import asyncio
import json

import httpx
from celery import chord
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, status
from httpx import HTT<PERSON>tatusError
from scenarios_service_client.inventory_api_client import InventoryApiAsyncClient
from scenarios_service_schema.inventory import InventoryInput

from config import get_settings
from fields.model import Fields
from fields.schema import Field
from helper.async_tools import batch_async_runner, batch_list
from logger import capture_exception, get_logger
from phases.enums import PhaseTypes
from programs.db import get_program
from programs.enums import AccountingMethod
from programs.model import Programs
from reported_outcomes.handle_approvals import handle_storing_inventory_program_outcomes
from reported_outcomes.schema import ReportedInventoryProgramOutcomes
from root_crud import get, update
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration import db, helpers, tasks
from scenarios_service.generalized_integration.db import (
    get_dndc_task_by_id,
    get_simulation_requests_for_task,
    record_dndc_simulation_request_error,
    record_dndc_simulation_request_success,
)
from scenarios_service.generalized_integration.helpers import (
    filter_out_already_submitted_fields_for_dndc_task,
    get_field_boundaries,
)
from scenarios_service.generalized_integration.inventory_inputs_translator import (
    InventoryInputsTranslator,
)
from scenarios_service.generalized_integration.measure_api_methods import (
    create_dndc_task_with_dndc_version,
)
from scenarios_service.generalized_integration.outcome_integration_common import (
    read_url_result,
)
from scenarios_service.generalized_integration.outcomes_translator import (
    translate_inventory_to_reported_outcomes,
)
from scenarios_service.generalized_integration.schema import (
    FieldSimulationContext,
    ScenariosServiceIntegrationError,
    ScenariosServiceSubmissionResult,
    SubmitToMeasureRequest,
)
from scenarios_service.model import (
    DndcTasks,
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)
from scenarios_service.utils import slugify

settings = get_settings()
logger = get_logger(__name__)


class InventoryIntegration:
    def __init__(
        self,
        request: Request,
        inventory_inputs_translator: InventoryInputsTranslator | None = None,
        inventory_client: InventoryApiAsyncClient | None = None,
    ):
        self.request = request
        self.inventory_inputs_translator = inventory_inputs_translator or InventoryInputsTranslator(request)
        self.inventory_client = inventory_client or InventoryApiAsyncClient(url=settings.SCENARIOS_SERVICE_INTERNAL_URL)

    async def prepare_inventory_project(
        self,
        program_id: int,
        field_ids: list[int],
        dry_run: bool = False,
    ) -> DndcTasks:
        """
        This prepares a new DNDC Task and corresponding project with Scenarios Service's inventory API, in preparation to
        generate final outcomes for an MRV intervention program. This is Manually initiated process soon after the end
        of a program Reporting Period.

        As part of this process, it validates the provided fields to ensure they are in the correct state for inventory
        submission, throwing an HTTPException if any field is invalid. It will also raise an error if the SS project
        cannot be created.

        Note this does not actually submit the fields to Inventory API; this is intended to be done via celery task by
        executing tasks/submit_prepared_program_for_inventory_outcomes_task after the task is created (this will invoke
        submit_task_to_inventory below).
        Preparing the task synchronously allows for validation and setup to fail/succeed immediately so that we can
        provide immediate feedback to the UI.
        """

        program = await get_program(request=self.request, program_id=program_id)  # Raises a 404 if not found
        ss_consumer_id = await db.get_program_modeling_consumer_id(request=self.request, program_id=program_id)

        await helpers.validate_fields(request=self.request, program_id=program_id, submitted_field_ids=field_ids)

        task = await create_dndc_task_with_dndc_version(
            request=self.request,
            program_id=program_id,
            project_id=None,
            phase=PhaseTypes.MONITORING,
            scenarios_service_api=ScenariosServiceApi.inventory_api,
            is_dry_run=dry_run,
            ss_consumer_id=ss_consumer_id,
            accounting_method=AccountingMethod.inventory,
            submitted_field_ids=field_ids,
        )

        await self.create_ss_project_returning_name(program, task.id, ss_consumer_id)  # type: ignore[arg-type]
        return task

    async def prepare_existing_inventory_project(
        self,
        task_to_add_fields_to: str,
        field_ids: list[int],
    ) -> tuple[DndcTasks, list[int]]:
        """
        This re-prepares an existing DNDC Task to resubmit previously failed fields to Scenario Service's inventory API.
        The task's status must not be in_progress-, interrupted, or finalized. The provided field_ids are filtered to
        only include those that have not been successfully submitted.

        As part of this process, it validates the provided fields to ensure they are in the correct state for inventory
        submission, throwing an HTTPException if any field is invalid.

        Note this does not actually submit the fields to Measure API; this is intended to be done via celery task by
        executing tasks/submit_prepared_program_for_measure_outcomes_task after the task is created (this will invoke
        submit_task_to_measure below).
        Preparing the task synchronously allows for validation and setup to fail/succeed immediately so that we can
        provide immediate feedback to the UI.
        """
        task = await get_dndc_task_by_id(self.request, task_to_add_fields_to)

        if task.status in {
            DndcTaskStatusChoices.in_progress,
            DndcTaskStatusChoices.interrupted,
            DndcTaskStatusChoices.finalized,
        }:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Can't add more fields to task {task_to_add_fields_to} in status {task.status}.",
            )
        task.status = DndcTaskStatusChoices.in_progress
        await update.update(request=self.request, instances=[task], type=DndcTasks)

        field_ids = await filter_out_already_submitted_fields_for_dndc_task(self.request, task.id, field_ids)
        # Delete simulation requests for unsubmitted fields (these will have been errors)
        await db.delete_simulation_requests(self.request, task.id, field_ids)
        if field_ids:
            await helpers.validate_fields(self.request, task.program_id, field_ids)
        else:
            logger.info("Task already successfully submitted all provided fields. May proceed to finalization.")
        return task, field_ids

    async def submit_fields_for_inventory_project(
        self,
        dndc_task_id: str,
        field_ids: list[int],
    ) -> None:
        """
        This submits the provided subset of fields for a previously prepared DNDC Task and corresponding Scenarios
        Service Inventory project. This is intended to be run as a celery task, in parallel with other field batches for
        the same Inventory project. Results stored in mrv_dndc_simulation_requests table will be used by the subsequent
        finalize_inventory_project method to finalize the Inventory project.
        """

        task = await get_dndc_task_by_id(self.request, dndc_task_id)
        logger.info(
            f"Submitting {len(field_ids)} fields for task {dndc_task_id}, program {task.program_id} to Measure API"
        )
        program = await get_program(request=self.request, program_id=task.program_id)
        ss_consumer_id = await db.get_program_modeling_consumer_id(request=self.request, program_id=task.program_id)
        ss_project_name = helpers.build_ss_project_name(program, dndc_task_id)

        # submit fields in batches
        submission_responses = []
        error_responses = []
        for field_batch in batch_list(field_ids, batch_size=settings.MEASURE_INTEGRATION_FIELD_BATCH_SIZE_TO_SUBMIT):
            fields: list[Field] = await get.get(
                request=self.request,
                orm_type=Fields,
                type_=Field,
                id_field=Fields.id,
                ids=field_batch,
                empty_return=True,
            )
            session_inputs, batch_error_responses = await self._get_inventory_inputs(dndc_task_id, program, fields)
            error_responses.extend(batch_error_responses)
            # Submit each of the generated SessionInputs
            submission_responses.extend(
                await self.submit_inventory_inputs(session_inputs, ss_project_name, ss_consumer_id)
            )

        if len(error_responses) > 0 or any(response.is_error for response in submission_responses) is True:
            logger.error(f"Error(s) in Inventory submission: {error_responses}")

    async def finalize_inventory_project(
        self,
        dndc_task_id: str,
        field_ids: list[int],
    ) -> None:
        """
        THis examines the results of the given DNDC Task and corresponding Scenarios Service Inventory project, from
        the mrv_dndc_simulation_requests table, and finalizes the Inventory project if all fields were successfully
        submitted to Inventory API. If any fields were not submitted, or if there are errors in the submission results,
        it will log an error and not finalize the project. If this is a dry run, it will not finalize the project
        but will log the successful completion of the dry run.
        """
        task = await get_dndc_task_by_id(self.request, dndc_task_id)
        logger.info(
            f"Finalizing Inventory API task {dndc_task_id}, program {task.program_id}"
            f"{' (dry run)' if task.is_dry_run else ''}."
        )
        program = await get_program(request=self.request, program_id=task.program_id)
        ss_consumer_id = await db.get_program_modeling_consumer_id(request=self.request, program_id=task.program_id)
        ss_project_name = helpers.build_ss_project_name(program, dndc_task_id)

        await db.set_dndc_task_status_and_submitted_field_ids(
            self.request, dndc_task_id, DndcTaskStatusChoices.finished
        )

        sim_reqs = await get_simulation_requests_for_task(self.request, dndc_task_id)

        missing_fields = [field_id for field_id in field_ids if field_id not in {req.field_id for req in sim_reqs}]

        requests_with_error = [req for req in sim_reqs if req.is_error]

        # Finalize only if there are no errors and this is not a dry run
        if missing_fields:
            logger.error(
                f"{len(missing_fields)} fields have no recorded request to Inventory API for task {dndc_task_id}: {missing_fields}"
            )
        elif requests_with_error:
            logger.error(
                f"{len(requests_with_error)} field requests returned errors from Inventory API for task {dndc_task_id}."
            )
        elif task.is_dry_run:
            logger.info(
                f"Dry run completed successfully for task {dndc_task_id} for program {program.id}. Program not finalized."
            )
        else:
            try:
                await self.inventory_client.finalize_project(project_name=ss_project_name, x_consumer_id=ss_consumer_id)
                await db.set_dndc_task_status_and_submitted_field_ids(
                    self.request, dndc_task_id, DndcTaskStatusChoices.finalized
                )
            except httpx.HTTPStatusError as er:
                capture_exception(
                    logger, er, message=f"Error finalizing project {ss_project_name}: {er.response.status_code}"
                )

    async def _get_inventory_inputs(
        self, dndc_task_id: str, program: Programs, fields: list[Field]
    ) -> tuple[list[tuple[FieldSimulationContext, InventoryInput]], list[ScenariosServiceSubmissionResult]]:
        """Makes batched async calls to the InventoryInputsTranslator and handles results."""

        fields_with_boundaries = await get_field_boundaries(fields)

        async def _get_inventory_input(
            field: Field,
        ) -> tuple[FieldSimulationContext, InventoryInput] | ScenariosServiceSubmissionResult:
            # Inventory has changed this from session_name to field_name
            ss_field_name = str(field.id)
            sim_context = FieldSimulationContext(
                field_id=field.id,
                task_id=dndc_task_id,
                ss_session_name=ss_field_name,
            )
            try:
                inventory_input = await self.inventory_inputs_translator.get_inventory_input_for_field(
                    field_and_boundary=fields_with_boundaries[field.id], program=program
                )
                return sim_context, inventory_input
            except Exception as er:
                error_code = None
                if isinstance(er, ScenariosServiceIntegrationError):
                    error_code = er.code
                    error_message = er.message
                else:
                    error_message = f"Error generating InventoryInput: {repr(er)}."
                await self._record_simulation_request_error(
                    context=sim_context, error_message=error_message, error_code=error_code
                )
                capture_exception(logger=logger, exc=er, message=error_message)
                return ScenariosServiceSubmissionResult(
                    field_id=field.id,
                    session_name=ss_field_name,
                    is_error=True,
                    error_message=error_message,
                    error_code=error_code,
                )

        results = await batch_async_runner(
            [_get_inventory_input(field) for field in fields], batch_limit=settings.MYSQL_POOL_SIZE
        )
        return (
            [res for res in results if isinstance(res, tuple)],
            [res for res in results if isinstance(res, ScenariosServiceSubmissionResult)],
        )

    async def create_ss_project_returning_name(self, program: Programs, task_id: str, ss_consumer_id: str) -> str:
        """Scenarios Service has a 'project' concept that is different from MRV projects. SS projects are just a
        container for all the Field SessionInputs that will be simulated with the same configuration (protocol and
        baseline method). The SS project maps to an MRV program. Once fields are submitted to a project, they can't be
        edited, so we create a new project with every run."""
        project_name: str = f"{slugify(program.name)}_{task_id}"
        await self.inventory_client.create_project(project_name=project_name, x_consumer_id=ss_consumer_id)
        return project_name

    async def submit_inventory_inputs(
        self,
        inventory_inputs: list[tuple[FieldSimulationContext, InventoryInput]],
        ss_project_name: str,
        ss_consumer_id: str,
    ) -> list[ScenariosServiceSubmissionResult]:
        return await batch_async_runner(
            [
                self.submit_field_inventory_input(context, ss_project_name, ss_consumer_id, inventory_input)
                for context, inventory_input in inventory_inputs
            ]
        )

    async def submit_field_inventory_input(
        self,
        context: FieldSimulationContext,
        ss_project_name: str,
        ss_consumer_id: str,
        inventory_input: InventoryInput,
    ) -> ScenariosServiceSubmissionResult:
        logger.debug(f"Submitting field InventoryInput to Measure API for field {context.field_id}.")
        try:
            inventory_response: httpx.Response = await self.inventory_client.submit_field(
                project_name=ss_project_name, x_consumer_id=ss_consumer_id, inventory_input=inventory_input
            )
            response_data = inventory_response.json()["data"]
            context.ss_field_request_id = response_data["field_request_id"]
            await self._record_simulation_request_success(context=context, inventory_input=inventory_input)
            return ScenariosServiceSubmissionResult(
                field_id=context.field_id,
                is_error=False,
                session_name=context.ss_session_name,
                project_name=response_data["project_name"],
                field_request_id=response_data["field_request_id"],
            )
        except Exception as e:
            if isinstance(e, HTTPStatusError) and e.response.status_code == 422:
                error_message = f"The InventoryInput failed Inventory API validation: {e.response.content}"
                error_code = ScenariosServiceIntegrationErrorCode.scenarios_service_validation_failure
            else:  # http_e.response.status_code >= 500:
                error_message = f"Inventory API experienced a server error: {repr(e)}"
                error_code = ScenariosServiceIntegrationErrorCode.generic_scenarios_service_failure
            capture_exception(logger=logger, exc=e, message=error_message)
            return await self._record_simulation_request_error(context, error_message, error_code, inventory_input)

    async def _record_simulation_request_success(
        self, context: FieldSimulationContext, inventory_input: InventoryInput | None = None
    ) -> None:
        tasks = [
            record_dndc_simulation_request_success(
                request=self.request,
                context=context,
                ss_api=ScenariosServiceApi.inventory_api,
            )
        ]
        if inventory_input:
            tasks.append(
                helpers.store_session_input_in_bucket(
                    request=self.request,
                    task_id=context.task_id,
                    field_id=context.field_id,
                    session_input=inventory_input,
                )
            )
        await asyncio.gather(*tasks)

    async def _record_simulation_request_error(
        self,
        context: FieldSimulationContext,
        error_message: str,
        error_code: ScenariosServiceIntegrationErrorCode | None,
        inventory_input: InventoryInput | None = None,
    ) -> ScenariosServiceSubmissionResult:
        context.error_message = error_message
        context.error_code = error_code
        context.is_error = True

        async_tasks = [
            record_dndc_simulation_request_error(
                request=self.request,
                sim_context=context,
                ss_api=ScenariosServiceApi.inventory_api,
            )
        ]
        if inventory_input:
            async_tasks.append(
                helpers.store_session_input_in_bucket(
                    request=self.request,
                    task_id=context.task_id,
                    field_id=context.field_id,
                    session_input=inventory_input,
                )
            )
        await asyncio.gather(*async_tasks)
        return ScenariosServiceSubmissionResult(
            field_id=context.field_id,
            is_error=True,
            error_message=error_message,
            error_code=error_code,
            session_name=context.ss_session_name,
        )


class InventoryOutputIntegration:
    def __init__(
        self,
        request: Request,
        inventory_client: InventoryApiAsyncClient | None = None,
    ):
        self.request = request
        self.inventory_client = inventory_client or InventoryApiAsyncClient(settings.SCENARIOS_SERVICE_INTERNAL_URL)

    async def approve_outcomes_for_program(self, program_id: int, selected_task_id: str, is_test_run: bool) -> None:
        if selected_task_id is None:
            raise AssertionError("Unset task ids should no longer make it to this point")
        program_outcomes = await self._obtain_inventory_outcomes(program_id, selected_task_id, is_test_run)
        await handle_storing_inventory_program_outcomes(self.request, program_outcomes, selected_task_id)

    async def _obtain_inventory_outcomes(
        self, program_id: int, selected_task_id: str, is_test_run: bool
    ) -> ReportedInventoryProgramOutcomes:
        program: Programs = await get_program(request=self.request, program_id=program_id)
        ss_consumer_id: str = await db.get_program_modeling_consumer_id(request=self.request, program_id=program_id)
        ss_project_name = helpers.build_ss_project_name(program, selected_task_id)
        try:
            inventory_signed_res = await self.inventory_client.get_project_results(
                ss_project_name, ss_consumer_id, signed_url=True
            )
        except httpx.HTTPStatusError:
            error_msg = f"Scenario service did not handle providing results from ss_project_name={ss_project_name} ss_consumer_id={ss_consumer_id} for project task {selected_task_id}"
            logger.error(error_msg)
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": error_msg})

        if inventory_signed_res.status_code == 202:
            error_msg = f"Results are not finalized from ss_project_name={ss_project_name} ss_consumer_id={ss_consumer_id} for task {selected_task_id} and are not ready to be used"
            logger.error(error_msg)
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": error_msg})

        inventory_signed_url = inventory_signed_res.json()["download_link"]
        inventory_result_str = read_url_result(inventory_signed_url)
        inventory_results = json.loads(inventory_result_str)
        del inventory_result_str
        return await translate_inventory_to_reported_outcomes(
            self.request, program_id, inventory_results, selected_task_id, is_test_run
        )


async def submit_fields_to_inventory_project(request: Request, inventory_request: SubmitToMeasureRequest) -> None:
    """
    This function prepares a DNDC Task for Inventory API submission and submits the provided fields in batches
    via parallel celery tasks with submit_fields_for_inventory_project_task. The batch size is configured by the setting
    INVENTORY_INTEGRATION_FIELD_BATCH_SIZE_PER_WORKER. Once all parallel tasks are complete, it finalizes the Inventory
    project by invoking the finalize_inventory_project_task celery task.

    It handles both new Inventory projects and existing Inventory projects for which to re-run failed fields.
    """
    field_ids = inventory_request.field_ids
    if inventory_request.task_to_add_fields_to:
        (dndc_task, field_ids) = await InventoryIntegration(request).prepare_existing_inventory_project(
            task_to_add_fields_to=inventory_request.task_to_add_fields_to,
            field_ids=field_ids,
        )
    else:
        dndc_task = await InventoryIntegration(request).prepare_inventory_project(
            program_id=inventory_request.program_id,
            field_ids=field_ids,
            dry_run=inventory_request.dry_run,
        )
    chord(
        (
            tasks.submit_fields_for_inventory_project_task.s(
                dndc_task_id=dndc_task.id,
                field_ids=field_id_batch,
                fs_user_id=request.state.fs_user_id,
                fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            )
            for field_id_batch in batch_list(field_ids, settings.MEASURE_INTEGRATION_FIELD_BATCH_SIZE_TO_SUBMIT)
        ),
        tasks.finalize_inventory_project_task.si(
            dndc_task_id=dndc_task.id,
            field_ids=field_ids,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        ),
    ).delay()
