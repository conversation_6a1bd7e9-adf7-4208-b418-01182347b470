from collections import defaultdict
from datetime import datetime, timezone
from io import String<PERSON>
from itertools import chain
from unittest.mock import patch

from defaults.attribute_options import CropUsage, ResidueHarvested, TillagePractice
from entity_events.event_creators.cargill_eu_25x_2024_event_creator import (
    post_create_eu_25x_events,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.reduction_event import ReductionEvent, StandardReductionTypes
from entity_events.events.tillage_event import TillageEvent
from entity_events.events.yield_rate import YieldRate
from entity_events.measures import Depth, Interval
from entity_events.units import AreaUnit, LengthUnit, VolumeUnit
from phases.enums import PhaseTypes, StageTypes
from values.enums import EntityTypeChoices


def get_cropping_gapfilling_csv(phase: str) -> StringIO:
    return StringIO(
        '"mrv_field_id","phase","crop_name","plant_date","harvest_date","crop_usage","termination_method","residue_harvested","yield","yield_unit","tillage_season","tillage_date","tillage_practice","tillage_depth","tillage_inversion"\n'
        f'"1","{phase}","corn","2024-01-01","2024-06-01","Commodity","","No residue harvested","1","bu/ac","","","","",""\n'
        f'"1","{phase}","corn","2024-07-01","2024-12-01","Commodity","","No residue harvested","1","bu/ac","","","","",""\n'
    )


def get_tillage_gapfilling_csv(phase: str) -> StringIO:
    return StringIO(
        '"mrv_field_id","phase","crop_name","plant_date","harvest_date","crop_usage","termination_method","residue_harvested","yield","yield_unit","tillage_season","tillage_date","tillage_practice","tillage_depth","tillage_inversion"\n'
        f'"1","{phase}","","","","","","","","","spring","2024-01-01","conventional till","20","1"\n'
        f'"1","{phase}","","","","","","","","","fall","2024-02-01","reduced till","20","0"\n'
    )


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_cropping_events_enrollment(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = get_cropping_gapfilling_csv("enrollment")
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    values_data = {
        PhaseTypes.ENROLMENT: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_CROP_ROTATION: [
                CroppingEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent.parse_obj(
                            {
                                "occurred_at": datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                                **StandardReductionTypes.GRAIN_HARVEST,
                            }
                        )
                    ],
                ),
                CroppingEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 7, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    reductions=[
                        ReductionEvent.parse_obj(
                            {
                                "occurred_at": datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                                **StandardReductionTypes.GRAIN_HARVEST,
                            }
                        )
                    ],
                    residue_harvested=ResidueHarvested.percent_00,
                ),
            ]
        }
    }
    assert res == expected_res


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_tillage_events_enrollment(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = get_tillage_gapfilling_csv("enrollment")
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    values_data = {
        PhaseTypes.ENROLMENT: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_TILLAGE: [
                TillageEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=True,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.conventional_till,
                ),
                TillageEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 2, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=False,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.reduced_till,
                ),
            ]
        }
    }
    assert res == expected_res


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_cropping_events_monitoring(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = get_cropping_gapfilling_csv("measurement")
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    values_data = {
        PhaseTypes.ENROLMENT: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {
        PhaseTypes.MONITORING: {
            StageTypes.HISTORICAL_CROP_ROTATION: [
                CroppingEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent(
                            occurred_at=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                            root_removed_fraction=0.0,
                            root_residue_fraction=0.0,
                            stem_removed_fraction=0.05,
                            stem_residue_fraction=0.95,
                            leaf_removed_fraction=0.05,
                            leaf_residue_fraction=0.95,
                            grain_removed_fraction=1.0,
                            grain_residue_fraction=0.0,
                        )
                    ],
                ),
                CroppingEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 7, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent(
                            occurred_at=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                            root_removed_fraction=0.0,
                            root_residue_fraction=0.0,
                            stem_removed_fraction=0.05,
                            stem_residue_fraction=0.95,
                            leaf_removed_fraction=0.05,
                            leaf_residue_fraction=0.95,
                            grain_removed_fraction=1.0,
                            grain_residue_fraction=0.0,
                        )
                    ],
                ),
            ]
        }
    }
    assert res == expected_res


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_tillage_events_monitoring(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = get_tillage_gapfilling_csv("measurement")
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    values_data = {
        PhaseTypes.ENROLMENT: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {
        PhaseTypes.MONITORING: {
            StageTypes.TILLAGE: [
                TillageEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=True,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.conventional_till,
                ),
                TillageEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 2, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=False,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.reduced_till,
                ),
            ]
        }
    }
    assert res == expected_res


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_tillage_events_monitoring_no_events(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = get_tillage_gapfilling_csv("measurement")
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    # gapfilling CSV does not have row with mrv_field_id = 2
    values_data = {
        PhaseTypes.ENROLMENT: {2: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {2: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {}
    assert res == expected_res


@patch("entity_events.event_creators.cargill_eu_25x_2024_event_creator._get_gapfilling_csv")
def test_post_create_eu_25x_events(mock_get_gapfilling_csv):
    mock_get_gapfilling_csv.return_value = StringIO(
        "".join(
            chain(
                get_cropping_gapfilling_csv("enrollment").readlines(),
                get_tillage_gapfilling_csv("enrollment").readlines()[1:],
                get_cropping_gapfilling_csv("measurement").readlines()[1:],
                get_tillage_gapfilling_csv("measurement").readlines()[1:],
            )
        )
    )
    events_by_phase_stage = defaultdict(lambda: defaultdict(list))
    values_data = {
        PhaseTypes.ENROLMENT: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
        PhaseTypes.MONITORING: {1: {StageTypes.HISTORICAL_CROP_ROTATION: {1: {}}}},
    }
    res = post_create_eu_25x_events(events_by_phase_stage, values_data)
    expected_res = {
        PhaseTypes.ENROLMENT: {
            StageTypes.HISTORICAL_CROP_ROTATION: [
                CroppingEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent.parse_obj(
                            {
                                "occurred_at": datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                                **StandardReductionTypes.GRAIN_HARVEST,
                            }
                        )
                    ],
                ),
                CroppingEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 7, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent.parse_obj(
                            {
                                "occurred_at": datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                                **StandardReductionTypes.GRAIN_HARVEST,
                            }
                        )
                    ],
                ),
            ],
            StageTypes.HISTORICAL_TILLAGE: [
                TillageEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=True,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.conventional_till,
                ),
                TillageEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 2, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=False,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.reduced_till,
                ),
            ],
        },
        PhaseTypes.MONITORING: {
            StageTypes.HISTORICAL_CROP_ROTATION: [
                CroppingEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent(
                            occurred_at=datetime(2024, 6, 1).replace(tzinfo=timezone.utc),
                            root_removed_fraction=0.0,
                            root_residue_fraction=0.0,
                            stem_removed_fraction=0.05,
                            stem_residue_fraction=0.95,
                            leaf_removed_fraction=0.05,
                            leaf_residue_fraction=0.95,
                            grain_removed_fraction=1.0,
                            grain_residue_fraction=0.0,
                        )
                    ],
                ),
                CroppingEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    interval=Interval(
                        start=datetime(2024, 7, 1).replace(tzinfo=timezone.utc),
                        end=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                    ),
                    crop_type="corn",
                    crop_usage=CropUsage.COMMODITY,
                    crop_yield=YieldRate(value=1, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
                    residue_harvested=ResidueHarvested.percent_00,
                    reductions=[
                        ReductionEvent(
                            occurred_at=datetime(2024, 12, 1).replace(tzinfo=timezone.utc),
                            root_removed_fraction=0.0,
                            root_residue_fraction=0.0,
                            stem_removed_fraction=0.05,
                            stem_residue_fraction=0.95,
                            leaf_removed_fraction=0.05,
                            leaf_residue_fraction=0.95,
                            grain_removed_fraction=1.0,
                            grain_residue_fraction=0.0,
                        )
                    ],
                ),
            ],
            StageTypes.TILLAGE: [
                TillageEvent(
                    id=0,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 1, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=True,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.conventional_till,
                ),
                TillageEvent(
                    id=1,
                    entity_id=1,
                    entity_type=EntityTypeChoices.field,
                    occurred_at=datetime(2024, 2, 1).replace(tzinfo=timezone.utc),
                    depth=Depth(value=20.0, unit=LengthUnit.CENTIMETRE),
                    soil_inversion=False,
                    strip_fraction=None,
                    tillage_practice=TillagePractice.reduced_till,
                ),
            ],
        },
    }
    assert res == expected_res
