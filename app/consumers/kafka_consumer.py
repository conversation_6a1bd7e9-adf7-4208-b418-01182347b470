import json
import traceback
from dataclasses import dataclass
from typing import Any, Awaitable, Callable, TypeVar

from fastapi import Request
from google.protobuf.json_format import MessageToDict
from google.protobuf.message import Message
from regrow_kafka.consumer import (
    ConsumerResponse,
    KafkaConsumer,
    TouchFileHealthChecker,
)

import config
from config import get_settings
from db.db_instance import DB
from logger import capture_exception, get_logger
from slack_integration.enums import SlackBlockType
from slack_integration.integration import post_message
from slack_integration.schema import SlackBlock

_settings = get_settings()

logger = get_logger(__name__)


MSG = TypeVar("MSG", bound=Message)


@dataclass
class State:
    fs_user_id: int
    fs_impersonator_user_id: int | None = None
    sql_session: Any | None = None
    smtp_session: Any | None = None
    gcloud_storage_client: Any | None = None
    gcloud_session: Any | None = None
    is_super_user: bool | None = None


@dataclass
class MockRequest:
    state: State | None = None


async def consume_from_kafka(
    topic: str,
    proto_value_class: MSG,
    message_handler: Callable[[Request, MSG], Awaitable[None]],
) -> None:
    """
    Runs forever consuming messages from a Kafka topic, delegating the processing of each message to the provided
    handler. Each message is expected to be a protobuf message of the type specified by `proto_value_class`.

    The handler should accept a FastAPI Request object and the message itself, and return an awaitable that processes
    the message.
    (Note the FastAPI Request object is provided to allow access to the request context, such as the database session -
    this is currently faked until and unless we refactor general MRV methods to use a more generic context object.)

    Messages are consumed and committed one at a time - this gives us at-least-once delivery guarantee. In the future,
    we may want to implement asynchronous processing of messages in batches to achieve better throughput, but this is
    sufficient to start - we would also want better idempotency assurances in that case.

    Currently, no provisions are made for idempotency, order, or error handling, so in case of any problem, it is up to
    the engineering team to somehow manually recover the state of the system. MRV-5894 will address this soon.
    """

    await DB.start_db(settings=config.get_settings())

    logger.info(f"Starting consumer on kafka topic {topic}...")
    consumer = _create_consumer(topic, proto_value_class)
    await _consume_messages(topic, consumer, message_handler)


def _create_consumer(topic: str, proto_value_class: MSG) -> KafkaConsumer:
    health_checker = (
        TouchFileHealthChecker(
            _settings.KAFKA_CONSUMER_HEALTH_CHECK_PATH, _settings.KAFKA_CONSUMER_HEALTH_CHECK_INTERVAL_SECONDS
        )
        if "KAFKA_CONSUMER_HEALTH_CHECK_PATH" in _settings and _settings.KAFKA_CONSUMER_HEALTH_CHECK_PATH
        else None
    )
    return KafkaConsumer.create_default(
        topic=topic,
        consumer_group_id=_settings.KAFKA_CONSUMER_GROUP_ID,
        proto_value_class=proto_value_class,
        health_checker=health_checker,
        autocommit=True,
    )


async def _consume_messages(
    topic: str, consumer: KafkaConsumer, message_handler: Callable[[Request, MSG], Awaitable[None]]
) -> None:
    for message in consumer.consume():
        if message.no_data:
            continue

        logger.debug(
            f"Received message from kafka; offset: {message.offset}, partition: {message.partition}, key: {message.key}",
            extra={"value": message.value},
        )
        request = MockRequest(state=State(fs_user_id=int(_settings.CORE_USER_ID), sql_session=DB.session))
        value: MSG = message.value
        try:
            await message_handler(request, value)
        except Exception as ex:
            # TODO MRV-5894 add error processing logic - need to differentiate between recoverable and non-recoverable errors
            capture_exception(logger, ex, f"Error processing message from Kafka topic {topic}", {"payload": message})
            await _notify_error_in_slack(topic, message, ex)


async def _notify_error_in_slack(topic: str, message: ConsumerResponse, ex: Exception) -> None:
    if _settings.SlACK_WEBHOOK_URL_CONSUMER_ALERTS:
        message_dict = {
            "key": message.key,
            "timestamp_ms": message.timestamp_ms,
            "partition": message.partition,
            "offset": message.offset,
            "value": MessageToDict(message.value),
        }
        await post_message(
            slack_webhook_url=_settings.SlACK_WEBHOOK_URL_CONSUMER_ALERTS,
            blocks=[
                SlackBlock(type=SlackBlockType.header, text=f"Error processing message from Kafka topic {topic}"),
                SlackBlock(type=SlackBlockType.section, text=f"```\n{''.join(traceback.format_exc())}\n```"),
                SlackBlock(type=SlackBlockType.header, text="Kafka message"),
                SlackBlock(type=SlackBlockType.section, text=f"```\n{json.dumps(message_dict, indent=2)}\n```"),
            ],
        )
