from datetime import date

from ui.projects.completion.dataclasses import EvaluationContext


async def test_should_validation_errors_block_completion(mdl):

    blocking_program = await mdl.Programs(id=1126)

    non_blocking_program = await mdl.Programs(id=1119)

    test_completion_end_date = date(year=2023, month=2, day=1)

    project = await mdl.Projects(program_id=blocking_program.id)
    phase = mdl.Phases(program_id=blocking_program.id, deleted_at=None, enabled=True)

    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None)

    ec = EvaluationContext(
        program=blocking_program, field=field, phase=phase, completion_end_date=test_completion_end_date
    )

    assert ec.should_validation_errors_block_completion()

    ec = EvaluationContext(
        program=non_blocking_program, field=field, phase=phase, completion_end_date=test_completion_end_date
    )

    assert not ec.should_validation_errors_block_completion()
