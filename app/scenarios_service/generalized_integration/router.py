from typing import Optional

import elasticapm
import httpx
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from scenarios_service_client.inventory_api_client import InventoryApiAsyncClient
from starlette import status
from starlette.requests import Request

from config import get_settings
from logger import get_logger
from permissions.enums import Permission
from permissions.resolver import Permissions
from programs.db import get_program
from scenarios_service.generalized_integration import db, explore_integration, tasks
from scenarios_service.generalized_integration.biofuels_integration import (
    BiofuelsIntegration,
)
from scenarios_service.generalized_integration.inventory_integration import (
    submit_fields_to_inventory_project,
)
from scenarios_service.generalized_integration.measure_api_methods import (
    get_intervention_consumer_configurations,
)
from scenarios_service.generalized_integration.measure_integration import (
    submit_fields_to_measure_project,
)
from scenarios_service.generalized_integration.outcome_approval_handler import (
    handle_outcome_approval,
    revoke_existing_approval,
)
from scenarios_service.generalized_integration.schema import (
    ApproveProgramOutcomesRequest,
    ConsumerConfiguration,
    ExploreRequestBody,
    MeasureIntegrationRequest,
    MeasureSubmissionResult,
    SubmitToMeasureRequest,
)
from scenarios_service.model import ProgramModelingConfigurations
from scenarios_service.schema import ProgramModelingConfiguration

router = APIRouter(prefix="/modeling")
programs_router = APIRouter(prefix="/programs/{program_id}")

settings = get_settings()
logger = get_logger(__name__)


@router.post(
    "/measure/submit-program",
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def submit_fields_to_measure(request: Request, body: MeasureIntegrationRequest) -> None:
    if not body.field_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Field IDs must be provided in the request body.",
        )
    await submit_fields_to_measure_project(request, body)


@router.post(
    "/explore/submit-project",
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def submit_project_fields_to_explore(request: Request, body: ExploreRequestBody) -> None:
    tasks.submit_project_fields_to_explore_task.delay(
        body=body, fs_user_id=request.state.fs_user_id, fs_impersonator_user_id=request.state.fs_impersonator_user_id
    )


@router.post(
    "/measure/submit-biofuels",
    status_code=status.HTTP_200_OK,
    response_model=MeasureSubmissionResult,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def submit_biofuels_program_to_measure(request: Request, body: SubmitToMeasureRequest) -> MeasureSubmissionResult:
    return await BiofuelsIntegration(request).submit_program_for_biofuels_outcomes(
        program_id=body.program_id, field_ids=body.field_ids, dry_run=body.dry_run
    )


@router.post(
    "/measure/submit-inventory",
    status_code=status.HTTP_200_OK,
    dependencies=[
        Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))
    ],  # TODO(lucia): Is this correct for inventory?
)
async def submit_inventory_program_to_measure(request: Request, body: SubmitToMeasureRequest) -> None:
    if not body.field_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Field IDs must be provided in the request body.",
        )
    await submit_fields_to_inventory_project(request, body)


@router.post(
    "/measure/approve-program-outcomes",
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def approve_program_outcomes(request: Request, body: ApproveProgramOutcomesRequest) -> None:
    # "test run" default is False
    if body.is_test_run is not None and body.is_test_run:
        is_test_run = True
    else:
        is_test_run = False

    # "async" default is false
    if body.is_async is not None and body.is_async:
        is_async = True
    else:
        is_async = False

    await handle_outcome_approval(request, body.program_id, body.task_id, is_test_run, is_async)


@router.post(
    "/measure/revoke-program-outcomes-approval",
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def revoke_program_outcomes(request: Request, body: ApproveProgramOutcomesRequest) -> None:
    # "test run" default is False
    if body.is_test_run is not None and body.is_test_run:
        is_test_run = True
    else:
        is_test_run = False

    # "async" default is false
    if body.is_async is not None and body.is_async:
        is_async = True
    else:
        is_async = False

    await revoke_existing_approval(request, body.program_id, body.task_id, is_test_run, is_async)


@elasticapm.async_capture_span()
@router.get(
    "/consumer_configurations",
    status_code=status.HTTP_200_OK,
    response_model=list[ConsumerConfiguration],
    dependencies=[Depends(Permissions([Permission.GET_PROGRAM]))],
)
async def get_consumer_configurations() -> list[ConsumerConfiguration]:
    return await get_intervention_consumer_configurations()


@elasticapm.async_capture_span()
@router.get(
    "/inventory_consumer_configurations",
    status_code=status.HTTP_200_OK,
    response_model=list[ConsumerConfiguration],
    dependencies=[Depends(Permissions([Permission.GET_PROGRAM]))],
)
async def get_inventory_consumer_configurations() -> list[ConsumerConfiguration]:
    client = InventoryApiAsyncClient(settings.SCENARIOS_SERVICE_INTERNAL_URL, timeout=600)
    try:
        response = (await client.list_inventory_consumer_configurations()).json()
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": exc.response.text})
    return response


@elasticapm.async_capture_span()
@router.post(
    "/explore/orphaned-results",
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def recover_orphaned_explore_results(request: Request) -> None:
    return await explore_integration.recover_orphaned_explore_results(request=request)


class ModelingConfigurationBody(BaseModel):
    program_id: int
    consumer_id: str


@elasticapm.async_capture_span()
@programs_router.post(
    "/modeling-configuration",
    status_code=status.HTTP_201_CREATED,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.PATCH_PROGRAM]))],
)
async def submit_modeling_configuration(
    request: Request, body: ModelingConfigurationBody
) -> ProgramModelingConfigurations:
    # Check that program exists
    await get_program(request=request, program_id=body.program_id)  # Raises a 404 if not found
    return await db.create_program_modeling_configuration(
        request,
        program_id=body.program_id,
        consumer_id=body.consumer_id,
    )


class ModelingConfigurationPatchBody(BaseModel):
    consumer_id: Optional[str]


@elasticapm.async_capture_span()
@programs_router.patch(
    "/modeling-configuration",
    status_code=status.HTTP_200_OK,
    response_model=ProgramModelingConfiguration,
    dependencies=[Depends(Permissions([Permission.PATCH_PROGRAM]))],
)
async def patch_modeling_configuration(
    request: Request, body: ModelingConfigurationPatchBody
) -> ProgramModelingConfigurations:
    # Check that program exists
    program_id = request.path_params["program_id"]
    await get_program(request=request, program_id=program_id)  # Raises a 404 if not found
    return await db.patch_program_modeling_configuration(request, body)


@elasticapm.async_capture_span()
@programs_router.get(
    "/modeling-configuration",
    status_code=status.HTTP_200_OK,
    response_model=ProgramModelingConfiguration,
    dependencies=[Depends(Permissions([Permission.GET_PROGRAM]))],
)
async def get_modeling_configuration(request: Request, program_id: int) -> ProgramModelingConfiguration:
    try:
        result = await db.get_program_modeling_configuration(request, program_id=program_id)
    except HTTPException as e:
        if e.status_code == status.HTTP_404_NOT_FOUND:
            return None
        raise e
    return result


@programs_router.get(
    "/adjusted-cropping-events",
    status_code=status.HTTP_200_OK,
    response_model=None,
    dependencies=[Depends(Permissions([Permission.RUN_DNDC_FOR_PROJECT_FIELDS]))],
)
async def get_adjusted_cropping_events(request: Request, program_id: int) -> None:
    tasks.get_adjusted_cropping_events_task.delay(
        program_id=program_id,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )
