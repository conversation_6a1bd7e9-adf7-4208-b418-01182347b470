<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
    <meta charset="utf-8"/>
    <meta name="generator" content="pandoc"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
    <title>Bartlett <PERSON> Grower Agreement - Soy</title>
    <style>
        html {
            line-height: 1.5;
            font-family: Times New Roman, sans-serif;
            font-size: 12px;
            color: #000000;
            background-color: #ffffff;
        }

        body {
            margin: 0 auto;
            max-width: 44em;
            padding: 40px;
            hyphens: auto;
            overflow-wrap: break-word;
            text-rendering: optimizeLegibility;
            font-kerning: normal;
        }

        @media (max-width: 600px) {
            body {
                font-size: 0.9em;
                padding: 1em;
            }

            h1 {
                font-size: 1.8em;
            }
        }

        @media print {
            body {
                background-color: transparent;
                color: black;
                font-size: 12pt;
            }

            h2,
            h3,
            p {
                orphans: 3;
                widows: 3;
            }

            h2,
            h3,
            h4 {
                page-break-after: avoid;
            }
        }

        p {
            margin: 1em 0;
            text-align: justify;
        }

        a {
            color: #1a1a1a;
        }

        a:visited {
            color: #1a1a1a;
        }

        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 1.4em;
        }

        h5,
        h6 {
            font-size: 1em;
            font-style: italic;
        }

        h6 {
            font-weight: normal;
        }

        ol,
        ul {
            padding-left: 1.7em;
            margin-top: 1em;
        }

        li > ol,
        li > ul {
            margin-top: 0;
        }

        blockquote {
            margin: 1em 0 1em 1.7em;
            padding-left: 1em;
            border-left: 2px solid #e6e6e6;
            color: #606060;
        }

        code {
            font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
            font-size: 85%;
            margin: 0;
        }

        pre {
            margin: 1em 0;
            overflow: auto;
        }

        pre code {
            padding: 0;
            overflow: visible;
            overflow-wrap: normal;
        }

        .sourceCode {
            background-color: transparent;
            overflow: visible;
        }

        hr {
            background-color: #1a1a1a;
            border: none;
            height: 1px;
            margin: 1em 0;
        }

        table {
            margin: 1em 0;
            border-collapse: collapse;
            width: 100%;
            overflow-x: auto;
            font-variant-numeric: lining-nums tabular-nums;
        }

        table caption {
            margin-bottom: 0.75em;
        }

        tbody {
            margin-top: 0.5em;
            border-top: 1px solid #1a1a1a;
            border-bottom: 1px solid #1a1a1a;
        }

        th {
            border-top: 1px solid #1a1a1a;
            padding: 0.25em 0.5em;
            text-align: left;
        }

        td {
            padding: 0.125em 0.5em 0.25em;
        }

        header {
            margin-bottom: 4em;
            text-align: center;
        }

        #TOC li {
            list-style: none;
        }

        #TOC ul {
            padding-left: 1.3em;
        }

        #TOC > ul {
            padding-left: 0;
        }

        #TOC a:not(:hover) {
            text-decoration: none;
        }

        code {
            white-space: pre-wrap;
        }

        span.smallcaps {
            font-variant: small-caps;
        }

        span.underline {
            text-decoration: underline;
        }

        div.column {
            display: inline-block;
            vertical-align: top;
            width: 50%;
        }

        div.hanging-indent {
            margin-left: 1.5em;
            text-indent: -1.5em;
        }

        ul.task-list {
            list-style: none;
        }

        .display.math {
            display: block;
            text-align: center;
            margin: 0.5rem auto;
        }

        li::marker {
            width: 2.4cm;
            text-align: left !important;
        }

        .header-container {
            display: flex;
            align-items: flex-start;
        }

        .header-text {
            font-weight: bold;
            font-style: italic;
            margin-left: 20px;
            font-size: 15.9;
        }
         .container {
            justify-content: flex-end;
        }
        .content {
            width: 50%;
            font-size: 12px;
        }

    </style>
</head>
<body>
    {% set ns1 = namespace(producer_legal_name="") %}
    {% for key, value in custom_inputs.items() %}
        {% if key.startswith("Legal Entity Name") %}
            {% set ns1.producer_legal_name = key %}
        {% endif %}
    {% endfor %}
    {% set legal_name = custom_inputs[ns1.producer_legal_name] | default("") %}
    {% if legal_name == "" %}
        {% set legal_name = user_fname + " " + user_lname %}
    {% endif %}
    <div class="header-container">
        <img src="data:image/png;base64,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">
        <span class="header-text">SHARP-Soy Agreement</span>
    </div>

    <p style="font-size: 12px;"><strong>Bartlett Grain Company, LLC</strong> (“Bartlett”) sells agricultural commodities to agricultural commodity users who demand assurances that agricultural commodities are produced by way of the employment of farming practices that protect the soil and the environment for future generations. To that end Bartlett has established a Soil Health and Resilience Program (“SHARP”) to encourage producers to use such practices when producing agricultural commodities.</p>

    <p style="font-size: 12px;"><strong>{{ legal_name }}</strong> (“Producer”) is a Bartlett customer that produces agricultural commodities and uses—or is willing to use—SHARP practices to produce agricultural commodities on some or all of the land upon which it produces agricultural commodities.</p>

    <p style="font-size: 12px;">Bartlett and Producer desire to enter an arrangement in which:</p>

    <p style="font-size: 12px;">Producer agrees to produce a Crop by way of the employment of SHARP Practices on Enrolled Acres;</p>

    <p style="font-size: 12px;">and</p>

    <p style="font-size: 12px;">Producer transfers and Bartlett purchases the right to market any environmental or sustainability claims arising from Producer's use of SHARP Practices, which Bartlett will transfer to purchasers of the Crop who wish to make environmental or sustainability claims about their purchases</p>

    <p style="font-size: 12px;">under the following terms and conditions:</p>

    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">1) Definitions:</p>

  <ol type="a" style="font-size: 12px;">
    <li>
        <p><strong>Agreement:</strong> The “Agreement” is this SHARP Agreement.</p>
    </li>
    <li>
        <p><strong>Crop:</strong> The agricultural commodity reported on the Platform and harvested from the Enrolled Acres during the Reporting Year.</p>
    </li>
    <li>
        <p><strong>Effective Date:</strong> {{ current_date }}</p>
    </li>
    <li>
        <p><strong>Enrolled Acre:</strong> Any of the acres a Producer has enrolled in the Program through the SHARP Platform.</p>
    </li>
    <li>
        <p><strong>Final Reporting Date:</strong> October 31 of the Reporting Year. If environmental conditions delay harvest, Bartlett may elect to delay the Final Reporting Date for every SHARP producer.</p>
    </li>
    <li>
        <p><strong>Party:</strong> Bartlett and Producer are each a “Party” and, collectively, are the “Parties”.</p>
    </li>
    <li>
        <p><strong>Platform:</strong> An Internet web application, created and operated by Regrow within which Producer creates an account, logs in and submits data from which Regrow estimates and calculates the environmental impact that result from Producer's practices.</p>
    </li>
    <li>
        <p><strong>Practices:</strong> SHARP Practices are farming practices described on the Platform that Producer elects to use to grow Crops on the Enrolled Acres during the Term.</p>
    </li>
    <li>
        <p><strong>Program:</strong> The “Program” refers to the relationship and responsibilities of the Parties established by and set forth in this Agreement.</p>
    </li>
    <li>
        <p><strong>Rate:</strong> Bartlett will pay Producer for committing to and executing certain SHARP Practices on the Enrolled Acres at the rates set forth below.</p>
        <table style="margin-top: 1em; margin-bottom: 1em;">
            <thead>
                <tr>
                    <th style="text-align: center;">Practice</th>
                    <th style="text-align: center;">Rate</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align: center;">Cover Crops</td>
                    <td style="text-align: center;">$20.00 per acre</td>
                </tr>
                <tr>
                    <td style="text-align: center;">No Till</td>
                    <td style="text-align: center;">$5.00 per acre</td>
                </tr>
            </tbody>
        </table>
    </li>
    <li>
        <p><strong>Regrow:</strong> Regrow Agriculture Inc., developer of the Platform.</p>
    </li>
    <li>
        <p><strong>Reporting Year:</strong> The Reporting Year is 2026.</p>
    </li>
</ol>



    <p style="width: 100%; display: block; font-size: 12px;"><strong>2) Term:</strong> This Agreement will commence on the Effective Date and will terminate on December 31. 2026 (the “Term”).</p>

    <p style="width: 100%; display: block; font-size: 12px;"><strong>3) Payment:</strong> Within thirty (30) calendar days of the Final Reporting Date, Bartlett will pay to Producer an amount equal to the number of Enrolled Acres for which a SHARP Practice has been implemented for the Reporting Year multiplied by the per-acre Rate for such Practice as set forth above. For the avoidance of doubt, the Rates are cumulative to the extent Producer implements more than one of the SHARP Practices for the same Enrolled Acres.</p>

    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">4) Producer Representations:</p>

    <ol type="a" style="font-size: 12px;">
        <li>
            <p>Producer represents and warrants that it has the power and authority (in terms of corporate authority, possession of property, compliance with applicable law, previous contractual arrangements with third parties, or otherwise) to:</p>
            <ol type="i">
                <li><p>implement the SHARP Practices on the Enrolled Acres under this Agreement; and</p></li>
                <li><p>sell and convey the exclusive right to market claims arising from implementation of the SHARP Practices to Bartlett.</p></li>
            </ol>
            <p>Producer shall take reasonable action to maintain this power and authority over the term of the Agreement.</p>
        </li>
        <li>
            <p>Producer warrants that it will not participate in any other program that (i) authorizes anyone but Bartlett to market claims related to the Enrolled Acres or Producer's implementation of the SHARP Practices or (ii) otherwise pays Producer to implement any of the SHARP Practices on the Enrolled Acres during the Term.</p>
        </li>
    </ol>

    <p style="width: 100%; display: block; font-size: 12px;"><strong>5) Producer Attestation:</strong> The Producer attests that all data provided and to be provided in connection with the Program is accurate to the best of the Producer’s knowledge and belief.</p>

    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">6) Producer Obligations:</p>

    <ol type="a" style="font-size: 12px;">
        <li><p>Producer agrees to grow the Crop on the Enrolled Acres using the SHARP Practices.</p></li>
        <li><p>Producer is responsible for keeping accurate paper or electronic records of:</p>
            <ol type="i">
                <li><p>the planned and actual SHARP Practices; and</p></li>
                <li><p>farming operational data.</p></li>
            </ol>
        </li>
        <li><p>Producer will supply farming operational data as required by the Platform.</p></li>
        <li><p>Producer will cooperate with Verification Activities.</p></li>
        <li><p>Producer is solely responsible for all taxes payable in connection with Bartlett's payment obligations hereunder.</p></li>
        <li><p>Producer may, but need not, sell Crops to Bartlett.</p></li>
        <li><p>Producer must provide Bartlett a properly completed IRS Form W-9.</p></li>
    </ol>

    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">7) Bartlett Obligations: </p>

    <ol type="a" style="font-size: 12px;">
        <li><p>Bartlett will conduct Verification Activities.</p></li>
        <li><p>Bartlett will collect data about the SHARP Practices employed on the Enrolled Acres.</p></li>
        <li><p>Bartlett will pay Producer the Rate in accordance with the terms of this Agreement.</p></li>
    </ol>

    <p style="font-size: 12px;">Bartlett may perform its obligations directly or arrange to have them done through a third party.</p>

    <p style="width: 100%; display: block; font-size: 12px;"><strong>8) Verification Activities: </strong> Bartlett's ability to market claims related to the SHARP Practices may require verification of Producer's implementation of SHARP Practices and records pertaining to operations and production and gathering relevant environmental data (collectively, "Verification Activities"). Bartlett will take reasonable measures to schedule verification activities with the Producer.</p>


    <ol type="1" style="font-size: 12px;">
        <li><p><strong>Site Visits:</strong> Bartlett may visit Enrolled Acres during the Term to ensure compliance with the terms of this Agreement. Bartlett will inform the Producer when site visits will occur.</p></li>
        <li><p><strong>Monitoring:</strong> Bartlett may—at its expense—collect soil samples from the Enrolled Acres.</p></li>
        <li><p><strong>Record Production:</strong> Bartlett may ask for records to demonstrate Producer's compliance with this Agreement. Producer owns all data contained in the records but grants a limited license to Bartlett to acquire and use records and other data to determine Producer's compliance with this Agreement, to perform Bartlett's obligations under this Agreement, and to create and to sell the claims related to Producer's implementation of the SHARP Practices.</p></li>
    </ol>

    <p style="width: 100%; display: block; font-size: 12px;"><strong>9) Non-Compliance, Termination and Repayment Obligation: </strong>If Producer fails to comply with the terms of this Agreement, Bartlett may terminate the Agreement, cancel future payments to Producer without further obligation to Producer and recover amounts paid to Producer pursuant to this Agreement. Repayment must be made to Bartlett within thirty (30) days of the Bartlett repayment request.</p>


    <p style="width: 100%; display: block; font-size: 12px;"><strong>10) Assignment of Environmental Claims:</strong>Producer hereby assigns, sells, and conveys to Bartlett, all rights Producer may have, or which may arise, to any environmental claims that may be produced from the implementation of any of the SHARP Practices on the Enrolled Acres.</p>

    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">11) Force Majeure:</p>

    <ol type="a" style="font-size: 12px;">
        <li><p>Neither Party shall be liable for any failure of or delay in the performance of this Agreement for the period that such failure or delay is due to Acts of God or public enemy, war, riot, civil disturbance, fire, insurrection, government embargo or moratoria, acts of terrorism, strikes or labor disputes, or other occurrence that is not within the reasonable control of the Party claiming Force Majeure and which could not have been avoided by the exercise of reasonable diligence or use of foresight. It is understood that lack of financial resources is not deemed a cause beyond a Party's control. Each Party shall promptly notify the other of the occurrence of any such cause and shall carry out its obligations under this Agreement as promptly as practicable after such cause terminates.</p></li>
        <li><p>In the event of a Force Majeure, the Party whose performance is affected will notify the other Party of the event in writing and will take all reasonable steps to resume performance as soon after the Force Majeure as possible. Time for the affected Party's performance shall be extended on a day for day basis equivalent to the period of Force Majeure, up to a maximum of sixty (60) days. However, the existence or continuation of any such cause shall not extend the Term of this Agreement more than sixty (60) days.</p></li>
    </ol>

    <p style="width: 100%; display: block; font-size: 12px;"><strong>12) Dispute Resolution: </strong>The Parties agree that any dispute with respect to this Agreement shall be settled by binding arbitration administered by the National Grain and Feed Association ("NGFA") under its Arbitration Rules.</p>


    <p style="width: 100%; display: block; font-size: 12px;"><strong>13) Amendment:</strong>This Agreement can only be amended or modified by a subsequent written
agreement executed by duly authorized representatives of each Party.</p>


    <p style="width: 100%; display: block; font-weight: bold; font-size: 12px;">14) JURY TRIAL WAIVER: EACH OF THE PARTIES AFTER HAVING HAD AN
OPPORTUNITY TO CONSULT WITH COUNSEL, HEREBY KNOWINGLY AND
UNCONDITIONALLY WAIVES ANY RIGHT TO A JURY TRIAL WITH RESPECT TO AND
IN ANY ACTION, PROCEEDING, CLAIM, COUNTERCLAIM, DEMAND, DISPUTE OR
OTHER MATTER ARISING OUT OF THIS AGREEMENT.</p>

    <table width="100%" style="page-break-inside: avoid; font-size: 12px;">
        <tbody style="border-top: 0px; border-bottom: 0px;">
            <tr>
                <td width="50%"></td>
                <td width="50%"><p style="font-size: 12px;">I acknowledge that I have thoroughly reviewed and fully understand the terms and conditions outlined in this agreement and that I have the legal authority to sign on behalf of the Producer.</p></td>
            </tr>
            <tr>
                <td width="50%">
                    BARTLETT
                    <br>
                    <br>
                    <br>
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
                <td width="50%">
                    PRODUCER
                    <br>
                    <span style="display: inline-block; color: white;">/sn1/</span>
                    <br>
                    <br>
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
            </tr>
            <tr>
                <td width="50%">
                    Bartlett Grain Company, LLC
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
                <td width="50%">
                    {{ user_fname }} {{ user_lname }}
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
            </tr>
            <tr>
                <td width="50%">
                    TITLE
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
                <td width="50%">
                    TITLE
                    <br>
                    <br>
                    <span style="display: inline-block; width: 80%; border-bottom: 1px solid black;"></span>
                </td>
            </tr>
            <tr>
                <td width="50%">
                    DATE
                </td>
                <td width="50%">
                    DATE
                </td>
            </tr>
        </tbody>
    </table>


    <br />
    <br />
    <br />
    <h2 style="text-align: center;">Attachment A: Enrolled Fields and Practices</h2>

    <!-- Table of regenerative practices -->
    <table width="100%" style="table-layout: fixed;">
        <thead style="vertical-align: middle;">
            <tr class="header">
                <th scope="col" style="padding-left:10px;text-align:left;">Farm name
                </th>
                <th scope="col" style="padding-left:10px;text-align:left;">Field name
                </th>
                <th scope="col" style="padding-left:10px;text-align:left;">Area (acre)
                </th>
                <th scope="col" style="padding-left:10px;text-align:left;">Practice
                </th>
            </tr>
        </thead>
        <tbody>
            {% for row in fields %}
            <tr>
                <td style="padding-top:10px;">{{ row.note["farm_name"]}}</td>
                <td style="padding-top:10px;">{{ row.note["field_name"]}}</td>
                <td style="padding-top:10px;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                <td style="padding-top:10px;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>
