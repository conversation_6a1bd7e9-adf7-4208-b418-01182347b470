import zipfile
from datetime import date, datetime
from io import By<PERSON><PERSON>
from math import isclose

from defaults.defaults import defaults_retriever
from entity_events.units import AreaUnit
from fields.enums import EntityDataType, FieldDataState
from pint_configuration import ureg
from programs.enums import Protocols
from reported_outcomes.db import (
    get_crop_outcomes_for_program,
    get_program_outcomes_for_program,
)
from reported_outcomes.enums import OutcomeApprovalStatusType
from reported_outcomes.outcome_construction_methods import (
    _build_crop_level_field_totals,
    _build_csv_content_for_crop_inventory_outcomes,
    _build_csv_content_for_field_inventory_outcomes,
    _build_csv_content_for_field_outcomes,
    _calculate_yield_rate,
    _clean_value,
    _format_date_as_string,
    _get_crop_inventory_outcome_headers,
    _get_crop_outcome_headers,
    _get_field_inventory_outcome_headers,
    _get_field_outcome_headers,
    _get_program_outcome_headers,
    _inventory_result_rounding,
    _map_crop_outcome,
    _map_program_outcome,
    build_raw_combined_zip_for_program,
)
from reported_outcomes.read_me_definitions import (
    _get_read_me_headers,
    get_crop_outcomes_read_me,
    get_field_outcomes_read_me,
    get_program_outcomes_read_me,
)
from reported_outcomes.tests.support_functions import (
    crop_outcomes_1,
    crop_outcomes_3,
    field_outcomes_1,
    field_outcomes_2,
    program_outcomes_1,
)
from scenarios_service.enums import MeasureProtocolResponseTypes, ScenariosServiceApi


async def setup_test_scope_3_outcomes(app_request, mdl) -> int:
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_md5 = "123"
    test_field_size = 100
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )

    test_field_outcomes = field_outcomes_2(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    test_crop_outcomes = crop_outcomes_1(program.id)
    test_crop_outcomes["created_at"] = datetime.now()
    test_crop_outcomes["task_id"] = task.id
    await mdl.CropLevelOutcomes(**test_crop_outcomes)

    return program.id, task.id


async def setup_test_scope_1_outcomes(app_request, mdl) -> int:
    program = await mdl.Programs(protocol=Protocols.VERRA)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_md5 = "123"
    test_field_size = 100
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )

    test_field_outcomes = field_outcomes_1(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    test_program_outcomes = program_outcomes_1(program.id)
    test_program_outcomes["created_at"] = datetime.now()
    test_program_outcomes["task_id"] = task.id
    await mdl.ProgramLevelOutcomes(**test_program_outcomes)

    return program.id, task.id


async def setup_test_inventory_outcomes(app_request, mdl) -> int:
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_md5 = "123"
    test_field_size = 158.31056014403754
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.inventory_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )

    test_field_inventory_outcomes = {
        "program_id": program.id,
        "project_id": project.id,
        "field_id": field.id,
        "crop_name": "wheat_winter",
        "mapped_acres": 158.31056014403754,
        "total_yield": 6332.422405761501,
        "net_emissions_percentage": 100.0,
        "task_id": task.id,
    }
    await mdl.FieldLevelInventoryOutcomes(**test_field_inventory_outcomes)

    test_crop_inventory_outcomes = {
        "program_id": program.id,
        "crop_type": "wheat_winter",
        "number_of_fields": 1,
        "mapped_acres": 158.31056014403754,
        "total_yield": 6332.422405761501,
        "total_emissions": 19.149933696238563,
        "total_emissions_per_bushel": 0.0030241087010896743,
        "direct_n2o_emissions": 13.369911577457772,
        "indirect_n2o_emissions": 5.780022118780792,
        "soil_ch4_emissions": 0.1,  # this one is made up for testing
        "soc": -43.69954593511091,
        "soc_per_bushel": -0.0069009208696108534,
        "task_id": task.id,
    }
    await mdl.CropLevelInventoryOutcomes(**test_crop_inventory_outcomes)

    return program.id, task.id


async def test_combined_scope_3_outcome_zip_construction(app_request, mdl):
    program_id, task_id = await setup_test_scope_3_outcomes(app_request, mdl)
    result = await build_raw_combined_zip_for_program(app_request, program_id, task_id)
    assert isinstance(result, bytes) is True

    in_memory = BytesIO(result)

    crop_outcomes_filename = f"crop_outcomes_for_program_{program_id}.csv"
    field_outcomes_filename = f"field_outcomes_for_program_{program_id}.csv"
    readme_filename = f"readme_for_program_{program_id}.csv"

    with zipfile.ZipFile(in_memory, "r") as zip_file:
        assert set(zip_file.namelist()) == {crop_outcomes_filename, field_outcomes_filename, readme_filename}


async def test_combined_scope_1_outcome_zip_construction(app_request, mdl):
    program_id, task_id = await setup_test_scope_1_outcomes(app_request, mdl)
    result = await build_raw_combined_zip_for_program(app_request, program_id, task_id)
    assert isinstance(result, bytes) is True

    in_memory = BytesIO(result)

    program_outcomes_filename = f"program_outcomes_for_program_{program_id}.csv"
    field_outcomes_filename = f"field_outcomes_for_program_{program_id}.csv"
    readme_filename = f"readme_for_program_{program_id}.csv"

    with zipfile.ZipFile(in_memory, "r") as zip_file:
        assert set(zip_file.namelist()) == {field_outcomes_filename, program_outcomes_filename, readme_filename}


async def test_scope_3_field_outcome_csv_construction(app_request, mdl):
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_baseline_practices = [["Conventional Till", "No Cover Crop"]]
    test_measured_practices = [["Conventional Till, Cover Crops"]]
    test_enrollment_year = 2021
    test_md5 = "123"
    test_field_size = 100.0
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        measurement_eligibility=1,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2021)
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.baseline,
        data_type=EntityDataType.entity_practices,
        facts=test_baseline_practices,
    )
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.measured,
        data_type=EntityDataType.entity_practices,
        facts=test_measured_practices,
    )

    test_field_outcomes = field_outcomes_1(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    outcomes = await _build_csv_content_for_field_outcomes(app_request, program.id, task.id)
    assert len(outcomes) == 1
    outcome = outcomes[0]
    headers = _get_field_outcome_headers(True, MeasureProtocolResponseTypes.scope_3)
    for header in headers:
        assert outcome[header] is not None
    assert outcome["Project ID"] == project.id
    assert outcome["Field ID"] == field.id
    assert outcome["Farm Name"] == test_farm_name
    assert outcome["Field Name"] == test_field_name
    assert isclose(outcome["Mapped Area (acres)"], test_field_size, rel_tol=0.0001)
    assert outcome["Email"] == test_email
    assert outcome["First Name"] == test_first_name
    assert outcome["Last Name"] == test_last_name
    assert outcome["Co-op"] == test_coop
    assert outcome["Region"] == test_region
    assert outcome["Country"] == test_country
    assert outcome["Credited Crop"] == "wheat_winter"
    assert isclose(outcome["Yield (bushels/acre*)"], test_field_outcomes["crop_yield"])
    assert outcome["Baseline Practice"] == ", ".join(test_baseline_practices[0])
    assert outcome["Completed Practice Change"] == ", ".join(test_measured_practices[0])
    assert outcome["Eligible"] == "yes"
    assert outcome["Enrollment Year"] == test_enrollment_year


async def test_scope_1_field_outcome_csv_construction(app_request, mdl):
    program = await mdl.Programs(protocol=Protocols.VERRA)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_baseline_practices = [["Conventional Till", "No Cover Crop"]]
    test_measured_practices = [["Conventional Till", "Cover Crops"]]
    test_enrollment_year = 2021
    test_md5 = "123"
    test_field_size = 100.0
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        measurement_eligibility=1,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2021)
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.baseline,
        data_type=EntityDataType.entity_practices,
        facts=test_baseline_practices,
    )
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.measured,
        data_type=EntityDataType.entity_practices,
        facts=test_measured_practices,
    )

    test_field_outcomes = field_outcomes_1(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    outcomes = await _build_csv_content_for_field_outcomes(app_request, program.id, task.id)
    assert len(outcomes) == 1
    outcome = outcomes[0]
    headers = _get_field_outcome_headers(True, MeasureProtocolResponseTypes.scope_1)
    for header in headers:
        assert outcome[header] is not None
    assert outcome["Project ID"] == project.id
    assert outcome["Field ID"] == field.id
    assert outcome["Farm Name"] == test_farm_name
    assert outcome["Field Name"] == test_field_name
    assert isclose(outcome["Mapped Area (acres)"], test_field_size, rel_tol=0.0001)
    assert outcome["Email"] == test_email
    assert outcome["First Name"] == test_first_name
    assert outcome["Last Name"] == test_last_name
    assert outcome["Co-op"] == test_coop
    assert outcome["Region"] == test_region
    assert outcome["Country"] == test_country
    assert "Credited Crop" not in outcome
    assert "Yield (bushels/acre*)" not in outcome
    assert "Baseline Practice" not in outcome
    assert "Completed Practice Change" not in outcome
    assert outcome["Eligible"] == "yes"
    assert outcome["Enrollment Year"] == test_enrollment_year


async def test_map_crop_outcomes(mdl, app_request):
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3)
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_crop_outcomes = crop_outcomes_3(program.id)
    test_crop_outcomes["created_at"] = datetime.now()
    test_crop_outcomes["task_id"] = task.id
    await mdl.CropLevelOutcomes(**test_crop_outcomes)
    crop_outcomes = await get_crop_outcomes_for_program(app_request, program.id, task.id)
    crop_level_field_totals = {
        "alfalfa": {
            "Number Of Fields": 18,
            "Mapped Area (acres)": 500.1392915263749,
            "Total Yield (bushels*)": 103863.7833507343,
        }
    }
    outcome = _map_crop_outcome(crop_outcomes[0], crop_level_field_totals)

    assert isclose(
        outcome["Total Credits (mtCO2e)"],
        outcome["Total Non Reversible Emissions Reductions (mtCO2e)"]
        + outcome["Total Reversible Emissions Reductions (mtCO2e)"],
        rel_tol=0.0001,
    )
    assert isclose(
        outcome["Total Credits (mtCO2e)"] / outcome["Total Yield (bushels*)"],
        outcome["Total Credits Per Yield (mtCO2e/bushel)"],
        rel_tol=0.0001,
    )
    assert isclose(outcome["Total Credits (mtCO2e)"], 205.17437200000006, rel_tol=0.0001)


async def test_field_outcome_csv_construction_no_coops(app_request, mdl):
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_baseline_practices = [["Conventional Till", "No Cover Crop"]]
    test_measured_practices = [["Conventional Till", "Cover Crops"]]
    test_md5 = "123"
    test_field_size = 100.0
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        measurement_eligibility=1,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2021)
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.baseline,
        data_type=EntityDataType.entity_practices,
        facts=test_baseline_practices,
    )
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.measured,
        data_type=EntityDataType.entity_practices,
        facts=test_measured_practices,
    )

    test_field_outcomes = field_outcomes_1(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    outcomes = await _build_csv_content_for_field_outcomes(app_request, program.id, task.id)
    assert len(outcomes) == 1
    outcome = outcomes[0]

    headers = _get_field_outcome_headers(False, MeasureProtocolResponseTypes.scope_3)
    for header in headers:
        assert outcome[header] is not None

    assert "Co-op" not in outcome


async def test_crop_outcome_csv_construction(app_request, mdl):
    program_id, task_id = await setup_test_scope_3_outcomes(app_request, mdl)
    field_outcomes = await _build_csv_content_for_field_outcomes(app_request, program_id, task_id)
    crop_level_field_totals = _build_crop_level_field_totals(field_outcomes)

    outcomes = await get_crop_outcomes_for_program(app_request, program_id, task_id)
    assert len(outcomes) == 1
    mapped_outcome = _map_crop_outcome(outcomes[0], crop_level_field_totals)
    headers = _get_crop_outcome_headers()
    for header in headers:
        assert mapped_outcome[header] is not None


async def test_field_inventory_outcome_cvs_construction(app_request, mdl):
    program_id, task_id = await setup_test_inventory_outcomes(app_request, mdl)
    field_outcome_content = await _build_csv_content_for_field_inventory_outcomes(app_request, program_id, task_id)
    assert len(field_outcome_content) == 1
    outcome = field_outcome_content[0]
    headers = _get_field_inventory_outcome_headers()
    for header in headers:
        assert outcome[header] is not None
    for outcome_label in outcome.keys():
        assert outcome_label in headers
    assert isclose(outcome["Mapped Area (acres)"], 158.3)
    assert isclose(outcome["Yield (bushels/acre*)"], 40.0)


async def test_crop_inventory_outcome_cvs_construction(app_request, mdl):
    program_id, task_id = await setup_test_inventory_outcomes(app_request, mdl)
    field_outcome_content = await _build_csv_content_for_field_inventory_outcomes(app_request, program_id, task_id)
    crop_outcome_content = await _build_csv_content_for_crop_inventory_outcomes(
        app_request, program_id, task_id, field_outcome_content
    )
    assert len(crop_outcome_content) == 1
    outcome = crop_outcome_content[0]
    headers = _get_crop_inventory_outcome_headers()
    for header in headers:
        assert outcome[header] is not None
    for outcome_label in outcome.keys():
        assert outcome_label in headers
    assert isclose(outcome["Mapped Area (acres)"], 158.3)
    assert isclose(outcome["Land Management Removals (mtCO2e/Yield Unit)"], -0.0069)
    # 13.369911577457772/6332.422405761501 = 0.002111342345907511
    assert isclose(outcome["Direct N2O Emissions (mtCO2e/Yield Unit)"], 0.00211)

    # here's how we got to the number below:
    # direct n2o per bushel
    # 13.369911577457772/6332.422405761501 = 0.002111342345907511 = 0.00211
    # indirect_n2o_emissions
    # 5.780022118780792/6332.422405761501 = 0.0009127663551821634 =  0.000913
    # soil ch4
    # 0.1/6332.422405761501 = 1.579174502146538e-05 = 0.0000158
    # soc
    # -43.69954593511091/6332.422405761501 = -0.0069009208696108534 = -0.00690
    # 0.00211+0.000913+0.0000158-0.00690 = -0.0038612000000000004 = -0.00386
    assert isclose(outcome["Net Emissions (mtCO2e/Yield Unit)"], -0.00386)


def test_inventory_result_rounding():
    assert isclose(11.3, _inventory_result_rounding(11.29))
    assert isclose(-11.3, _inventory_result_rounding(-11.29))
    assert isclose(1.3, _inventory_result_rounding(1.29))
    assert isclose(-1.3, _inventory_result_rounding(-1.29))
    assert isclose(1.0, _inventory_result_rounding(1.0))
    assert isclose(-1.0, _inventory_result_rounding(-1.0))
    assert isclose(0.895, _inventory_result_rounding(0.8946))
    assert isclose(-0.895, _inventory_result_rounding(-0.8946))
    assert isclose(0.00895, _inventory_result_rounding(0.008946))
    assert isclose(-0.00895, _inventory_result_rounding(-0.008946))
    assert isclose(0.0, _inventory_result_rounding(0.0))


def test_format_date_as_string() -> None:
    assert "2024-10-23" == _format_date_as_string(date(2024, 10, 23))
    assert "2024-03-04" == _format_date_as_string(date(2024, 3, 4))


def test_clean_value() -> None:
    assert _clean_value(None) == "NA"
    assert _clean_value("") == "NA"
    assert _clean_value("#N/A") == "NA"
    assert _clean_value("null") == "NA"
    assert _clean_value("j") == "j"
    assert _clean_value(1) == 1


async def test_program_outcome_csv_construction(app_request, mdl) -> None:
    program = await mdl.Programs()
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_outcomes = program_outcomes_1(program.id)
    test_outcomes["created_at"] = datetime.now()
    test_outcomes["task_id"] = task.id
    await mdl.ProgramLevelOutcomes(**test_outcomes)
    outcomes = await get_program_outcomes_for_program(app_request, program.id, task.id)
    assert len(outcomes) == 1
    mapped_outcome = _map_program_outcome(outcomes[0])
    headers = _get_program_outcome_headers()
    for header in headers:
        assert header in mapped_outcome.keys()


async def test_yield_fill_in(app_request, mdl) -> int:
    await defaults_retriever.fetch_all_crops(use_file=True)
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3)
    project = await mdl.Projects(program_id=program.id)
    test_farm_name = "test_farm"
    test_field_name = "test_field_name"
    test_email = "<EMAIL>"
    test_first_name = "Bosco"
    test_last_name = "LaPasta"
    test_region = "test_region"
    test_country = "test_country"
    test_coop = "LaPasta Co-op"
    test_md5 = "123"
    test_field_size = 100
    mrv_field_size = (
        ureg.Quantity(test_field_size, AreaUnit.ACRE.value.lower()).to(AreaUnit.HECTARE.value.lower()).magnitude
    )
    test_yield_rate_bu = 159.0
    test_yield_rate_lb = test_yield_rate_bu * 56.0
    test_yield_unit = "lb/ac"
    task = await mdl.DndcTasks(program_id=program.id, scenarios_service_api=ScenariosServiceApi.measure_api)
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    user = await mdl.Users(name=test_first_name, surname=test_last_name, email=test_email)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    co_op_key = await mdl.ProgramCustomRegInputs(input_key="Restriction", program_id=program.id)
    await mdl.ProjectValues(key=co_op_key.id, project_id=project.id, value=test_coop)

    region = await mdl.CoreRegions(name=test_region, country_name=test_country)
    kml_file = await mdl.KMLFiles(md5=test_md5, region_id=region.id)
    kml_group = await mdl.KMLGroups(name=test_field_name, kml_id=kml_file.id)
    group = await mdl.Groups(name=test_farm_name)
    field = await mdl.Fields(
        parent_project_id=project.id,
        deleted_at=None,
        area=mrv_field_size,
        md5=test_md5,
        fs_field_id=kml_group.id,
        farm_id=group.id,
    )
    await mdl.FieldFacts(
        field_id=field.id,
        state=FieldDataState.measured,
        data_type=EntityDataType.entity_yield,
        facts=[[f"{test_yield_rate_lb} {test_yield_unit}"]],
    )

    test_field_outcomes = field_outcomes_2(program.id, project.id, field.id)
    test_field_outcomes["created_at"] = datetime.now()
    test_field_outcomes["task_id"] = task.id
    await mdl.FieldLevelOutcomes(**test_field_outcomes)

    outcomes = await _build_csv_content_for_field_outcomes(app_request, program.id, task.id)
    assert len(outcomes) == 1
    outcome = outcomes[0]
    assert outcome["Yield (bushels/acre*)"] != "NA"
    assert isclose(outcome["Yield (bushels/acre*)"], test_yield_rate_bu)


async def test_calculate_yield_rate():
    await defaults_retriever.fetch_all_crops(use_file=True)
    assert isclose(await _calculate_yield_rate(140.0, "bu/ac", "corn"), 140.0)
    assert isclose(await _calculate_yield_rate(56.0 * 100, "lb/ac", "corn"), 100.0)
    assert isclose(await _calculate_yield_rate(1, "T/ac", "corn"), 2000.0 / 56.0)
    assert (await _calculate_yield_rate(140.0, "lb/ac", "not a crop so no yield here")) is None


def test_get_crop_outcomes_read_me():
    read_me_content = get_crop_outcomes_read_me()
    headers = _get_read_me_headers()
    expected_first_row = {
        "File": "Crop Outcomes",
        "Parameter": "Crop",
        "Unit": "",
        "Description": "The common name of the crop for which carbon outcomes are quantified.",
    }
    assert len(read_me_content[0]) == len(expected_first_row)
    for head in headers:
        assert read_me_content[0][head] == expected_first_row[head]
    items_tested = 0
    for content_item in read_me_content:
        if content_item["File"] == "Crop Outcomes" and content_item["Parameter"] == "Total Yield":
            assert (
                content_item["Description"]
                == "The total reported yield for a crop. \n* Yield units are reported in bushels for all crops, except for pumpkins, which are reported in crates (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report."
            )
            items_tested = items_tested + 1
        if content_item["File"] == "Crop Outcomes" and content_item["Parameter"] == "Average Yield":
            assert (
                content_item["Description"]
                == "The total reported yield for a crop divided by the total area harvested\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report."
            )
            items_tested = items_tested + 1
    assert items_tested == 2


def test_get_field_outcomes_read_me():
    read_me_content = get_field_outcomes_read_me(True, MeasureProtocolResponseTypes.scope_3)
    headers = _get_read_me_headers()
    expected_first_row = {
        "File": "Field Outcomes",
        "Parameter": "Field ID",
        "Unit": "",
        "Description": "A unique record representing a field enrollment in a program",
    }
    assert len(read_me_content[0]) == len(expected_first_row)
    for head in headers:
        assert read_me_content[0][head] == expected_first_row[head]
    items_tested = 0
    for content_item in read_me_content:
        if content_item["File"] == "Field Outcomes" and content_item["Parameter"] == "Yield":
            assert (
                content_item["Description"]
                == "Harvested yield of the credited crop. \n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report."
            )
            items_tested = items_tested + 1
    assert items_tested == 1


def test_get_programs_outcomes_read_me():
    read_me_content = get_program_outcomes_read_me()
    headers = _get_read_me_headers()
    expected_first_row = {
        "File": "Program Outcomes",
        "Parameter": "Number Of Fields",
        "Unit": "",
        "Description": "The total number of fields quantified for this program.",
    }
    assert len(read_me_content[0]) == len(expected_first_row)
    for head in headers:
        assert read_me_content[0][head] == expected_first_row[head]

    parameters = [row["Parameter"] for row in read_me_content]
    assert parameters == [
        "Number Of Fields",
        "Mapped Area",
        "Reporting Period Start Date (earliest)",
        "Reporting Period End Date (latest)",
        "Total Credits",
        "Total Reversible Credit",
        "Reversible Credit Mean",
        "Reversible Credit Standard Deviation",
        "Total Reversible Emissions Reductions",
        "Total Non Reversible Credit",
        "Non Reversible Credit Mean",
        "Non Reversible Credit Standard Deviation",
        "Total Non Reversible Emissions Reductions",
    ]
