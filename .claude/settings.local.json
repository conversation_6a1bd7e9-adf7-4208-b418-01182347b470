{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(poetry run pytest:*)", "<PERSON><PERSON>(comm:*)", "WebFetch(domain:simplelocalize.io)", "<PERSON><PERSON>(poetry run alembic:*)", "<PERSON><PERSON>(python:*)", "Bash(make test_no_cov:*)", "Bash(msgfmt --version)", "Bash(msgfmt:*)", "Bash(LANG=C msgfmt /Users/<USER>/IdeaProjects/mrv-service/app/locale/fr/LC_MESSAGES/messages.po -o /Users/<USER>/IdeaProjects/mrv-service/app/locale/fr/LC_MESSAGES/messages.mo)", "Bash(echo \"Exit code: $?\")", "<PERSON><PERSON>(poetry run:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__browser-mcp__browser_navigate", "mcp__browser-mcp__browser_screenshot", "mcp__browser-mcp__browser_press_key", "mcp__browser-mcp__browser_click", "Bash(osascript:*)", "Bash(/Users/<USER>/focus-chrome.sh)", "mcp__playwright__browser_navigate", "mcp__sequential-thinking__sequentialthinking", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_press_key", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_close", "mcp__zen__challenge", "mcp__browsermcp__browser_navigate", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_press_key", "mcp__zen__analyze", "mcp__zen__codereview", "<PERSON><PERSON>(poetry lock:*)", "mcp__zen__chat", "<PERSON><PERSON>(mkdir:*)", "mcp__atlassian__getAccessibleAtlassianResources", "mcp__atlassian__getConfluencePage", "mcp__atlassian__getJiraIssue", "<PERSON><PERSON>(trash:*)", "mcp__github__get_pull_request", "mcp__github__get_pull_request_files", "mcp__github__search_issues", "mcp__slack__conversations_history", "mcp__slack__conversations_replies", "mcp__slack__channels_list", "mcp__slack__conversations_search_messages", "Bash(ast-grep:*)", "mcp__atlassian__searchJiraIssuesUsingJql", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(sed:*)", "Bash(fd:*)", "Bash(git log:*)", "mcp__github__get_pull_request_comments"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/IdeaProjects/ses", "/Users/<USER>/IdeaProjects/ui-service/", "/Users/<USER>/IdeaProjects/fluro_core/"]}, "enableAllProjectMcpServers": false}