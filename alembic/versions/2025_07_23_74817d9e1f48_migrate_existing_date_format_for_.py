"""migrate existing date format for programs

Revision ID: 74817d9e1f48
Revises: d06638189291
Create Date: 2025-07-23 14:04:02.760239

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "74817d9e1f48"
down_revision = "d06638189291"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """UPDATE mrv_programs
        SET date_format = 'yyyy-MM-dd' WHERE date_format = 'YYYY-MM-DD';"""
    )


def downgrade():
    pass
