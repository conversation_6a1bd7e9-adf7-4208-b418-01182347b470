from datetime import datetime, timezone
from unittest.mock import Mock, patch
from uuid import UUID

import pytest
from ses_client.application import ApplicationActivity
from ses_client.crop import (
    crop_id_from_label,
    crop_purpose_commodity_harvest,
    HarvestedCrop,
    PlantedCrop,
)
from ses_client.harvest import HarvestActivity
from ses_client.planting import PlantingActivity

from boundaries_service import client as boundaries_client
from entity_events.events.entity_event import SESEventWithContext
from phases.enums import PhaseTypes, StageTypes
from ui.projects.field_events.methods import (
    build_field_cultivation_cycles_for_calculations,
)
from ui.projects.field_events.tests.mocks import get_mock_geom

field_id = "test-field-id"

# Predictable event IDs for testing
TEST_HARVEST_EVENT_ID = UUID("12345678-1234-5678-9abc-123456789abc")
TEST_PLANTING_EVENT_ID = UUID("*************-8765-cba9-987654321cba")
TEST_NUTRIENT_EVENT_ID = UUID("11111111-**************-************")

# Create shared SES activities for testing at module level
crop_period_id = "test-crop-period"
crop_type = "corn"
crop_id = crop_id_from_label(crop_type)
planted_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
harvested_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())

PLANTING_ACTIVITY = (
    PlantingActivity(event_id=str(TEST_PLANTING_EVENT_ID), user_id="1")
    .cropping_period_identifier(crop_period_id)
    .crop(planted_crop)
    .start(datetime(year=2021, month=5, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)

HARVEST_ACTIVITY = (
    HarvestActivity(event_id=str(TEST_HARVEST_EVENT_ID), user_id="1")
    .cropping_period_identifier(crop_period_id)
    .crop(harvested_crop)
    .start(datetime(year=2021, month=11, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)

APPLICATION_ACTIVITY = (
    ApplicationActivity(event_id=str(TEST_NUTRIENT_EVENT_ID), user_id="1")
    .start(datetime(year=2021, month=6, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)

TEST_CROP_SEQUENCE = (
    SESEventWithContext(event=PLANTING_ACTIVITY[0], context=PLANTING_ACTIVITY[1]),
    SESEventWithContext(event=HARVEST_ACTIVITY[0], context=HARVEST_ACTIVITY[1]),
)

TEST_PRACTICE_EVENT = SESEventWithContext(event=APPLICATION_ACTIVITY[0], context=APPLICATION_ACTIVITY[1])


async def mock_get_ses_field_events(*args, **kwargs):
    return {field_id: [TEST_CROP_SEQUENCE]}, {field_id: [TEST_PRACTICE_EVENT]}


async def mock_fetch_events_func(event_ids):
    events = []
    for eid in event_ids:
        if eid == str(TEST_PLANTING_EVENT_ID):
            events.append(PLANTING_ACTIVITY[0])
        elif eid == str(TEST_HARVEST_EVENT_ID):
            events.append(HARVEST_ACTIVITY[0])
        elif eid == str(TEST_NUTRIENT_EVENT_ID):
            events.append(APPLICATION_ACTIVITY[0])
    return events


@pytest.mark.parametrize(
    "stage_required_previous, stage_required_current, event_complete_previous, event_complete_current, expected_locked",
    [
        # Row 1: No requirements, no completions
        (False, False, False, False, False),
        # Row 2: No requirements, current complete only
        (False, False, False, True, False),
        # Row 3: No requirements, previous complete only
        (False, False, True, False, False),
        # Row 4: No requirements, both complete
        (False, False, True, True, False),
        # Row 5: Current required only, no completions
        (False, True, False, False, False),
        # Row 6: Current required, current complete
        (False, True, False, True, True),
        # Row 7: Current required, previous complete only
        (False, True, True, False, False),
        # Row 8: Current required, both complete
        (False, True, True, True, True),
        # Row 9: Previous required only, no completions
        (True, False, False, False, False),
        # Row 10: Previous required, current complete
        (True, False, False, True, True),
        # Row 11: Previous required, previous complete
        (True, False, True, False, True),
        # Row 12: Previous required, both complete
        (True, False, True, True, True),
        # Row 13: Both required, no completions
        (True, True, False, False, False),
        # Row 14: Both required, current complete
        (True, True, False, True, True),
        # Row 15: Both required, previous complete
        (True, True, True, False, True),
        # Row 16: Both required, both complete
        (True, True, True, True, True),
    ],
)
@patch.object(boundaries_client, "get_geometry_for_md5", get_mock_geom)
@patch("ui.projects.field_events.methods.get_ses_field_events", mock_get_ses_field_events)
async def test_cultivation_cycle_locking_all_scenarios(
    stage_required_previous: bool,
    stage_required_current: bool,
    event_complete_previous: bool,
    event_complete_current: bool,
    expected_locked: bool,
    app_request,
    mdl,
):
    """
    Test all 16 scenarios from the cultivation cycle locking logic truth table.
    Verifies that stage_cultivation_cycle_is_locked follows the correct business rules.
    """

    # Setup test data
    start = datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2025, month=12, day=31, tzinfo=timezone.utc)

    # Create previous program with stage requirements based on scenario
    previous_program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=5,
    )
    previous_phase = await mdl.Phases(program_id=previous_program.id, type_=PhaseTypes.ENROLMENT)

    # Create stages in previous program - CROP_EVENTS requirement varies with test scenario
    await mdl.Stage(
        phase_id=previous_phase.id,
        type_=StageTypes.CROP_EVENTS,
        required=stage_required_previous,  # Test parameter controls CROP_EVENTS requirement
    )
    await mdl.Stage(
        phase_id=previous_phase.id,
        type_=StageTypes.NUTRIENT_EVENTS,
        required=stage_required_previous,  # Both stage types follow same requirement pattern
    )

    # Create current program
    current_program = await mdl.Programs(
        previous_program_id=previous_program.id,
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=5,
    )
    current_phase = await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.ENROLMENT)

    # Create stages in current program - CROP_EVENTS requirement varies with test scenario
    await mdl.Stage(
        phase_id=current_phase.id,
        type_=StageTypes.CROP_EVENTS,
        required=stage_required_current,  # Test parameter controls CROP_EVENTS requirement
    )
    test_stage = await mdl.Stage(
        phase_id=current_phase.id,
        type_=StageTypes.NUTRIENT_EVENTS,
        required=stage_required_current,  # Both stage types follow same requirement pattern
    )

    # Create test field and projects for both programs
    user = await mdl.Users()

    # Previous program project
    previous_project = await mdl.Projects(program_id=previous_program.id)
    await mdl.ProjectPermissions(project=previous_project.id, user=user.id)

    # Current program project
    current_project = await mdl.Projects(program_id=current_program.id)
    await mdl.ProjectPermissions(project=current_project.id, user=user.id)

    # Field belongs to current project
    field = await mdl.Fields(parent_project_id=current_project.id, md5=field_id)

    # Create baseline for the field
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2020)

    # Set up FieldEventAssociation for the harvest event (CROP_EVENTS)
    harvest_field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(TEST_HARVEST_EVENT_ID),
        field_md5=field.md5,
        project_id=current_project.id,
        program_id=current_program.id,
    )

    # Set up FieldEventAssociation for the nutrient application event (NUTRIENT_EVENTS)
    nutrient_field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(TEST_NUTRIENT_EVENT_ID),
        field_md5=field.md5,
        project_id=current_project.id,
        program_id=current_program.id,
    )

    # Link harvest event to current phase for crop event locking
    await mdl.PhaseEventAssociation(
        field_event_association_id=harvest_field_event_association.id,
        phase_id=current_phase.id,
        revision=1,
    )

    # Link nutrient event to current phase for nutrient event locking
    await mdl.PhaseEventAssociation(
        field_event_association_id=nutrient_field_event_association.id,
        phase_id=current_phase.id,
        revision=1,
    )

    await mdl.ProjectPhaseCompletion(
        project_id=current_project.id,
        phase_id=current_phase.id,
        is_completed=event_complete_current,
        allow_post_close_edit=False,
    )

    # Also link both events to previous phase for legacy locking
    await mdl.PhaseEventAssociation(
        field_event_association_id=harvest_field_event_association.id,
        phase_id=previous_phase.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=nutrient_field_event_association.id,
        phase_id=previous_phase.id,
        revision=1,
    )
    await mdl.ProjectPhaseCompletion(
        project_id=current_project.id,  # Both completions should be in the current project
        phase_id=previous_phase.id,
        is_completed=event_complete_previous,
        allow_post_close_edit=False,
    )

    # Use shared mock function for SES client
    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    # Apply mocks for SES client only
    with patch("ui.projects.field_events.methods.ses_client", mock_client):
        # Call the function under test
        result = await build_field_cultivation_cycles_for_calculations(
            request=app_request,
            program=current_program,
            acting_user_id=str(user.id),
            owner_user_id=str(user.id),
            phase=current_phase,
            stage=test_stage,
            fields=[field],
        )

        cultivation_cycles = result[field.id]

        # Find the cycle to test
        found_cycle = False
        for cycle in cultivation_cycles:
            if (
                hasattr(cycle, "id")
                and cycle.id
                and hasattr(cycle.id, "crop_event_id")
                and cycle.id.crop_event_id
                and cycle.id.crop_event_id.harvesting_id == TEST_HARVEST_EVENT_ID
            ):
                found_cycle = True
                assert cycle.stage_cultivation_cycle_is_locked == expected_locked, (
                    f"Scenario failed: stage_required_previous={stage_required_previous}, "
                    f"stage_required_current={stage_required_current}, "
                    f"event_complete_previous={event_complete_previous}, "
                    f"event_complete_current={event_complete_current} "
                    f"Expected locked={expected_locked}, got locked={cycle.stage_cultivation_cycle_is_locked}"
                )

                # Verify that the nutrient application event is present in the cycle
                application_event_found = False
                for event in cycle.events:
                    if hasattr(event, "id") and str(event.id) == str(TEST_NUTRIENT_EVENT_ID):
                        application_event_found = True
                        break

                assert application_event_found, (
                    f"NUTRIENT_EVENTS application event {TEST_NUTRIENT_EVENT_ID} not found in cultivation cycle events. "
                    f"Events found: {[str(getattr(event, 'id', 'no-id')) for event in cycle.events]}"
                )

                break  # Found the cycle we care about, stop looking

        assert found_cycle, f"No cycle found with harvesting_id {TEST_HARVEST_EVENT_ID}"


@pytest.mark.parametrize(
    "stage_type, expected_uses_stage_logic",
    [
        (StageTypes.CROP_EVENTS, False),  # Crop events use direct lock status
        (StageTypes.TILLAGE_EVENTS, True),  # Non-crop events use stage logic
        (StageTypes.NUTRIENT_EVENTS, True),  # Non-crop events use stage logic
        (None, False),  # No stage uses direct lock status
    ],
)
@patch.object(boundaries_client, "get_geometry_for_md5", get_mock_geom)
@patch("ui.projects.field_events.methods.get_ses_field_events", mock_get_ses_field_events)
async def test_cultivation_cycle_locking_stage_type_behavior(
    stage_type,
    expected_uses_stage_logic: bool,
    app_request,
    mdl,
):
    """
    Test that different stage types follow the appropriate locking logic.
    CROP_EVENTS should use direct crop_event_is_locked, others should use stage requirement logic.
    """

    # Setup minimal test data
    start = datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2025, month=12, day=31, tzinfo=timezone.utc)

    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=5,
    )
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    # Create stage if stage_type is provided
    stage = None
    if stage_type:
        stage = await mdl.Stage(phase_id=phase.id, type_=stage_type, required=True)

    # Create test field and project
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id, md5=field_id)

    # Create baseline for the field
    await mdl.FieldsBaseline(field_id=field.id, baseline_year=2020)

    # Use shared mock function for SES client
    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    # Mock dependencies
    with patch("ui.projects.field_events.methods.ses_client", mock_client):
        # Call the function under test
        result = await build_field_cultivation_cycles_for_calculations(
            request=app_request,
            program=program,
            acting_user_id=str(user.id),
            owner_user_id=str(user.id),
            phase=phase,
            stage=stage,
            fields=[field],
        )

        # Verify the function completes without error
        # (More detailed verification would require deeper mocking of SES events)
        assert field.id in result or len(result) == 0  # Accept either result for this test
