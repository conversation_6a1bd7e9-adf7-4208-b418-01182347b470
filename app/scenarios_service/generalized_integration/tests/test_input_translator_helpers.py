from datetime import datetime, timedelta
from unittest.mock import MagicMock

import pytest
from scenarios_service_schema.schema import (
    ApplicationMethodEnum,
    AutoIrrigationEvent,
    FertilizerEvent,
    FertilizerMethodEnum,
    FloodEvent,
    IrrigationMethodEnum,
    OrganicAmendmentEvent,
)
from starlette.requests import Request

from cultivation_cycles.schema import CultivationCycle
from defaults.attribute_options import (
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    IrrigationMethods,
    PlantingMethod,
)
from defaults.consts import RegrowCropName, RegrowProductName
from defaults.tests import mock_defaults_translator
from defaults.tests.mock_defaults_translator import (
    mock_get_regrow_product_name_liquid_density,
    mock_is_regrow_product_name_dry,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent, PlantingRate, YieldRate
from entity_events.events.fire_event import FireEvent
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import ApplicationInput, ApplicationRate
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from entity_events.units import AreaUnit, MassUnit, VolumeUnit
from fields.model import Fields
from programs.model import Programs
from projects.model import Projects
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration import input_translator_helpers
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)


@pytest.fixture(autouse=True)
def mock_get_product_category_by_regrow_name(mocker):
    return mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.get_product_category_by_regrow_name",
        mock_defaults_translator.mock_get_product_category_by_regrow_name,
    )


@pytest.fixture(autouse=True)
def mock_is_regrow_product_name_dry_fixture(mocker):
    return mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_dry",
        mock_defaults_translator.mock_is_regrow_product_name_dry,
    )


@pytest.fixture(autouse=True)
def mock_is_regrow_product_type_valid_additive_product_fixture(mocker):
    return mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_valid_additive_product",
        mock_defaults_translator.mock_is_regrow_product_name_valid_additive_product,
    )


@pytest.fixture(autouse=True)
def mock_is_regrow_product_name_valid_fertilizer_product(mocker):
    return mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_valid_fertilizer_product",
        mock_defaults_translator.mock_is_regrow_product_name_valid_fertilizer_product,
    )


@pytest.fixture(autouse=True)
def mock_is_regrow_product_name_valid_organic_product(mocker):
    return mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_valid_organic_product",
        mock_defaults_translator.mock_is_regrow_product_name_valid_organic_product,
    )


async def test_get_reporting_info(app_request: Request, mdl, cultivation_cycles: list[CultivationCycle]):
    program: Programs = await mdl.Programs(
        crediting_year=2021,
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
    )
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    # Reporting Information should be determined by the latest commodity CroppingEvent in the latest cultivation cycle
    last_cycle = max(cultivation_cycles, key=lambda cycle: cycle.start)
    crop_event = next(ev for ev in last_cycle.events if isinstance(ev, CroppingEvent))
    crop_event.entity_id = field.id
    crop_event.crop_type = "rice"
    crop_event.crop_yield = YieldRate(value=100.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE)
    # Let's just assume that the translation of the core crop name to the regrow name returns rice
    expected_reporting_info = {
        "start_date": last_cycle.start.strftime("%Y-%m-%d"),
        "end_date": last_cycle.end.strftime("%Y-%m-%d"),
        "crop_name": crop_event.crop_type,
        "crop_yield": 100.0,
    }

    reporting_info = await input_translator_helpers.get_reporting_info(
        app_request, cultivation_cycles, crop_event.entity_id, program, ScenariosServiceApi.explore_api
    )
    assert reporting_info == expected_reporting_info


async def test_get_reporting_info_with_multiple_crops_uses_final_crop_for_explore_api(
    app_request: Request, mdl, cultivation_cycles: list[CultivationCycle]
):
    program: Programs = await mdl.Programs(
        crediting_year=2021,
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
    )
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    # Reporting Information should be determined by the latest commodity CroppingEvent in the latest cultivation cycle
    last_cycle = max(cultivation_cycles, key=lambda cycle: cycle.start)
    crop_event_1 = next(ev for ev in last_cycle.events if isinstance(ev, CroppingEvent))
    crop_event_1.entity_id = field.id
    crop_event_1.crop_type = "wheat"
    crop_event_1.crop_yield = YieldRate(value=50.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE)
    crop_event_1.crop_usage = CropUsage.COMMODITY
    # insert a second commodity crop event in the reporting period cycle
    crop_event_2 = CroppingEvent(
        entity_id=111,
        entity_type="field",
        crop_type="rice",
        crop_usage=CropUsage.COMMODITY,
        crop_yield=YieldRate(value=100.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
        interval=Interval(
            start=crop_event_1.interval.end + timedelta(days=1), end=crop_event_1.interval.end + timedelta(days=30)
        ),
    )
    last_cycle.events.append(crop_event_2)

    # Let's just assume that the translation of the core crop name to the regrow name returns wheat
    expected_reporting_info = {
        "start_date": last_cycle.start.strftime("%Y-%m-%d"),
        "end_date": last_cycle.end.strftime("%Y-%m-%d"),
        "crop_name": crop_event_2.crop_type,
        "crop_yield": crop_event_2.crop_yield.value,
    }

    reporting_info = await input_translator_helpers.get_reporting_info(
        app_request, cultivation_cycles, crop_event_1.entity_id, program, ScenariosServiceApi.explore_api
    )
    assert reporting_info == expected_reporting_info


async def test_get_reporting_info_with_multiple_crops_raises_for_measure_api(
    app_request: Request, mdl, cultivation_cycles: list[CultivationCycle]
):
    program: Programs = await mdl.Programs(
        crediting_year=2021,
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
    )
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    # Reporting Information should be determined by the latest commodity CroppingEvent in the latest cultivation cycle
    last_cycle = max(cultivation_cycles, key=lambda cycle: cycle.start)
    crop_event_1 = next(ev for ev in last_cycle.events if isinstance(ev, CroppingEvent))
    crop_event_1.entity_id = field.id
    crop_event_1.crop_type = "wheat"
    crop_event_1.crop_yield = YieldRate(value=50.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE)
    crop_event_1.crop_usage = CropUsage.COMMODITY
    # insert a second commodity crop event in the reporting period cycle
    crop_event_2 = CroppingEvent(
        entity_id=111,
        entity_type="field",
        crop_type="rice",
        crop_usage=CropUsage.COMMODITY,
        crop_yield=YieldRate(value=100.0, numerator_unit=VolumeUnit.BUSHEL, denominator_unit=AreaUnit.ACRE),
        interval=Interval(
            start=crop_event_1.interval.end + timedelta(days=1), end=crop_event_1.interval.end + timedelta(days=30)
        ),
    )
    last_cycle.events.append(crop_event_2)

    with pytest.raises(ScenariosServiceIntegrationError):
        await input_translator_helpers.get_reporting_info(
            app_request, cultivation_cycles, crop_event_1.entity_id, program, ScenariosServiceApi.measure_api
        )


async def test_get_reporting_info_program_1666_looks_up_yields_and_hardcodes_soybean(
    app_request: Request, mdl, cultivation_cycles: list[CultivationCycle], mocker
):
    program: Programs = await mdl.Programs(
        crediting_year=2021,
        reporting_period_start_date=datetime(2022, 1, 1, 0, 0, 0),
        reporting_period_end_date=datetime(2022, 12, 31, 23, 59, 59),
    )
    cultivation_cycles = input_translator_helpers.select_cultivation_cycles_for_simulation(
        cultivation_cycles, program.get_reporting_period()
    )
    # Set the final cultivation cycle's crop to soybean
    rp_events = [ev for ev in cultivation_cycles[-1].events if isinstance(ev, CroppingEvent)]
    rp_events[0].crop_type = RegrowCropName.soybean
    rp_events[0].crop_usage = CropUsage.COMMODITY

    project: Projects = await mdl.Projects(program_id=program.id)
    program.id = 1666
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.YIELD_LOOKUP_155_INVENTORY_RERUN",
        {field.id: {"yield_value": 30, "yield_unit": "bu/ac"}},
    )
    reporting_info = await input_translator_helpers.get_reporting_info(
        app_request, cultivation_cycles, field.id, program, ScenariosServiceApi.inventory_api
    )
    assert reporting_info["crop_yield"] == 30
    assert reporting_info["crop_name"] == RegrowCropName.soybean


@pytest.mark.parametrize("denominator", [AreaUnit.ACRE, AreaUnit.HECTARE])
@pytest.mark.parametrize(
    "product_type,numerator,rate_type",
    # dap is dry and aqamm is liquid
    [
        (RegrowProductName.di_ammonium_phosphate, MassUnit.KILOGRAM, ApplicationRateType.PRODUCT_RATE),
        (RegrowProductName.di_ammonium_phosphate, MassUnit.KILOGRAM, ApplicationRateType.NITROGEN_RATE),
        (RegrowProductName.ammonia_aqueous, MassUnit.POUND, ApplicationRateType.NITROGEN_RATE),
        (RegrowProductName.ammonia_aqueous, VolumeUnit.LITRE, ApplicationRateType.PRODUCT_RATE),
        (RegrowProductName.ammonia_aqueous, VolumeUnit.US_GALLON, ApplicationRateType.PRODUCT_RATE),
        (RegrowProductName.ammonia_anhydrous, MassUnit.KILOGRAM, ApplicationRateType.PRODUCT_RATE),
        (RegrowProductName.ammonia_anhydrous, MassUnit.KILOGRAM, ApplicationRateType.NITROGEN_RATE),
    ],
)
async def test_standardize_application_rate(mocker, numerator, denominator, rate_type, product_type):
    application_input = ApplicationInput(
        product_name=product_type,
        application_rate=ApplicationRate(
            value=5, numerator_unit=numerator, denominator_unit=denominator, rate_type=rate_type
        ),
    )
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.is_regrow_product_name_dry",
        side_effect=mock_is_regrow_product_name_dry,
    )
    mocker.patch(
        "scenarios_service.generalized_integration.input_translator_helpers.defaults_translator.get_regrow_product_name_liquid_density",
        side_effect=mock_get_regrow_product_name_liquid_density,
    )
    amount, unit = await input_translator_helpers._standardize_application_rate(application_input)
    assert amount > 0
    if product_type == RegrowProductName.ammonia_anhydrous and rate_type == ApplicationRateType.NITROGEN_RATE:
        assert unit == "kilogram"
    elif product_type == RegrowProductName.ammonia_anhydrous and rate_type == ApplicationRateType.PRODUCT_RATE:
        assert unit == "liter"
        if denominator == AreaUnit.HECTARE:
            assert (
                pytest.approx(amount, 0.0001) == (5 * 2.20462 / 5.15) * 3.78541
            )  # (5 kg * 2.20462 lbs/kg / 5.15 lbs/gal) * 3.78541 liters/gal
    elif product_type == RegrowProductName.di_ammonium_phosphate:
        assert unit == "kilogram"
    elif product_type == RegrowProductName.ammonia_aqueous and rate_type == ApplicationRateType.NITROGEN_RATE:
        assert unit == "kilogram"
    elif product_type == RegrowProductName.ammonia_aqueous and rate_type == ApplicationRateType.PRODUCT_RATE:
        assert unit == "liter"


async def test_translate_cropping_event_to_ss_event(
    mdl, app_request, create_cropping_event_data, create_interval_data, create_reduction_event_data
):
    program: Programs = await mdl.Programs()
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    start = datetime.now() - timedelta(days=90)
    end = datetime.now() - timedelta(days=1)
    cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field.id,
            interval=create_interval_data(start=start, end=end),
            crop_type=RegrowCropName.barley,
            reductions=[create_reduction_event_data(occurred_at=end)],
        )
    )
    cropping_event_no_reduction = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field.id, interval=create_interval_data(start=start, end=end), reductions=[]
        )
    )

    ss_cropping_event = await input_translator_helpers._translate_cropping_event_to_ss_event(
        app_request, cropping_event
    )

    assert ss_cropping_event.start_date == start.strftime("%Y-%m-%d")
    assert ss_cropping_event.end_date == end.strftime("%Y-%m-%d")
    assert ss_cropping_event.name == RegrowCropName.barley
    assert len(ss_cropping_event.reductions) == 1
    assert ss_cropping_event.plant_density is None

    ss_cropping_event_no_reduction = await input_translator_helpers._translate_cropping_event_to_ss_event(
        app_request, cropping_event_no_reduction
    )
    assert ss_cropping_event_no_reduction.reductions == []


async def test_translate_rice_cropping_event_to_ss_event(
    mdl, app_request, create_cropping_event_data, create_interval_data, create_reduction_event_data
):
    # Rice requires a translation in planting rate/density
    program: Programs = await mdl.Programs()
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    start = datetime.now() - timedelta(days=90)
    end = datetime.now() - timedelta(days=1)
    cropping_event = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field.id,
            interval=create_interval_data(start=start, end=end),
            crop_type=RegrowCropName.rice,
            reductions=[create_reduction_event_data(occurred_at=end)],
            planting_rate=PlantingRate(value=75.0, numerator_unit=MassUnit.KILOGRAM, denominator_unit=AreaUnit.HECTARE),
            planting_method=PlantingMethod.TRANSPLANT_WET,
        )
    )
    cropping_event_no_reduction = CroppingEvent.parse_obj(
        create_cropping_event_data(
            entity_id=field.id, interval=create_interval_data(start=start, end=end), reductions=[]
        )
    )

    ss_cropping_event = await input_translator_helpers._translate_cropping_event_to_ss_event(
        app_request, cropping_event
    )

    assert ss_cropping_event.start_date == start.strftime("%Y-%m-%d")
    assert ss_cropping_event.end_date == end.strftime("%Y-%m-%d")
    assert ss_cropping_event.name == RegrowCropName.rice
    assert len(ss_cropping_event.reductions) == 1
    assert pytest.approx(186.72, 0.01) == ss_cropping_event.plant_density

    ss_cropping_event_no_reduction = await input_translator_helpers._translate_cropping_event_to_ss_event(
        app_request, cropping_event_no_reduction
    )
    assert ss_cropping_event_no_reduction.reductions == []


async def test_translate_tillage_event_to_ss_event(app_request, create_tillage_event_data):
    tillage_event_with_strip_inversion = TillageEvent.parse_obj(
        create_tillage_event_data(soil_inversion=True, strip_fraction=0.5)
    )
    tillage_event_without_strip_inversion = TillageEvent.parse_obj(create_tillage_event_data())

    # These not erroring is part of the test
    ss_tillage_event_strip = input_translator_helpers._translate_tillage_event_to_ss_event(
        tillage_event_with_strip_inversion
    )
    ss_tillage_event_no_strip = input_translator_helpers._translate_tillage_event_to_ss_event(
        tillage_event_without_strip_inversion
    )

    assert ss_tillage_event_strip.soil_inverted is True
    assert abs(ss_tillage_event_strip.strip_frac - 0.5) <= 0.00001  # Equality on floats is bad

    assert ss_tillage_event_no_strip.soil_inverted is False
    assert ss_tillage_event_no_strip.strip_frac is None


async def test_translate_application_event_to_ss_events_broadcasted(
    create_application_event_data,
    create_application_products_data,
):
    # Broadcast a dry inorganic product
    application_event_broadcasted = ApplicationEvent.parse_obj(
        create_application_event_data(
            products=create_application_products_data(main_product_name=RegrowProductName.ammonium_nitrate),
            additives="anvol,centuro",
        )
    )

    field = MagicMock()
    ss_event_broadcasted = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_broadcasted, field, 10.0
        )
    )[0]
    assert isinstance(ss_event_broadcasted, FertilizerEvent)
    assert ss_event_broadcasted.application_method == ApplicationMethodEnum.BROADCAST
    assert len(ss_event_broadcasted.additives) == 2
    assert ss_event_broadcasted.internal_is_amount_in_N_product is False


async def test_translate_application_event_to_ss_events_injected(
    create_application_event_data,
    depth_data,
    create_application_products_data,
):
    application_event_injected = ApplicationEvent.parse_obj(  # Injection of a dry OA product
        create_application_event_data(
            products=create_application_products_data(main_product_name=RegrowProductName.manure_poultry_solid),
            method=ApplicationMethod.INJECTED,
            depth=depth_data,
        )
    )

    field = MagicMock()
    ss_event_injected = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_injected, field, 10.0
        )
    )[0]
    assert isinstance(ss_event_injected, OrganicAmendmentEvent)
    assert ss_event_injected.application_method == ApplicationMethodEnum.INJECT
    assert ss_event_injected.depth > 0


async def test_translate_application_event_to_ss_events_fertigated_depth(
    create_application_event_data,
    depth_data,
):
    application_event_fertigated_depth = ApplicationEvent.parse_obj(
        create_application_event_data(method=ApplicationMethod.FERTIGATION, depth=depth_data, water_amount=depth_data)
    )  # Fert SSD of dry basic_inorganic w/ depth water

    field = MagicMock()
    ss_event_fertigated_depth = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_fertigated_depth, field, 10.0
        )
    )[0]
    assert isinstance(ss_event_fertigated_depth, FertilizerEvent)
    assert ss_event_fertigated_depth.application_method == ApplicationMethodEnum.FERTIGATION_SUBSURFACE_DRIP
    assert ss_event_fertigated_depth.depth > 0
    assert ss_event_fertigated_depth.water_amount is not None
    assert ss_event_fertigated_depth.additives is None  # Fertigation-related methods get additives stripped


async def test_translate_application_event_to_ss_events_fertigated_volume(
    create_application_event_data,
    depth_data,
    volume_data,
    create_application_products_data,
    create_application_rate_data,
):
    application_event_fertigated_volume = ApplicationEvent.parse_obj(  # Fert SSD of liq basic_inorganic w/ vol water
        create_application_event_data(
            products=create_application_products_data(
                main_product_name=RegrowProductName.ammonia_aqueous,
                main_product_application_rate=create_application_rate_data(numerator_unit=VolumeUnit.LITRE),
            ),
            method=ApplicationMethod.FERTIGATION,
            depth=depth_data,
            water_amount=volume_data,
        )
    )

    field = MagicMock()
    ss_event_fertigated_volume = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_fertigated_volume, field, 10.0
        )
    )[0]
    assert isinstance(ss_event_fertigated_volume, FertilizerEvent)
    assert ss_event_fertigated_volume.application_method == ApplicationMethodEnum.FERTIGATION_SUBSURFACE_DRIP
    assert ss_event_fertigated_volume.depth > 0
    assert ss_event_fertigated_volume.water_amount is not None
    assert ss_event_fertigated_volume.additives is None  # Fertigation-related methods get additives stripped


async def test_translate_application_event_to_ss_events_nitrogen_rate(
    create_application_event_data,
    create_application_products_data,
    create_application_rate_data,
):
    application_event_nitrogen_rate = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.BROADCASTED,
            products=create_application_products_data(
                main_product_application_rate=create_application_rate_data(rate_type=ApplicationRateType.NITROGEN_RATE),
            ),
        )
    )

    field = MagicMock()
    ss_event_nitrogen_rate = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_nitrogen_rate, field, 10.0
        )
    )[0]
    assert ss_event_nitrogen_rate.internal_is_amount_in_N_product


async def test_translate_application_event_to_ss_events_two_main_products(
    create_application_event_data,
    create_application_products_data,
    application_rate_data,
):
    # 1. An ApplicationEvent with multiple "main" products
    two_main_products_data = [
        *create_application_products_data(),
        {"product_name": RegrowProductName.di_ammonium_phosphate, "application_rate": application_rate_data},
    ]
    application_event_two_main_products = ApplicationEvent.parse_obj(
        {**create_application_event_data(products=two_main_products_data), "additives": "anvol,centuro"}
    )  # Broadcast of two dry basic inorganic products

    field = MagicMock()
    # Does the pair of main products get split correctly?
    ss_event_pair = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_two_main_products, field, 10.0
    )
    assert len(ss_event_pair) == 2
    (
        ss_application_event_two_main_products_one,
        ss_application_event_two_main_products_two,
    ) = ss_event_pair
    assert ss_application_event_two_main_products_one.name != ss_application_event_two_main_products_two.name
    # Are the additives shared across the two events?
    assert len(ss_application_event_two_main_products_one.additives) == 2
    assert len(ss_application_event_two_main_products_two.additives) == 2


async def test_translate_application_event_to_ss_events_non_oa_injection(
    create_application_event_data,
    depth_data,
):
    field = MagicMock()
    # 2. An injected non-OA main product that gets substituted to a fertigated one
    application_event_non_oa_injection = ApplicationEvent.parse_obj(
        create_application_event_data(method=ApplicationMethod.INJECTED, depth=depth_data)
    )  # Injection of a dry basic inorganic product

    # Does the non-OA injection get turned to a subsurface?
    ss_application_event_non_oa_injection = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_non_oa_injection, field, 10.0
        )
    )[0]
    assert isinstance(ss_application_event_non_oa_injection, FertilizerEvent)
    assert ss_application_event_non_oa_injection.application_method == ApplicationMethodEnum.SUBSURFACE


async def test_translate_application_event_to_ss_events_oa_fertigation(
    create_application_event_data,
    depth_data,
    create_application_products_data,
):
    field = MagicMock()
    # 3. A fertigated OA main product that gets substituted to an injected one (+ Additives stripped)
    application_event_oa_fertigation = ApplicationEvent.parse_obj(  # Fert SSD of dry basic_inorganic w/ depth water
        create_application_event_data(
            products=create_application_products_data(main_product_name=RegrowProductName.manure_poultry_solid),
            method=ApplicationMethod.FERTIGATION,
            depth=depth_data,
            water_amount=depth_data,
        )
    )

    # Does the OA fertigation get turned to an injection?
    ss_application_event_oa_fertigation = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_oa_fertigation, field, 10.0
        )
    )[0]
    assert isinstance(ss_application_event_oa_fertigation, OrganicAmendmentEvent)
    assert ss_application_event_oa_fertigation.application_method == ApplicationMethodEnum.INJECT


async def test_translate_application_event_to_ss_events_eenf_product(
    create_application_event_data,
    create_application_products_data,
):
    # 4. An EENF product (that should have its additives stripped out)
    application_event_eenf_product = ApplicationEvent.parse_obj(
        create_application_event_data(products=create_application_products_data(main_product_name="superu"))
    )  # Broadcast of a dry eenf

    field = MagicMock()
    # Does the EENF broadcast get its additives removed?
    ss_application_event_eenf_product = (
        await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_eenf_product, field, 10.0
        )
    )[0]
    assert isinstance(ss_application_event_eenf_product, FertilizerEvent)
    assert ss_application_event_eenf_product.additives is None


async def test_translate_irrigation_event_to_ss_irrigation_event(create_irrigation_event_data, depth_data):
    # Drip
    ss_event_drip = input_translator_helpers._translate_irrigation_event_to_ss_event(
        IrrigationEvent.parse_obj(create_irrigation_event_data(method=IrrigationMethods.drip))
    )
    assert isinstance(ss_event_drip, AutoIrrigationEvent)
    assert ss_event_drip.method == IrrigationMethodEnum.DRIP

    # Furrow
    ss_event_furrow = input_translator_helpers._translate_irrigation_event_to_ss_event(
        IrrigationEvent.parse_obj(create_irrigation_event_data(method=IrrigationMethods.furrow, flood_percentage=50))
    )
    assert ss_event_furrow.method == IrrigationMethodEnum.FURROW
    assert ss_event_furrow.furrow_percentage is not None

    # Sprinkler
    ss_event_sprinkler = input_translator_helpers._translate_irrigation_event_to_ss_event(
        IrrigationEvent.parse_obj(create_irrigation_event_data(method=IrrigationMethods.sprinkler))
    )
    assert ss_event_sprinkler.method == IrrigationMethodEnum.SPRINKLER

    # Subsurface Drip
    ss_event_ssd = input_translator_helpers._translate_irrigation_event_to_ss_event(
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(method=IrrigationMethods.subsurface_drip, subsurface_depth=depth_data)
        )
    )

    assert ss_event_ssd.method == IrrigationMethodEnum.SUBSURFACE_DRIP
    assert ss_event_ssd.depth > 0


async def test_translate_irrigation_event_to_ss_flood_event(create_irrigation_event_data):
    irrigation_event = IrrigationEvent.parse_obj(create_irrigation_event_data(method=IrrigationMethods.flood))
    ss_event = input_translator_helpers._translate_irrigation_event_to_ss_flood_event(irrigation_event)
    assert isinstance(ss_event, FloodEvent)


def test_translate_fire_event_to_ss_fire_event(create_fire_event_data):
    mrv_fire_event = FireEvent.parse_obj(create_fire_event_data())
    ss_fire_event = input_translator_helpers._translate_fire_event_to_ss_event(mrv_fire_event)

    assert ss_fire_event.date == mrv_fire_event.occurred_at.strftime("%Y-%m-%d")
    assert ss_fire_event.burn_area_fraction == 1.0
    assert ss_fire_event.combusted_fraction == 1.0


async def test_translate_application_event_to_ss_events_fertigation_methods(
    create_application_event_data,
    depth_data,
):
    field = MagicMock()

    application_event_furrow = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.FERTIGATION_FURROW,
            depth=None,
            water_amount=depth_data,
        )
    )
    ss_events_furrow = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_furrow, field, 10.0
    )
    assert len(ss_events_furrow) > 0
    ss_event_furrow = ss_events_furrow[0]
    assert isinstance(ss_event_furrow, FertilizerEvent)
    assert ss_event_furrow.application_method == ApplicationMethodEnum.FERTIGATION_FURROW
    assert ss_event_furrow.depth == 0
    assert ss_event_furrow.water_amount is not None

    application_event_sprinkler = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.FERTIGATION_SPRINKLER,
            depth=None,
            water_amount=depth_data,
        )
    )
    ss_events_sprinkler = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_sprinkler, field, 10.0
    )
    assert len(ss_events_sprinkler) > 0
    ss_event_sprinkler = ss_events_sprinkler[0]
    assert isinstance(ss_event_sprinkler, FertilizerEvent)
    assert ss_event_sprinkler.application_method == ApplicationMethodEnum.FERTIGATION_SPRINKLER
    assert ss_event_sprinkler.depth == 0
    assert ss_event_sprinkler.water_amount is not None

    application_event_drip = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.FERTIGATION_DRIP,
            depth=None,
            water_amount=depth_data,
        )
    )
    ss_events_drip = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_drip, field, 10.0
    )
    assert len(ss_events_drip) > 0
    ss_event_drip = ss_events_drip[0]
    assert isinstance(ss_event_drip, FertilizerEvent)
    assert ss_event_drip.application_method == ApplicationMethodEnum.FERTIGATION_DRIP
    assert ss_event_drip.depth == 0
    assert ss_event_drip.water_amount is not None


async def test_translate_application_event_to_ss_events_depth_requirement(
    create_application_event_data,
    depth_data,
):
    field = MagicMock()
    application_event_with_depth = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.SUBSURFACE,
            depth=depth_data,
        )
    )
    ss_events = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_with_depth, field, 10.0
    )
    assert len(ss_events) > 0
    ss_event = ss_events[0]
    assert ss_event.depth > 0

    for method in [
        ApplicationMethod.FERTIGATION_FURROW,
        ApplicationMethod.FERTIGATION_SPRINKLER,
        ApplicationMethod.FERTIGATION_DRIP,
    ]:
        application_event_no_depth = ApplicationEvent.parse_obj(
            create_application_event_data(
                method=method,
                depth=None,
                water_amount=depth_data,
            )
        )
        ss_events = await input_translator_helpers._translate_application_event_to_ss_events(
            application_event_no_depth, field, 10.0
        )
        assert len(ss_events) > 0
        ss_event = ss_events[0]
        assert ss_event is not None
        assert ss_event.depth == 0

    application_event_with_depth = ApplicationEvent.parse_obj(
        create_application_event_data(
            method=ApplicationMethod.FERTIGATION,
            depth=depth_data,
            water_amount=depth_data,
        )
    )
    ss_events = await input_translator_helpers._translate_application_event_to_ss_events(
        application_event_with_depth, field, 10.0
    )
    assert len(ss_events) > 0
    ss_event = ss_events[0]
    assert ss_event is not None
    assert ss_event.application_method == FertilizerMethodEnum.FERTIGATION_SUBSURFACE_DRIP
    assert ss_event.depth > 0


async def test_translate_planting_rate(mdl, create_cropping_event_data, create_interval_data):
    program: Programs = await mdl.Programs()
    project: Projects = await mdl.Projects(program_id=program.id)
    field: Fields = await mdl.Fields(parent_project_id=project.id)
    start = datetime.now() - timedelta(days=90)
    end = datetime.now() - timedelta(days=1)

    planting_methods_to_results = [
        (PlantingMethod.TRANSPLANT_WET, 186.72),
        (PlantingMethod.DIRECT_SEEDING_WET, 155.60),
        (PlantingMethod.DIRECT_SEEDING_DRY, 155.60),
        (None, 311.20),
    ]
    for planting_method_pair in planting_methods_to_results:
        event = CroppingEvent.parse_obj(
            create_cropping_event_data(
                entity_id=field.id,
                interval=create_interval_data(start=start, end=end),
                crop_type="rice",
                reductions=[],
                planting_rate=PlantingRate(
                    value=75.0, numerator_unit=MassUnit.KILOGRAM, denominator_unit=AreaUnit.HECTARE
                ),
                planting_method=planting_method_pair[0],
            )
        )
        translated_rate = input_translator_helpers._translate_planting_rate(
            planting_rate=event.planting_rate, crop_type=event.crop_type, planting_method=event.planting_method
        )
        assert pytest.approx(planting_method_pair[1], 0.01) == translated_rate
