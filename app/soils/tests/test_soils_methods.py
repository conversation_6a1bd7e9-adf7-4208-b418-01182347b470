from datetime import date, datetime

from fields.enums import FieldStatus
from fields.schema import Field, SoilPropertiesData
from soils.methods import get_soil_properties_by_field_lineage, upsert_soil_properties
from soils.model import SoilsOverride


async def test_upsert_soil_properties_inserts_new_properties(app_request, mdl, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    properties: list[SoilPropertiesData] = [
        SoilPropertiesData(
            md5=field.md5,
            field_id=field.id,
            program_id=program.id,
            sampling_date=date(2023, 10, 1),
            pred_soc=1.2,
            pred_bd=1.3,
            percent_valid=95.0,
            lower90_soc=1.1,
            upper90_soc=1.3,
            lower90_bd=1.2,
            upper90_bd=1.4,
            ph=6.5,
            clay_fraction=0.2,
        )
    ]

    result = await upsert_soil_properties(app_request, program.id, properties)

    assert len(result) == 1
    assert result[0].md5 == field.md5
    assert result[0].field_id == field.id
    assert result[0].program_id == program.id
    assert result[0].sampling_date == date(2023, 10, 1)
    assert result[0].pred_soc == 1.2
    assert result[0].pred_bd == 1.3
    assert result[0].ph == 6.5
    assert result[0].clay_fraction == 0.2

    assert await orm_select(SoilsOverride, where=[SoilsOverride.field_id == field.id])


async def test_upsert_soil_properties_updates_existing_properties(app_request, mdl, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    initial_properties: list[SoilPropertiesData] = [
        SoilPropertiesData(
            md5=field.md5,
            field_id=field.id,
            program_id=program.id,
            sampling_date=date(2023, 10, 1),
            pred_soc=1.0,
            pred_bd=1.1,
            percent_valid=90.0,
            lower90_soc=0.9,
            upper90_soc=1.1,
            lower90_bd=1.0,
            upper90_bd=1.2,
            ph=6.0,
            clay_fraction=0.1,
        )
    ]

    await upsert_soil_properties(app_request, program.id, initial_properties)

    updated_properties: list[SoilPropertiesData] = [
        SoilPropertiesData(
            md5=field.md5,
            field_id=field.id,
            program_id=program.id,
            sampling_date=date(2023, 10, 1),
            pred_soc=1.2,
            pred_bd=1.3,
            percent_valid=95.0,
            lower90_soc=1.1,
            upper90_soc=1.3,
            lower90_bd=1.2,
            upper90_bd=1.4,
            ph=6.5,
            clay_fraction=0.12,
        )
    ]

    result = await upsert_soil_properties(app_request, program.id, updated_properties)

    # Confirm the properties saved by the first call were soft deleted
    deleted_properties = await orm_select(
        SoilsOverride, where=[SoilsOverride.field_id == field.id, SoilsOverride.deleted_at.is_not(None)]
    )
    assert len(deleted_properties) == 1

    assert len(result) == 1
    assert result[0].pred_soc == 1.2
    assert result[0].pred_bd == 1.3
    assert result[0].ph == 6.5
    assert result[0].clay_fraction == 0.12

    saved_properties = await orm_select(
        SoilsOverride, where=[SoilsOverride.field_id == field.id, SoilsOverride.deleted_at.is_(None)]
    )
    assert len(saved_properties) == 1
    assert saved_properties[0].pred_soc == 1.2
    assert saved_properties[0].pred_bd == 1.3


async def test_get_soil_properties_by_field_lineage_returns_none(app_request, mdl):
    program = await mdl.Programs(reporting_period_end_date=datetime(2023, 12, 31))
    project = await mdl.Projects(program_id=program.id)
    field_lineage = await mdl.FieldLineage()
    field = await mdl.Fields(
        parent_project_id=project.id, status=FieldStatus.enrolled, field_lineage_id=field_lineage.id
    )

    result = await get_soil_properties_by_field_lineage(app_request, Field.from_orm(field))
    assert result is None


async def test_get_soil_properties_by_field_lineage_returns_most_recent_sampled(app_request, mdl, orm_select):
    program1 = await mdl.Programs(reporting_period_end_date=datetime(2023, 12, 31))
    program2 = await mdl.Programs(reporting_period_end_date=datetime(2024, 12, 31), previous_program_id=program1.id)
    program3 = await mdl.Programs(reporting_period_end_date=datetime(2025, 12, 31), previous_program_id=program2.id)
    project1 = await mdl.Projects(program_id=program1.id)
    project2 = await mdl.Projects(program_id=program2.id)
    project3 = await mdl.Projects(program_id=program3.id)
    field_lineage = await mdl.FieldLineage()
    field1 = await mdl.Fields(
        parent_project_id=project1.id, status=FieldStatus.enrolled, field_lineage_id=field_lineage.id
    )
    field2 = await mdl.Fields(
        parent_project_id=project2.id, status=FieldStatus.enrolled, field_lineage_id=field_lineage.id
    )
    field3 = await mdl.Fields(
        parent_project_id=project3.id, status=FieldStatus.enrolled, field_lineage_id=field_lineage.id
    )

    # Insert two soil overrides with different sampling dates
    await mdl.SoilsOverride(
        field_id=field1.id,
        sampling_date=date(2023, 10, 1),
        pred_soc=1.0,
        pred_bd=1.1,
        ph=6.0,
        clay_fraction=0.1,
        deleted_at=None,
    )
    soil2 = await mdl.SoilsOverride(
        field_id=field2.id,
        sampling_date=date(2024, 10, 1),
        pred_soc=2.0,
        pred_bd=2.1,
        ph=7.0,
        clay_fraction=0.2,
        deleted_at=None,
    )

    result = await get_soil_properties_by_field_lineage(app_request, Field.from_orm(field3))
    # Should return soil2, most recent sampling date
    assert result.field_id == field2.id
    assert result.sampling_date == date(2024, 10, 1)
    assert result.pred_soc == 2.0

    result2 = await get_soil_properties_by_field_lineage(app_request, Field.from_orm(field2))
    result3 = await get_soil_properties_by_field_lineage(app_request, Field.from_orm(field1))
    # Should still return soil2
    assert result2.id == soil2.id
    assert result3.id == soil2.id


async def test_get_soil_properties_by_field_lineage_uses_md5_as_fallback(app_request, mdl, orm_select):
    program1 = await mdl.Programs(reporting_period_end_date=datetime(2023, 12, 31))
    program2 = await mdl.Programs(reporting_period_end_date=datetime(2024, 12, 31), previous_program_id=program1.id)
    project1 = await mdl.Projects(program_id=program1.id)
    project2 = await mdl.Projects(program_id=program2.id)
    # No field_lineage_id
    field1 = await mdl.Fields(
        parent_project_id=project1.id, status=FieldStatus.enrolled, md5="test_md5", field_lineage_id=None
    )
    field2 = await mdl.Fields(
        parent_project_id=project2.id, status=FieldStatus.enrolled, md5="test_md5", field_lineage_id=None
    )

    await mdl.SoilsOverride(
        field_id=field1.id,
        md5=field1.md5,
        sampling_date=date(2024, 10, 1),
        pred_soc=2.0,
        pred_bd=2.1,
        ph=7.0,
        clay_fraction=0.2,
        deleted_at=None,
    )

    result = await get_soil_properties_by_field_lineage(app_request, Field.from_orm(field2))
    # Should return soil2, most recent sampling date
    assert result.field_id == field1.id
    assert result.sampling_date == date(2024, 10, 1)
    assert result.pred_soc == 2.0
