import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Callable, Dict, List, Optional

from regrow.ses.pbtype.interval_pb2 import Interval
from ses_client.application import ApplicationFertigation
from ses_client.crop import (
    crop_id_from_label,
    crop_purpose_commodity_harvest,
    HarvestedCrop,
    PlantedCrop,
    TerminatedCrop,
)
from ses_client.event import new_event_id, StructuredEvent
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.irrigation import IrrigationActivity
from ses_client.pb_value import kilograms_per_hectare
from ses_client.planting import PlantingActivity
from ses_client.search import Filter
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from entity_events.events.entity_event import SESEventWithContext
from entity_events.events.enums import EntityEventType

mock_event_id = "6daec06d-6c56-4b8b-92fc-1fa2f430ec57"
mock_geom = {
    "type": "MultiPolygon",
    "coordinates": [
        [
            [
                [-91.29233641703286, 41.30334338630458],
                [-91.28745699999997, 41.305083999999745],
                [-91.28756061103775, 41.299742821734874],
                [-91.29232964157615, 41.29971351579836],
                [-91.29233641703286, 41.30334338630458],
            ]
        ]
    ],
}

event_interval = Interval(start_time=datetime(2021, 1, 1), end_time=datetime(2021, 5, 1))

start_date = datetime.now() - timedelta(days=100)
end_date = datetime.now() - timedelta(days=30)
date = datetime.now()

event_id = str(uuid.uuid4())
crop_period_id = str(uuid.uuid4())
crop_type = "corn"
crop_id = crop_id_from_label(crop_type)
sown_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
terminated_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
harvested_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
planting_activity = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id)
    .crop(sown_crop)
    .start(datetime(year=2020, month=11, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)
termination_activity = (
    TerminationActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id)
    .crop(terminated_crop)
    .start(datetime(year=2021, month=11, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)
harvest_activity = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id)
    .crop(harvested_crop)
    .start(datetime(year=2021, month=11, day=1, tzinfo=timezone.utc))
    .association_context(
        StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION,
        json.dumps(
            {
                EntityEventType.TILLAGE_EVENT: True,
                EntityEventType.APPLICATION_EVENT: True,
                EntityEventType.IRRIGATION_EVENT: True,
            }
        ),
    )
    .event_and_context_pb()
)
crop_period_id1 = str(uuid.uuid4())
planting_activity1 = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id1)
    .crop(sown_crop)
    .start(datetime(year=2021, month=12, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)
termination_activity1 = (
    TerminationActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id1)
    .crop(terminated_crop)
    .start(datetime(year=2022, month=12, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)
harvest_activity1 = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .cropping_period_identifier(crop_period_id1)
    .crop(harvested_crop)
    .start(datetime(year=2022, month=12, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)

planting_activity_cross_year = (
    PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(sown_crop)
    .start(datetime(year=2024, month=10, day=1))
    .event_and_context_pb()
)
harvest_activity_cross_year = (
    HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
    .crop(harvested_crop)
    .start(datetime(year=2025, month=2, day=12))
    .event_and_context_pb()
)


mock_cropping_sequences = [
    (
        SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
        SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
    ),
    (
        SESEventWithContext(event=planting_activity1[0], context=planting_activity1[1]),
        SESEventWithContext(event=harvest_activity1[0], context=harvest_activity1[1]),
    ),
]


mock_cropping_sequences_cross_year = [
    (
        SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
        SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
    ),
    (
        SESEventWithContext(event=planting_activity1[0], context=planting_activity1[1]),
        SESEventWithContext(event=harvest_activity1[0], context=harvest_activity1[1]),
    ),
    (
        SESEventWithContext(event=planting_activity_cross_year[0], context=planting_activity_cross_year[1]),
        SESEventWithContext(event=harvest_activity_cross_year[0], context=harvest_activity_cross_year[1]),
    ),
]


def get_mock_crop_sequences(field_id: str) -> Callable:
    async def mock_cropping_sequences_for_fields(
        self,
        field_ids: List[str],
        from_date: datetime,
        to_date: datetime,
        user_ids: Optional[List[str]] = None,
        min_overlap_percentage: Optional[int] = 50,
        crop_purpose: Optional[str] = None,
    ) -> Dict[str, list[tuple]]:
        return {field_id: mock_cropping_sequences}

    return mock_cropping_sequences_for_fields


def get_mock_crop_sequences_cross_year(field_id: str) -> Callable:
    async def mock_cropping_sequences_for_fields_cross_year(
        self,
        field_ids: List[str],
        from_date: datetime,
        to_date: datetime,
        user_ids: Optional[List[str]] = None,
        min_overlap_percentage: Optional[int] = 50,
        crop_purpose: Optional[str] = None,
    ) -> Dict[str, list[tuple]]:
        return {field_id: mock_cropping_sequences_cross_year}

    return mock_cropping_sequences_for_fields_cross_year


tillage_activity = (
    TillageActivity(event_id=new_event_id(), user_id="user-123")
    .geom("{}")
    .start(datetime(2022, 1, 1, 0, 0, tzinfo=timezone.utc))
)
irrigation_activity = (
    IrrigationActivity(event_id=new_event_id(), user_id="user-123")
    .geom("{}")
    .start(datetime(2022, 1, 2, 0, 0, tzinfo=timezone.utc))
    .end(datetime(2022, 1, 2, 0, 0, tzinfo=timezone.utc))
)
cow_poop = BasicFertiliser(input_name="cow poop").mass_rate(kilograms_per_hectare(1500)).pb()
application_activity = (
    ApplicationFertigation(event_id=new_event_id(), user_id="user-123")
    .geom("{}")
    .start(datetime(2022, 1, 3, 0, 0, tzinfo=timezone.utc))
    .input(cow_poop)
)
mock_field_practice_events = [
    SESEventWithContext(event=tillage_activity.pb_event, context=tillage_activity.pb_context),
    SESEventWithContext(event=irrigation_activity.pb_event, context=irrigation_activity.pb_context),
    SESEventWithContext(event=application_activity.pb_event, context=application_activity.pb_context),
]

fallow_period = (
    FallowPeriod(event_id=new_event_id(), user_id="user-123")
    .geom("{}")
    .start(datetime(2023, 1, 1, tzinfo=timezone.utc))
    .end(datetime(2023, 2, 1, tzinfo=timezone.utc))
    .association_context(
        StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION,
        json.dumps(
            {
                EntityEventType.TILLAGE_EVENT: True,
                EntityEventType.APPLICATION_EVENT: True,
                EntityEventType.IRRIGATION_EVENT: True,
            }
        ),
    )
)
mock_field_practice_events_with_fallow_period = mock_field_practice_events + [
    SESEventWithContext(event=fallow_period.pb_event, context=fallow_period.pb_context)
]


def get_mock_field_practice_events(field_id: str) -> Callable:
    async def mock_get_field_practice_events(
        self, field_ids: List[str], search_filter: Filter
    ) -> dict[str, list[SESEventWithContext]]:
        return {field_id: mock_field_practice_events}

    return mock_get_field_practice_events


def get_mock_field_practice_events_with_fallow_period(field_id: str) -> Callable:
    async def mock_get_field_practice_events(
        self, field_ids: List[str], search_filter: Filter
    ) -> dict[str, list[SESEventWithContext]]:
        return {field_id: mock_field_practice_events_with_fallow_period}

    return mock_get_field_practice_events
