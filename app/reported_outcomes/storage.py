from typing import Any

from fastapi import Request

from config import get_settings
from gcloud_client.storage.delete import delete_object
from gcloud_client.storage.download import get_object
from gcloud_client.storage.upload import upload_object
from logger import get_logger
from reported_outcomes.constants import OUTCOMES_DIR, ZIP_CONTENT_TYPE

settings = get_settings()
logger = get_logger(__name__)


# interface


async def upload_task_combined_outcomes(request: Request, program_id: int, task_id: str, file_data: bytes) -> None:
    object_name = _get_task_object_name(program_id, task_id)
    bucket_name = settings.GCLOUD_STORAGE_PRIVATE_BUCKET
    logger.info(f"uploading combined outcomes of {program_id} to {object_name} in bucket {bucket_name}")
    await upload_object(
        request=request,
        bucket_name=bucket_name,
        data=file_data,
        object_name=object_name,
        content_type=ZIP_CONTENT_TYPE,
    )


async def get_object_by_name(request: Request, object_name: str) -> Any | None:
    try:
        return await get_object(request, settings.GCLOUD_STORAGE_PRIVATE_BUCKET, object_name)
    except Exception:
        logger.info(f"failed to find object {object_name}")
        return None


async def download_task_combined_outcomes(request: Request, program_id: int, task_id: str) -> Any | None:
    object_name = _get_task_object_name(program_id, task_id)
    return await get_object_by_name(request, object_name)


async def delete_task_combined_outcomes(request: Request, program_id: int, task_id: str) -> str:
    object_name = _get_task_object_name(program_id, task_id)
    try:
        result = await delete_object(request, settings.GCLOUD_STORAGE_PRIVATE_BUCKET, object_name)
    except Exception:
        logger.info(f"failed to delete combined outcomes for program {program_id}")
        result = None
    return result


def get_combined_outcomes_object_name(appendix: str) -> str:
    return f"combined_outcomes_for_{appendix}.zip"


# internals
def _get_task_object_name(program_id: int, task_id: str) -> str:
    return f"{OUTCOMES_DIR}/{get_combined_outcomes_object_name(f'{program_id}_{task_id}')}"
