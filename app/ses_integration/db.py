import uuid
from dataclasses import dataclass
from typing import Optional
from uuid import UUID

from ses_client.client import Client
from sqlalchemy import and_, func, select, update
from sqlalchemy.dialects.mysql import insert
from starlette.requests import Request

import config
from fields.model import Fields
from helper.async_tools import batch_list
from logger import get_logger
from phases.enums import PhaseTypes, StageTypes
from phases.model import Phases, Stage
from programs.model import Programs
from projects.model import ProjectPermissions, ProjectPhaseCompletion
from root_crud import get
from ses_integration.model import (
    FieldEventAssociation,
    PhaseEventAssociation,
)

logger = get_logger(__name__)
settings = config.get_settings()
ses_client = Client(settings.SES_INTERNAL_URL_BASE, settings.SES_SEARCH_INTERNAL_URL_BASE)


@dataclass
class RawPhaseEventAssociation:
    """
    A raw representation of a PhaseEventAssociation for bulk operations.
    This is used to avoid ORM overhead when performing bulk inserts/updates.
    """

    field_event_association_id: int
    phase_id: int
    revision: int


async def bulk_associate_phases_with_events(
    request: Request, phase_event_associations: list[RawPhaseEventAssociation]
) -> None:
    """Bulk associate phases with fields for particular event revisions."""
    if not phase_event_associations:
        return

    # Sort to help avoid deadlocks
    phase_event_associations = sorted(
        phase_event_associations, key=lambda assoc: (assoc.field_event_association_id, assoc.phase_id)
    )
    for batch in batch_list(phase_event_associations, batch_size=100):
        async with request.state.sql_session.begin() as s:
            # Prepare the insert statement with on duplicate key update
            insert_stmt = insert(PhaseEventAssociation).values(
                [
                    {
                        "field_event_association_id": assoc.field_event_association_id,
                        "phase_id": assoc.phase_id,
                        "revision": assoc.revision,
                        "created_at": func.now(),
                        "updated_at": func.now(),
                        "deleted_at": None,
                    }
                    for assoc in batch
                ]
            )
            upsert_stmt = insert_stmt.on_duplicate_key_update(
                revision=insert_stmt.inserted.revision,
                updated_at=func.now(),
                deleted_at=None,
            )
            await s.execute(upsert_stmt)


async def associate_phase_with_event(
    request: Request, field_event_association_id: int, phase_id: int, revision: int
) -> None:
    """
    Associate an SES Event revision with a Phase.
    """
    async with request.state.sql_session.begin() as s:
        # Unique constraint: phase_id, field_event_association_id
        upsert_stmt = (
            insert(PhaseEventAssociation)
            .values(
                field_event_association_id=field_event_association_id,
                phase_id=phase_id,
                revision=revision,
                created_at=func.now(),
                updated_at=func.now(),
                deleted_at=None,
            )
            .on_duplicate_key_update(revision=revision, updated_at=func.now(), deleted_at=None)
        )
        await s.execute(upsert_stmt)


async def bulk_update_and_undelete_field_event_associations(
    request: Request, field_event_association_ids: list[int], field_md5: Optional[str] = None
) -> list[FieldEventAssociation]:
    """
    Undelete field event associations. This function does not check that provided associations are deleted beforehand.
    Returns:
        list[FieldEventAssociation]: list of undeleted field event associations
    """
    if not field_event_association_ids:
        return []
    async with request.state.sql_session.begin() as s:
        update_query = (
            update(FieldEventAssociation)
            .where(FieldEventAssociation.id.in_(field_event_association_ids))
            .values(deleted_at=None)
            .execution_options(synchronize_session="fetch")
        )
        if field_md5 is not None:
            update_query = update_query.values(field_md5=field_md5)
        await s.execute(update_query)
    return await get.get(
        request=request,
        orm_type=FieldEventAssociation,
        id_field=FieldEventAssociation.id,
        ids=field_event_association_ids,
    )


async def _get_potentially_locked_event_ids_for_user(
    request: Request,
    owner_user_id: int,
    acting_phase_type: PhaseTypes,
    field_md5s: list[str] | None = None,
) -> dict[uuid.UUID, set[int]]:
    """
    Derive SES event locks based on association with a completed phase for the events a user is permitted to see.
    E Phases assume a lock based on a completed E or M Phase, whereas M Phases only assume a lock if the event was is
    associated with a completed M Phase.

    Args:
        request: FastAPI request object
        owner_user_id: User ID that is the owner of the events
        acting_phase_type: Phase type that the user is acting under.
        field_md5s: Optional list of field MD5s to filter by

    Returns:
        dict mapping event_id -> set of program_ids that potentially locked
        final locking status should be determined with the stage type and stage required status
    """
    respect_locks_from_phase_types = [PhaseTypes.MONITORING]
    if acting_phase_type == PhaseTypes.ENROLMENT:
        # M Phases only respect M phase locks, whereas E Phases respect both
        # This allows M Phases to make corrections as long as it hasn't been quantified
        respect_locks_from_phase_types.append(PhaseTypes.ENROLMENT)
    async with request.state.sql_session.begin() as session:
        # For a lock to apply to a given event:
        # 1. User's project(s) has access to the event
        # 2. The user has completed at least one phase that is associated with the event
        # 3. The phase locking the event is not in a future program (in the program series)
        query = (
            select(FieldEventAssociation.ses_event_id, Phases.program_id)
            .join(
                PhaseEventAssociation,
                PhaseEventAssociation.field_event_association_id == FieldEventAssociation.id,
            )
            .join(
                ProjectPermissions,
                ProjectPermissions.project == FieldEventAssociation.project_id,
            )
            .join(
                ProjectPhaseCompletion,
                and_(
                    ProjectPhaseCompletion.project_id == ProjectPermissions.project,
                    ProjectPhaseCompletion.phase_id == PhaseEventAssociation.phase_id,
                ),
            )
            .join(Phases, Phases.id == ProjectPhaseCompletion.phase_id)
            .join(Fields, Fields.id == FieldEventAssociation.field_id)
            .where(Phases.type_.in_(respect_locks_from_phase_types))
            .where(FieldEventAssociation.deleted_at.is_(None))
            .where(PhaseEventAssociation.deleted_at.is_(None))
            .where(ProjectPhaseCompletion.deleted_at.is_(None))
            .where(ProjectPermissions.deleted_at.is_(None))
            .where(Fields.deleted_at.is_(None))
            .where(Phases.deleted_at.is_(None))
            .where(ProjectPermissions.user == owner_user_id)
            .where(ProjectPhaseCompletion.is_completed.is_(True))
        )

        if field_md5s:
            query = query.where(FieldEventAssociation.field_md5.in_(field_md5s))

        results = (await session.execute(query)).all()

    # Group results by event_id
    event_to_programs: dict[uuid.UUID, set[int]] = {}
    for ses_event_id, program_id in results:
        event_id = UUID(ses_event_id)
        if event_id not in event_to_programs:
            event_to_programs[event_id] = set()
        event_to_programs[event_id].add(program_id)

    return event_to_programs


async def get_programs_required_stage_types(
    request: Request, program_ids: list[int], phase_type: PhaseTypes
) -> set[StageTypes]:
    """
    Get a set of stage types that are required in the specified programs.

    Args:
        request: FastAPI request object
        program_ids: List of program IDs to check

    Returns:
        set[StageTypes]: Set of stage types that are required in any of the specified programs
    """
    if not program_ids:
        return set()

    async with request.state.sql_session() as session:
        query = (
            select(Stage.type_)
            .distinct()
            .join(Phases, Phases.id == Stage.phase_id)
            .join(Programs, Programs.id == Phases.program_id)
            .where(Programs.id.in_(program_ids))
            .where(Stage.enabled.is_(True))
            .where(Stage.required.is_(True))
            .where(Stage.deleted_at.is_(None))
            .where(Phases.deleted_at.is_(None))
            .where(Phases.type_ == phase_type)
            .where(Programs.deleted_at.is_(None))
        )

        result = await session.execute(query)
        stage_types = result.scalars().all()
        return set(stage_types)


async def get_field_event_associations(
    request: Request,
    event_ids: Optional[list[UUID]] = None,
    field_md5: Optional[str] = None,
    field_ids: Optional[list[int]] = None,
    project_ids: Optional[int] = None,
    filter_deleted: bool = True,
) -> list[FieldEventAssociation]:
    filters = []
    if event_ids is not None:
        filters.append(get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(ev_id) for ev_id in event_ids]))
    if field_md5 is not None:
        filters.append(get.Filter(id_field=FieldEventAssociation.field_md5, ids=[field_md5]))
    if field_ids is not None:
        filters.append(get.Filter(id_field=FieldEventAssociation.field_id, ids=field_ids))
    if project_ids is not None:
        filters.append(get.Filter(id_field=FieldEventAssociation.project_id, ids=[project_ids]))
    return await get.generic_get(
        request=request,
        orm_type=FieldEventAssociation,
        filters=filters,
        filter_deleted=filter_deleted,
        empty_return=True,
    )


async def get_user_owned_field_event_associations(
    request: Request,
    owner_user_id: str,
    event_ids: Optional[list[uuid.UUID]] = None,
    project_ids: Optional[list[int]] = None,
    field_md5: Optional[str] = None,
    field_id: Optional[int] = None,
) -> list[FieldEventAssociation]:
    async with request.state.sql_session() as s:
        query = (
            select(FieldEventAssociation)
            .join(
                ProjectPermissions,
                and_(
                    ProjectPermissions.project == FieldEventAssociation.project_id,
                    ProjectPermissions.user == owner_user_id,
                ),
            )
            .where(FieldEventAssociation.deleted_at.is_(None))
        )
        if event_ids is not None:
            query = query.where(FieldEventAssociation.ses_event_id.in_([str(ev_id) for ev_id in event_ids]))
        if project_ids is not None:
            query = query.where(FieldEventAssociation.project_id.in_(project_ids))
        if field_md5 is not None:
            query = query.where(FieldEventAssociation.field_md5 == field_md5)
        if field_id is not None:
            query = query.where(FieldEventAssociation.field_id == field_id)
        return (await s.execute(query)).scalars().all()


async def get_phase_event_associations(
    request: Request, field_event_association_ids: list[int], phase_type: PhaseTypes | None = None
) -> list[PhaseEventAssociation]:
    async with request.state.sql_session() as sess:
        query = (
            select(PhaseEventAssociation)
            .join(Phases, Phases.id == PhaseEventAssociation.phase_id)
            .where(Phases.deleted_at.is_(None))
            .where(PhaseEventAssociation.field_event_association_id.in_(field_event_association_ids))
            .where(PhaseEventAssociation.deleted_at.is_(None))
        )
        if phase_type is not None:
            query = query.where(Phases.type_ == phase_type)
        results = await sess.execute(query)

        return results.scalars().all()
