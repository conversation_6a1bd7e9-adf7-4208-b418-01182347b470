import elasticapm
from fastapi import APIRouter, Depends
from starlette import status
from starlette.requests import Request

from fields import paths
from fields.schema import SoilPropertiesData, SoilPropertiesResponse
from permissions.enums import Permission
from permissions.resolver import Permissions
from soils import methods as soils_methods

tags = ["soils"]
super_admin = ["super_admin"]
router = APIRouter(prefix="/programs/{program_id}")


@elasticapm.async_capture_span()
@router.post(
    paths.soils_override,
    response_model=list[SoilPropertiesResponse],
    tags=tags + super_admin,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(Permissions([Permission.UPSERT_SOIL_PROPERTIES]))],
)
async def upsert_soil_properties(
    request: Request, program_id: int, body: list[SoilPropertiesData]
) -> list[SoilPropertiesResponse]:
    return await soils_methods.upsert_soil_properties(request, program_id, body)
