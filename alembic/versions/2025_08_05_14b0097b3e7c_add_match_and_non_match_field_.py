"""Add match and non match field relationship types

Revision ID: 14b0097b3e7c
Revises: 4189f3d1f2a5
Create Date: 2025-08-05 15:17:41.021952

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "14b0097b3e7c"
down_revision = "4189f3d1f2a5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_field_relationship",
        "relationship",
        existing_type=mysql.ENUM("SPLIT", "MERGE", "SKIP"),
        type_=sa.Enum("MATCH", "NON_MATCH", "SPL<PERSON>", "MERGE", "SKIP", name="fieldrelationshiptype"),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_field_relationship",
        "relationship",
        existing_type=sa.Enum("MATCH", "NON_MATCH", "SPL<PERSON>", "MERGE", "SKIP", name="fieldrelationshiptype"),
        type_=mysql.ENUM("SPLIT", "MERGE", "SKIP"),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
