import math
from collections import defaultdict
from typing import Any

import elasticapm
from celery import chain, group
from fastapi import Request

from celery_helper.decorator_class import DBTask
from celery_worker import async_to_sync, celery_app
from config import get_settings
from field_lineage.methods import set_field_baselines
from fields.db import get_fields_by_program
from helper.async_tools import batch_list
from slack_integration.integration import post_message

settings = get_settings()

SET_FIELD_BASELINES_FOR_PROGRAM_MAX_CONCURRENT_TASKS = 20


# if called in response to a field creation event, then field lineage and field
# relationships will both be set or both not be set.
# if called by the set_field_baselines endpoint (ex. for a backfill), then field
# lineage and field relationships may be set independently of each other.
@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def set_field_baselines_task(
    self: Any,
    *,
    program_id: int,
    project_id: int,
    field_ids: list[int],
    set_field_lineage: bool = False,
    set_field_relationship: bool = False,
    set_field_baseline: bool = True,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    try:
        await set_field_baselines(
            request=request,
            program_id=program_id,
            project_id=project_id,
            field_ids=field_ids,
            set_field_lineage=set_field_lineage,
            set_field_relationship=set_field_relationship,
            set_field_baseline=set_field_baseline,
        )
    except Exception as e:
        await post_message(
            slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
            message=f":x: Field lineage could not be set for program {program_id} project {project_id}: {e}",
        )
        raise e


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def _set_field_baselines_for_projects_task(
    self: Any,
    *,
    program_id: int,
    project_id_to_field_ids: dict[int, list[int]],
    set_field_lineage: bool = False,
    set_field_relationship: bool = False,
    set_field_baseline: bool = True,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    for project_id, field_ids in project_id_to_field_ids.items():
        try:
            await set_field_baselines(
                request=request,
                program_id=program_id,
                project_id=project_id,
                field_ids=field_ids,
                set_field_lineage=set_field_lineage,
                set_field_relationship=set_field_relationship,
                set_field_baseline=set_field_baseline,
            )
        except Exception as e:
            await post_message(
                slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
                message=f":x: Field lineage could not be set for program {program_id} project {project_id}: {e}",
            )
            continue


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def _communicate_completion_of_set_field_baselines_for_program_task(
    self: Any,
    previous_task_outputs: Any,
    *,
    program_id: int,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    await post_message(
        slack_webhook_url=settings.SLACK_WEBHOOK_URL_DATA_EXPORT_ALERTS,
        message=f":white_check_mark: Field lineage set for program {program_id}.",
    )


@celery_app.task(base=DBTask, bind=True)
@async_to_sync
@elasticapm.async_capture_span()
async def set_field_baselines_for_program_task(
    self: Any,
    *,
    program_id: int,
    set_field_lineage: bool = False,
    set_field_relationship: bool = False,
    set_field_baseline: bool = False,
    fs_user_id: int | None = None,
    fs_impersonator_user_id: int | None = None,
    request: Request | None = None,
) -> None:
    fields = await get_fields_by_program(request=request, program_id=program_id)
    project_id_to_field_ids: dict[int, list[int]] = defaultdict(list)
    for field in fields:
        project_id_to_field_ids[field.parent_project_id].append(field.id)
    project_ids = list(project_id_to_field_ids.keys())

    batch_size = math.ceil(len(project_ids) / SET_FIELD_BASELINES_FOR_PROGRAM_MAX_CONCURRENT_TASKS)
    batches = []
    for project_id_batch in batch_list(project_ids, batch_size):
        batches.append(
            {
                project_id: field_ids
                for project_id, field_ids in project_id_to_field_ids.items()
                if project_id in project_id_batch
            }
        )

    chain(
        group(
            _set_field_baselines_for_projects_task.s(
                program_id=program_id,
                project_id_to_field_ids=batch,
                set_field_lineage=set_field_lineage,
                set_field_relationship=set_field_relationship,
                set_field_baseline=set_field_baseline,
                fs_user_id=fs_user_id,
                fs_impersonator_user_id=fs_impersonator_user_id,
                request=request,
            )
            for batch in batches
        ),
        _communicate_completion_of_set_field_baselines_for_program_task.s(
            program_id=program_id,
            fs_user_id=fs_user_id,
            fs_impersonator_user_id=fs_impersonator_user_id,
            request=request,
        ),
    ).delay()
