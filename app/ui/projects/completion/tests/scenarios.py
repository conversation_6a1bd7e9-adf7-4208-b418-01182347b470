import json
import uuid
from datetime import datetime, timedelta
from enum import auto, Enum

import pytz
from regrow.ses.harvest.v1 import harvest_pb2
from regrow.ses.pbtype import area_pb2, fraction_pb2, mass_pb2
from regrow.ses.pbtype.interval_pb2 import Interval
from ses_client import pb_value
from ses_client.application import ApplicationFertigation
from ses_client.crop import (
    crop_id_from_label,
    crop_purpose_commodity_harvest,
    crop_purpose_cover,
    HarvestedCrop,
    PlantedCrop,
    TerminatedCrop,
)
from ses_client.event import new_event_id, StructuredEvent

# from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.input import BasicFertiliser
from ses_client.irrigation import IrrigationActivity
from ses_client.pb_value import kilograms_per_hectare
from ses_client.planting import PlantingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity

from entity_events.events.entity_event import SESEventWithContext
from entity_events.events.enums import EntityEventType
from logger import get_logger

# from ses_client.search import Filter

logger = get_logger(__name__)


class ScenarioForTesting(Enum):
    complete_and_correct_no_cover_crops = auto()
    complete_and_correct_with_end_cover_crop_event = auto()
    complete_and_correct_with_end_field_practice_event = auto()
    complete_and_correct_single_phase = auto()
    # complete_and_correct_some_cover_crops = auto()
    # complete_and_correct_all_cover_crops = auto()
    # missing_data_at_start = auto()
    missing_final_year_data = auto()


def should_testing_scenario_pass_completion(scenario: ScenarioForTesting) -> bool:
    match scenario:
        case ScenarioForTesting.complete_and_correct_no_cover_crops:
            return True
        case ScenarioForTesting.complete_and_correct_with_end_cover_crop_event:
            return True
        case ScenarioForTesting.complete_and_correct_with_end_field_practice_event:
            return True
        case ScenarioForTesting.complete_and_correct_single_phase:
            return True
        # case ScenarioForTesting.complete_and_correct_some_cover_crops:
        #    return True
        # case ScenarioForTesting.complete_and_correct_all_cover_crops:
        #    return True
        # case ScenarioForTesting.missing_data_at_start:
        #    return False
        case ScenarioForTesting.missing_final_year_data:
            return False
        case _:
            raise ValueError("Unexpected testing scenario {scenario}")


def is_scenario_for_single_phase_programs(scenario: ScenarioForTesting):
    return scenario == ScenarioForTesting.complete_and_correct_single_phase


mock_event_id = "6daec06d-6c56-4b8b-92fc-1fa2f430ec57"
mock_geom = {
    "type": "MultiPolygon",
    "coordinates": [
        [
            [
                [-91.29233641703286, 41.30334338630458],
                [-91.28745699999997, 41.305083999999745],
                [-91.28756061103775, 41.299742821734874],
                [-91.29232964157615, 41.29971351579836],
                [-91.29233641703286, 41.30334338630458],
            ]
        ]
    ],
}

event_interval = Interval(start_time=datetime(2021, 1, 1), end_time=datetime(2021, 5, 1))

start_date = datetime.now() - timedelta(days=100)
end_date = datetime.now() - timedelta(days=30)
date = datetime.now()

event_id = str(uuid.uuid4())


def generate_test_crop_sequences(field_id: str, scenario: ScenarioForTesting) -> dict[str, list[tuple]]:

    if scenario in [
        ScenarioForTesting.complete_and_correct_no_cover_crops,
        ScenarioForTesting.complete_and_correct_with_end_field_practice_event,
        ScenarioForTesting.complete_and_correct_single_phase,
    ]:
        npos = get_scenario_npos(scenario)
        test_cropping_sequences = [
            _generate_ordinary_corn_cropping_for_year(2020, npos),
            _generate_ordinary_corn_cropping_for_year(2021, npos),
            _generate_ordinary_corn_cropping_for_year(2022, npos),
            _generate_ordinary_corn_cropping_for_year(2023, npos),
        ]
    elif scenario in [ScenarioForTesting.complete_and_correct_with_end_cover_crop_event]:
        npos = get_scenario_npos(scenario)
        test_cropping_sequences = [
            _generate_ordinary_corn_cropping_for_year(2020, npos),
            _generate_ordinary_corn_cropping_for_year(2021, npos),
            _generate_ordinary_corn_cropping_for_year(2022, npos),
            _generate_ordinary_corn_cropping_for_year(2023, npos),
            _generate_cover_cropping_at_year_end(2022),
        ]
    elif scenario in [ScenarioForTesting.missing_final_year_data]:
        npos = get_scenario_npos(scenario)
        test_cropping_sequences = [
            _generate_ordinary_corn_cropping_for_year(2020, npos),
            _generate_ordinary_corn_cropping_for_year(2021, npos),
        ]
    else:
        raise NotImplementedError(f"Unexpected scenario: {scenario}")
    return {field_id: test_cropping_sequences}


def generate_test_field_practice_events(field_id: str, scenario: ScenarioForTesting) -> dict[str, list[tuple]]:
    if scenario in [
        ScenarioForTesting.complete_and_correct_with_end_cover_crop_event,
        ScenarioForTesting.complete_and_correct_no_cover_crops,
        ScenarioForTesting.complete_and_correct_single_phase,
    ]:
        npos = get_scenario_npos(scenario)
        test_field_practice_events = []
        for year in [2020, 2021, 2022, 2023]:
            for year_practice in _generate_ordinary_field_practices_for_year(year, npos):
                test_field_practice_events.append(year_practice)
    elif scenario in [ScenarioForTesting.complete_and_correct_with_end_field_practice_event]:
        npos = get_scenario_npos(scenario)
        test_field_practice_events = []
        for year in [2020, 2021, 2022, 2023]:
            for year_practice in _generate_ordinary_field_practices_for_year(year, npos):
                test_field_practice_events.append(year_practice)
        test_field_practice_events.append(_generate_application_at_year_end(2022))
    elif scenario in [ScenarioForTesting.missing_final_year_data]:
        npos = get_scenario_npos(scenario)
        test_field_practice_events = []
        for year in [2020, 2021]:
            for year_practice in _generate_ordinary_field_practices_for_year(year, npos):
                test_field_practice_events.append(year_practice)
    else:
        raise NotImplementedError(f"Unexpected scenario: {scenario}")

    return {field_id: test_field_practice_events}


def _generate_ordinary_corn_cropping_for_year(
    year: int, npos: dict[EntityEventType, bool]
) -> tuple[SESEventWithContext]:
    crop_type = "corn"
    crop_id = crop_id_from_label(crop_type)
    sown_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    # terminated_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    harvested_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_commodity_harvest())
    planting_activity = (
        PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(sown_crop)
        .start(datetime(year=year, month=4, day=1, tzinfo=pytz.timezone("America/Chicago")))
        .event_and_context_pb()
    )
    ses_area = area_pb2.AreaMeasure(value=1.0, unit=area_pb2.AreaUnit.AREA_UNIT_HECTARE)
    mass_measure = mass_pb2.MassAreaMeasure(
        mass=mass_pb2.MassMeasure(value=60.0, unit=mass_pb2.MassUnit.MASS_UNIT_KILOGRAM), area=ses_area
    )
    harvested_crop.mass_yield(mass_measure)

    raw_residue_percentages = {"root": 0.75, "stem": 0.50, "leaf": 0.50, "grain": 0.01}
    residue_percentages = {part: fraction_pb2.Fraction(fraction=frac) for part, frac in raw_residue_percentages.items()}
    residue_fractions = harvest_pb2.CropFractions(**residue_percentages)
    harvested_crop.pb().residue_fractions.CopyFrom(residue_fractions)

    removed_percentages = {
        part: fraction_pb2.Fraction(fraction=(1.0 - frac)) for part, frac in raw_residue_percentages.items()
    }
    removed_fractions = harvest_pb2.CropFractions(**removed_percentages)
    harvested_crop.pb().removed_fractions.CopyFrom(removed_fractions)

    harvest_activity = (
        HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvested_crop)
        .start(datetime(year=year, month=11, day=1))
        .association_context(
            StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION,
            json.dumps(npos),
        )
        .event_and_context_pb()
    )
    return (
        SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
        SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
    )


def _generate_cover_cropping_at_year_end(year: int):
    crop_type = "basic_cover_crop"
    crop_id = crop_id_from_label(crop_type)
    sown_crop = PlantedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_cover())
    harvested_crop = HarvestedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_cover())
    terminated_crop = TerminatedCrop(crop_id=crop_id, crop_label=crop_type).purpose(crop_purpose_cover())

    planting_activity = (
        PlantingActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(sown_crop)
        .start(datetime(year=year, month=12, day=15, tzinfo=pytz.timezone("America/Chicago")))
        .event_and_context_pb()
    )
    harvest_activity = (
        HarvestActivity(event_id=str(uuid.uuid4()), user_id="1")
        .crop(harvested_crop)
        .start(datetime(year=year, month=12, day=31))
        .event_and_context_pb()
    )
    termination_activity = (
        (TerminationActivity(event_id=str(uuid.uuid4()), user_id="1"))
        .crop(terminated_crop)
        .start(datetime(year=year, month=12, day=31))
        .event_and_context_pb()
    )
    return (
        SESEventWithContext(event=planting_activity[0], context=planting_activity[1]),
        SESEventWithContext(event=harvest_activity[0], context=harvest_activity[1]),
        SESEventWithContext(event=termination_activity[0], context=termination_activity[1]),
    )


def _generate_ordinary_field_practices_for_year(
    year: int, npos: dict[EntityEventType, bool]
) -> list[SESEventWithContext]:
    year_practices = []
    if not npos[EntityEventType.IRRIGATION_EVENT]:
        year_practices.append(_generate_irrigation_for_year(year))
    if not npos[EntityEventType.APPLICATION_EVENT]:
        year_practices.append(_generate_application_for_year(year))
    if not npos[EntityEventType.TILLAGE_EVENT]:
        year_practices.append(_generate_tillage_for_year(year))
    return year_practices


def _generate_irrigation_for_year(year: int):
    irrigation_activity = (
        IrrigationActivity(event_id=new_event_id(), user_id="user-123")
        .geom("{}")
        .start(datetime(year, 1, 2, 0, 0))
        .end(datetime(year, 1, 2, 0, 0))
        .method_flood()
    )
    return SESEventWithContext(event=irrigation_activity.pb_event, context=irrigation_activity.pb_context)


def _generate_application_for_year(year: int) -> SESEventWithContext:
    uan_32 = BasicFertiliser(input_name="urea").product_rate(kilograms_per_hectare(1500)).pb()
    application_activity = (
        ApplicationFertigation(event_id=new_event_id(), user_id="user-123")
        .geom("{}")
        .start(datetime(year, 1, 3, 0, 0))
        .input(uan_32)
    )
    return SESEventWithContext(event=application_activity.pb_event, context=application_activity.pb_context)


def _generate_application_at_year_end(year: int) -> SESEventWithContext:
    uan_32 = BasicFertiliser(input_name="urea").product_rate(kilograms_per_hectare(1500)).pb()
    application_activity = (
        ApplicationFertigation(event_id=new_event_id(), user_id="user-123")
        .geom("{}")
        .start(datetime(year, 12, 15, 0, 0))
        .input(uan_32)
    )
    return SESEventWithContext(event=application_activity.pb_event, context=application_activity.pb_context)


def _generate_tillage_for_year(year: int) -> SESEventWithContext:
    depth_value = pb_value.depth_inches(1.5)
    tillage_activity = (
        (TillageActivity(event_id=new_event_id(), user_id="user-123").geom("{}").start(datetime(year, 1, 1, 0, 0)))
        .depth(depth_value)
        .soil_inversion(False)
    )
    return SESEventWithContext(event=tillage_activity.pb_event, context=tillage_activity.pb_context)


def get_scenario_npos(scenario: ScenarioForTesting):
    if scenario in [
        ScenarioForTesting.complete_and_correct_no_cover_crops,
        ScenarioForTesting.complete_and_correct_with_end_cover_crop_event,
        ScenarioForTesting.complete_and_correct_with_end_field_practice_event,
        ScenarioForTesting.missing_final_year_data,
        ScenarioForTesting.complete_and_correct_single_phase,
    ]:
        return {
            EntityEventType.TILLAGE_EVENT: False,
            EntityEventType.APPLICATION_EVENT: False,
            EntityEventType.IRRIGATION_EVENT: False,
        }
    else:
        raise NotImplementedError(f"Unexpected scenario: {scenario}")
