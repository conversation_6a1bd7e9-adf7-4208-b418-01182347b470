import uuid
from collections import defaultdict
from datetime import timedelta, timezone
from urllib.request import Request

from ses_client.client import Client
from ses_client.crop import crop_id_from_label
from ses_client.event import StructuredEvent
from ses_client.search import Filter

from config import get_settings
from cultivation_cycles.methods import get_unique_index_for_next_empty_cultivation_cycle
from defaults.attribute_options import <PERSON>ropUsage, TillagePractice
from defaults.consts import COVER_CROP_USAGES, RegrowCropName
from domain_event_bus.domain_event_bus import event_bus
from domain_event_bus.domain_events import BulkFieldEventsChanged
from entity_events.data_classes import EventCreationSpecification
from entity_events.events.enums import EntityEventType
from entity_events.events.schema import CroppingIDs
from entity_events.methods import get_entity_events_for_project_stages
from fields.db import (
    get_field_facts_for_fields,
    get_fields_orm_by_project_ids,
)
from fields.enums import EntityDataType, FieldDataState
from fields.schema import Field
from logger import get_logger
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from programs.enums import PracticeChange
from programs.model import Programs
from projects.db import get_owner_user_id_for_project
from ses_integration.constants import MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE
from ses_integration.migration.build_mrv_events_for_migration import (
    _convert_core_crop_names_to_regrow_names,
)
from ses_integration.no_practice_observations import (
    update_no_practice_observations_for_event,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.field_events.methods import create_update_field_events
from ui.projects.field_events.schema import (
    CultivationCycleResponse,
    CultivationCycleResponseId,
    FieldEvent,
    FieldEventRequest,
    NoPracticeObservations,
)
from values.enums import EntityTypeChoices

logger = get_logger(__name__)
settings = get_settings()
ses_client = Client(settings.SES_INTERNAL_URL_BASE, settings.SES_SEARCH_INTERNAL_URL_BASE)


async def prefill_monitoring_phase_commodity_crops_for_project(
    request: Request,
    program_id: int,
    project_id: int,
    intended_commodity_crops_stage_id: int,
    m_phase_id: int,
    acting_user_id: str,
) -> None:
    owner_user_id = await get_owner_user_id_for_project(request=request, project_id=project_id)
    fields = await get_fields_orm_by_project_ids(request=request, project_ids=[project_id])
    field_id_to_field: dict[int, Field] = {field.id: Field.from_orm(field) for field in fields}
    field_id_to_harvest_event_ids = await _save_intended_commodity_crop_events(
        request=request,
        field_id_to_field=field_id_to_field,
        project_id=project_id,
        intended_commodity_crops_stage_id=intended_commodity_crops_stage_id,
        m_phase_id=m_phase_id,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
    )
    if not field_id_to_harvest_event_ids:
        return
    await _save_tillage_no_practice_observations(
        request=request,
        field_id_to_harvest_event_ids=field_id_to_harvest_event_ids,
        field_id_to_field=field_id_to_field,
        program_id=program_id,
        m_phase_id=m_phase_id,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
    )
    await event_bus.publish(
        event=BulkFieldEventsChanged(project_id=project_id, field_ids=list(field_id_to_harvest_event_ids.keys())),
        request=request,
    )


async def _save_intended_commodity_crop_events(
    request: Request,
    field_id_to_field: dict[int, Field],
    project_id: int,
    intended_commodity_crops_stage_id: int,
    m_phase_id: int,
    acting_user_id: str,
    owner_user_id: str,
) -> dict[int, list[str]]:
    phase_stage_to_intended_events = await get_entity_events_for_project_stages(
        request=request,
        project_id=project_id,
        phase_type=PhaseTypes.ENROLMENT,
        stage_ids=[intended_commodity_crops_stage_id],
        entity_type=EntityTypeChoices.field,
        event_creation_specification=EventCreationSpecification(),
    )
    intended_events = phase_stage_to_intended_events.get(PhaseTypes.ENROLMENT, {}).get(
        StageTypes.INTENDED_COMMODITY_CROPS, []
    )
    field_id_to_harvest_event_ids: dict[int, list[str]] = defaultdict(list)
    for event in intended_events:
        field = field_id_to_field.get(event.entity_id)
        if not field:
            continue
        planting_date = event.get_interval_start_or_occurred_at()
        harvest_date = event.get_interval_end_or_occurred_at()
        if not planting_date and not harvest_date:
            continue
        try:
            await _convert_core_crop_names_to_regrow_names(crop_events=[event])
        except Exception as e:
            logger.warning(e)
            continue
        field_md5_to_existing_events = await ses_client.fetch_events_for_fields(
            field_ids=[field.md5],
            search_filter=Filter(
                event_types=[StructuredEvent.TYPE_HARVEST_ACTIVITY],
                interval_from=harvest_date - timedelta(days=1),
                interval_to=harvest_date + timedelta(days=1),
                user_ids=[owner_user_id],
                min_overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
            ),
        )
        existing_events = field_md5_to_existing_events.get(field.md5)
        if existing_events and any(
            e.harvest_activity.crops
            and e.harvest_activity.crops[0].crop.id == crop_id_from_label(crop_label=event.crop_type)
            for e in existing_events
        ):
            continue
        field_event_request = FieldEventRequest(
            id=None,
            event_values={
                AttributeTypes.crop_type: event.crop_type,
                AttributeTypes.crop_usage: event.crop_usage,
                AttributeTypes.planting_date: planting_date,
                AttributeTypes.harvest_date: harvest_date,
            },
            type=EntityEventType.CROPPING_EVENT,
        )
        field_event_responses = await create_update_field_events(
            request=request,
            events=[field_event_request],
            project_id=project_id,
            phase_id=m_phase_id,
            field_id=event.entity_id,
            acting_user_id=acting_user_id,
        )
        for res in field_event_responses:
            if not res.succeeded:
                logger.warning(
                    f"Could not create event from intended commodity crop for field {event.entity_id}: {res.error_detail}"
                )
                continue
            harvest_event_id = res.id.harvesting_id_as_str()
            field_id_to_harvest_event_ids[event.entity_id].append(harvest_event_id)
    return field_id_to_harvest_event_ids


async def _save_tillage_no_practice_observations(
    request: Request,
    field_id_to_harvest_event_ids: dict[int, list[str]],
    field_id_to_field: dict[int, Field],
    program_id: int,
    m_phase_id: int,
    acting_user_id: str,
    owner_user_id: str,
) -> None:
    field_ids = list(field_id_to_harvest_event_ids.keys())
    field_id_to_assigned_practices = await get_field_facts_for_fields(
        request=request,
        field_ids=field_ids,
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )
    for field_id, assigned_practices in field_id_to_assigned_practices.items():
        assigned_practices = assigned_practices[0]
        for practice in assigned_practices:
            if practice != PracticeChange.no_till:
                continue
            field = field_id_to_field[field_id]
            harvest_event_ids = field_id_to_harvest_event_ids[field_id]
            for event_id in harvest_event_ids:
                await update_no_practice_observations_for_event(
                    event_id=event_id,
                    no_practice_observations={EntityEventType.TILLAGE_EVENT: True},
                    field=field,
                    acting_user_id=acting_user_id,
                    owner_user_id=owner_user_id,
                    ses_client=ses_client,
                )


def prefill_monitoring_phase_cover_crops_or_tillage_for_field(
    field_id: int,
    cultivation_cycles: list[CultivationCycleResponse],
    stage_type: StageTypes,
    program: Programs,
    field_id_to_stage_completion: dict[int, NarrowestCompletionStatus],
    field_id_to_assigned_practices: dict[int, list[PracticeChange]],
) -> list[CultivationCycleResponse]:
    if field_id_to_stage_completion.get(field_id) == NarrowestCompletionStatus.complete:
        return cultivation_cycles

    reporting_period_start_date = program.reporting_period_start_date.replace(tzinfo=timezone.utc)
    reporting_period_end_date = program.reporting_period_end_date.replace(tzinfo=timezone.utc)
    for cycle in cultivation_cycles:
        if not reporting_period_start_date <= cycle.end <= reporting_period_end_date:
            continue
        for event in cycle.events:
            if _is_prefillable_event_in_stage(event=event, stage_type=stage_type):
                return cultivation_cycles

    assigned_practices = field_id_to_assigned_practices.get(field_id, [])
    field_events = []
    for practice in assigned_practices:
        field_event = _generate_field_event_from_assigned_practice(assigned_practice=practice)
        if not field_event:
            continue
        if _is_prefillable_event_in_stage(event=field_event, stage_type=stage_type):
            field_events.append(field_event)
    if not field_events:
        return cultivation_cycles

    cycles_end = max(cycle.end for cycle in cultivation_cycles)
    start_date = cycles_end + timedelta(minutes=1)
    end_date = cycles_end + timedelta(days=2)
    empty_cycle = CultivationCycleResponse(
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=end_date.year,
            index=get_unique_index_for_next_empty_cultivation_cycle(
                harvest_year=end_date.year, previous_cultivation_cycle=cultivation_cycles[0]
            ),
            phase_id=cultivation_cycles[0].id.phase_id,
            stage_id=cultivation_cycles[0].id.stage_id,
        ),
        start=start_date,
        end=end_date,
        events=field_events,
        no_practice_observations=NoPracticeObservations(),
        contains_prefilled_monitoring_phase_events=True,
    )
    return [empty_cycle] + cultivation_cycles


def _generate_field_event_from_assigned_practice(assigned_practice: PracticeChange) -> FieldEvent | None:
    if assigned_practice == PracticeChange.cover_crops:
        return FieldEvent(
            id=CroppingIDs(harvesting_id=uuid.uuid4()),
            event_values={AttributeTypes.crop_usage: CropUsage.COVER},
            type=EntityEventType.CROPPING_EVENT,
            is_prefilled=True,
        )
    elif assigned_practice == PracticeChange.basic_cover_crops:
        return FieldEvent(
            id=CroppingIDs(harvesting_id=uuid.uuid4()),
            event_values={
                AttributeTypes.crop_type: RegrowCropName.basic_cover_crop,
                AttributeTypes.crop_usage: CropUsage.COVER,
            },
            type=EntityEventType.CROPPING_EVENT,
            is_prefilled=True,
        )

    elif assigned_practice == PracticeChange.premium_cover_crops:
        return FieldEvent(
            id=CroppingIDs(harvesting_id=uuid.uuid4()),
            event_values={
                AttributeTypes.crop_type: RegrowCropName.premium_cover_crop_mix,
                AttributeTypes.crop_usage: CropUsage.COVER,
            },
            type=EntityEventType.CROPPING_EVENT,
            is_prefilled=True,
        )
    elif assigned_practice == PracticeChange.reduced_till:
        return FieldEvent(
            id=uuid.uuid4(),
            event_values={
                AttributeTypes.tillage_practice: TillagePractice.reduced_till,
            },
            type=EntityEventType.TILLAGE_EVENT,
            is_prefilled=True,
        )
    elif assigned_practice == PracticeChange.conventional_till:
        return FieldEvent(
            id=uuid.uuid4(),
            event_values={
                AttributeTypes.tillage_practice: TillagePractice.conventional_till,
            },
            type=EntityEventType.TILLAGE_EVENT,
            is_prefilled=True,
        )
    return None


def _is_prefillable_event_in_stage(event: FieldEvent, stage_type: StageTypes) -> bool:
    if (
        event.type == EntityEventType.CROPPING_EVENT
        and event.event_values.get(AttributeTypes.crop_usage) in COVER_CROP_USAGES
        and stage_type == StageTypes.CROP_EVENTS
    ):
        return True
    elif event.type == EntityEventType.TILLAGE_EVENT and stage_type == StageTypes.TILLAGE_EVENTS:
        return True
    return False
