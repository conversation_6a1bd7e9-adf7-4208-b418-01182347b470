import json
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional

from google.protobuf.json_format import MessageToJson
from google.protobuf.timestamp_pb2 import Timestamp
from regrow.ses.context.v1 import context_pb2
from regrow.ses.crop.v1 import crop_pb2
from regrow.ses.event.v1 import event_pb2
from regrow.ses.pbtype import interval_pb2


@dataclass
class EventWithContext:
    """EventWithContext is a dataclass that holds an event and its context."""

    event: Optional[event_pb2.Event] = None
    context: Optional[context_pb2.EventContext] = None


class StructuredEvent:
    """Base class for creating events."""

    TYPE_TILLAGE_ACTIVITY = "tillageActivity"
    TYPE_SOWING_ACTIVITY = "sowingActivity"
    TYPE_PLANTING_ACTIVITY = "plantingActivity"
    TYPE_APPLICATION_ACTIVITY = "applicationActivity"
    TYPE_HARVEST_ACTIVITY = "harvestActivity"
    TYPE_IRRIGATION_ACTIVITY = "irrigationActivity"
    TYPE_FALLOW_PERIOD = "fallowPeriod"
    TYPE_TERMINATION_ACTIVITY = "terminationActivity"

    # Note: these are acceptable context keys for one or more context types: creation, source, archival and association
    # They are specified in the service configuration via env vars:
    # SES_CONTEXT_KEYS_CREATION: regrowUserId,regrowService,regrowServiceId
    # SES_CONTEXT_KEYS_SOURCE: regrowUserId,fms,fmsEntityId,regrowService,regrowServiceId
    # SES_CONTEXT_KEYS_ARCHIVAL: regrowUserId,regrowService,regrowServiceId
    # SES_CONTEXT_KEYS_ASSOCIATION: regrowService,regrowServiceId,noPracticeObservation
    CONTEXT_KEY_REGROW_USER_ID = "regrowUserId"
    CONTEXT_KEY_NO_PRACTICE_OBSERVATION = "noPracticeObservation"

    def __init__(self, event_id: str, user_id: str | None = None):
        """Initialize the New Event with an existing id, otherwise a new id will be generated."""
        self.pb_event = event_pb2.Event()
        self.pb_context = context_pb2.EventContext()

        self.pb_event.id = event_id

        # If a user id is supplied we can set the context here
        if user_id:
            self.user(user_id)

    def _creation_context(self, key: str, val: str):
        """Add the key-value pair to the event's creation context."""
        self.pb_context.creation.update({key: val})
        return self

    def _source_context(self, key: str, val: str):
        """Add the key-value pair to the event's source context."""
        self.pb_context.source.update({key: val})
        return self

    def _archival_context(self, key: str, val: str):
        """Add the key-value pair to the event's archival context."""
        self.pb_context.archival.update({key: val})
        return self

    def _association_context(self, key: str, val: str):
        """Add the key-value pair to the event's association context."""
        self.pb_context.association.update({key: val})
        return self

    def user(self, user_id: str):
        """Set the event creation and source context to the specified user id."""
        self._creation_context(self.CONTEXT_KEY_REGROW_USER_ID, user_id)
        self._source_context(self.CONTEXT_KEY_REGROW_USER_ID, user_id)
        return self

    def no_practice_observation(self, observations: str | dict):
        """Set the event association context to the specified no practice observation."""
        # If we have a dict, convert it to a proper JSON string as context values can only be strings.
        if isinstance(observations, dict):
            observations = json.dumps(observations)
        self._association_context(
            self.CONTEXT_KEY_NO_PRACTICE_OBSERVATION, observations
        )
        return self

    def association_context(self, key: str, val: str):
        """Add the key-value pair to the event's association context."""
        self.pb_context.association.update({key: val})
        return self

    def geom(self, geojson: str):
        """Set the target geometry for the event"""
        self.pb_event.geojson = geojson
        return self

    def start(self, dt: datetime):
        """Set the event start and end times"""
        interval = interval_pb2.Interval(
            start_time=event_timestamp(dt), end_time=event_timestamp(dt)
        )
        self.pb_event.interval.CopyFrom(interval)
        return self

    def end(self, dt: datetime):
        """Set the event end time [optional"""
        # ensure start time is set before setting end time
        if not self.pb_event.interval.HasField("start_time"):
            raise ValueError("StructuredEvent start time must be set before end time.")
        interval = interval_pb2.Interval(
            start_time=self.pb_event.interval.start_time, end_time=event_timestamp(dt)
        )
        self.pb_event.interval.CopyFrom(interval)
        return self

    def cropping_period_identifier(self, period_id: str):
        """Add a cropping period identifier"""
        self.pb_event.cropping_period_identifier = period_id
        return self

    def validate(self):
        """Validate the event"""
        if not is_valid_uuid(self.pb_event.id):
            raise ValueError("event id is not a valid UUID")
        if not self.pb_event.id or self.pb_event.id == "":
            raise ValueError("event id is not set")
        if not self.pb_event.geojson or self.pb_event.geojson == "":
            raise ValueError("event target geojson is not set")
        if not self.pb_event.interval.HasField("start_time"):  # Use HasField here
            raise ValueError("event start time is not set")
        if self.pb_event.cropping_period_identifier and not is_valid_uuid(
            self.pb_event.cropping_period_identifier
        ):
            raise ValueError("event id is not a valid UUID")
        return self

    def event_and_context_pb(self):
        """Return the event as a protobuf message"""
        return self.pb_event, self.pb_context

    def string(self):
        """Return the event as a string"""
        return MessageToJson(self.pb_event)


def new_event_id() -> str:
    """Generate a new event id"""
    return str(uuid.uuid4())


def is_valid_uuid(uuid_str: str) -> bool:
    """Check if a string is a valid UUID"""
    try:
        uuid.UUID(uuid_str)
        return True
    except ValueError:
        return False


def event_timestamp(dt: datetime):
    """Create a timestamp for an event"""
    ts = Timestamp()
    ts.FromDatetime(dt)
    return ts


def event_type(event: event_pb2.Event) -> str | None:
    """Return the event type"""
    # This would be good, but is not implemented in the current buf plugin that generates python.
    # See: https://buf.build/protocolbuffers/python
    # return event.WhichOneof("data")
    if event.HasField("tillage_activity"):
        return StructuredEvent.TYPE_TILLAGE_ACTIVITY
    elif event.HasField("sowing_activity"):
        return StructuredEvent.TYPE_SOWING_ACTIVITY
    elif event.HasField("planting_activity"):
        return StructuredEvent.TYPE_PLANTING_ACTIVITY
    elif event.HasField("application_activity"):
        return StructuredEvent.TYPE_APPLICATION_ACTIVITY
    elif event.HasField("harvest_activity"):
        return StructuredEvent.TYPE_HARVEST_ACTIVITY
    elif event.HasField("irrigation_activity"):
        return StructuredEvent.TYPE_IRRIGATION_ACTIVITY
    elif event.HasField("fallow_period"):
        return StructuredEvent.TYPE_FALLOW_PERIOD
    elif event.HasField("termination_activity"):
        return StructuredEvent.TYPE_TERMINATION_ACTIVITY
    else:
        return None


def get_cropping_period_identifier(event: event_pb2.Event) -> str | None:
    """Get the cropping period identifier as a UUID or None if not defined"""
    if event.cropping_period_identifier == "":
        return None
    return event.cropping_period_identifier


def has_crop(event: event_pb2.Event) -> bool:
    """Return True if the event is a type that can have a crop."""
    return event_type(event) in [
        StructuredEvent.TYPE_SOWING_ACTIVITY,
        StructuredEvent.TYPE_PLANTING_ACTIVITY,
        StructuredEvent.TYPE_HARVEST_ACTIVITY,
    ]


def has_matching_crop_purpose(
    ev: event_pb2.Event, crop_purposes: List[crop_pb2.CropPurpose]
) -> bool:
    """Return True if the event is a type that has a crop with the specified purpose."""
    if not has_crop(ev):
        return False
    if ev.HasField("sowing_activity"):
        for sown_crop in ev.sowing_activity.crops:
            if any(purpose in crop_purposes for purpose in sown_crop.crop.purpose):
                return True

    if ev.HasField("planting_activity"):
        for planted_crop in ev.planting_activity.crops:
            if any(purpose in crop_purposes for purpose in planted_crop.crop.purpose):
                return True

    if ev.HasField("harvest_activity"):
        for harvested_crop in ev.harvest_activity.crops:
            if any(purpose in crop_purposes for purpose in harvested_crop.crop.purpose):
                return True

    return False


def _short_event(event: event_pb2.Event, geom_overlap_fraction: float = 1.0) -> dict:
    """Return the event as a short event string suitable for the adding to the event-search cache.
    Note: This is really only for testing purposes.

    Args:
        event: The event protobuf object
        geom_overlap_fraction: Overlap as fraction (0.0-1.0), will be converted to percentage (0-100)
    """
    data_type = event_type(event)
    if data_type is None:
        raise ValueError("Event data type not set")

    # Convert fraction to percentage to match what event-srvc produces
    overlap_percentage = geom_overlap_fraction * 100.0

    short_event = {
        "event_id": event.id,
        "type": data_type,
        "created": event.created.ToJsonString(),
        "revision": event.revision,
        "start_time": event.interval.start_time.ToJsonString(),
        "end_time": event.interval.end_time.ToJsonString(),
        "overlap": overlap_percentage,
    }

    if event.HasField("archived"):
        short_event["archived"] = event.archived.ToJsonString()

    return short_event


def sort_list_by_interval_start_time(
    event_list: list[event_pb2.Event],
) -> list[event_pb2.Event]:
    """Sort a list of events by their start date"""
    return sorted(event_list, key=lambda x: x.interval.start_time.ToDatetime())
