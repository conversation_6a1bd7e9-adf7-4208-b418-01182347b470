from __future__ import annotations

import asyncio
import base64
import json
import math
import traceback
from collections import defaultdict
from dataclasses import dataclass
from datetime import date, datetime, UTC
from itertools import chain, groupby
from typing import Any, Collection, Coroutine, Generator, Type, TYPE_CHECKING

import elasticapm
import pdfkit
import sqlalchemy as sa
from babel.dates import format_datetime
from docusign_esign import Envelope
from fastapi import HTTPException, Query, status
from httpx import Response
from pydantic import ValidationError
from sqlalchemy import and_, exc, func
from sqlalchemy.exc import NoResultFound
from sqlalchemy.future import select

import core.consts as core_const
import ses_integration.reconcile_event_associations
from boundaries_service.schema import FeatureIntersections, Intersection
from config import get_settings
from core import methods as core_methods
from core.enums import MeasurementUnits
from core.methods import get_user_info
from core.schema import User
from docusign.controller import Docusign, get_document
from docusign.enums import EnvelopeDeletionMethod, EnvelopeStatus
from docusign.model import DocusignAccess
from docusign.schema import DocusignAccessDetailsResponse, FieldData
from domain_event_bus.domain_event_bus import event_bus
from domain_event_bus.domain_events import FieldBoundaryUpdatedEvent, FieldsCreatedEvent
from entity_events.validation.annotations import StageValidationErrors
from entity_events.validation.validator import AttributeValuesValidator
from fields import schema as fields_schema
from fields.baselines.methods import get_or_create_fields_baselines
from fields.db import (
    create_fields_history,
    db_create_fields,
    get_fields_by_fs_field_id,
    get_fields_by_md5s_and_program_id,
    get_fields_by_md5s_and_project_id,
    get_fields_by_project_id,
    update_field,
)
from fields.enums import (
    FieldBoundaryCheckErrorLevel,
    FieldBoundaryCheckRule,
    FieldHistoryChangeReason,
    OptisStatusChoices,
)
from fields.methods import (
    back_fill_field_status,
    check_field_boundaries,
    generate_field_histories,
    generate_field_updates,
    get_core_field_from_mrv_field,
    parse_core_fields_response,
    undelete_fields,
)
from fields.model import Fields
from fields.schema import (
    CoreFarmGroupRequest,
    CoreField,
    CreateField,
    EventValue,
    FarmRequest,
    FieldBasicResponse,
    FieldBoundaryConfigsByRule,
    FieldBoundaryRuleConfig,
    FieldBoundaryRuleResponse,
    FieldBoundaryRuleResult,
    UpdatedFieldResponse,
    UpdateFieldBoundary,
    UpdateFieldRequest,
    UploadCoreBoundary,
    UploadCoreField,
)
from helper import async_tools, external_api
from helper.async_tools import Tasks
from helper.helper import run_query, UserInfoRetriever, UserSyncInfoRetriever
from helper.schema import (
    CoreFieldsResponse,
    CoreFieldT,
    CreateCoreFieldResponse,
    UploadKMLResponse,
)
from http_requests.callbacks.constants import M2_PER_HECTARE
from http_requests.tasks import send_optis_callback_notification
from logger import get_logger
from notifications.enums import NotificationEntityTypes
from notifications.messages import (
    FIELD_ALL_DONE,
    FIELD_ERROR,
    FIELD_NO_DATA,
    PROJECT_ALL_DONE,
)
from notifications.methods import get_notifications, mark_dismissed
from paperform.methods import get_submission_pdf
from permissions.db import get_role_users_by_user_id
from permissions.methods import is_original_user_super_admin, is_super_admin
from phases import enums as phase_enums
from phases.constants import ALLOWED_ATTRIBUTES_FOR_PREFILLING
from phases.db import (
    get_attribute_id_stage_id_map_by_stage_ids,
    get_attribute_type_by_id,
    get_phase_by_id,
    get_phase_date_window_by_type,
    get_phase_from_project_id,
    get_stage_with_prefill_optis_option_and_ui_years_by_program_id,
)
from phases.enums import AttributeTypes, EligibilityTypes, PhaseTypes, StageTypes
from phases.methods import (
    get_eligibility_type,
    get_enabled_stages_by_phase_id,
    get_phases_by_ids_and_type_,
    get_phases_by_program_id_and_type_,
    get_stage_id_attribute_id_for_record_year,
    get_stages,
    get_stages_by_ids,
)
from phases.model import Attribute, Phases, Stage
from phases.schema import (
    AttributeResponse,
    DependencyGroup,
    DependencyObject,
    PhaseRequestCreate,
    PhaseResponse,
    StageResponse,
    StageYearWithId,
)
from programs import utils
from programs.db import (
    get_geoms_from_program_id,
    get_program_by_id,
    get_program_by_project_id,
)
from programs.enums import PayoutStructure, PracticeChange, UnitsTypes
from programs.methods import (
    check_program_enrolment_open,
    get_fields_stats,
    get_practice_change_set_by_program_id,
    get_program,
    get_translated_default_boundary_rule_message,
)
from programs.model import (
    BoundaryRuleConfig,
    BoundaryRuleDeviation,
    ProgramFieldOverlap,
    ProgramPermissions,
    Programs,
)
from programs.monitor_config.methods import compute_monitor_api_version
from programs.schema import ProgramPermission, ProgramResponseStats
from projects import db, paths, tasks
from projects.classes.practice_change_rule_validator import PracticeChangeRuleValidator
from projects.classes.prefill_data_creator import PrefillDataCreator
from projects.classes.year_row_data_creator import YearRowDataCreator
from projects.commercials.shortcuts import (
    is_pay_per_area_contract_line_items_invalidated,
    is_pay_per_practice_contract_line_items_invalidated,
)
from projects.consts import (
    FIELD_BOUNDARY_CHECK_ERROR,
    MAX_AREA_HA,
    MAX_AREA_HA_SUPER_ADMIN,
)
from projects.dataclasses import RequestStoreIdMd5OptisRespMapPair
from projects.db import (
    back_fill_deleted_project_status,
    back_fill_enrolled_project_status_from_phase_completion,
    back_fill_generated_contract_status,
    back_fill_project_contract_status,
    back_fill_project_status_from_contract_status,
    back_fill_signed_contract_status,
    back_fill_voided_contract_status,
    delete_contract_by_ids,
    delete_contract_line_items_for_contract,
    get_percentage_completion,
    get_project_by_id,
    get_project_contract,
    get_project_contract_latest_signed,
    get_project_phase_completion as db_get_project_phase_completion,
    get_projects as db_get_projects,
    set_project_show_email_flag,
    upsert_project_phase_completion,
)
from projects.dndc_methods import (
    calculate_dndc_status,
    handle_invalidation_of_dndc_results,
)
from projects.eligibility.cargill_cotton_2022 import run_cargill_cotton_2022
from projects.eligibility.cargill_cotton_2023 import run_cargill_cotton_2023
from projects.eligibility.cargill_cotton_2024 import run_cargill_cotton_2024
from projects.eligibility.cargill_eu_2023 import run_cargill_eu_2023
from projects.eligibility.cargill_eu_2024 import run_cargill_eu_2024
from projects.eligibility.cargill_grain_2022 import run_cargill_grain_2022
from projects.eligibility.cargill_grain_2023 import run_cargill_grain_2023
from projects.eligibility.cargill_grain_2024 import run_cargill_grain_2024
from projects.eligibility.cargill_grain_2025 import run_cargill_grain_2025
from projects.eligibility.eligibility import flatten_eligible_practices
from projects.enums import (
    ContractStatus,
    ProjectStatus,
    ProjectValueTypes,
    StructuredOrderKeys,
    StructuredSortKeys,
)
from projects.farms.methods import _create_project_farms
from projects.model import (
    ProjectContractLineItems,
    ProjectContracts,
    ProjectContractsNoBlob,
    ProjectPermissions,
    Projects,
    ProjectValues,
)
from projects.monitor.constants import (
    MONITOR_CONVENTIONAL_TILLAGE_VALUE,
    MONITOR_REDUCED_TILLAGE_VALUE,
)
from projects.monitor.enums import (
    MonitorApiVersion,
    MonitorEventType,
    RequestStoreStatus,
)
from projects.monitor.event_readers.factory import READER_FACTORY
from projects.monitor.methods import (
    create_if_not_exists_new_response_stores,
    get_latest_request_group_by_project_id,
    get_latest_request_stores_by_project_id,
    get_request_store_by_id,
    get_request_store_by_job_id,
    get_request_stores_by_project_id,
    get_response_store_by_project_and_fields,
    get_response_store_by_request_store_id_and_md5s,
    update_existing_request_group,
    update_request_store_by_id,
    update_response_store_processed_at_and_error,
)
from projects.run_optis import run_optis_v2
from projects.schema import (
    EntityAttributeCompletion,
    NewUserProject,
    OptisFieldV2,
    ProjectConfigWithPermissionsRequest,
    ProjectContractCreateRequest,
    ProjectContractsNoBlobResponse,
    ProjectDocusignData,
    ProjectGeom,
    ProjectNoProgram,
    ProjectPermission,
    ProjectPermissionRequestBasic,
    ProjectPermissionsCreationResponse,
    ProjectPhaseCompletionResponse,
    ProjectStageCompletion,
    StageValuesResponseStructured,
    StructuredValue,
    UpdateValuesResponse,
    ValueResponseStructured,
    ValuesResponseStructured,
)
from projects.user_groups.db import update_project_groups
from projects.utils import (
    create_or_update_contract,
    is_allowed_enrolment_practice,
    update_project_with_docusign_details_to_null,
)
from projects.validators import validate_entity_ids
from root_crud import create, delete, get, update
from scenarios_service.methods import (
    create_notification_for_results_invalidation,
)
from scenarios_service.schema import DndcResultsOutputPrice
from ui.projects.completion.constants import SUPPORTED_FIELD_EVENT_STAGE_TYPES
from ui.projects.completion.handle_project_stage_updates import (
    obtain_stage_completion_for_project,
)
from values import crud as values_crud
from values.crud import delete_field_values, update_values
from values.enums import EntityTypeChoices, ImportDataSources, ProgressChoices
from values.model import Values
from values.schema import ValuesRequest, ValuesResponseWithFieldID

if TYPE_CHECKING:
    from fastapi import Request

    from phases.schema import AttributeIdType, StageYearAttributesEntityType
    from projects.monitor.event_readers.base import BaseEventReader
    from projects.monitor.model import RequestStore
    from projects.schema import FieldEligiblity, RowData

logger = get_logger(__name__)
settings = get_settings()


def min_stage_year(
    stages: list[StageResponse],
    start_year: int | None = None,
    end_year: int | None = None,
) -> tuple[int, int]:
    if not start_year or not end_year:
        stage_years = set(
            chain.from_iterable((i.fmi_import_start_date.year, i.fmi_import_end_date.year) for i in stages)
        )
        start_year = min(stage_years) if not start_year else start_year
        end_year = max(stage_years) if not end_year else start_year
    return (start_year, end_year)


@elasticapm.async_capture_span()
async def get_num_fields(request: Request, project_ids: list[int]) -> int:
    async with request.state.sql_session() as s:
        query = select(func.count(sa.distinct(Fields.id))).where(Fields.parent_project_id.in_(project_ids))
        res = await run_query(query=query, s=s)
        tmp = list(res.all())
        return tmp[0]


@elasticapm.async_capture_span()
async def get_field_ids(
    request: Request,
    project_id: int,
    created_before: datetime | None = None,
) -> list[int]:
    async with request.state.sql_session() as s:
        query = select(Fields.id).where(Fields.parent_project_id == project_id)
        if created_before:
            query = query.filter(Fields.created_at > created_before)
        res = await run_query(query=query, s=s)
        return list(res.scalars().all())


@elasticapm.capture_span()
def generate_attribute_values_map(
    values: list[ValuesResponseWithFieldID],
) -> dict[int, list[Any]]:
    """Return a map of attribute_id to all his values."""
    attributes_values_map = {}

    def grouper(item: ValuesResponseWithFieldID) -> int:
        return item.attribute_id

    for attr_id, grouped_values in groupby(sorted(values, key=grouper), key=grouper):
        attributes_values_map[attr_id] = list(grouped_values)
    logger.debug(attributes_values_map)

    return attributes_values_map


def generate_row_map(
    attributes: list[Attribute],
    attributes_values_map: dict[int, list[ValuesResponseWithFieldID]],
    bad_tuples: set[tuple[int, int]],
) -> dict[int, int]:
    """Return a map of field_id to the largest row number of the corresponding values
    passed in except rows in the bad_tuples.
    """
    row_map: dict[int, int] = {}

    def grouper(item: ValuesResponseWithFieldID) -> int:
        return item.field_id

    for att in attributes:
        for field_id, grouped_values_iter in groupby(
            sorted(attributes_values_map.get(att.id, []), key=grouper), key=grouper
        ):
            grouped_values = list(grouped_values_iter)
            rows_number = len(grouped_values)
            rows_in_bad_tuples = sum(1 for x in grouped_values if (x.row_id, field_id) in bad_tuples)
            max_rows_number_candidate = rows_number - rows_in_bad_tuples
            if max_rows_number_candidate > row_map.get(field_id, 0):
                row_map[field_id] = max_rows_number_candidate

    return row_map


@dataclass
class ProjStageAttVals:
    field_id: int
    attribute_id: int
    attribute_type: AttributeTypes
    row_id: int
    value: Any
    locked: bool
    confirmed: bool
    dependencies: DependencyObject = None


async def get_survey_completion(request: Request, project_id: int, stage_id: int) -> ProjectStageCompletion:
    query = (
        select(ProjectValues.value)
        .where(ProjectValues.project_id == project_id)
        .where(ProjectValues.stage_id == stage_id)
        .where(ProjectValues.type_ == ProjectValueTypes.survey.value)
        .where(ProjectValues.key == "completed")
        .order_by(ProjectValues.id.desc())  # There are instances where more than one survey result exists
        .limit(1)  # Just get the most recent
    )
    async with request.state.sql_session() as s:
        try:
            completed = (await run_query(s, query)).one()[0]
        except IndexError:
            return ProjectStageCompletion(
                completed=0,
                to_complete=1,
                total=1,
                is_completed=False,
                percentage_complete=0,
            )
        except exc.NoResultFound:
            return ProjectStageCompletion(
                completed=0,
                to_complete=2,
                total=2,
                is_completed=False,
                percentage_complete=0,
            )
        except Exception as e:
            raise e
        completed = int(completed) + 1
        percentage_complete = 0
        if completed == 2:
            percentage_complete = 100
        elif completed == 1:
            percentage_complete = 50

        return ProjectStageCompletion(
            completed=completed,
            to_complete=2 - completed,
            total=2,
            is_completed=bool(completed == 2),
            percentage_complete=percentage_complete,
        )


async def get_field_id_eligibility_map(
    request: Request, project_id: int, stage_id: int, eligibility_method: EligibilityTypes
) -> dict[int, FieldEligiblity] | None:
    # if method is cotton or grain 2022, run the eligibility checks based on eligible fields count
    if eligibility_method in [
        EligibilityTypes.CARGILL_COTTON_2022,
        EligibilityTypes.CARGILL_COTTON_2023,
        EligibilityTypes.CARGILL_COTTON_2024,
        EligibilityTypes.CARGILL_GRAIN_2022,
        EligibilityTypes.CARGILL_GRAIN_2023,
        EligibilityTypes.CARGILL_GRAIN_2024,
        EligibilityTypes.CARGILL_GRAIN_2025,
        EligibilityTypes.CARGILL_EU_2023,
        EligibilityTypes.CARGILL_EU_2024,
        EligibilityTypes.CUSTOM,
        EligibilityTypes.PARAMETERISED_ELIGIBILITY,
    ]:
        start_date = await get_enrolment_start_date(request, project_id)
        if eligibility_method == EligibilityTypes.CARGILL_GRAIN_2022:
            fields = await run_cargill_grain_2022(request, project_id, start_date)
        elif eligibility_method == EligibilityTypes.CARGILL_COTTON_2022:
            fields = await run_cargill_cotton_2022(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_COTTON_2023:
            fields = await run_cargill_cotton_2023(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_COTTON_2024:
            fields = await run_cargill_cotton_2024(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_EU_2023:
            fields = await run_cargill_eu_2023(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_EU_2024:
            fields = await run_cargill_eu_2024(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_GRAIN_2023:
            fields = await run_cargill_grain_2023(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_GRAIN_2024:
            fields = await run_cargill_grain_2024(request, project_id)
        elif eligibility_method == EligibilityTypes.CARGILL_GRAIN_2025:
            fields = await run_cargill_grain_2025(request, project_id)
        else:
            from projects.eligibility.eligibility import custom_eligibility_runner

            eligibility_details = await get_eligibility_type(request=request, stage_id=stage_id)
            fields = await custom_eligibility_runner(
                request=request,
                project_id=project_id,
                stage_id=stage_id,
                phase_id=eligibility_details.phase_id,
                entity_type=eligibility_details.entity_type,
                default_baseline_year=eligibility_details.baseline_year,
            )

        # Flatten the eligible practices and add always eligible practices as options
        if eligibility_method == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
            from programs.methods import get_practice_changes_by_program_id

            # Flatten the eligible practices, also append the always eligible practices
            program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
            all_program_practices = await get_practice_changes_by_program_id(request=request, program_id=program_id)
            always_eligible_practices = [
                practice.practice_change for practice in all_program_practices if practice.always_eligible
            ]
            fields = flatten_eligible_practices(
                eligibilities=fields,
                always_eligible_practices=always_eligible_practices,
            )
        return fields
    return None


# completion for eligibility check stage is ratio of eligible fields / all fields
async def get_eligibility_completion(request: Request, project_id: int, stage_id: int) -> ProjectStageCompletion | None:
    eligibility_method = await db.get_eligibility_method(request, stage_id)
    if eligibility_method is None:
        return ProjectStageCompletion(
            completed=0,
            to_complete=1,
            total=1,
            is_completed=False,
            percentage_complete=0,
        )

    fields = await get_field_id_eligibility_map(
        request=request, project_id=project_id, stage_id=stage_id, eligibility_method=eligibility_method
    )
    if fields:
        eligible_fields_count = 0
        for field_eligibility_values in fields.values():
            is_eligible = (
                field_eligibility_values.eligible
                if field_eligibility_values.eligible is not None
                else len(field_eligibility_values.eligible_practices) > 0
            )
            eligible_fields_count += 1 if is_eligible else 0

        try:
            percentage_complete = math.floor(eligible_fields_count / len(fields) * 100)
        except ZeroDivisionError:
            percentage_complete = 0
        is_completed = bool(percentage_complete == 100)
        to_complete = len(fields) - eligible_fields_count
        total = len(fields)
        # check zero cases
        if total == 0:
            is_completed = False

        return ProjectStageCompletion(
            completed=eligible_fields_count,
            to_complete=to_complete,
            total=total,
            is_completed=is_completed,
            percentage_complete=percentage_complete,
        )
    # if always true method, then just return completed stats
    elif eligibility_method == EligibilityTypes.ELIGIBILITY_ALWAYS_TRUE:
        return ProjectStageCompletion(
            completed=1,
            to_complete=0,
            total=1,
            is_completed=True,
            percentage_complete=100,
        )
    return ProjectStageCompletion(
        completed=0,
        to_complete=0,
        total=0,
        is_completed=False,
        percentage_complete=0,
    )


async def get_view_outcomes_completion(request: Request, project_id: int) -> ProjectStageCompletion:
    project_contract = await get_project_contract(request=request, project_id=project_id)
    if project_contract and project_contract.docusign_status == EnvelopeStatus.completed:
        return ProjectStageCompletion(
            completed=1,
            to_complete=0,
            total=1,
            is_completed=True,
            percentage_complete=100,
        )
    payout_structure = (await get_program_by_project_id(request=request, project_id=project_id)).payout_structure
    if payout_structure == PayoutStructure.pay_per_area_enrolled:
        is_completed = not await is_pay_per_area_contract_line_items_invalidated(request=request, project_id=project_id)
        return ProjectStageCompletion(
            completed=1 if is_completed else 0,
            to_complete=0 if is_completed else 1,
            total=1,
            is_completed=is_completed,
            percentage_complete=100 if is_completed else 0,
        )
    elif payout_structure == PayoutStructure.pay_per_practice:
        is_completed = not await is_pay_per_practice_contract_line_items_invalidated(
            request=request, project_id=project_id
        )
        return ProjectStageCompletion(
            completed=1 if is_completed else 0,
            to_complete=0 if is_completed else 1,
            total=1,
            is_completed=is_completed,
            percentage_complete=100 if is_completed else 0,
        )
    else:
        return await get_producer_agreement_completion(request=request, project_id=project_id)


# completion for producer agreement stage is checking whether a signed contract exists
async def get_producer_agreement_completion(request: Request, project_id: int) -> ProjectStageCompletion:
    contract_exists = False
    project_contract = await get_project_contract(request=request, project_id=project_id)
    if project_contract and project_contract.docusign_status == "completed":
        contract_exists = True
    return ProjectStageCompletion(
        completed=int(contract_exists),
        to_complete=int(not contract_exists),
        total=1,
        is_completed=contract_exists,
        percentage_complete=100 if contract_exists else 0,
    )


async def get_field_boundaries_completion(request: Request, project_id: int) -> ProjectStageCompletion:
    """Field boundaries stage is complete when there is at least one field enrolled in the project"""
    fields_enrolled = False
    try:
        async with request.state.sql_session() as s:
            query = (
                select(True)
                .select_from(Fields)
                .where(Fields.parent_project_id == project_id)
                .filter(Fields.deleted_at.is_(None))
                .distinct()
            )
            query_response = await run_query(query=query, s=s)
            fields_enrolled = query_response.one()[0]

    except exc.NoResultFound:
        fields_enrolled = False

    return ProjectStageCompletion(
        completed=int(fields_enrolled),
        to_complete=int(not fields_enrolled),
        total=1,
        is_completed=fields_enrolled,
        percentage_complete=100 if fields_enrolled else 0,
    )


def get_basic_counted_completion(
    att_vals: list[ProjStageAttVals],
) -> ProjectStageCompletion:
    """some stages just require a simple non null value counting check for completion

    note: assumes values such as "[]" are valid
    """
    completed = 0
    total = len(att_vals)
    for att_val in att_vals:
        if att_val.value:
            completed += 1
    try:
        percentage_complete = math.floor(completed / total * 100)
    except ZeroDivisionError:
        percentage_complete = 0
    return ProjectStageCompletion(
        completed=completed,
        to_complete=total - completed,
        total=total,
        is_completed=total == completed,
        percentage_complete=percentage_complete,
    )


@elasticapm.async_capture_span()
async def get_completion_stats(
    request: Request,
    stage_id: int,
    project_id: int,
    stage_type: StageTypes,
    stage_year_start: int,
    stage_year_end: int,
    stage_entity_type: EntityTypeChoices = EntityTypeChoices.field,
    include_validations: bool = False,
) -> ProjectStageCompletion:
    # Need to know if enrolment or monitoring
    res = await get_progress_from_stage_id(request, stage_id)
    progress = res[0]

    if progress is None:
        raise ValueError(f"could not determine progress for stage id {stage_id}")
    if progress not in {ProgressChoices.enrolment, ProgressChoices.monitoring}:
        raise ValueError(f"progress should be one of {ProgressChoices.enrolment} or {ProgressChoices.monitoring}")
    # Fetches all project stages attributes, for all fields, and values / null values
    # att_val_total is the total amount of attributes we are expecting to be completed
    # att_vals returns a list of attributes per field that exist, and may or may not have valid values
    att_val_total, att_vals = await project_stage_attribute_values(
        request=request,
        project_id=project_id,
        stage_id=stage_id,
        progress_choice=progress,
        stage_entity_type=stage_entity_type,
    )

    # no att vals here means that no data has been input so probably not complete
    if att_val_total == 0 and not att_vals:
        return ProjectStageCompletion(
            completed=0,
            to_complete=0,
            total=0,
            is_completed=False,
            percentage_complete=0,
            entity_attribute_completion=None,
        )

    # some stages just require a counting check as they do not have a record_year attribute to autogenerate rows
    if stage_type == StageTypes.FIELD_INFORMATION:
        return get_basic_counted_completion(
            att_vals=att_vals,
        )

    # run completion check, as there may be missing values in the current list of att_vals which we haven't accounted for
    total_possible_values, completed_values, field_completion_lookup = await get_completed_total_attributes(
        att_vals,
        stage_year_start,
        stage_year_end,
    )

    try:
        percentage_complete = math.floor(completed_values / total_possible_values * 100)
    except ZeroDivisionError:
        # by this point if there are no possible values, then completion is 100% as there would be no values left that are required to be filled
        # different case to the previous ZeroDivisionError check as every value has been accounted for this time
        # this case is rare but occurs for example if a set of values is completely disabled via dependencies
        percentage_complete = 100

    if percentage_complete > 100:
        percentage_complete = 100

    validation_errors = None
    is_valid = True
    if include_validations:
        is_valid, validation_errors = await fetch_missing_values_and_run_validation(
            request, project_id, stage_id, att_vals, stage_entity_type
        )

        if is_valid:
            is_completed = completed_values == total_possible_values
        else:
            contract = await get_project_contract(
                request=request, project_id=project_id, orm_type=ProjectContractsNoBlob
            )
            if contract and contract.docusign_status == "completed" and progress == ProgressChoices.enrolment:
                is_completed = True
            else:
                is_completed = False
    else:
        is_completed = completed_values == total_possible_values

    return ProjectStageCompletion(
        completed=completed_values,
        to_complete=total_possible_values - completed_values,
        total=total_possible_values,
        is_completed=is_completed,
        percentage_complete=percentage_complete,
        entity_attribute_completion=field_completion_lookup,
        validation_passed=is_valid if include_validations else None,
        validation_errors=validation_errors,
    )


@elasticapm.async_capture_span()
async def get_project_stage_completion(
    request: Request,
    project_id: int,
    stage_id: int,
    value_changed: bool = False,  # pass True if a value has changed, so we can invalidate dndc results and contract
    include_validations: bool = False,  # pass True if you want to run validations
) -> ProjectStageCompletion | None:
    query = select(Stage.type_, Stage.year_start, Stage.year_end, Stage.entity_type, Stage.phase_id).where(
        Stage.id == stage_id
    )
    async with request.state.sql_session() as s:
        try:
            res = await run_query(query=query, s=s)
            stage = res.one()
        except NoResultFound:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "Invalid stage ID was provided"},
            )

        stage_type = stage.type_
        year_start = int(stage.year_start) if stage.year_start else 0
        year_end = int(stage.year_end) if stage.year_end else 0
        stage_entity_type = stage.entity_type

    should_invalidate = (
        value_changed  # should a field values have change trigger invalidation of dndc results and contract
    )
    is_event_based_stage = stage_type in SUPPORTED_FIELD_EVENT_STAGE_TYPES
    if is_event_based_stage:
        return await get_event_based_project_stage_completion(
            request, project_id, stage_id, value_changed, include_validations
        )
    if stage_type in [StageTypes.CONFIRM_HISTORY, StageTypes.ASSIGN_PRACTICES]:
        stats = await get_historic_practices_completion(
            request=request,
            stage_id=stage_id,
            phase_id=stage.phase_id,
            project_id=project_id,
            stage_type=stage_type,
            stage_year_start=year_start,
            stage_year_end=year_end,
            stage_entity_type=stage_entity_type,
        )
    elif stage_type == StageTypes.SURVEY:
        should_invalidate = False
        stats = await get_survey_completion(
            request=request,
            stage_id=stage_id,
            project_id=project_id,
        )

    elif stage_type == StageTypes.ELIGIBILITY:
        stats = await get_eligibility_completion(
            request=request,
            project_id=project_id,
            stage_id=stage_id,
        )

    elif stage_type == StageTypes.VIEW_OUTCOMES:
        stats = await get_view_outcomes_completion(
            request=request,
            project_id=project_id,
        )

    elif stage_type == StageTypes.CONTRACT:
        stats = await get_producer_agreement_completion(
            request=request,
            project_id=project_id,
        )

    elif stage_type == StageTypes.FIELD_BOUNDARIES:
        stats = await get_field_boundaries_completion(
            request=request,
            project_id=project_id,
        )
    else:
        stats = await get_completion_stats(
            request=request,
            stage_id=stage_id,
            project_id=project_id,
            stage_type=stage_type,
            stage_year_start=year_start,
            stage_year_end=year_end,
            stage_entity_type=stage_entity_type,
            include_validations=include_validations,
        )

    if stats:
        tasks.save_percentage_complete(
            project_id=project_id,
            stage_id=stage_id,
            percentage_complete=stats.percentage_complete,
            to_complete=stats.to_complete,
            completed=stats.completed,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
            request=request,
        )

    # for some stages we need to check if Values have been updated, if so we need
    # to handle the possible invalidation of dndc results and contract
    if should_invalidate:
        await handle_invalidation_of_contract(request=request, project_id=project_id)
        await handle_invalidation_of_dndc_results(request=request, project_id=project_id)

    return stats


async def get_event_based_project_stage_completion(
    request: Request, project_id: int, stage_id: int, should_invalidate: bool, include_validations: bool
) -> ProjectStageCompletion | None:
    if include_validations:
        logger.warning("Event-based stages do not provide validations in legacy format")
    completion_result = await obtain_stage_completion_for_project(
        request=request, stage_id=stage_id, project_id=project_id
    )
    if should_invalidate:
        await handle_invalidation_of_contract(request=request, project_id=project_id)
        await handle_invalidation_of_dndc_results(request=request, project_id=project_id)
    return completion_result


async def handle_invalidation_of_contract(request: Request, project_id: int) -> None:
    contract = await get_project_contract(request=request, project_id=project_id, orm_type=ProjectContractsNoBlob)

    if contract is None:
        return  # no contract to invalidate

    # contract signed
    if contract.docusign_status == "completed" and contract.signed_at is not None:
        logger.warning("Failed deleting contract from project: %s , project has signed contract", project_id)
        return

    # delete contract and invalidate contract document in docusign
    await delete_project_contract(
        request=request, project_id=project_id, send_email=False, docusign_deletion_method=EnvelopeDeletionMethod.void
    )


def count_stage_having_all_null_values(att_vals: list[ProjStageAttVals]) -> int:
    """return number of null values if all non-completion values are null, otherwise 0

    we want to check if all values in a stage have null row_ids, there seem to be cases where a stage is newly created but has not had its rows created in the UI
    so its default values' row_ids are set to null, these are not
    covered in the `get_completed_total_attributes` function so separate check here
    """
    attributes_to_ignore_from_completion = [
        AttributeTypes.record_year,
    ]
    total = 0
    null_values = 0
    for att_val in att_vals:
        if att_val.attribute_type not in attributes_to_ignore_from_completion:
            total += 1
            if att_val.value is None:
                null_values += 1
    if null_values == total:
        return total
    return 0


def get_row_ids_and_attributes_for_fields(
    att_vals: list[ProjStageAttVals],
) -> tuple[list, list, list, dict[str, ProjStageAttVals], dict]:
    field_ids = []
    field_row_ids = []
    row_ids = []
    attributes = []
    attribute_types = []
    # create mapping for lookup later
    att_field_row_mapping: dict[str, ProjStageAttVals] = {}
    att_dep_mapping = {}
    for att_val in att_vals:
        if att_val.field_id not in field_ids:
            field_ids.append(att_val.field_id)
        if att_val.row_id is not None and (att_val.field_id, att_val.row_id) not in field_row_ids:
            field_row_ids.append((att_val.field_id, att_val.row_id))
        if att_val.row_id is not None and att_val.row_id not in row_ids:
            row_ids.append(att_val.row_id)
        if att_val.attribute_type not in attributes:
            attributes.append(att_val.attribute_type)
            attribute_types.append((att_val.attribute_id, att_val.attribute_type))
        att_field_row_mapping[att_val.attribute_type + "_" + str(att_val.field_id) + "_" + str(att_val.row_id)] = (
            att_val
        )
        att_dep_mapping[str(att_val.attribute_id)] = att_val

    return field_ids, field_row_ids, attribute_types, att_field_row_mapping, att_dep_mapping


def get_att_field_row_matrix(attribute_types: list, field_row_ids: list) -> list:
    matrix = []
    for attribute in attribute_types:
        for field_row in field_row_ids:
            field_id, row_id = field_row
            attribute_id, attribute_type = attribute
            matrix.append((attribute_id, attribute_type, field_id, row_id))
    return matrix


def get_filtered_attributes_for_stage_fields(
    att_vals: list[ProjStageAttVals],
) -> tuple[list[ProjStageAttVals], list[int]]:
    field_ids, field_row_ids, attribute_types, att_field_row_mapping, att_dep_mapping = (
        get_row_ids_and_attributes_for_fields(att_vals)
    )
    att_field_row_matrix = get_att_field_row_matrix(attribute_types, field_row_ids)
    # create all attribute-field-row permutations, preserving any that exist already
    full_att_vals: list[ProjStageAttVals] = []
    for row in att_field_row_matrix:
        # each attribute-(field-row) permutation must exist
        attribute_id, attribute_type, field_id, row_id = row
        mapping_key = attribute_type + "_" + str(field_id) + "_" + str(row_id)
        # if the permutation already exists in the mapping, preserve its values
        if mapping_key in att_field_row_mapping:
            full_att_vals.append(att_field_row_mapping[mapping_key])
        # otherwise, generate a field-row attribute with no value to indicate a missing piece of completion
        else:
            if str(attribute_id) in att_dep_mapping:
                att_dep = att_dep_mapping[str(attribute_id)]
                new_project_state_att_val = ProjStageAttVals(
                    field_id=field_id,
                    attribute_id=attribute_id,
                    attribute_type=attribute_type,
                    row_id=row_id,
                    value=None,
                    locked=att_dep.locked,
                    confirmed=att_dep.confirmed,
                    dependencies=att_dep.dependencies,
                )
                full_att_vals.append(new_project_state_att_val)
    return full_att_vals, field_ids


def get_aggregated_field_attributes_completion(
    att_vals: list[ProjStageAttVals],
    filtered_full_att_vals: list[ProjStageAttVals],
    field_ids: list[int],
) -> tuple[int, int, dict[int, EntityAttributeCompletion]]:
    """return number of total and completed values for each field

    given a list of field ids and stage attributes, determine how many there are in total, how many are completed per field, accounting for dependencies if applicable
    """
    # record year should not be considered for completion
    attributes_to_ignore_from_completion = [
        AttributeTypes.record_year,
    ]

    # track which fields have incomplete values
    field_completion_lookup: dict[int, EntityAttributeCompletion] = {}
    for field_id in field_ids:
        # total is the count of all possible values excluding record_year
        field_completion_lookup[field_id] = {"total": 0, "completed": 0}

    # check dependencies for each field-row combo, as rows can exist without attributes being present
    attributes_to_exclude = 0
    completed_attributes = 0
    total = 0
    for att_val in filtered_full_att_vals:
        if att_val.attribute_type not in attributes_to_ignore_from_completion:
            total += 1
            field_completion_lookup[att_val.field_id].update(
                {"total": field_completion_lookup[att_val.field_id].get("total") + 1}
            )
        # if a dependency exists, but is not satisfied, we need to exclude it for completion
        if (
            att_val.dependencies
            and att_val.dependencies != "[]"
            and att_val.attribute_type not in attributes_to_ignore_from_completion
        ):
            check = check_dependency(att_val, att_vals)
            if not check:
                attributes_to_exclude += 1
                field_completion_lookup[att_val.field_id].update(
                    {"total": field_completion_lookup[att_val.field_id].get("total") - 1}
                )
            # if dependency is satisfied and has a value, then increment completed count
            elif check and att_val.value is not None and att_val.value.strip() != "" and att_val.confirmed:
                completed_attributes += 1
                field_completion_lookup[att_val.field_id].update(
                    {"completed": field_completion_lookup[att_val.field_id].get("completed") + 1}
                )
        # if no dependency, we just need to check if value exists
        elif (
            att_val.dependencies == "[]"
            and att_val.confirmed
            and att_val.value is not None
            and att_val.attribute_type not in attributes_to_ignore_from_completion
            and att_val.value.strip() != ""
        ):
            completed_attributes += 1
            field_completion_lookup[att_val.field_id].update(
                {"completed": field_completion_lookup[att_val.field_id].get("completed") + 1}
            )

    return total - attributes_to_exclude, completed_attributes, field_completion_lookup


async def get_completed_total_attributes(
    att_vals: list[ProjStageAttVals],
    year_start: int,
    year_end: int,
) -> tuple[int, int, (dict[int, EntityAttributeCompletion] | None)]:
    """return number of total and completed values

    given a list of stage attributes, determine how many there are in total, how many are completed, accounting for dependencies if applicable
    """

    number_of_null_values = count_stage_having_all_null_values(att_vals)
    if number_of_null_values > 0:
        return number_of_null_values, 0, None

    complete_att_val_matrix, field_ids = get_filtered_attributes_for_stage_fields(att_vals)

    # filter out any values that are not within the stage's year range
    filtered_full_att_vals = filter_attribute_values_by_stage_year_range(complete_att_val_matrix, year_start, year_end)

    total, completed, lookup = get_aggregated_field_attributes_completion(att_vals, filtered_full_att_vals, field_ids)

    return total, completed, lookup


async def enrolment_completions(att_vals: list[ProjStageAttVals]) -> int:
    """Determines the number of enrolment attributes that have been fulfilled and handles associated nuance."""

    completed_count = 0

    field_att_vals: dict[int, list[ProjStageAttVals]] = {}
    for att_val in att_vals:
        field_att_vals.setdefault(att_val.field_id, []).append(att_val)

    # Work through each field's attributes and determine completions for "sets" of related attributes
    for field_att_val in field_att_vals.values():
        processed_indexes = []

        # Record Year
        completions, indexes = record_year_enrolment_completions(field_att_val)
        completed_count += completions
        processed_indexes.extend(indexes)

        # Row 4 and locked hack
        completions, indexes = row4_locked_enrolment_completions(field_att_val)
        completed_count += completions
        processed_indexes.extend(indexes)

        # Everything else - process whatever did NOT get picked up by the custom functions above
        remaining_field_att_vals: list[ProjStageAttVals] = [
            att_val for idx, att_val in enumerate(field_att_val) if idx not in processed_indexes
        ]
        completions, indexes = generic_completions(remaining_field_att_vals)
        completed_count += completions
        processed_indexes.extend(indexes)

    return completed_count


def filter_attribute_values_by_stage_year_range(
    att_vals: list[ProjStageAttVals], year_start: int, year_end: int
) -> list[ProjStageAttVals]:
    # there are cases where stages don't need a record year, so we don't need to filter then as this wouldn't be applicable
    attribute_types = [av.attribute_type for av in att_vals]
    if AttributeTypes.record_year not in attribute_types:
        return att_vals

    field_year_row_mapping = {}
    for att_val in att_vals:
        # value can be None as we have populated the entire dataset for a field to calculate dependency related checks
        if att_val.attribute_type == AttributeTypes.record_year and att_val is not None and att_val.value is not None:
            field_year_row_mapping[f"{att_val.field_id}_{att_val.row_id}"] = int(att_val.value)
    filtered_att_vals = []
    for att_val in att_vals:
        year = field_year_row_mapping.get(f"{att_val.field_id}_{att_val.row_id}")
        if year:
            if year < year_start or year > year_end:
                continue
            filtered_att_vals.append(att_val)
    return filtered_att_vals


def record_year_enrolment_completions(
    att_vals: list[ProjStageAttVals],
) -> tuple[int, list[int]]:
    """Forces completion = 1 for enrolment attribute type record_year."""

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx, av in enumerate(att_vals):
        if av.attribute_type == AttributeTypes.record_year:
            count += 1
            indexes.append(idx)

    return count, indexes


def row4_locked_enrolment_completions(
    att_vals: list[ProjStageAttVals],
) -> tuple[int, list[int]]:
    """Forces completion = 1 for fall_tillage_practice and winter_crop_type when row is 4 and locked is true.

    :_(
    """

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx, av in enumerate(att_vals):
        if (
            (
                av.attribute_type == AttributeTypes.fall_tillage_practice
                or av.attribute_type == AttributeTypes.winter_crop_type
            )
            and av.row_id == 4
            and av.locked
        ):
            count += 1
            indexes.append(idx)

    return count, indexes


def winter_crop_monitoring_completions(
    att_vals: list[ProjStageAttVals],
) -> tuple[int, list[int]]:
    """Determines the completion status 0/1 for sets of attribute values that are related to a winter crop.

    The list of att_vals here MUST belong to a single field!

    Note: this is very brittle and makes the assumption that the 'Winter Crop' stage is composed of sets (rows)
    of the following attribute types:
    - winter_crop_commitment - str "0"/"1" effectively a 'toggle', if 0 then no commitment so other values are redundant
    - winter_crop_type - str describing the crop type the exact value of which is irrelevant here
    - winter_planting_date - str date - actual value doesn't matter

    If winter_crop_commitment = "0" then the user has not committed to a winter crop! Ergo, the other two values
    don't matter. However, we treat this as 3 complete (not missing) values for the sake of the final tally.

    If winter_crop_commitment = "1" then the other two should have a value that is not None.

    Finally, we return a count of fulfilled / not missing values, and the indexes where they occurred in the list
    of ProjStageAttVals. This is so they can be removed / excluded for subsequent operations.
    """

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx1, av1 in enumerate(att_vals):
        if av1.attribute_type == AttributeTypes.winter_crop_commitment:
            row_id = av1.row_id
            crop_commitment = str(av1.value)  # "0" / "1"
            crop_type = None
            planting_date = None

            indexes.append(idx1)

            # find corresponding crop type
            for idx2, av2 in enumerate(att_vals):
                if av2.attribute_type == AttributeTypes.winter_crop_type and av2.row_id == row_id:
                    indexes.append(idx2)
                    crop_type = av2.value
                    break

            # find corresponding planting date
            for idx3, av3 in enumerate(att_vals):
                if av3.attribute_type == AttributeTypes.winter_planting_date and av3.row_id == row_id:
                    indexes.append(idx3)
                    planting_date = av3.value
                    break

            # No crop commitment means the other two values don't matter - this is as complete as it needs to be
            if crop_commitment == "0":
                count += 3
            elif crop_commitment == "1":
                count += 1  # crop commitment itself
                if crop_type is not None:
                    count += 1
                if planting_date is not None:
                    count += 1

    return count, indexes


def summer_crop_monitoring_completions(
    att_vals: list[ProjStageAttVals],
) -> tuple[int, list[int]]:
    """Determines the completion status 0/1 for sets of attribute values that are related to a summer crop.

    The list of att_vals here MUST belong to a single field!

    Note: this is also brittle and makes the assumption that a 'Summer Crop' stage is composed of sets (rows)
    of the following attribute types:
    - summer_crop_type - str describing the crop type
    - summer_planting_date - str date
    - irrigation_method - str describing type of irrigation
    - summer_harvest_date - str date
    - summer_dry_yield - float yield in one unit or another
    - summer_residue_harvested - a string describing the percentage of residue harvested

    The actual values don't matter for calculating completions, just the presence of a value.
    """

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx1, av1 in enumerate(att_vals):
        if av1.attribute_type == AttributeTypes.summer_crop_type:
            crop_type = av1.value
            row_id = av1.row_id
            indexes.append(idx1)

            # Other values we need for this row
            # planting_date = None
            planting_date_att_exists = False
            irrigation = None
            harvest_date = None
            dry_yield = None
            residue_percentage = None

            # find planting date
            for idx2, av2 in enumerate(att_vals):
                if av2.attribute_type == AttributeTypes.summer_planting_date and av2.row_id == row_id:
                    indexes.append(idx2)
                    # planting_date = av2.value
                    planting_date_att_exists = True
                    break

            # find irrigation
            for idx3, av3 in enumerate(att_vals):
                if av3.attribute_type == AttributeTypes.irrigation_method and av3.row_id == row_id:
                    indexes.append(idx3)
                    irrigation = av3.value
                    break

            # find harvets date
            for idx4, av4 in enumerate(att_vals):
                if av4.attribute_type == AttributeTypes.summer_harvest_date and av4.row_id == row_id:
                    indexes.append(idx4)
                    dry_yield = av4.value
                    break

            # find dry yield
            for idx5, av5 in enumerate(att_vals):
                if av5.attribute_type == AttributeTypes.summer_dry_yield and av5.row_id == row_id:
                    indexes.append(idx5)
                    dry_yield = av5.value
                    break

            # find residue percentage
            for idx6, av6 in enumerate(att_vals):
                if av6.attribute_type == AttributeTypes.summer_residue_harvested and av6.row_id == row_id:
                    indexes.append(idx6)
                    residue_percentage = av6.value
                    break

            # This is straightforward here, a bit bulky but this is where we'd deal with any quirks.

            # We're ignoring planting date for now, so add 1 complete value for planting date
            if planting_date_att_exists:
                count += 1

            # For the rest, check for an actual value
            for field_val in [
                crop_type,
                irrigation,
                harvest_date,
                dry_yield,
                residue_percentage,
            ]:
                if field_val:
                    count += 1

    return count, indexes


def tillage_monitoring_completions(
    att_vals: list[ProjStageAttVals],
) -> tuple[int, list[int]]:
    """Determines the completion status 0/1 for sets of attribute values that are related to tillage.

    The list of att_vals here MUST belong to a single field!

    Note: again, this is brittle and makes the assumption that a 'Tillage' stage is composed of sets (rows)
    of the following attribute types:

    - tillage_practice - str describing tillage practice, possibly fall_tillage_practice or spring_tillage_practice
    - tillage_period - str 'fall' or 'spring' ... kind of redundant but is there, nonetheless
    - tillage_date - str date value of tillage operation, possibly also spring_tillage_date or fall_tillage_date
    - tillage_depth - float tillage depth, or fall_tillage_depth, spring_tillage_depth

    Again, the actual values don't matter for calculating completions, just the presence of a value. But in the
    case of tillage we should check for the 'spring' 'fall' variations on attribute types.
    """

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx1, av1 in enumerate(att_vals):
        # Special case for spring
        # If tillage_period is spring then we are not interested in any values for now, so we'll flag them
        # all as 'fulfilled'. However, some spring tillage rows are incomplete so cannot just add a count
        # for each expected value in that row. We need to see how many attributes exist for that row and then
        # add that as the count.

        row_cell_count = 0

        if (
            av1.attribute_type == AttributeTypes.tillage_practice
            or av1.attribute_type == AttributeTypes.fall_tillage_practice
            or av1.attribute_type == AttributeTypes.spring_tillage_practice
        ):
            tillage_practice = av1.value
            row_id = av1.row_id
            indexes.append(idx1)
            row_cell_count += 1

            # Other values we need for this row
            tillage_period = None
            tillage_date = None
            tillage_depth = None

            # find tillage period
            for idx2, av2 in enumerate(att_vals):
                if av2.attribute_type == AttributeTypes.tillage_period and av2.row_id == row_id:
                    indexes.append(idx2)
                    tillage_period = av2.value
                    row_cell_count += 1
                    break

            # tillage date
            for idx3, av3 in enumerate(att_vals):
                if (
                    av3.attribute_type == AttributeTypes.tillage_date
                    or av3.attribute_type == AttributeTypes.fall_tillage_date
                    or av3.attribute_type == AttributeTypes.spring_tillage_date
                ) and av3.row_id == row_id:
                    indexes.append(idx3)
                    tillage_date = av3.value
                    row_cell_count += 1
                    break

            # tillage_depth
            for idx4, av4 in enumerate(att_vals):
                if (
                    av4.attribute_type == AttributeTypes.tillage_depth
                    or av4.attribute_type == AttributeTypes.fall_tillage_depth
                    or av4.attribute_type == AttributeTypes.spring_tillage_depth
                ) and av4.row_id == row_id:
                    indexes.append(idx4)
                    tillage_depth = av4.value
                    row_cell_count += 1
                    break

            # If tillage period is 'spring' fulfill all cells
            if tillage_period == "spring":
                count += row_cell_count
                continue

            # Always want a tillage period value, either 'fall' or 'spring'
            if tillage_period:
                count += 1

            # If tillage practice is 'No Tillage' then tillage date and depth are irrelevant.
            if tillage_practice and tillage_practice.lower() == "no till":
                count += 3  # one each for practice, date and depth

            else:
                for field_val in [
                    tillage_practice,
                    tillage_date,
                    tillage_depth,
                ]:
                    if field_val:
                        count += 1

    return count, indexes


def check_dependency_object(
    attribute: AttributeResponse,
    dependency_object: DependencyObject,
    att_vals: list[ProjStageAttVals],
) -> bool:
    attribute_id = dependency_object["id"]
    value_operator = dependency_object["operator"]
    expected_value = dependency_object["value"]
    dependent_attribute = None
    for att in att_vals:
        if att.attribute_id == attribute_id and att.row_id == attribute.row_id and att.field_id == attribute.field_id:
            dependent_attribute = att
            break
    if dependent_attribute:
        if value_operator == "is":
            return dependent_attribute.value == expected_value
        elif value_operator == "is_not":
            return dependent_attribute.value != expected_value
    return False


def check_dependency_group(
    attribute: AttributeResponse,
    dependency_group: DependencyGroup,
    att_vals: list[ProjStageAttVals],
) -> bool:
    operator = dependency_group["operator"]
    dependencies = dependency_group["dependencies"]
    dependency_checks = []
    for dependency in dependencies:
        # recursive check for groups
        if "dependencies" in dependency:
            check = check_dependency_group(attribute, dependency, att_vals)
            dependency_checks.append(check)
        elif "value" in dependency:
            check = check_dependency_object(attribute, dependency, att_vals)
            dependency_checks.append(check)
    if operator == "and":
        return all(dependency_checks)
    elif operator == "or":
        return any(dependency_checks)

    # returning False because there is no check where these methods are called.
    return False


# check dependency of attribute and return whether attribute should be counted or not
def check_dependency(av: ProjStageAttVals, att_vals: list[ProjStageAttVals]) -> bool:
    # cases where we use dependencies
    if av.dependencies and av.dependencies != "[]":
        # find the dependent attribute to get its value
        dependencies_json = json.loads(av.dependencies)
        # store overall dependency checks, need to check all() on this later
        dependency_object_truth_values = []
        # dependencies can be a mix of DependencyObjects and DependencyGroups
        for dependency_obj in dependencies_json:
            # DependencyGroup case
            if "dependencies" in dependency_obj:
                check = check_dependency_group(av, dependency_obj, att_vals)
                dependency_object_truth_values.append(check)
            # DependencyObjects case
            elif "value" in dependency_obj:
                check = check_dependency_object(av, dependency_obj, att_vals)
                dependency_object_truth_values.append(check)
        # implied operator is AND
        return all(dependency_object_truth_values)

    # if we somehow dont find a dependent attribute, the dependency is broken and shouldn't be counted anyway
    return False


def generic_completions(att_vals: list[ProjStageAttVals]) -> tuple[int, list[int]]:
    """Determines the completion status 0/1 for sets of attribute values without regard to nuance.

    The list of att_vals here MUST belong to a single field.
    """

    # ensure single field
    if len({att_val.field_id for att_val in att_vals}) > 1:
        raise ValueError("att_vals must all have the same field_id")

    indexes = []
    count = 0

    for idx, av in enumerate(att_vals):
        if av.value:
            count += 1
            indexes.append(idx)

    return count, indexes


@elasticapm.async_capture_span()
async def project_stage_attribute_values(
    request: Request,
    project_id: int,
    stage_id: int,
    progress_choice: ProgressChoices,
    stage_entity_type: EntityTypeChoices = EntityTypeChoices.field,
) -> tuple[int, list[ProjStageAttVals]]:
    """Retrieves stage attributes for all fields enrolled in a project, with corresponding values or null values."""
    if progress_choice not in [ProgressChoices.enrolment, ProgressChoices.monitoring]:
        raise ValueError(
            f"progress choice must be one of {ProgressChoices.enrolment} or {ProgressChoices.monitoring}, got {progress_choice}"
        )

    phase_type = None
    if progress_choice == ProgressChoices.enrolment:
        phase_type = PhaseTypes.ENROLMENT
    if progress_choice == ProgressChoices.monitoring:
        phase_type = PhaseTypes.MONITORING

    # get total (rows * columns) we are expecting based off the distinct row_ids
    # we are excluding the year rows for completion as they are not editable anyway
    # TODO: Refactor this after pooling values are done.
    if stage_entity_type == EntityTypeChoices.field:
        sql = """
    SELECT SUM(CASE WHEN att.type = 'record_year' THEN 1 ELSE 0 END) years, COUNT(DISTINCT(att.type)) cols, COUNT(DISTINCT(val.row_id)) `rows`
    FROM mrv_fields fld
             JOIN mrv_projects prj ON fld.parent_project_id = prj.id
             JOIN mrv_programs prg ON prj.program_id = prg.id
             JOIN mrv_phases tbl ON prg.id = tbl.program_id
             JOIN mrv_stages stg ON tbl.id = stg.phase_id
             JOIN mrv_attributes att ON stg.id = att.parent_stage_id
             LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.field_id)
    WHERE att.deleted_at is NULL
      AND fld.deleted_at IS NULL
      AND stg.deleted_at IS NULL
      AND tbl.deleted_at IS NULL
      AND prj.deleted_at IS NULL
      AND prg.deleted_at IS NULL
      AND fld.parent_project_id = :project_id
      AND stg.id = :stage_id
      AND tbl.type_ = :phase_type
      AND att.enabled = 1
      GROUP BY fld.id;
    """
    elif stage_entity_type == EntityTypeChoices.farm:
        sql = """
    SELECT SUM(CASE WHEN att.type = 'record_year' THEN 1 ELSE 0 END) years, COUNT(DISTINCT(att.type)) cols, COUNT(DISTINCT(val.row_id)) `rows`
    FROM mrv_farms fld
             JOIN mrv_projects prj ON fld.parent_project_id = prj.id
             JOIN mrv_programs prg ON prj.program_id = prg.id
             JOIN mrv_phases tbl ON prg.id = tbl.program_id
             JOIN mrv_stages stg ON tbl.id = stg.phase_id
             JOIN mrv_attributes att ON stg.id = att.parent_stage_id
             LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.entity_id)
    WHERE att.deleted_at is NULL
      AND fld.deleted_at IS NULL
      AND stg.deleted_at IS NULL
      AND tbl.deleted_at IS NULL
      AND prj.deleted_at IS NULL
      AND prg.deleted_at IS NULL
      AND fld.parent_project_id = :project_id
      AND stg.id = :stage_id
      AND tbl.type_ = :phase_type
      AND att.enabled = 1
      GROUP BY fld.id;
    """
    elif stage_entity_type == EntityTypeChoices.mob:
        sql = """
    SELECT SUM(CASE WHEN att.type = 'record_year' THEN 1 ELSE 0 END) years, COUNT(DISTINCT(att.type)) cols, COUNT(DISTINCT(val.row_id)) `rows`
    FROM mrv_mobs fld
             JOIN mrv_projects prj ON fld.parent_project_id = prj.id
             JOIN mrv_programs prg ON prj.program_id = prg.id
             JOIN mrv_phases tbl ON prg.id = tbl.program_id
             JOIN mrv_stages stg ON tbl.id = stg.phase_id
             JOIN mrv_attributes att ON stg.id = att.parent_stage_id
             LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.entity_id)
    WHERE att.deleted_at is NULL
      AND stg.deleted_at IS NULL
      AND tbl.deleted_at IS NULL
      AND prj.deleted_at IS NULL
      AND prg.deleted_at IS NULL
      AND fld.parent_project_id = :project_id
      AND stg.id = :stage_id
      AND tbl.type_ = :phase_type
      AND att.enabled = 1
      GROUP BY fld.id;
    """

    total = 0
    async with request.state.sql_session() as s:
        try:
            query = sa.text(sql)
            res = await run_query(
                query=query,
                s=s,
                stage_id=stage_id,
                phase_type=phase_type,
                project_id=project_id,
            )
            records = list(res)
        except Exception as e:
            raise e

    for rec in records:
        years, cols, rows = rec
        total += cols * rows - years

    # get all values for enabled columns and rows
    if stage_entity_type == EntityTypeChoices.field:
        stmt = """
        SELECT fld.id, att.id, att.type, val.row_id, val.value, val.locked, val.confirmed, att.dependencies
        FROM mrv_fields fld
                JOIN mrv_projects prj ON fld.parent_project_id = prj.id
                JOIN mrv_programs prg ON prj.program_id = prg.id
                JOIN mrv_phases tbl ON prg.id = tbl.program_id
                JOIN mrv_stages stg ON tbl.id = stg.phase_id
                JOIN mrv_attributes att ON stg.id = att.parent_stage_id
                LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.field_id)
        WHERE att.deleted_at is NULL
        AND fld.deleted_at IS NULL
        AND stg.deleted_at IS NULL
        AND tbl.deleted_at IS NULL
        AND prj.deleted_at IS NULL
        AND prg.deleted_at IS NULL
        AND fld.parent_project_id = :project_id
        AND stg.id = :stage_id
        AND tbl.type_ = :phase_type
        AND att.enabled = 1
        GROUP BY stg.id, prj.id, fld.id, att.id, att.type, val.row_id, val.id
        ORDER BY fld.id, att.type, val.row_id;
        """
    elif stage_entity_type == EntityTypeChoices.farm:
        stmt = """
        SELECT fld.id, att.id, att.type, val.row_id, val.value, val.locked, val.confirmed, att.dependencies
        FROM mrv_farms fld
                JOIN mrv_projects prj ON fld.parent_project_id = prj.id
                JOIN mrv_programs prg ON prj.program_id = prg.id
                JOIN mrv_phases tbl ON prg.id = tbl.program_id
                JOIN mrv_stages stg ON tbl.id = stg.phase_id
                JOIN mrv_attributes att ON stg.id = att.parent_stage_id
                LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.entity_id)
        WHERE att.deleted_at is NULL
        AND fld.deleted_at IS NULL
        AND stg.deleted_at IS NULL
        AND tbl.deleted_at IS NULL
        AND prj.deleted_at IS NULL
        AND prg.deleted_at IS NULL
        AND fld.parent_project_id = :project_id
        AND stg.id = :stage_id
        AND tbl.type_ = :phase_type
        AND att.enabled = 1
        GROUP BY stg.id, prj.id, fld.id, att.id, att.type, val.row_id, val.id
        ORDER BY fld.id, att.type, val.row_id;
        """
    elif stage_entity_type == EntityTypeChoices.mob:
        stmt = """
        SELECT fld.id, att.id, att.type, val.row_id, val.value, val.locked, val.confirmed, att.dependencies
        FROM mrv_mobs fld
                JOIN mrv_projects prj ON fld.parent_project_id = prj.id
                JOIN mrv_programs prg ON prj.program_id = prg.id
                JOIN mrv_phases tbl ON prg.id = tbl.program_id
                JOIN mrv_stages stg ON tbl.id = stg.phase_id
                JOIN mrv_attributes att ON stg.id = att.parent_stage_id
                LEFT JOIN mrv_values val ON (att.id = val.attribute_id AND fld.id = val.entity_id)
        WHERE att.deleted_at is NULL
        AND stg.deleted_at IS NULL
        AND tbl.deleted_at IS NULL
        AND prj.deleted_at IS NULL
        AND prg.deleted_at IS NULL
        AND fld.parent_project_id = :project_id
        AND stg.id = :stage_id
        AND tbl.type_ = :phase_type
        AND att.enabled = 1
        GROUP BY stg.id, prj.id, fld.id, att.id, att.type, val.row_id, val.id
        ORDER BY fld.id, att.type, val.row_id;
        """

    async with request.state.sql_session() as s:
        try:
            query = sa.text(stmt)
            res = await run_query(
                query=query,
                s=s,
                stage_id=stage_id,
                phase_type=phase_type,
                project_id=project_id,
            )
            records = list(res)
        except Exception as e:
            raise e

    results = []
    for rec in records:
        results.append(
            ProjStageAttVals(
                field_id=rec[0],
                attribute_id=rec[1],
                attribute_type=rec[2],
                row_id=rec[3],
                value=rec[4],
                locked=bool(rec[5]),
                confirmed=bool(rec[6]),
                dependencies=rec[7],
            )
        )
    return total, results


async def get_historic_practices_completion(
    request: Request,
    stage_id: int,
    phase_id: int,
    project_id: int,
    stage_type: StageTypes,
    stage_year_start: int,
    stage_year_end: int,
    stage_entity_type: EntityTypeChoices = EntityTypeChoices.field,
) -> ProjectStageCompletion:
    io_tasks = Tasks()
    assign_practices_stages_task = io_tasks.add(  # TODO: to db
        get.generic_get(
            request=request,
            orm_type=Stage,
            filters=[
                get.Filter(id_field=Stage.phase_id, ids=[phase_id]),
                get.Filter(id_field=Stage.type_, ids=[StageTypes.ASSIGN_PRACTICES]),
            ],
            empty_return=True,
        )
    )
    max_row_id = stage_year_end - stage_year_start + 1
    # TODO: Refactor this after pooling values are done.
    if stage_entity_type == EntityTypeChoices.field:
        query = """
    select
        mf.id as entity_id,
        SUM(case when ma.name is not null then 1 else 0 end)
    from
        mrv_fields mf
        left join mrv_values mv on mv.field_id = mf.id and mv.entity_type = 'field'
        left join mrv_attributes ma on ma.id = mv.attribute_id and (
            ma.parent_stage_id = :stage_id
            or ma.parent_stage_id is null
        )
    where
        mf.deleted_at is null
        and mf.parent_project_id = :project_id
        and (
            (
                (
                    ma.type in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
                or (
                    ma.type not in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
            )
            or ma.type = 'practice'
            or ma.id is Null
        )
        and (
            ma.`type` != "record_year"
            or ma.`type` is null
        )
        and (
            mv.confidence > 40 or mv.confirmed = True
        )
    group by
        mf.id
        """
    elif stage_entity_type == EntityTypeChoices.farm:
        query = """
    select
        mf.id as entity_id,
        SUM(case when ma.name is not null then 1 else 0 end)
    from
        mrv_farms mf
        left join mrv_values mv on mv.entity_id = mf.id and mv.entity_type = 'farm'
        left join mrv_attributes ma on ma.id = mv.attribute_id and (
            ma.parent_stage_id = :stage_id
            or ma.parent_stage_id is null
        )
    where
        mf.deleted_at is null
        and mf.parent_project_id = :project_id
        and (
            (
                (
                    ma.type in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
                or (
                    ma.type not in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
            )
            or ma.type = 'practice'
            or ma.id is Null
        )
        and (
            ma.`type` != "record_year"
            or ma.`type` is null
        )
        and (
            mv.confidence > 40 or mv.confirmed = True
        )
    group by
        mf.id
        """
    elif stage_entity_type == EntityTypeChoices.mob:
        query = """
    select
        mf.id as entity_id,
        SUM(case when ma.name is not null then 1 else 0 end)
    from
        mrv_mobs mf
        left join mrv_values mv on mv.entity_id = mf.id and mv.entity_type = 'mob'
        left join mrv_attributes ma on ma.id = mv.attribute_id and (
            ma.parent_stage_id = :stage_id
            or ma.parent_stage_id is null
        )
    where
        mf.parent_project_id = :project_id
        and (
            (
                (
                    ma.type in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
                or (
                    ma.type not in ('fall_tillage_practice', 'winter_crop_type')
                    and mv.row_id < :max_row_id
                )
            )
            or ma.type = 'practice'
            or ma.id is Null
        )
        and (
            ma.`type` != "record_year"
            or ma.`type` is null
        )
        and (
            mv.confidence > 40 or mv.confirmed = True
        )
    group by
        mf.id
        """
    async with request.state.sql_session() as s:
        try:
            query = sa.text(query)
            res = await run_query(
                query=query,
                s=s,
                project_id=project_id,
                stage_id=stage_id,
                max_row_id=max_row_id,
            )
            completed = {i[0]: i[1] for i in res.all()}

        except Exception as e:
            raise e
    logger.debug(completed)

    field_id_eligibility_map = None
    eligibility_method: EligibilityTypes | None = None
    if assign_practice_stages := await assign_practices_stages_task:
        assign_practice_stage = assign_practice_stages[0]
        eligibility_method = await db.get_eligibility_method(request, assign_practice_stage.id)
        if eligibility_method is not None:
            try:
                field_id_eligibility_map = await get_field_id_eligibility_map(
                    request=request, project_id=project_id, stage_id=stage_id, eligibility_method=eligibility_method
                )
            # completion should not fail if we can't compute eligibility.
            except BaseException as e:
                logger.warning(f"Error while computing eligibility for project {project_id} and stage {stage_id}: {e}")
                field_id_eligibility_map = None

    assigned_practices = await db.get_assigned_practice_values(request, project_id=project_id)

    practice_change_validator = None
    if eligibility_method == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
        practice_change_validator = PracticeChangeRuleValidator(request=request, stage_id=stage_id)

    async def is_eligible(field_id_: int) -> bool:
        if field_id_eligibility_map is None:
            return True

        field_eligibility = field_id_eligibility_map.get(field_id_)
        if field_eligibility is None:
            return True

        # if already assigned practice, check if current assigned practice is eligible
        assigned_practice = assigned_practices.get(field_id_)
        if assigned_practice is not None and len(field_eligibility.eligible_practices) > 0:
            # if current assigned practice is not eligible, return False
            if eligibility_method == EligibilityTypes.PARAMETERISED_ELIGIBILITY:
                # Custom v2 eligibility is not based on combinations, So we only check each assigned practice is eligible
                # though it is not based on combinations, we still use the same data structure to store eligible practices, hence we still need to flatten it
                eligible_practices_set = {
                    practice for combination in field_eligibility.eligible_practices for practice in combination
                }

                if any(practice not in eligible_practices_set for practice in assigned_practice):
                    return False

                # check if the assigned practices are eligible based on the rules
                if practice_change_validator is not None:
                    if not await practice_change_validator.validate_all_practice_rules(
                        assigned_practices=assigned_practice,
                        baseline_practices=list(field_eligibility.baseline_practices),
                    ):
                        return False

            else:
                # Other eligibility check is based on combinations
                if not any(
                    set(practice) == set(assigned_practice) for practice in field_eligibility.eligible_practices
                ):
                    return False

        return (
            field_eligibility.eligible
            if field_eligibility.eligible is not None
            else len(field_eligibility.eligible_practices) > 0
        )

    total_required = 0
    total_completed = 0
    # max_required_value is based on the assumption confirm_history stage having
    # 4 attributes (spring_tillage, summer_crop, fall_tillage, winter_crop) and
    # topmost row not filled with 2 attributes fall_tillage and winter_crop
    # that will give us total_stage_years * number of attributes(4) - 2 attribute
    # values not filled for 1 row.
    max_required_value = (4 * max_row_id) - 2
    if stage_type == StageTypes.CONFIRM_HISTORY:
        for _, num in completed.items():
            logger.debug("%s %s %s", total_required, total_completed, num)
            total_required += max_required_value
            # we can't have more than 18 completed here, so if num is somehow larger, cap it
            if num > max_required_value:
                total_completed += max_required_value
            else:
                total_completed += num
    else:
        for field_id in completed.keys():
            if completed.get(field_id, 0) > 0 and await is_eligible(field_id):
                total_completed += 1
        total_required = len(completed.keys())
    try:
        percentage_complete = math.floor(total_completed / total_required * 100)
    except ZeroDivisionError:
        percentage_complete = 0

    # if we somehow have overcompletion, consider it complete
    if percentage_complete > 100:
        percentage_complete = 100
    is_completed = bool(percentage_complete == 100)

    # set to zero if smaller than 0
    to_complete = total_required - total_completed
    if to_complete < 0:
        to_complete = 0
    # This will happen if no fields are added
    tasks.save_percentage_complete(
        project_id=project_id,
        stage_id=stage_id,
        percentage_complete=percentage_complete,
        to_complete=to_complete,
        completed=total_completed,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        request=request,
    )
    return ProjectStageCompletion(
        completed=total_completed,
        to_complete=total_required - total_completed,
        total=total_required,
        percentage_complete=percentage_complete,
        is_completed=is_completed,
    )


@elasticapm.async_capture_span()
async def get_stages_from_attribute_ids(request: Request, attribute_ids: list[int]) -> list[Stage]:
    async with request.state.sql_session() as s:
        query = (
            select(Stage).join(Attribute, Attribute.parent_stage_id == Stage.id).where(Attribute.id.in_(attribute_ids))
        )
        res = await run_query(query=query, s=s)
        tmp = list(res.unique().scalars())
        logger.debug([i.__dict__ for i in tmp])
        return tmp


@elasticapm.async_capture_span()
async def get_projects(
    request: Request,
    ids: list[int] | None = None,
    output_type: type | None = ProjectNoProgram,
    check_404: bool = True,
) -> list[type]:
    if ids:
        ret = await get.get(
            request=request,
            orm_type=Projects,
            type_=output_type,
            ids=ids,
            id_field=Projects.id,
            empty_return=True,
        )
    else:
        ret = await get.get(
            request=request,
            orm_type=Projects,
            type_=output_type,
            empty_return=True,
        )
    if not ret and check_404:
        await get.check_or_404_by_id(request, id, Projects, Projects.id)

    return ret


@elasticapm.async_capture_span()
async def get_project_object(
    request: Request,
    id: int | None = None,
) -> Projects | None:
    if id is None:
        return None

    result = await get.get(
        request=request,
        orm_type=Projects,
        ids=[id],
        id_field=Projects.id,
        empty_return=True,
    )

    if not result:
        return None

    return result[0]


@elasticapm.async_capture_span()
async def get_project_with_fields(
    request: Request,
    project_id: int,
) -> Projects | None:
    async with request.state.sql_session() as s:
        query = (
            select(Projects.program_id, Fields.fs_field_id, Fields.area)
            # isouter=True is "LEFT JOIN". Is used to filter Projects without fields
            .join(Projects.fields, isouter=True).filter(Projects.id == project_id)
        )

        res = await run_query(query=query, s=s)
        ret = res.all()
        if not ret:
            return None

        fields = [Fields(fs_field_id=i[1], area=i[2]) for i in ret if i[1] is not None]
        return Projects(id=project_id, program_id=ret[0][0], fields=fields)


def get_progress_from_stage(stage: StageResponse) -> tuple[ProgressChoices, str] | None:
    if stage.phase_id is not None:
        if stage.phase_type == PhaseTypes.MONITORING.value.upper():
            return ProgressChoices.monitoring, stage.name
        if stage.phase_type == PhaseTypes.ENROLMENT.value.upper():
            return ProgressChoices.enrolment, stage.name
    return None


@elasticapm.async_capture_span()
async def get_progress_from_stage_id(request: Request, stage_id: int) -> tuple[Any, str] | tuple[None, None] | None:
    stages = await get_stages(request=request, stage_id=stage_id)
    if stages:
        return get_progress_from_stage(stages[0])
    return (None, None)


@elasticapm.async_capture_span()
async def get_progress_from_attribute_ids(request: Request, attribute_ids: int) -> ProgressChoices:
    stages = await get_stages_from_attribute_ids(request=request, attribute_ids=attribute_ids)
    # we are hoping only one result
    if len(set(stages)) > 1:
        return None
    stage = stages[0]
    logger.debug(stage.__dict__)
    return get_progress_from_stage(stage)


@elasticapm.async_capture_span()
async def construct_values_response(
    request: Request,
    values: list[Values],
    project_id: int,
    fields_updated: list[Fields] | None = None,
    completion_stats: bool = False,
    stage_id: int | None = None,
    include_validations: bool = False,
) -> UpdateValuesResponse:
    attribute_ids = {value.attribute_id for value in values}
    # TODO FSB-13586 Can we use a single ORM query to get attribute_type?
    attribute_type_map = await get_attribute_type_by_id(request=request, ids=attribute_ids)
    updated_values = [
        StructuredValue(
            **ValuesResponseWithFieldID.from_orm(i).dict(),
            attribute_type=attribute_type_map.get(i.attribute_id),
        )
        for i in values
    ]
    if stage_id is None:
        stage_id = await get_stage_id_from_attributes(request=request, attribute_ids=attribute_ids)

    flag_value_changed = True

    # disable invalidation if superupdate performed by super admin
    if request.headers.get("x-fs-super-update") == "true" and (await is_original_user_super_admin(request)):
        logger.info("Executing super update, skipping contract invalidation")
        flag_value_changed = False

    if stage_id is not None and completion_stats is True:
        # get new completion stats, and invalidating dndc status and contract if needed
        completion = await get_project_stage_completion(
            request=request,
            project_id=project_id,
            stage_id=stage_id,
            value_changed=flag_value_changed,
            include_validations=include_validations,
        )
        return UpdateValuesResponse(
            completion=completion,
            values=updated_values,
            stage_id=stage_id,
        )
    return UpdateValuesResponse(values=updated_values, fields_updated=fields_updated)


@elasticapm.async_capture_span()
async def get_docusign_object(request: Request, docusign_client_id: str, program_id: int | None = None) -> Docusign:
    docusign_credentials = await get.get(
        request=request,
        orm_type=DocusignAccess,
        type_=DocusignAccessDetailsResponse,
        id_field=DocusignAccess.docusign_client_id,
        ids=[docusign_client_id],
        empty_return=True,
    )

    if not docusign_credentials:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": f"No Docusign credentials found for program: {program_id}",
            },
        )

    return Docusign(
        client_id=docusign_credentials[0].docusign_client_id,
        client_secret=docusign_credentials[0].docusign_secret_id,
        authorization_server=docusign_credentials[0].docusign_authorization_server_url,
        target_account_id=docusign_credentials[0].docusign_target_account_id,
    )


@elasticapm.async_capture_span()
async def get_contract_type_from_phase_id(
    request: Request,
    phase_id: int,
    phase_type: PhaseTypes,
) -> str | None:
    phases = await get_phases_by_ids_and_type_(
        request=request,
        ids=[phase_id],
        type_=phase_type,
        return_type=PhaseRequestCreate,
    )
    contract_type = None
    if len(phases) > 0:
        contract_type = phases[0].contract_type
    return contract_type


@elasticapm.async_capture_span()
async def get_enrolment_start_date(request: Request, project_id: int) -> datetime:
    async with request.state.sql_session() as s:
        query = (
            select(Phases.start_date)
            .join(Projects, Projects.program_id == Phases.program_id)
            .where(Projects.id == project_id, Phases.type_ == PhaseTypes.ENROLMENT, Phases.deleted_at.is_(None))
        )
        res = (await run_query(query=query, s=s)).scalar_one()
        logger.debug(f"Enrolment start date for project {project_id} is {res}")
    return res


@elasticapm.async_capture_span()
async def get_project_permissions(request: Request, project_id: int, admins: bool = True) -> list[ProjectPermission]:
    project_admins: list[ProjectPermission]
    program_id: int
    project_admins, program_id = await asyncio.gather(
        get.generic_get(
            request=request,
            orm_type=ProjectPermissions,
            type_=ProjectPermission,
            filters=[get.Filter(id_field=ProjectPermissions.project, ids=[project_id])],
            empty_return=True,
        ),
        db.get_program_id_by_project_id(request, project_id),
    )

    if program_id and admins:
        program_admins: list[ProgramPermission] = await get.generic_get(
            request=request,
            orm_type=ProgramPermissions,
            type_=ProgramPermission,
            filters=[get.Filter(id_field=ProgramPermissions.program_id, ids=[program_id])],
            empty_return=True,
        )

        project_admins += [
            ProjectPermission(
                id=i.id,
                project=project_id,
                program_id=i.program_id,
                details=i.details,
                deleted_at=i.deleted_at,
                user=i.user_id,
            )
            for i in program_admins
        ]

    user_info_retriever = UserInfoRetriever(project_admins)
    await user_info_retriever.load_user_info()
    for permission in project_admins:
        permission.user_info = user_info_retriever.get_user_info(permission.user)

    return project_admins


async def get_program_id_by_project_id(request: Request, project_id: int) -> int | None:
    query = """
        select program_id from mrv_projects
        where id = :project_id
    """
    async with request.state.sql_session() as s:
        return (await run_query(query=sa.text(query), s=s, project_id=project_id)).scalar()


def does_exceed_ha_limit(
    limit_value: int | float | None,
    current_area: int | float,
    new_area: int | float = 0,
) -> bool:
    if limit_value and limit_value > 0:
        return current_area + new_area > limit_value

    return False


async def get_survey_values_by_project_id(request: Request, project_id: int, stage_id: int) -> dict:
    async with request.state.sql_session() as s:
        query = (
            select(ProjectValues.key, ProjectValues.value)
            .where(ProjectValues.project_id == project_id)
            .where(ProjectValues.stage_id == stage_id)
            .where(ProjectValues.type_ == ProjectValueTypes.survey.value)
        )
        config = (await run_query(s, query)).all()
    results = {value.key: value.value for value in config}
    logger.info(results)
    submission_id = results.get("submission_id", "-1")
    if submission_id != "-1":
        try:
            results["results_pdf_url"] = await get_submission_pdf(submission_id=submission_id)
            logger.info("it was ok")
            logger.info(results)
            return results
        except HTTPException:
            logger.info("failure")
            return results
    return results


@elasticapm.async_capture_span()
async def delete_contract_line_items(*, request: Request, contract_id: int) -> list[int]:
    async with request.state.sql_session() as s:
        res = await s.execute(
            select(ProjectContractLineItems.id).where(
                ProjectContractLineItems.contract_id == contract_id,
                ProjectContractLineItems.deleted_at.is_(None),
            )
        )
    if ids := [x.id for x in res]:
        await delete.soft(
            request=request,
            ids=ids,
            orm_type=ProjectContractLineItems,
            id_field=ProjectContractLineItems.id,
        )
    return ids


def reformat_values_response(
    values: list[ValuesResponseWithFieldID], entity_type: EntityTypeChoices
) -> tuple[dict[int, dict[int, list[ValuesResponseWithFieldID]]], set[int]]:
    """
    This function takes a flat list of Pydantic values objects, and returns two things:

        - A nested structure, a dict of dicts of lists, where we list values by row ID and entity ID e.g.::

            {
                # entity ID
                123: {
                    # row ID
                    456: [
                        some_value_1,
                        some_value_2,
                        ...
                    ],
                    # row ID
                    321: [
                        ...
                    ],
                }
                # entity ID
                543: {
                    ...
                }
            }

        - A `set` of all the attribute IDs used in the returned values

    Args:
        values - flat list of Values (pydantic models, not ORM models)
        entity_type - structure the response by either `field_id` or `entity_id` depending on type
    """
    processed: dict[int, dict[int, list[ValuesResponseWithFieldID]]] = {}
    attr_key = "field_id" if entity_type == EntityTypeChoices.field else "entity_id"
    for value in values:
        entity_id = getattr(value, attr_key)
        if entity_id is None:
            logger.info("unable to find entity_id for value_id : %s", value.id)
        elif entity_id not in processed:
            processed[entity_id] = {value.row_id: [value]}
        elif value.row_id not in processed[entity_id]:
            processed[entity_id][value.row_id] = [value]
        else:
            processed[entity_id][value.row_id].append(value)
    logger.debug(processed)

    return processed, {value.attribute_id for value in values}


async def finalise_values_response_structured(
    request: Request,
    processed: dict[int, dict[int, list[ValuesResponseWithFieldID]]],
    attribute_ids: set[int],
    entity_type: EntityTypeChoices,
    year_start: int,
    year_end: int,
    order: StructuredOrderKeys = StructuredOrderKeys.asc,
    sort_key: StructuredSortKeys = StructuredSortKeys.year,
    override_sorting: bool = False,
) -> ValuesResponseStructured:
    """
    Given values structured by entity IDs and row IDs, reorganise and return values structured by entity IDs

    If a row of values has a record year attribute, and that year falls outside the range `[year_start, year_end]`
    inclusive, then the entire row isn't included in the result.

    The object returned maps entity IDs to lists. In other words, we map field/farm/mob ID to a list.
    Each of those lists is itself a list of `ValueResponseStructured`, a pydantic model with fields for
    entity ID, row ID, year and a dict that maps attribute IDs to values.

    Each list of `ValueResponseStructured` is sorted by `sort_key` (`row_id` or `year`) in `order` (ascending or
    descending), but if `override_sorting` is `True`, special-case handling is done:

        - If at least one `record_year` value was found, then sort by `year` descending
        - Otherwise, sort by `row_id` ascending

    Args:
        request: API call request object
        processed: A dict of dicts of lists. Maps stage IDs to dicts of row IDs to lists of values
        attribute_ids: Set of all the attribute IDs used in `processed`
        entity_type: e.g. field, farm, mob
        year_start: If a row has a record year, only include values whose record year value >= year_start
        year_end: If a row has a record year, only include values whose record year value <= year_end
        order: For each entity ID, order associated `list[ValueResponseStructured]` ascending or descending
        sort_key: For each entity ID, order associated `list[ValueResponseStructured]` by either `year` or `row_id`
        override_sorting: If `True`, use special case ordering - see description above for details.

    Returns:
        A dict that maps entity IDs to lists of `ValueResponseStructured`. Each `list[ValueResponseStructured]` is
        sorted by either `row_id` or `year`. See function description for more.
    """
    attribute_type_map = await get_attribute_type_by_id(request=request, ids=attribute_ids)
    outputs: ValuesResponseStructured = {}
    attr_record_year_exists = False
    for entity_id, rows in processed.items():
        outputs[entity_id] = []
        for row_id, values in rows.items():
            return_values: list[StructuredValue] = []
            year_list = []
            for value in values:
                attr_type = attribute_type_map.get(value.attribute_id)
                if attr_type == AttributeTypes.record_year:
                    attr_record_year_exists = True
                    year_list.append(value.value)
                return_values.append(StructuredValue(**value.dict(), attribute_type=attr_type))
            year = None
            if year_list:
                year = year_list[0]
                if int(year) < year_start or int(year) > year_end:
                    continue
            outputs[entity_id].append(
                ValueResponseStructured(
                    year=year,
                    row_id=row_id,
                    field_id=entity_id if entity_type == EntityTypeChoices.field else None,
                    entity_id=entity_id if entity_type != EntityTypeChoices.field else None,
                    entity_type=entity_type,
                    # What happens if there's more than one value for a given attribute ID? Is this possible?
                    values={value.attribute_id: value for value in return_values},
                )
            )

    def take_year(elem: ValueResponseStructured) -> int:
        if elem.year is None:
            return 0
        return elem.year

    def take_row_id(elem: ValueResponseStructured) -> int:
        return elem.row_id

    value_list: list[ValueResponseStructured]
    key = take_row_id
    if sort_key == StructuredSortKeys.year:
        key = take_year

    if override_sorting:
        if attr_record_year_exists:
            key = take_year
            reverse = True
        else:
            key = take_row_id
            reverse = False
    else:
        reverse = bool(order == StructuredOrderKeys.desc)

    for value_list in outputs.values():
        value_list.sort(key=key, reverse=reverse)

    return outputs


async def _get_project(
    request: Request,
    project_id: int,
    include_core_attributes: bool = True,
    include_user_info: bool = True,
    include_field_area: bool = True,
    include_user_sync_info: bool = False,
    include_ofs_status: bool = False,
    include_dndc_status: bool = False,
    include_dndc_results: bool = False,
) -> list[ProjectGeom]:
    """
    Get project by id.
    Cognitive Complexity: 56
    TODO: reduce complexity
    """

    async def stub() -> None:
        """Dummy coroutine function to have the same number of the output values from
        asyncio.gather() by any condition.
        suggestion: each task could return a dict of it's key and value. with this approach we should not need stubs
                    using the return of gather we could examine the keys and return the appropriate values.
        """
        return None

    task_get_project = asyncio.create_task(db_get_projects(request=request, project_ids=[project_id]))
    task_get_project_stats = (
        asyncio.create_task(get_fields_stats(request=request, project_ids=[project_id])) if include_field_area else None
    )
    projects = await task_get_project
    try:
        project = projects[0]
    except IndexError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"detail": "Project not found."},
        )

    # dedup farms
    # in the future, consider deduping mrv_farms table, and adding unique constraint to mrv_farms table
    project.farms = list({farm.core_farm_group_id: farm for farm in project.farms}.values())

    tasks_2 = []
    fs_field_ids = []
    if include_core_attributes:
        # get fields data
        # this can be optimized once we have api for getting core attributes of
        # list of fields.
        if fs_field_ids := list({field.fs_field_id for field in project.fields}):
            tasks_2 += [
                core_methods.get_fields_regions(field_ids=fs_field_ids),
                external_api.core_fields_get_api(fs_field_ids, include_deleted=True),
            ]
        else:
            tasks_2 += [stub(), stub()]
    else:
        tasks_2 += [stub(), stub()]

    monitor_api_version = await compute_monitor_api_version(
        request=request,
        project_id=project_id,
    )
    program_id = project.program_id
    program = await get_program_by_id(request=request, program_id=program_id)
    # if program optis is not enabled then we don't need to fetch optis status.
    program_optis_enabled = program.is_optis_enabled
    # check only if program optis is enabled.
    # if enrolment window is not open then we don't need to fetch optis status.
    is_enrolment_window_open: bool = False
    if program_optis_enabled:
        enrolment_date_range = await get_phase_date_window_by_type(
            request=request,
            program_id=program_id,
            phase_type=PhaseTypes.ENROLMENT,
        )
        if enrolment_date_range is not None:
            is_enrolment_window_open = (
                enrolment_date_range.start_date <= datetime.now().date() <= enrolment_date_range.end_date
            )
    if include_ofs_status and program_optis_enabled and is_enrolment_window_open:
        field_md5_id_map: dict[str, int] = {field.md5: field.id for field in project.fields}
        if monitor_api_version == MonitorApiVersion.V2:
            tasks_2.append(get_project_optis_status(request, field_md5_id_map, project.id))
    else:
        tasks_2.append(stub())

    if include_user_info:
        user_info_retriever = UserInfoRetriever(project.permissions)
        tasks_2.append(user_info_retriever.load_user_info())
    else:
        tasks_2.append(stub())

    if include_user_sync_info:
        # collate users to query
        permission_user_ids = [permission.user for permission in project.permissions]
        # requires admin auth on current fs-user-id
        user_sync_info_retriever = UserSyncInfoRetriever(permission_user_ids)
        tasks_2.append(user_sync_info_retriever.load_user_sync_info())
    else:
        tasks_2.append(stub())

    if include_dndc_status or include_dndc_results:
        tasks_2.append(
            calculate_dndc_status(
                request=request,
                project=project,
                include_dndc_results=include_dndc_results,
            )
        )
    else:
        tasks_2.append(stub())

    # TODO: better solution would be to chain coroutine(s) to not wait until
    #  all the tasks done and continue operations right after getting results
    (
        region,
        field_data_response,
        optis_status,
        _,
        _,
        _,
    ) = await asyncio.gather(*tasks_2)

    if include_ofs_status and program_optis_enabled and is_enrolment_window_open:
        project.ofs_status = optis_status.get("project")
        fields_with_status = []
        for field in project.fields:
            field.ofs_status = optis_status.get("fields").get(field.id)
            fields_with_status.append(field)
        project.fields = fields_with_status
        logger.debug("Optis fields statuses %s", optis_status)

    project.program_name = program.name
    project.program_type = program.program_type

    enrolments = await get_phases_by_program_id_and_type_(
        request=request,
        program_id=program_id,
        type_=PhaseTypes.ENROLMENT,
        return_type=PhaseResponse,
    )

    if enrolments:
        project.exceeds_project_area_limit = does_exceed_ha_limit(
            enrolments[0].params.get("limit_program_ha"),
            current_area=sum([i.area for i in project.fields]),
        )
        project.exceeds_program_area_limit = does_exceed_ha_limit(
            enrolments[0].params.get("limit_program_ha"),
            current_area=sum((await get.get_fields_from_program(request=request, program_id=program_id)).values()),
        )

    if include_user_info:
        for permission in project.permissions:
            permission.user_info = user_info_retriever.get_user_info(permission.user)

    # fetching latest sync dates for each fms for all users within the project
    if include_user_sync_info:
        user_sync_info = user_sync_info_retriever.get_user_sync_info()
        for permission in project.permissions:
            # get permission from retrieved list and attach
            if user_sync_info and str(permission.user) in user_sync_info:
                permission.user_sync_info = user_sync_info[str(permission.user)]

    if include_core_attributes and fs_field_ids:
        project.region = region  # update region

        if field_data_response.status_code == status.HTTP_200_OK:
            try:
                field_data = field_data_response.json()
                for field in field_data["result"]:
                    for project_field in project.fields:
                        if field["field_id"] == project_field.fs_field_id:
                            try:
                                project_field.core_attributes = CoreField(
                                    field_area=project_field.area,
                                    **field,
                                )
                            except ValidationError as e:
                                logger.error("Core gave an unexpected response: %s", e)
                                project_field.core_attributes = None
            except Exception as e:
                logger.exception(e)

    field_baseline_map = await get_or_create_fields_baselines(request, [f.id for f in project.fields])
    for field in project.fields:
        field_baseline = field_baseline_map.get(field.id)
        if field_baseline:
            field.is_returning = field_baseline.is_returning
            field.baseline_year = field_baseline.baseline_year

    if task_get_project_stats:
        # update field_area_ha
        logger.debug(project_stats := await task_get_project_stats)
        project.field_area_ha = project_stats.get(project.program_id, ProgramResponseStats()).area_ha

    return [project]


def create_entity_row_id_map(
    year_start: int,
    year_end: int,
    structured_values: ValuesResponseStructured,
    entity_ids: list[int],
    attributes: list[AttributeIdType],
) -> dict[int, dict[int, list[int]]]:
    """
    Takes values for a particular stage and project, a list of entity IDs, and attributes to pre-fill, and generates
    a list of row IDs to create by entity ID and year, e.g.::

        {
            entity_id_1: {
                2023: [0],
                2023: [1, 2],
            },
            entity_id_2: {
                2019: [4, 5],
                2018: [6],
            }
        }

    Args:
        year_start: Start year for stage
        year_end: End year for stage
        structured_values: Values for a particular project and stage. Maps entity IDs (i.e. field/farm/mob ID) to
            lists. Each of those lists is itself a list of `ValueResponseStructured`, a pydantic model with fields
            for entity ID, row ID, year and a dict that maps attribute IDs to values.
        entity_ids: Depending on the entity type of the stage in question, this is the full list of field, farm, or
            mob IDs belonging to the project
        attributes: Attributes to pre-fill

    Returns:
        A dict mapping entity ID to another dict, which maps year to a list of row IDs to create
    """
    # returns dict of entity_id and value being dict of year and list of row_ids and values with attributes.
    # we create attribute combination which we have to prefill.
    # Assumption record year is always present, and other attributes data will be prefilled with record year
    # Example: for year 2022 - 2020 prefilling with tillage period fall and spring
    # 2022 -> fall
    # 2021 -> spring
    # 2021 -> fall
    # 2020 -> spring
    # 2020 -> fall
    # it's a very specific request for Attribute tillage period to have single season for end year.
    # we can support other attribute as well but we would need to define their class to generate default
    # values or add the support for attribute types to existing class here: app/projects/classes.py#L92

    tillage_period_data_creator = PrefillDataCreator(attributes=attributes)

    # existing_year_row_map is a dict of dicts of lists of row_ids, mapped to by `entity_id` and `record_year`, e.g.
    #   {
    #       entity_id_1: {
    #           record_year_4: [row_id_2, row_id_8, ...]
    #       }
    #   }
    # In other words, for a particular entity_id, we have a dict of record_years, each of which maps on to a list
    # of row_ids for values that exist for that record_year
    # 1
    existing_year_row_map: dict[int, dict[int, list[int]]] = {}
    entity_row_id_to_create: dict[int, dict[int, list[int]]] = {}

    year_row_data_creator = YearRowDataCreator(
        year_start=year_start,
        year_end=year_end,
        number_of_rows_per_year=tillage_period_data_creator.get_values_len(),
        first_year_single_row=tillage_period_data_creator.is_first_year_single_row(),
    )

    # Generate row IDs to create by year for entities that have existing values
    # 2
    for entity_id, record_year_values in structured_values.items():
        entity_id = int(entity_id)
        for record_year_value in record_year_values:
            record_year = record_year_value.year
            row_id = record_year_value.row_id
            # 3
            if existing_year_row_map.get(entity_id) is None:
                existing_year_row_map[entity_id] = {}
            if existing_year_row_map[entity_id].get(record_year) is None:
                existing_year_row_map[entity_id][record_year] = []

            existing_year_row_map[entity_id][record_year].append(row_id)

        # For a given entity_id (remembering that an entity_id can refer to a field, farm or mob id), get the
        # record year to list of row IDs mapping for that entity_id, and set it to year_row_data_creator
        year_row_data_creator.set_year_with_existing_values(
            year_with_existing_values=existing_year_row_map.get(entity_id, {})
        )
        entity_row_id_to_create[entity_id] = year_row_data_creator.generate()

    # For entities that don't have any existing values, generate row IDs
    for entity_id in entity_ids:
        year_row_data_creator.set_year_with_existing_values(year_with_existing_values={})
        if entity_row_id_to_create.get(entity_id) is None:
            entity_row_id_to_create[entity_id] = year_row_data_creator.generate()

    return entity_row_id_to_create


async def generate_autofill_values(
    request: Request,
    project_id: int,
    stage_data: dict[int, StageYearAttributesEntityType],
    field_ids: list[int],
    farm_ids: list[int],
    mob_ids: list[int],
    enabled_stages: bool = True,
) -> dict[EntityTypeChoices, dict[int, list[ValuesRequest]]]:
    """
    Given:
        - a project,
        - a collection of stages that have at least one autofill attribute,
        - field, farm and mob IDs this project owns

    generate values to add to the DB for all the missing values for autofill attributes

    Args:
        request: API call request object
        project_id: The project for which to generate autofill data
        field_ids: fields owned by this project
        farm_ids: farms owned by this project
        mob_ids: mobs owned by this project
        stage_data: A mapping of stage IDs to `StageYearAttributesEntityType`. This function expects that
            `autofill=True` attributes are listed in `StageYearAttributesEntityType.attributes`
        enabled_stages: By default, only return values from enabled stages.

    Returns:
        A dict of dicts of lists. Map entity type (i.e. farm, field, mob) to entity ID to a list of
            `ValueRequests`::
                {
                    EntityTypeChoices.field: {
                        entity_id_32: [ValuesRequest_1, ValuesRequest_2],
                        entity_id_4: [ValuesRequest_3, ValuesRequest_4],
                    },
                    EntityTypeChoices.farm: {},
                    EntityTypeChoices.mob: {},
                }
    """
    prefill_values: dict[EntityTypeChoices, dict[int, list[ValuesRequest]]] = {
        EntityTypeChoices.field: defaultdict(list),
        EntityTypeChoices.farm: defaultdict(list),
        EntityTypeChoices.mob: defaultdict(list),
    }
    # iterate each record year value
    for stage_id, stage in stage_data.items():
        attributes_to_prefill: list[AttributeIdType] = []
        is_record_year_attr_present = False
        for attr in stage.attributes:
            if attr.attribute_type in ALLOWED_ATTRIBUTES_FOR_PREFILLING:
                attributes_to_prefill.append(attr)
                if attr.attribute_type == AttributeTypes.record_year:
                    is_record_year_attr_present = True
            else:
                # The caller should have ensured that only stages with at least one autofill=True attribute should
                # be `stage_data`. See _get_stage_autofill_attribute_map_by_project_id()
                logger.warning(
                    f"only attribute type {ALLOWED_ATTRIBUTES_FOR_PREFILLING} are supported for prefilling, passed attr_type: {attr.attribute_type}"
                )

        if len(attributes_to_prefill) == 0 or not is_record_year_attr_present:
            # The caller should have ensured that only stages with at least one autofill=True attribute should
            # be `stage_data`. See _get_stage_autofill_attribute_map_by_project_id(). Furthermore, we expect
            # that every stage here has a record_year attribute
            logger.warning("need record_year attribute in stage to create values")
            continue

        # get existing values
        structured_values_resp: ValuesResponseStructured = await get_project_stage_entity_values(
            request=request,
            project_id=project_id,
            stage_id=stage_id,
            sort_key=StructuredSortKeys.row_id,
            enabled_stages=enabled_stages,
        )
        entity_ids = []
        if stage.entity_type == EntityTypeChoices.field:
            entity_ids = field_ids
        elif stage.entity_type == EntityTypeChoices.farm:
            entity_ids = farm_ids
        elif stage.entity_type == EntityTypeChoices.mob:
            entity_ids = mob_ids
        # Get row IDs we want to create, mapped by entity ID and year
        entity_row_ids_map: dict[int, dict[int, list[int]]] = create_entity_row_id_map(
            year_start=stage.year_start,
            year_end=stage.year_end,
            structured_values=structured_values_resp,
            entity_ids=entity_ids,
            attributes=attributes_to_prefill,
        )

        if stage.phase_type == PhaseTypes.ENROLMENT:
            progress = ProgressChoices.enrolment
        elif stage.phase_type == PhaseTypes.MONITORING:
            progress = ProgressChoices.monitoring
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": f"Record year is not supported for phase type: {stage.phase_type}",
                },
            )
        prefill_data_creator = PrefillDataCreator(attributes=attributes_to_prefill)
        for entity_id in entity_ids:
            year_row_id_map = entity_row_ids_map[entity_id]
            for record_year, row_ids in year_row_id_map.items():
                row_data: list[RowData] = prefill_data_creator.generate_prefill_attribute_row_data(
                    year=record_year,
                    row_ids=row_ids,
                )
                for data in row_data:
                    row_id = data.row_id
                    for attr_value in data.attribute_value:
                        attribute_id = attr_value.id
                        value = attr_value.value
                        prefill_values[stage.entity_type][entity_id].append(
                            ValuesRequest(
                                value=str(value),
                                locked=False,
                                confirmed=True,
                                progress=progress,
                                attribute_id=attribute_id,
                                row_id=row_id,
                                source=ImportDataSources.user,
                                confidence=None,
                            )
                        )

    return prefill_values


async def get_project_stage_entity_values(
    request: Request,
    project_id: int,
    stage_id: int,
    sort_key: StructuredSortKeys = StructuredSortKeys.year,
    order: StructuredOrderKeys = StructuredOrderKeys.asc,
    override_sorting: bool = False,
    enabled_stages: bool = True,
) -> ValuesResponseStructured:
    """
    Get values belonging to project `project_id` and stage `stage_id`. This stage will have `year_start` and
    `year_end` values.

    If a row of values has a record year attribute, and that year falls outside the range `[year_start, year_end]`
    inclusive, then the entire row isn't included in the result.

    The object returned maps entity IDs to lists. In other words, we map field/farm/mob ID to a list.
    Each of those lists is itself a list of `ValueResponseStructured`, a pydantic model with fields for
    entity ID, row ID, year and a dict that maps attribute IDs to values.

    Each list of `ValueResponseStructured` is sorted by `sort_key` (`row_id` or `year`) in `order` (ascending or
    descending), but if `override_sorting` is `True`, special-case handling is done:

        - If at least one `record_year` value was found, then sort by `year` descending
        - Otherwise, sort by `row_id` ascending

    Args:
        request: API call request object
        project_id: Get values belonging to this project id
        stage_id: Get values belonging to this stage id
        order: For each entity ID, order associated `list[ValueResponseStructured]` ascending or descending
        sort_key: For each entity ID, order associated `list[ValueResponseStructured]` by either `year` or `row_id`
        override_sorting: If `True`, use special case ordering - see description above for details.
        enabled_stages: By default, only return values from enabled stages.

    Returns:
        A dict that maps entity IDs to lists of `ValueResponseStructured`. Each `list[ValueResponseStructured]` is
        sorted by either `row_id` or `year`. See function description for more.
    """
    stages = await get_stages_by_ids(request=request, ids=[stage_id])
    entity_type = EntityTypeChoices.field
    if stages:
        stage = stages[0]
        entity_type = stage.entity_type
    if enabled_stages:
        stages = [stage for stage in stages if stage.enabled is True]
        if len(stages) == 0:
            return {}

    values = await get.get_values_from_project(
        request=request,
        project_id=project_id,
        stage_ids=[stage_id],
        return_empty=True,
        entity_type=entity_type,
    )
    values, attribute_ids = reformat_values_response(values, entity_type=entity_type)

    return await finalise_values_response_structured(
        request=request,
        processed=values,
        attribute_ids=attribute_ids,
        entity_type=entity_type,
        year_start=stage.year_start,
        year_end=stage.year_end,
        sort_key=sort_key,
        order=order,
        override_sorting=override_sorting,
    )


async def get_project_stages_entity_values(
    request: Request, project_id: int, enabled_stages: bool = True
) -> StageValuesResponseStructured:
    """This is inefficient.
    It should be refactored to use one query to get all values.
    """
    stages = await get_stages(request=request, project_id=project_id)
    async with Tasks() as tasks:
        for stage in stages:
            tasks.add(
                get_project_stage_entity_values(
                    request=request,
                    project_id=project_id,
                    stage_id=stage.id,
                    override_sorting=True,
                    enabled_stages=enabled_stages,
                )
            )
        stage_values = await tasks.complete_all()

    return {k.id: v for k, v in zip(stages, stage_values) if v}


def export_set_contract_void_status(
    input_orm_contracts: list[ProjectContracts],
) -> list[ProjectContractsNoBlobResponse]:
    ret = []
    for contract in input_orm_contracts:
        if contract.deleted_at is not None:
            contract.docusign_status = "voided"
        ret.append(ProjectContractsNoBlobResponse.from_orm(contract))
    return ret


async def get_stage_id_from_attributes(request: Request, attribute_ids: set[int]) -> int | None:
    """
    Get the stage ID from a list of attribute IDs.
    If there are attributes from different stages, return None
    """
    # need to get stage_id from the updated values:
    stages = await get_stages_from_attribute_ids(request=request, attribute_ids=list(attribute_ids))
    # we are hoping only one result
    stages = list(set(stages))
    if len(stages) != 1:
        return None
    return stages[0].id


async def _update_fields(
    request: Request,
    project_id: int,
    updates: dict[int, UpdateFieldRequest],
    allow_error_violations: bool = False,
    do_boundary_check: bool = True,
) -> list[fields_schema.FieldBasicResponse]:
    """
    Takes updates for fields and returns a list of updated field IDs
    Fills in all the missing info from core along the way too.

    Args:
        request: FastAPI call request object
        project_id: Project that owns these fields
        updates: A dict that maps MRV field IDs to UpdateFieldRequest objects
        allow_error_violations: Needed for FMI. If True, allow fields to be created, even if they break boundary rules
            with an `error` level enrolment experience.
        do_boundary_check: do boundary checks on updating fields
    """
    # Check that all the fields are actually in the project and not deleted
    field_ids_to_update = set(updates.keys())
    # Check there aren't more updates than core fields
    # Ie. we won't let it happen if there are dupe fields.
    if len([val.new_kml_group_id for val in updates.values()]) != len(field_ids_to_update):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "You can't set multiple fields to the same kml_group_id"},
        )
    # We deliberately want to update soft-deleted fields - hence filter_deleted=False
    #
    # I talked to Tomas about this, and from what I understood from our conversation, one use-case when doing FM import
    # is that a producer might upload values for a field which had been previously soft-deleted, and the boundary
    # might also be adjusted slightly (Mike said this isn't uncommon).
    #
    # In this case, _import_fms_to_values() calls this function, and follows it up with a call to
    # _create_fields(undelete=True), which means we want to update the boundary to the soft-deleted field and then
    # undelete it, rather than create a new mrv field.
    db_fields = await get_fields_by_project_id(
        request=request,
        project_id=project_id,
        field_ids=list(field_ids_to_update),
        filter_deleted=False,
    )
    non_deleted_db_field_ids = {field.id for field in db_fields if field.deleted_at is None}
    # filter out fields that aren't in project
    db_field_ids = {field.id for field in db_fields}
    update_dict: dict[int, UpdateFieldRequest] = {
        field: body for field, body in updates.items() if field in db_field_ids
    }
    # From the updates, we generate the db entities for field histories
    if len(update_dict) == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You can't update fields that don't exist",
        )
    program = await get_program_by_project_id(request, project_id)
    # Because this function can be called for soft-deleted fields, we need to make sure that we only do boundary
    # checks on fields that aren't soft-deleted
    check_requests = [update for field_id, update in update_dict.items() if field_id in non_deleted_db_field_ids]
    if do_boundary_check and check_requests:
        rule_check_results, core_fields_map = await _check_boundary_rule_violations(
            request=request,
            program_id=program.id,
            project_id=project_id,
            allow_error_violations=allow_error_violations,
            update_requests=check_requests,
        )
    histories_to_create = await generate_field_histories(
        to_update=update_dict, update_reason=FieldHistoryChangeReason.api
    )
    # Then we generate updates to the regular field
    field_updates = generate_field_updates(histories_to_create)
    # Then we apply changes to fields
    tasks = [update_field(request=request, id=key, update_data=value) for key, value in field_updates.items()]
    # finally we apply the field history updates
    tasks.append(create_fields_history(request=request, field_histories=histories_to_create))

    await async_tools.batch_async_runner(tasks=tasks)

    pydantic_fields_to_return = await get_fields_by_project_id(
        request=request,
        project_id=project_id,
        field_ids=list(field_updates),
        pydantic_return_type=fields_schema.FieldBasicResponse,
        filter_deleted=False,
    )
    if do_boundary_check:
        _add_boundary_check_results(rule_check_results, pydantic_fields_to_return, core_fields_map)
    return pydantic_fields_to_return


async def check_fields_in_bounds(request: Request, program_id: int, new_field_ids: list[int]) -> tuple[bool, set[int]]:
    field_id_set = set(new_field_ids)
    boundaries = await get_geoms_from_program_id(request=request, program_id=program_id)
    if boundaries:
        response = await external_api.get_fields_in_bounds(
            field_ids=new_field_ids, mrv_region_ids=[x[0] for x in boundaries]
        )
        good_fields = set(response.json()["result"])
        logger.debug(good_fields)
        bad_fields = field_id_set - good_fields
        if len(bad_fields) > 0:
            return False, bad_fields
    return True, set()


def _has_error_violations(rule_check_results: dict[str, list[FieldBoundaryRuleResult]]) -> bool:
    """
    Check to see if any of the field boundary rule checks resulted in an error

    Args:
        rule_check_results: A `dict` which maps md5 `str` (representing a field) to a list of
            `FieldBoundaryRuleResult`

    Returns:
        `True` if any of the boundary rule checks crossed the percentage threshold and resulted in an `error`,
        or False otherwise
    """
    # Go through each field
    for results_for_field in rule_check_results.values():
        # Go through each rule check result for that field
        for result in results_for_field:
            if result.error_level == FieldBoundaryCheckErrorLevel.error and result.threshold_crossed:
                return True
    return False


async def _save_boundary_rule_deviations(
    *,
    request: Request,
    project_id: int,
    rule_check_results: dict[str, list[FieldBoundaryRuleResult]],
    allow_error_violations: bool,
) -> list[BoundaryRuleDeviation]:
    """
    Save an entry in mrv_boundary_rule_deviation for each field that was checked for a boundary rule
    violation

    Args:
        request: FastAPI call request object
        project_id: project id of the project whose fields we're doing boundary checks for
        rule_check_results: results from `check_field_boundaries()`. Map each md5 to a list of boundary check results
        allow_error_violations: flag that field will be created/updated anyway, regardless of rule check outcome

    Returns:
        A list of BoundaryRuleDeviation objects that were saved to the database
    """
    instances: list[BoundaryRuleDeviation] = []
    mrv_fields = await get_fields_by_md5s_and_project_id(request, list(rule_check_results), project_id)
    mrv_fields_by_md5 = {field.md5: field for field in mrv_fields}
    for md5, field_results in rule_check_results.items():
        for result in field_results:
            field = mrv_fields_by_md5.get(md5)
            instances.append(
                BoundaryRuleDeviation(
                    md5=md5,
                    field_id=field.id if field else None,
                    percent_overlap=result.pct_deviation,
                    rule_config_id=result.rule_config_id,
                    threshold_crossed=result.threshold_crossed,
                    allow_anyway=allow_error_violations,
                    field_check_error=result.field_check_error,
                )
            )
    return await create.create(
        request=request,
        instances=instances,
        orm_type=BoundaryRuleDeviation,
        translate=False,
        return_orm=True,
    )


def _generate_overlaps_from_intersections(
    *,
    source_md5: str,
    deviation_id: int,
    source_field_id: int | None,
    intersections: list[Intersection],
    all_overlapped_with_fields_by_md5: dict[str, list[Fields]],
    field_overlaps: list[ProgramFieldOverlap],
) -> bool:
    """
    Given a list of Intersection objects, generate ProgramFieldOverlap objects for each intersection

    Args:
        source_md5: md5 of the boundary that is being checked
        deviation_id: id of the BoundaryRuleDeviation object that corresponds to the boundary that is being checked
        source_field_id: mrv_field id of the field that is being checked, or None if this is a new boundary that is
            about to be registered to the program
        intersections: list of Intersection objects that represent the overlaps between the source boundary and the
            registered fields in the program that overlap with it
        all_overlapped_with_fields_by_md5: dict that maps all md5s of fields that are overlapped with, to lists of
            Fields ORM objects
        field_overlaps: list of ProgramFieldOverlap objects to append to

    Returns:
        A boolean indicating whether the source boundary overlaps with itself
    """
    includes_self_overlap = False
    for intersection in intersections:
        if matching_fields := all_overlapped_with_fields_by_md5.get(intersection.intersecting_id):
            if self_overlapping := (source_md5 == intersection.intersecting_id):
                # If this is a self-overlapping boundary, we added this Intersection manually (it didn't come from
                # Boundaries Service) and we set its area to 0. We need to use the correct area here, which is the area
                # of the self-overlapping field.
                area_intersection_ha = matching_fields[0].area
                includes_self_overlap = True
            else:
                area_intersection_ha = intersection.area_intersection_m2 / M2_PER_HECTARE
            for field in matching_fields:
                # source_field_id will be None if it's a new boundary that is about to be registered to the program,
                # and it will be the mrv field id of the already registered field that is being checked when endpoint
                # `check_field_boundaries` is called
                if field.id == source_field_id:
                    # In practice, this happens with self-overlapping fields when we're calling check_field_boundaries
                    # on the program. Let's say two fields with the same md5, both already registered in the program,
                    # end up here. Each one will get its own BoundaryRuleDeviation object (a row in
                    # table mrv_boundary_rule_deviation) with field_id set to its mrv field id. When we're generating
                    # ProgramFieldOverlap objects for the overlaps, we don't want to include the overlap with itself.
                    continue
                field_overlaps.append(
                    ProgramFieldOverlap(
                        deviation_id=deviation_id,
                        field_id=field.id,
                        field_md5=field.md5,
                        percentage_overlap=max(
                            intersection.percent_intersection_first, intersection.percent_intersection_second
                        ),
                        area_intersection_ha=area_intersection_ha,
                        is_self_overlap=self_overlapping,
                    )
                )
    return includes_self_overlap


async def _get_overlapped_with_fields(
    *, request: Request, program_id: int, program_field_overlaps: FeatureIntersections
) -> dict[str, list[Fields]]:
    """
    Given a FeatureIntersections object containing the results of the field boundary checks, return a dict that maps
    all the md5s that are overlapped with, to lists of Fields objects (one list per md5).

    Args:
        request: FastAPI call request object
        program_id: program id of the program whose fields we're doing boundary checks for
        program_field_overlaps: FeatureIntersections object containing the results of the field boundary checks

    Returns:
        A dict that maps md5s to lists of Fields objects. Each md5 key maps to a list contains Fields whose md5 matches
            the key
    """
    all_overlapped_with_md5s: set[str] = set()
    for intersections in program_field_overlaps.feature_intersections.values():
        for intersection in intersections:
            all_overlapped_with_md5s.add(intersection.intersecting_id)
    all_overlapped_with_fields = await get_fields_by_md5s_and_program_id(
        request, list(all_overlapped_with_md5s), program_id
    )
    all_overlapped_with_fields_by_md5: dict[str, list[Fields]] = defaultdict(list)
    for field in all_overlapped_with_fields:
        all_overlapped_with_fields_by_md5[field.md5].append(field)

    return all_overlapped_with_fields_by_md5


async def _save_program_field_overlaps(
    *,
    request: Request,
    program_id: int,
    created_deviations: list[BoundaryRuleDeviation],
    program_field_overlaps: FeatureIntersections,
    rules_orm: list[BoundaryRuleConfig],
) -> None:
    """
    Given a list of saved BoundaryRuleDeviation, for each BoundaryRuleDeviation that has a rule type of
    `FieldBoundaryCheckRule.program_fields`, i.e. checking whether a boundary overlaps registered fields in the program,
    save the overlaps in the `mrv_program_field_overlap` table.

    mrv_program_field_overlap has a many-to-one relationship with mrv_boundary_rule_deviation, i.e. a given row
    (or boundary) in mrv_boundary_rule_deviation, can overlap with multiple fields in the program, and each of these
    overlaps is saved in mrv_program_field_overlap.

    Args:
        request: FastAPI call request object
        program_id: program id of the program whose fields we're doing boundary checks for
        created_deviations: list of saved BoundaryRuleDeviation of all rule types
        program_field_overlaps: FeatureIntersections object containing the results of the field boundary checks which
            check for overlaps between fields in the program
        rules_orm: list of BoundaryRuleConfig objects
    """
    # Find rule config for "Program fields" rule type
    program_overlap_rule = next(
        filter(lambda rule: rule.rule == FieldBoundaryCheckRule.program_fields, rules_orm), None
    )
    if program_overlap_rule is None:
        return
    # Create a dict that maps md5 to the deviation object - only contains deviations of the "Program fields" rule type
    deviations_by_md5 = {
        deviation.md5: deviation
        for deviation in created_deviations
        if deviation.rule_config_id == program_overlap_rule.id
    }
    # Get all mrv fields that are overlapped with
    all_overlapped_with_fields_by_md5 = await _get_overlapped_with_fields(
        request=request, program_id=program_id, program_field_overlaps=program_field_overlaps
    )
    field_overlaps: list[ProgramFieldOverlap] = []
    update_deviations: list[BoundaryRuleDeviation] = []
    for source_md5, intersections in program_field_overlaps.feature_intersections.items():
        if deviation := deviations_by_md5.get(source_md5):
            includes_self_overlap = _generate_overlaps_from_intersections(
                source_md5=source_md5,
                deviation_id=deviation.id,
                source_field_id=deviation.field_id,
                intersections=intersections,
                all_overlapped_with_fields_by_md5=all_overlapped_with_fields_by_md5,
                field_overlaps=field_overlaps,
            )
            deviation.includes_self_overlap = includes_self_overlap
            update_deviations.append(deviation)
    if update_deviations:
        await update.update(request=request, instances=update_deviations)
    if field_overlaps:
        await create.create(request=request, instances=field_overlaps, orm_type=ProgramFieldOverlap, translate=False)


async def _add_default_message_to_boundary_results(
    request: Request, results: dict[str, list[FieldBoundaryRuleResult]]
) -> None:
    """
    Add a default message to field boundary check results

    If a field boundary check result has an empty message, we send back a default message based on the error level
    of the rule config. This message is translated according to the user's locale.

    Args:
        request: FastAPI request object
        results: Maps MD5s to lists of boundary results. Empty messages in these results will be mapped to default
            strings
    """
    if results:
        user_info = await get_user_info(request.state.fs_user_id)
        for results_list in results.values():
            for result in results_list:
                if not result.message:
                    result.message = get_translated_default_boundary_rule_message(result.error_level, user_info)


def _create_core_fields_map(core_fields: list[CoreFieldT]) -> dict[int, CoreFieldT]:
    """
    Given a list of core fields, return a dict that maps core field id -> core field
    """
    return {field.field_id: field for field in core_fields}


async def _get_core_fields_from_update_request(
    core_fields_map: dict[int, CoreFieldT],
    core_fields: list[CoreFieldT] | None,
    update_requests: Collection[UpdateFieldRequest] | None,
) -> tuple[set[str], dict[str, CoreFieldsResponse], dict[int, CoreFieldT], set[str], dict[str, int]]:
    """
    Given a field update request, get the core field objects we're updating to, and the md5s for the core fields
    we're updating from.

    NOTE: This function is overly complicated because we have to support two modes of UpdateFieldRequest, one where
    `new_md5` is set and one where `new_kml_group_id` is set. Eventually, hopefully, we'll get rid of `new_kml_group_id`
    and we'll be able to simplify this function.

    Args:
        core_fields_map: Dict that maps core field ID -> core field. Might be empty and filled by this function
        core_fields: List of core fields to check - if this is not None, this function won't do anything
        update_requests: If core_fields is None, get list of core fields to check from this list of UpdateFieldRequest

    Returns:
        A tuple containing 5 things:
            - A set of md5s we're updating to: this might be empty if we're passing kml_group_ids
            - A `dict` which maps field MD5 (of the new boundary) to core field objects that we're checking (newly
                added core field). Will be empty if we're setting `update.new_md5`
            - A `dict` which maps core field ID to core field objects that we're checking (new core fields). Will be
                empty if we're setting `update.new_md5`
            - A `set` of md5s which should be excluded from checking against in the field overlap check (these are
                the md5s that we're updating from)
            - If any of the `update_requests` have `old_kml_group_id` and `new_md5`, this dict maps from `new_md5` to
                 `old_kml_group_id`, otherwise it is empty
    """
    exclude_md5s: set[str] = set()
    to_md5s: set[str] = set()
    old_kml_group_id_by_to_md5 = {}
    if core_fields is None and update_requests is not None:
        to_md5s = {field_update.new_md5 for field_update in update_requests if field_update.new_md5}
        old_kml_group_id_by_to_md5 = {
            field_update.new_md5: field_update.old_kml_group_id
            for field_update in update_requests
            if field_update.new_md5 and field_update.old_kml_group_id
        }
        tasks = Tasks()
        # Get core fields that we're updating to. We need them to get md5 values of fields we're updating to
        to_fields_ids = [
            field_update.new_kml_group_id for field_update in update_requests if field_update.new_kml_group_id
        ]
        if to_fields_ids:
            tasks.add(external_api.core_fields_get_api(to_fields_ids))
        # Get core fields that we're updating from. We need to exclude these fields when checking against for
        # overlaps
        from_field_ids = [
            field_update.old_kml_group_id for field_update in update_requests if field_update.old_kml_group_id
        ]
        if from_field_ids:
            tasks.add(external_api.core_fields_get_api(from_field_ids, include_deleted=True))
        all_task_response = await tasks.complete_all()
        if to_fields_ids:
            core_fields = parse_core_fields_response(all_task_response.pop(0))
        if from_field_ids:
            exclude_core_fields = parse_core_fields_response(all_task_response.pop(0))
            exclude_md5s = {field.md5 for field in exclude_core_fields}
        else:
            exclude_md5s = set()
        if core_fields:
            core_fields_map = _create_core_fields_map(core_fields)
    core_fields_by_md5 = {field.md5: field for field in core_fields} if core_fields else {}

    return to_md5s, core_fields_by_md5, core_fields_map, exclude_md5s, old_kml_group_id_by_to_md5


async def _check_boundary_rule_violations_for_rules(
    *,
    request: Request,
    program_id: int,
    project_id: int,
    allow_error_violations: bool,
    rules_orm: list[BoundaryRuleConfig],
    core_fields_map: dict[int, CoreFieldT],
    core_fields: list[CoreFieldT] | None = None,
    update_requests: Collection[UpdateFieldRequest] | None = None,
    enrolled_only: bool = False,
    eligible_for_measurement_only: bool = False,
) -> tuple[dict[str, list[FieldBoundaryRuleResult]], dict[int, CoreFieldT]]:
    """
    Given a program, a list of core fields and list of boundary rules, check whether these fields violate any of these
    rules.

    Args:
        request: FastAPI call request object
        program_id: Program ID, for which boundary rules may or may not be defined
        project_id: Project ID of the producer that is adding/updating fields whose boundaries we're checking
        allow_error_violations: Needed for FMI. If True, allow fields to be created, even if they break boundary rules
            with an `error` level enrolment experience.
        rules_orm: Rules to check
        core_fields_map: Dict that maps core field ID -> core field. Might be empty and filled by this function
        core_fields: List of core fields to check
        update_requests: If core_fields is None, get list of core fields to check from this list of UpdateFieldRequest
        enrolled_only: If True, only check fields and projects which are enrolled, where the project has
            reporting enabled and contract signed
        eligible_for_measurement_only: If True, check fields that have flag `measurement_eligibility` set
            to True

    Returns:
        A tuple containing two things:
            - A `dict` which maps field MD5 to boundary rule violations for that field
            - A `dict` which maps core field ID to `CoreFieldT`
    """
    configs_by_rule: FieldBoundaryConfigsByRule = FieldBoundaryConfigsByRule.parse_obj([])
    for rule_orm in rules_orm:
        if rule_orm.rule not in configs_by_rule:
            configs_by_rule[rule_orm.rule] = []
        configs_by_rule[rule_orm.rule].append(FieldBoundaryRuleConfig.from_orm(rule_orm))
    to_md5s, core_fields_by_md5, core_fields_map, exclude_md5s, old_kml_group_id_by_to_md5 = (
        await _get_core_fields_from_update_request(core_fields_map, core_fields, update_requests)
    )
    all_md5s_to_check = to_md5s | set(core_fields_by_md5)
    rule_check_results, program_field_overlaps = await check_field_boundaries(
        request=request,
        fields_md5=all_md5s_to_check,
        rules_to_apply=configs_by_rule,
        program_id=program_id,
        project_id=project_id,
        enrolled_only=enrolled_only,
        eligible_for_measurement_only=eligible_for_measurement_only,
        exclude_md5s=exclude_md5s,
    )
    await _add_default_message_to_boundary_results(request, rule_check_results)
    created_deviations = await _save_boundary_rule_deviations(
        request=request,
        project_id=project_id,
        rule_check_results=rule_check_results,
        allow_error_violations=allow_error_violations,
    )
    if created_deviations and program_field_overlaps:
        await _save_program_field_overlaps(
            request=request,
            program_id=program_id,
            created_deviations=created_deviations,
            program_field_overlaps=program_field_overlaps,
            rules_orm=rules_orm,
        )
    has_errors = _has_error_violations(rule_check_results)
    if has_errors and not allow_error_violations:
        results_by_field_id = {}
        # The key for each set of results can be one of 3 values:
        #   - the new kml group ID, if we know it
        #   - the old kml group ID, if we know it
        #   - the new md5
        # If none of those yield a key for the result dict, we have to raise a ValueError
        for core_field_md5, field_results in rule_check_results.items():
            if core_field_md5 in core_fields_by_md5:
                key = core_fields_by_md5[core_field_md5].field_id
            else:
                key = old_kml_group_id_by_to_md5.get(core_field_md5) or core_field_md5
            if not key:
                raise ValueError(f"No key found for results_by_field_id {program_id} {project_id}")
            results_by_field_id[key] = [
                FieldBoundaryRuleResponse(**result.dict()).dict()
                for result in field_results
                if result.threshold_crossed
            ]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": FIELD_BOUNDARY_CHECK_ERROR,
                "results": results_by_field_id,
            },
        )
    return rule_check_results, core_fields_map


async def _check_boundary_rule_violations(
    *,
    request: Request,
    program_id: int,
    project_id: int,
    allow_error_violations: bool,
    core_fields: list[CoreFieldT] | None = None,
    update_requests: Collection[UpdateFieldRequest] | None = None,
    enrolled_only: bool = False,
    eligible_for_measurement_only: bool = False,
) -> tuple[dict[str, list[FieldBoundaryRuleResult]], dict[int, CoreFieldT]]:
    """
    Given a program and a list of core fields, see if there are any boundary rules, and if so, check whether these
    fields violate any of these rules.

    Args:
        request: FastAPI call request object
        program_id: Program ID, for which boundary rules may or may not be defined
        project_id: Project ID of the producer that is adding/updating fields whose boundaries we're checking
        allow_error_violations: Needed for FMI. If True, allow fields to be created, even if they break boundary rules
            with an `error` level enrolment experience.
        core_fields: List of core fields to check
        update_requests: If core_fields is None, get list of core fields to check from this list of UpdateFieldRequest
        enrolled_only: If True, only check fields and projects which are enrolled, where the project has
            reporting enabled and contract signed
        eligible_for_measurement_only: If True, check fields that have flag `measurement_eligibility` set
            to True

    Returns:
        A tuple containing two things:
            - A `dict` which maps field MD5 to boundary rule violations for that field
            - A `dict` which maps core field ID to `CoreFieldT`
    """

    logger.debug(
        f"_check_boundary_rule_violations: program_id={program_id} project_id={project_id} "
        f"core field ids={[core_field.field_id for core_field in core_fields] if core_fields else ''}"
    )

    if core_fields is None and update_requests is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "update_requests must be set if core_fields is None"},
        )

    rule_check_results: dict[str, list[FieldBoundaryRuleResult]] = {}
    core_fields_map: dict[int, CoreFieldT] = _create_core_fields_map(core_fields) if core_fields else {}

    # Check feature flag - this will be removed once feature is stable
    # TODO MRV-1915: Can we get rid of CHECK_FIELD_BOUNDARIES?
    if not settings.CHECK_FIELD_BOUNDARIES:
        return rule_check_results, core_fields_map

    # The only reason we order by BoundaryRuleConfig.id is to make tests deterministic (the tests rely on rules
    # being checked in the same order that they were inserted into the DB)
    filters = [
        get.Filter(
            id_field=BoundaryRuleConfig.program_id,
            ids=[program_id],
        )
    ]
    rules_orm: list[BoundaryRuleConfig] = await get.generic_get(
        request=request,
        orm_type=BoundaryRuleConfig,
        filters=filters,
        filter_sql_expr=and_(BoundaryRuleConfig.enabled.is_(True)),
        empty_return=True,
        order_by_cols=[BoundaryRuleConfig.id],
    )

    if rules_orm:
        rule_check_results, core_fields_map = await _check_boundary_rule_violations_for_rules(
            request=request,
            program_id=program_id,
            project_id=project_id,
            allow_error_violations=allow_error_violations,
            rules_orm=rules_orm,
            core_fields_map=core_fields_map,
            core_fields=core_fields,
            update_requests=update_requests,
            enrolled_only=enrolled_only,
            eligible_for_measurement_only=eligible_for_measurement_only,
        )
    return rule_check_results, core_fields_map


def _add_boundary_check_results(
    results: dict[str, list[FieldBoundaryRuleResult]],
    fields: list[fields_schema.FieldBasicResponse],
    core_fields_map: dict[int, CoreFieldT],
) -> None:
    if results:
        for field_to_return in fields:
            core_field = core_fields_map[field_to_return.fs_field_id]
            field_to_return.boundary_rule_violations = [
                FieldBoundaryRuleResponse(**result.dict())
                for result in results[core_field.md5]
                if result.threshold_crossed
            ]


@elasticapm.async_capture_span()
async def _create_fields(
    request: Request,
    project_id: int,
    fields: list[fields_schema.FieldBasicRequest],
    request_optis: bool = True,
    invalidate_dndc: bool = Query(True),
    rerun_optis: bool = False,
    undelete: bool = False,
    allow_error_violations: bool = False,
    set_field_lineage: bool = False,
) -> list[fields_schema.FieldBasicResponse]:
    # Get project's enrolment to check fields limits
    if len(fields) == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Your list must contain some elements",
        )

    # If contract signed and user not super admin, then don't allow adding more fields
    contract = await get_project_contract_latest_signed(request=request, project_id=project_id)
    if (
        contract
        and contract.signed_at is not None
        and contract.docusign_status == EnvelopeStatus.completed
        and not await is_original_user_super_admin(request=request)
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You cannot add more fields once the contract has been signed.",
        )
    # get and Dismiss older notifications
    notifications = await get_notifications(
        session=request.state.sql_session,
        settings=settings,
        project_id=project_id,
    )
    notification_ids = [notification.id for notification in notifications]
    await mark_dismissed(
        session=request.state.sql_session,
        notification_ids=notification_ids,
        project_id=project_id,
    )

    project = await get_project_with_fields(request=request, project_id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    to_add_field_ids = [field.fs_field_id for field in fields]
    program_id = project.program_id
    in_bounds, bad_fields = await check_fields_in_bounds(
        request=request, program_id=program_id, new_field_ids=to_add_field_ids
    )
    if in_bounds is False:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Some fields are not in the allowed region",
                "disallowed_fields": list(bad_fields),
            },
        )
    enrolment_open, enrolment = await check_program_enrolment_open(request=request, program_id=program_id)
    if enrolment_open is False:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Either your program doesn't have an enrolment stage, or enrolment is not open at this time."
            },
        )

    # Get fields md5s and area to save
    core_fields = parse_core_fields_response(
        await external_api.core_fields_get_api([field.fs_field_id for field in fields])
    )
    farm_map = {field.farm_id: field.farm_name for field in core_fields}

    logger.debug("Fields response %s", core_fields)

    current_project_area = sum([float(i.area) for i in project.fields])
    new_field_area = sum([field.area for field in core_fields])

    # Check if new fields exceed project area limit
    if does_exceed_ha_limit(
        enrolment.params.get("limit_project_ha"),
        current_area=current_project_area,
        new_area=new_field_area,
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Project has exceeded project area limit. Please remove some fields.",
        )

    rule_check_results, core_fields_map = await _check_boundary_rule_violations(
        request=request,
        program_id=program_id,
        project_id=project_id,
        allow_error_violations=allow_error_violations,
        core_fields=core_fields,
    )
    await _create_project_farms(
        request=request,
        project_id=project_id,
        program_id=program_id,
        farm_request=FarmRequest(
            core_farm_groups=[
                CoreFarmGroupRequest(core_farm_group_id=farm_id, farm_name=farm_name)
                for farm_id, farm_name in farm_map.items()
            ]
        ),
    )

    all_fields: set[int] = {i.fs_field_id for i in fields}
    undeleted_fields_set: set[Fields] = set()
    undeleted_fields: list[Fields] = []
    fields_to_return: list[Fields] = []
    # undelete old fields
    if undelete:
        undeleted_fields_set = set(await undelete_fields(request=request, project_id=project_id, field_ids=all_fields))
        undeleted_fields = list(undeleted_fields_set)
    # We only want to create fields that aren't being undeleted
    # Also don't want to create fields that already exist, also don't want to throw an error, just don't do it.
    new_fields: set[int] = (
        all_fields - {i.fs_field_id for i in undeleted_fields_set} - {field.fs_field_id for field in project.fields}
    )
    # create new fields
    fields_to_create = []
    for field in fields:
        if field.fs_field_id in new_fields:
            entry = core_fields_map[field.fs_field_id]
            fields_to_create.append(
                CreateField(
                    fs_field_id=field.fs_field_id,
                    kml_id=entry.kml_id,
                    core_region_id=entry.region_id,
                    farm_id=entry.farm_id,
                    md5=entry.md5,
                    area=entry.area,
                    parent_project_id=project_id,
                )
            )

    if len(fields_to_create) > 0:
        fields_to_return += await db_create_fields(
            request=request,
            instances=fields_to_create,
        )
        core_fields_for_created = [core_field for core_field in core_fields if core_field.field_id in new_fields]
        histories_to_create = await generate_field_histories(
            to_update={i.id: UpdateFieldRequest(new_kml_group_id=i.fs_field_id) for i in fields_to_return},
            update_reason=FieldHistoryChangeReason.api,
            core_resp=core_fields_for_created,
        )
        await create_fields_history(request=request, field_histories=histories_to_create)
    mrv_field_id_map = {field.fs_field_id: field.id for field in fields_to_return}

    coros = []
    monitor_api_version = await compute_monitor_api_version(
        request=request,
        project_id=project_id,
    )
    if request_optis and settings.OPTIS_ENABLE:
        if monitor_api_version == MonitorApiVersion.V2:
            coros.append(
                run_optis_v2(
                    request=request,
                    program_id=program_id,
                    project_id=project_id,
                    core_fields_map=core_fields_map,
                    mrv_field_id_map=mrv_field_id_map,
                    rerun_optis=rerun_optis,
                )
            )
    if invalidate_dndc:
        for field_id in mrv_field_id_map.keys():
            coros.append(create_notification_for_results_invalidation(request=request, field_id=field_id))
    await async_tools.batch_async_runner(coros)

    pydantic_fields_to_return = [
        fields_schema.FieldBasicResponse.from_orm(field) for field in fields_to_return + undeleted_fields
    ]
    _add_boundary_check_results(rule_check_results, pydantic_fields_to_return, core_fields_map)

    # Notify the subscribers (field lineage handler) of a fields created event
    await event_bus.publish(
        event=FieldsCreatedEvent(
            program_id=program_id,
            project_id=project_id,
            field_ids=[field.id for field in pydantic_fields_to_return],
            set_field_lineage_and_relationship=set_field_lineage,
        ),
        request=request,
    )

    return pydantic_fields_to_return


def ensure_nonzero_ghg(
    carbon_data: DndcResultsOutputPrice, fields_data: dict[int, FieldData]
) -> DndcResultsOutputPrice:
    """
    # FSB-13877 if abs(GHG) is 0.0 then make it -0.1, just to make sure there is some difference
    # update fields_with_results each field
    # this is a hack to make sure that the contract has a value and is not Zero
    # see ticket + Slack Conversation,
    """

    for field_id, field_data in carbon_data.fields_with_results.items():
        if "ghg" not in field_data:
            continue

        ghg = field_data["ghg"]
        if abs(ghg) == 0.0:
            area = fields_data.get(field_id, {}).field_area
            if area == 0.0:
                area = 1
            field_data["ghg"] = -0.1 * area * core_const.HECTARE_TO_ACRE

        carbon_data.fields_with_results[field_id] = field_data

    return carbon_data


async def check_all_field_callback_recieved(
    request: Request, request_store_id: int | None = None, request_store: RequestStore | None = None
) -> bool:
    """
    Check if all fields have recieved callback and saved in the response store.
    """
    if request_store is None:
        request_store = await get_request_store_by_id(request=request, request_store_id=request_store_id)
    payload = request_store.payload
    boundary_ids = payload.get("inputs", {}).get("boundary_ids", [])
    response_stores = await get_response_store_by_request_store_id_and_md5s(
        request=request, request_store_id=request_store_id, md5s=boundary_ids
    )
    return len(response_stores) == len(boundary_ids)


async def check_all_request_store_done(request: Request, project_id: int) -> bool:
    """
    Check if all request store have recieved callback and saved in the response store.
    """
    request_stores = await get_latest_request_stores_by_project_id(request=request, project_id=project_id)
    tasks = [
        asyncio.create_task(check_all_field_callback_recieved(request=request, request_store_id=request_store.id))
        for request_store in request_stores
    ]
    out = True
    # check if all callback is received or not for store.
    for task in asyncio.as_completed(tasks):
        out = out or await task
    return out


async def get_project_fields_md5_id_map(request: Request, project_id: int) -> dict[str, int]:
    """
    Get a map of md5 to field id for a project
    """
    fields = await get_fields_by_project_id(request=request, project_id=project_id)
    return {field.md5: field.id for field in fields}


async def get_request_store_id_and_md5_optis_response_map(
    request: Request,
    field_data: list[OptisFieldV2],
    field_md5_id_map: dict[str, int],
    request_store_id: int | None = None,
    job_id: str | None = None,
) -> RequestStoreIdMd5OptisRespMapPair:
    if request_store_id is None:
        request_store = await get_request_store_by_job_id(request=request, job_id=job_id)
        if request_store is None:
            logger.info("No request store found for job id: %s", job_id)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No request store found for {job_id = }",
            )
        # if request store status not in waiting for callback, then return
        if request_store.status != RequestStoreStatus.WAITING_FOR_CALLBACK:
            return RequestStoreIdMd5OptisRespMapPair(
                request_store_id=request_store.id,
                md5_optis_response_map={},
            )
        request_store_id = request_store.id

    md5_optis_response_map: dict[str, dict] = {}
    for field_resp in field_data:
        field_md5 = field_resp.boundary_id
        field_id = field_md5_id_map.get(field_md5)
        if field_id is None:
            continue
        optis_resp = field_resp.result
        if optis_resp is None:
            logger.info("No optis response received for field: %s", field_id)
            continue

        optis_resp_json = optis_resp.dict(exclude={"start_date"})
        md5_optis_response_map[field_md5] = optis_resp_json

    return RequestStoreIdMd5OptisRespMapPair(
        request_store_id=request_store_id,
        md5_optis_response_map=md5_optis_response_map,
    )


def create_field_id_date_optis_response_map(
    optis_responses: dict[str, dict],
    field_md5_id_map: dict[str, int],
    only_reduced_and_conventional_till: bool = True,
) -> dict[int, dict[date, list[dict[str, list | None]]]]:
    """
    This method creates map of field md5 and list of responses which has same
    start_date(tillage, cover_crop) or plant_date(commodity_crop). Later this can be sorted on the basis of keys to create the values.
    """
    out: dict[int, dict[date, list[dict]]] = {}
    for field_md5, response_dict in optis_responses.items():
        field_id = field_md5_id_map.get(field_md5)
        if field_id is None:
            return {}
        if out.get(field_id) is None:
            out[field_id] = {}
        main_crop_type = response_dict.get("main_crop_type", [])
        if main_crop_type is None:
            main_crop_type = []
        green_cover = response_dict.get("green_cover", [])
        if green_cover is None:
            green_cover = []
        tillage = response_dict.get("tillage", [])
        if tillage is None:
            tillage = []

        def update_dict(
            original_d: dict[int, dict[date, list[dict]]],
            field_id: int,
            date_key: date,
            data: dict,
        ) -> dict[int, dict[date, list[dict]]]:
            if date_key not in original_d[field_id]:
                original_d[field_id][date_key] = [data]
            else:
                original_d[field_id][date_key].append(data)
            return original_d

        for crop_data in main_crop_type:
            date_key = crop_data.get("plant_date")
            crop_data["event_type"] = MonitorEventType.COMMODITY_CROP
            if date_key is not None:
                out = update_dict(out, field_id, date_key, crop_data)

        for cover_data in green_cover:
            date_key = cover_data.get("end_date")
            cover_data["event_type"] = MonitorEventType.COVER_CROP
            if date_key is not None:
                out = update_dict(out, field_id, date_key, cover_data)

        for tillage_data in tillage:
            # Ignore anything other than conventional or reduced till.
            if only_reduced_and_conventional_till and tillage_data.get("tillage_intensity") not in (
                MONITOR_CONVENTIONAL_TILLAGE_VALUE,
                MONITOR_REDUCED_TILLAGE_VALUE,
            ):
                continue
            date_key = tillage_data.get("start_date")
            tillage_data["event_type"] = MonitorEventType.TILLAGE
            if date_key is not None:
                out = update_dict(out, field_id, date_key, tillage_data)
    return out


def yield_row_id(
    field_id_date_optis_response_map: dict[int, dict[date, list[dict]]],
) -> Generator[tuple[int, dict, int], int, None]:
    """
    generates row id for each response. Increments the row id by 1 for tillage.
    While incrementing the row id by 1 for each green_cover and commodity found.
    """
    # just to initiate the generator.
    yield (0, {}, 0)
    field_id_event_type_row_id_map: dict[int, dict[MonitorEventType, int]] = {}
    for field_id, date_optis_response_map in field_id_date_optis_response_map.items():
        if field_id not in field_id_event_type_row_id_map:
            field_id_event_type_row_id_map[field_id] = {}
            field_id_event_type_row_id_map[field_id][MonitorEventType.TILLAGE] = 0
            field_id_event_type_row_id_map[field_id][MonitorEventType.COMMODITY_CROP] = 0
        event_dates = sorted(date_optis_response_map.keys(), reverse=True)
        for event_date in event_dates:
            optis_response_list = date_optis_response_map[event_date]
            for optis_response in optis_response_list:
                event_type = optis_response.get("event_type")
                if event_type is None:
                    continue
                if event_type == MonitorEventType.COVER_CROP:
                    event_type = MonitorEventType.COMMODITY_CROP
                increment = yield (
                    field_id,
                    optis_response,
                    field_id_event_type_row_id_map[field_id][event_type],
                )
                field_id_event_type_row_id_map[field_id][event_type] += increment


async def create_stage_values(
    request: Request,
    program_id: int,
    project_id: int,
    field_id_date_optis_response_map: dict[int, dict[date, list[dict]]],
) -> dict[int, list[ValuesRequest]]:
    """
    This method creates stage values for each field on the basis of config and date.
    """
    field_id_reader_obj_map: dict[int, dict[MonitorEventType, Type[BaseEventReader]]] = {}
    for field_id in field_id_date_optis_response_map:
        if field_id not in field_id_reader_obj_map:
            field_id_reader_obj_map[field_id] = {}
        for event_type in MonitorEventType:
            reader_class = READER_FACTORY.get_service(
                event_type=event_type,
            )
            reader_obj = reader_class(
                request=request, program_id=program_id, project_id=project_id, data_source=ImportDataSources.optis
            )
            await reader_obj.setup()
            if reader_obj.get_event_config(event_type=event_type) is not None:
                field_id_reader_obj_map[field_id][event_type] = reader_obj
    values_request_by_field: dict[int, list[ValuesRequest]] = defaultdict(list)
    field_event_row_gen = yield_row_id(field_id_date_optis_response_map)
    increment = 1
    # just to initiate the generator.
    next(field_event_row_gen)
    while True:
        try:
            field_id, event, row_id = field_event_row_gen.send(increment)
            event_type = event.get("event_type")
            reader_obj = field_id_reader_obj_map[field_id].get(event_type)
            if reader_obj is None:
                increment = 0
                continue
            values = await reader_obj.create_values_request(
                data=event,
                row_id=row_id,
            )
            if len(values) > 0:
                increment = 1
            else:
                increment = 0
            values_request_by_field.setdefault(field_id, []).extend(values)
        except StopIteration:
            break
    return values_request_by_field


async def create_project_contract(
    request: Request, project_id: int, phase_id: int, body: ProjectContractCreateRequest
) -> dict[str, str]:
    """
    Create a project contract
    return url as first string, and any error as second string
    """
    user_data = await core_methods.get_user_info(request.state.fs_user_id)
    if user_data is None:
        return {"error": f"User not found in core: user_id: {request.state.fs_user_id}"}

    phase: Phases = await get_phase_by_id(request=request, phase_id=phase_id)
    if phase is None:
        return {"error": f"Phase not found: {phase_id}"}

    contract_type = await get_contract_type_from_phase_id(
        request=request,
        phase_id=phase_id,
        phase_type=phase.type_,
    )

    if contract_type not in [
        phase_enums.ContractType.DOCUSIGN,
        phase_enums.ContractType.CONTRACT_TEMPLATE,
    ]:
        return {"error": f"Unknown Contract Type: {contract_type}"}

    project: ProjectGeom = await db.get_project_by_id(request=request, project_id=project_id)
    if project is None:
        return {"error": f"project not found project_id: project_id: {project_id}"}

    program: Programs = await get_program_by_id(request=request, program_id=project.program_id)
    if program is None:
        return {"error": f"program not found program_id: program_id: {project.program_id}"}

    project_contract = await get_project_contract(request=request, project_id=project.id)
    if has_completed_contract(project_contract):
        return {"error": f"Already a signed contract found for project: {project.id}"}

    if contract_type == phase_enums.ContractType.DOCUSIGN:
        return await create_project_contract_docusign(
            request=request,
            program=program,
            project=project,
            user_data=user_data,
            phase_id=phase_id,
            body=body,
            project_contract=project_contract,
            old_envelope_id=project_contract.docusign_envelope_id if project_contract else None,
        )

    if contract_type == phase_enums.ContractType.CONTRACT_TEMPLATE:
        return await create_project_contract_bare_template(
            request=request,
            program=program,
            project=project,
            phase_id=phase_id,
            project_contract=project_contract,
        )
    # Update project status when the contract is created
    await back_fill_contract_status_for_projects(request, project_ids=[project_id])
    await back_fill_field_status(request, project_ids=[project_id])
    await back_fill_project_status_for_projects(request, project_ids=[project_id])

    return {"error": f"Unknown Contract Type: {contract_type}"}


def has_completed_contract(project_contract: ProjectContracts) -> bool:
    """
    we check for EnvelopeStatus.completed, even if we are not using Docusign
    as we want to make sure we can't override it
    """
    return (
        project_contract
        and project_contract.docusign_envelope_id
        and project_contract.docusign_status == EnvelopeStatus.completed
    )


async def create_project_contract_docusign(
    request: Request,
    program: Programs,
    project: ProjectGeom,
    user_data: User,
    phase_id: int,
    body: ProjectContractCreateRequest,
    project_contract: ProjectContracts,
    old_envelope_id: str | None = None,
) -> dict:
    """
    Create Docusign Contract for a project
    """
    docusign_client_id = program.docusign_client_id

    docusign_obj = await get_docusign_object(
        request=request,
        docusign_client_id=docusign_client_id,
        program_id=program.id,
    )

    docusign_args = await docusign_obj.get_args_for_sending_contract(
        request,
        project.id,
        user_data,
        program,
        phase_id,
        body.redirect_url,
        override=False,
    )

    if old_envelope_id:
        try:
            await docusign_obj.void_envelope(docusign_args, old_envelope_id)
        except Exception as e:
            logger.warning(f"void_envelope error, this is usually OK, error:{e}")

    metadata = {
        "project_id": project.id,
        "program_id": program.id,
        "phase_id": phase_id,
        "reference": f"{program.id}-{project.id}",
        "currency_char": program.currency_char,
    }

    # check to see iof we need to update the template_id in docusign_args.get(template_args)
    # figure out if this program is using a Docusign Template ID
    template_id = docusign_args.get("envelope_args", {}).get("template_id", None)

    if template_id is not None:
        # if we have a template id then we need to create the envelope differently
        envelope_id = await docusign_obj.create_envelope_for_template(request, docusign_args, metadata)
        if not envelope_id:
            return {"error": f"Failed to create envelope from template_id:{template_id}  project_id: {project.id}"}
    else:
        envelope_id = await docusign_obj.create_envelope(request, docusign_args, metadata)
        if not envelope_id:
            return {"error": f"Failed to create standard envelope for project_id:{project.id}"}

    url = docusign_obj.create_recipient(docusign_args, envelope_id)
    if url is None:
        return {"error": f"Failed to get Docusign URL for project.id: {project.id}"}

    await create_or_update_contract(
        request=request,
        project_id=project.id,
        docusign_envelope_id=envelope_id,
        docusign_status=EnvelopeStatus.sent,
        phase_id=phase_id,
        project_contract=project_contract,
    )

    return {"url": url}


async def create_project_contract_bare_template(
    request: Request,
    program: Programs,
    project: ProjectGeom,
    phase_id: int,
    project_contract: ProjectContracts,
) -> dict:
    """
    create a contract from a template, this will just be a PDF we store in the DB
    TODO: NOT fully working yet, it was not working before, but now we get some type of content in the DB
    """
    contract_args = {
        "project_id": project.id,
        "phase_id": phase_id,
        "override": False,
        "currency_char": program.currency_char,
    }

    project_contract_content = await get_document(request, contract_args)
    pdf_obj = pdfkit.from_string(project_contract_content)
    contract_blob = base64.b64encode(pdf_obj)
    await create_or_update_contract(
        request=request,
        project_id=project.id,
        docusign_envelope_id=None,
        docusign_status=None,
        contract_blob=contract_blob,
        phase_id=phase_id,
        project_contract=project_contract,
    )
    url = f"{settings.MRV_EXTERNAL_URL}{paths.base}/{project.id}/contract"

    return {"url": url}


async def get_attr_ids_to_delete(
    request: Request,
    values_with_field_id: dict[int, list[ValuesRequest]],
    record_year_attr_ids: list[int],
) -> list[int]:
    """
    Get the list of record year attr ids to delete existing values.
    """
    attr_ids = []
    for _, values in values_with_field_id.items():
        for value in values:
            if value.attribute_id and value.attribute_id in record_year_attr_ids:
                attr_ids.append(value.attribute_id)
    return attr_ids


def init_record_year_stage_id_map(
    stage_id_with_year_map: dict[int, StageYearWithId],
) -> dict[int, dict[int, list[int]]]:
    """
    initialize record year stage id map with false values for all years.
    """

    record_year_stage_id_created_map: dict[int, dict[int, list[int]]] = {}
    for stage_id, stage_years in stage_id_with_year_map.items():
        record_year_stage_id_created_map[stage_id] = {}
        # you get data for extra years so keep a buffer of 3 years in the past for now.
        # TODO: this should be configurable
        for stage_year in range(stage_years.year_end, stage_years.year_start - 1, -1):
            record_year_stage_id_created_map[stage_id][stage_year] = []

    return record_year_stage_id_created_map


def finalise_row_id_for_stage(
    record_year_stage_id_created_map: dict[int, dict[int, list[int]]],
) -> dict[int, dict[int, dict[int, int]]]:  # dict[stage_id, dict[record_year, dict[old_row_id, new_row_id]]
    """
    finalise row ids for each stage.
    """
    record_year_old_new_row_id_map: dict[int, dict[int, dict[int, int]]] = (
        {}
    )  # dict[stage_id, dict[record_year, dict[old_row_id, new_row_id]]
    for stage_id, record_year_map in record_year_stage_id_created_map.items():
        record_year_old_new_row_id_map[stage_id] = {}
        row_id_tracker = 0
        for record_year, row_ids in record_year_map.items():
            if len(row_ids) == 0:
                record_year_old_new_row_id_map[stage_id][record_year] = {-1: row_id_tracker}
                row_id_tracker += 1
            else:
                new_row_ids = {old_row_id: row_id_tracker + i for i, old_row_id in enumerate(row_ids)}
                row_id_tracker += len(row_ids)
                record_year_old_new_row_id_map[stage_id][record_year] = new_row_ids
    return record_year_old_new_row_id_map


def update_existing_row_ids_and_add_new_record_year_values(
    values_to_create: dict[int, list[ValuesRequest]],
    stage_id_with_year_map: dict[int, StageYearWithId],
    attr_id_stage_id_map: dict[int, int],
    stage_id_record_year_attr_id_map: dict[int, int],
) -> dict[int, list[ValuesRequest]]:
    # keep track of record years that needs to be created for each stage.
    # reset whenever field id changes.
    # Why Do we need to do this?
    # Because we have record years created by default in the stages for fields with empty values. We first need to delete them
    # then create rows with record years and other values. Figuring out which row_id values belongs to is the hardest part,
    # because we want to keep them in order so that other APIs related to deleting and updating values don't get affected.
    # Logic -
    # 1. Group values by field id, stage id, row id and record year. To use them later to figure out which row id to use for existing values.
    # 2. Create new record years values for each stage for missing record years.
    # 3. Update row ids for values coming from monitor API
    # 4. This is all this method this does, rest creation of values is handled by other apis, this ensures all values have correct row_id.
    # Recent change -
    # 1. We started receiving values outside of stage years from monitor API, so we need to filter them out.
    # 2. For filtering them we need to make sure all the values of the same rows are filtered.
    # 3. Once filtered we can send the values to the value creation API without worrying about extra rows being created.
    # Assumption - We want to keep the old data, we don't want to keep the future data in the stage. That just messes up
    # with other APIs.

    record_year_attr_ids = list(stage_id_record_year_attr_id_map.values())
    values_grouped_by_field_id_stage_id_row_id_record_year: dict[int, dict[int, dict[int, int]]] = (
        {}
    )  # dict[field_id, dict[stage_id, dict[row_id, record_year]]
    final_values_per_field: dict[int, dict[int, dict[int, dict[int, int]]]] = (
        {}
    )  # dict[field_id, dict[stage_id, dict[record_year, dict[old_row_id, new_row_id]]]]
    for field_id, values in values_to_create.items():
        record_year_stage_id_created_map: dict[int, dict[int, list[int]]] = init_record_year_stage_id_map(
            stage_id_with_year_map
        )
        values_grouped_by_field_id_stage_id_row_id_record_year[field_id] = {}
        for value_request in values:
            attribute_id = value_request.attribute_id
            if attribute_id in record_year_attr_ids:
                record_year = int(value_request.value)
                row_id = value_request.row_id
                stage_id = attr_id_stage_id_map[attribute_id]
                if record_year_stage_id_created_map[stage_id].get(record_year) is not None:
                    record_year_stage_id_created_map[stage_id][record_year].append(row_id)
                    if values_grouped_by_field_id_stage_id_row_id_record_year[field_id].get(stage_id) is None:
                        values_grouped_by_field_id_stage_id_row_id_record_year[field_id][stage_id] = {}
                    values_grouped_by_field_id_stage_id_row_id_record_year[field_id][stage_id][row_id] = record_year

        final_values_per_field[field_id] = finalise_row_id_for_stage(record_year_stage_id_created_map)

    # update row ids for existing values
    for field_id, values in values_to_create.items():
        # remove values which are outside of stage years.
        value_request_to_be_removed: list[int] = []
        for idx, value_request in enumerate(values):
            row_id = value_request.row_id
            stage_id = attr_id_stage_id_map[value_request.attribute_id]
            try:
                record_year = values_grouped_by_field_id_stage_id_row_id_record_year[field_id][stage_id][row_id]
            except KeyError:
                value_request_to_be_removed.append(idx)
                continue
            value_request.row_id = final_values_per_field[field_id][stage_id][record_year][row_id]

        # sort the indices in reverse so that index doesn't change after removing an element.
        value_request_to_be_removed.sort(reverse=True)
        for idx in value_request_to_be_removed:
            del values[idx]

    # create new record years values for each stage for missing record years.
    for field_id, stage_record_year in final_values_per_field.items():
        for stage_id, record_year_row_id_map in stage_record_year.items():
            for record_year, row_id_map in record_year_row_id_map.items():
                for old_row_id, new_row_id in row_id_map.items():
                    if old_row_id == -1:
                        value_request = ValuesRequest(
                            value=str(record_year),
                            locked=False,
                            confirmed=True,
                            attribute_id=stage_id_record_year_attr_id_map[stage_id],
                            row_id=new_row_id,
                            source=ImportDataSources.user,
                            confidence=None,
                            progress=ProgressChoices.enrolment,  # fetch this from stage later.
                        )
                        values_to_create[field_id].append(value_request)
                    else:
                        continue

    return values_to_create


async def get_field_id_date_optis_response_map(
    request: Request,
    project_id: int,
    field_data: list[OptisFieldV2],
    job_id: str | None = None,
    request_store_id: int | None = None,
    received_at: datetime | None = None,
    only_reduced_and_conventional_till: bool = True,
) -> tuple[dict[int, dict[date, list[dict[str, list | None]]]], int, list[int]]:
    field_md5_id_map = await get_project_fields_md5_id_map(request=request, project_id=project_id)
    pair: RequestStoreIdMd5OptisRespMapPair = await get_request_store_id_and_md5_optis_response_map(
        request=request,
        request_store_id=request_store_id,
        job_id=job_id,
        field_data=field_data,
        field_md5_id_map=field_md5_id_map,
    )
    if not pair.md5_optis_response_map:
        logger.info("Callback is empty for project: %s", project_id)
        return ({}, pair.request_store_id, [])
    # 1. Save each response for a field to mrv_monitor_response_store
    response_stores = await create_if_not_exists_new_response_stores(
        request=request,
        request_store_id=pair.request_store_id,
        field_md5_id_map=field_md5_id_map,
        response=pair.md5_optis_response_map,
        received_at=received_at,
    )

    # 5. create field_id, date and optis_response map to create values later.
    return (
        create_field_id_date_optis_response_map(
            pair.md5_optis_response_map, field_md5_id_map, only_reduced_and_conventional_till
        ),
        pair.request_store_id,
        [response_store.id for response_store in response_stores],
    )


async def create_project_values_from_optis_v2(
    request: Request,
    project_id: int,
    field_data: list[OptisFieldV2],
    job_id: str | None = None,
    request_store_id: int | None = None,
    received_at: datetime | None = None,
) -> None:
    program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
    if not program_id:
        return
    field_id_date_optis_response_map, request_store_id, response_store_ids = await get_field_id_date_optis_response_map(
        request=request,
        project_id=project_id,
        field_data=field_data,
        job_id=job_id,
        request_store_id=request_store_id,
        received_at=received_at,
    )
    all_field_ids = list(field_id_date_optis_response_map.keys())
    # 6. Create enrolment values from each sorted object. Increment row_id on the basis of some logic.
    values_to_create = await create_stage_values(
        request=request,
        program_id=program_id,
        project_id=project_id,
        field_id_date_optis_response_map=field_id_date_optis_response_map,
    )

    # update row_ids of the value requests on the basis of stage years to show.
    # only update values for the fields which are present in the response.
    # 1. get record_year_attr_ids for the program.
    stage_id_record_year_attr_id_map = await get_stage_id_attribute_id_for_record_year(
        request=request, program_id=program_id
    )
    record_year_attr_ids: list[int] = list(stage_id_record_year_attr_id_map.values())

    # 2. get field and attr to delete values.
    field_ids_with_values = list(values_to_create.keys())
    attr_ids = await get_attr_ids_to_delete(
        request=request,
        values_with_field_id=values_to_create,
        record_year_attr_ids=record_year_attr_ids,
    )
    # 4. Get stage start year and max stage end year.
    stages_with_year_and_id: list[StageYearWithId] = (
        await get_stage_with_prefill_optis_option_and_ui_years_by_program_id(request=request, program_id=program_id)
    )
    # 5. convert to map of stage_id and year.
    stage_id_with_year_map: dict[int, StageYearWithId] = {stage.id: stage for stage in stages_with_year_and_id}

    # 5. get attr_id_stage_id_map for the above stage_ids.
    stage_ids = list(stage_id_with_year_map.keys())
    attr_id_stage_id_map = await get_attribute_id_stage_id_map_by_stage_ids(request=request, stage_ids=stage_ids)

    # 6. Update the row_ids of the values as well as add new record year rows.
    values_to_create = update_existing_row_ids_and_add_new_record_year_values(
        values_to_create=values_to_create,
        stage_id_with_year_map=stage_id_with_year_map,
        attr_id_stage_id_map=attr_id_stage_id_map,
        stage_id_record_year_attr_id_map=stage_id_record_year_attr_id_map,
    )

    # TODO: get entity type and pass here.
    if values_to_create:
        # 7. Delete the existing record year values for the fields.
        await delete_field_values(request=request, field_ids=field_ids_with_values, attribute_ids=attr_ids)
        await update_values(
            request=request,
            program_id=program_id,
            map=values_to_create,
        )

    # update store status if needed and find field status.

    # send notification for field
    if job_id and len(all_field_ids) == 1:
        # this happens only when we are reading callback from monitor api.
        # this guarantees we only have single field in field_ids computed above.
        field_id = all_field_ids[0]
        await send_optis_callback_notification(
            request=request,
            project_id=project_id,
            entity_id=field_id,
            entity=NotificationEntityTypes.Fields,
            message=FIELD_ALL_DONE,
        )

    # 9. Create notifications with all the new values created.
    # add check for project status success after callback recieved.
    are_all_fields_done = await check_all_field_callback_recieved(
        request=request,
        request_store_id=request_store_id,
    )
    if are_all_fields_done:
        # Update status of request store job to success, if all responses
        # are received
        await update_request_store_by_id(
            request=request,
            request_store_id=request_store_id,
            status=RequestStoreStatus.SUCCESS,
        )
        # update status of request group if last field of last request store raised exception
        are_all_requests_done = await check_all_request_store_done(
            request=request,
            project_id=project_id,
        )
        if are_all_requests_done:
            await update_existing_request_group(
                request=request,
                project_id=project_id,
                status=RequestStoreStatus.SUCCESS,
            )
            await send_optis_callback_notification(
                request=request,
                project_id=project_id,
                entity_id=project_id,
                entity=NotificationEntityTypes.Projects,
                message=PROJECT_ALL_DONE,
            )
    # updated processed at of response store once the values are created.
    await update_response_store_processed_at_and_error(
        request=request,
        response_store_ids=response_store_ids,
    )


async def process_monitor_exception(request: Request, error: str, project_id: int, field_md5: str, job_id: str) -> None:
    request_store = await get_request_store_by_job_id(
        request=request,
        job_id=job_id,
    )
    # send notification for field error
    fields = await get_fields_by_md5s_and_project_id(
        request=request,
        md5s=[field_md5],
        project_id=project_id,
    )
    if len(fields) == 1:
        field_id = fields[0].id
        await send_optis_callback_notification(
            request=request,
            project_id=project_id,
            entity_id=field_id,
            entity=NotificationEntityTypes.Fields,
            message=FIELD_NO_DATA,
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Field not found for {field_md5 = }",
        )
    existing_response_stores = await get_response_store_by_request_store_id_and_md5s(
        request=request,
        request_store_id=request_store.id,
        md5s=[field_md5],
    )
    if existing_response_stores:
        await update_response_store_processed_at_and_error(
            request=request,
            response_store_ids=[response_store.id for response_store in existing_response_stores],
            error=error,
        )
    else:
        await create_if_not_exists_new_response_stores(
            request=request,
            request_store_id=request_store.id,
            field_md5_id_map={
                field_md5: field_id,
            },
            response={
                field_md5: {
                    "tillage": [],
                    "green_cover": [],
                    "main_crop_type": [],
                }
            },
            received_at=datetime.now(tz=UTC),
            processed_at=datetime.now(tz=UTC),
            error_message=error,
        )

    # update status of request store if last field raised exception
    are_all_fields_done = await check_all_field_callback_recieved(
        request=request,
        request_store_id=request_store.id,
    )
    if are_all_fields_done:
        # Update status of request store job to Failed, if all responses
        # are received
        await update_request_store_by_id(
            request=request,
            request_store_id=request_store.id,
            status=RequestStoreStatus.FAILED,
        )
        # update status of request group if last field of last request store raised exception
        are_all_requests_done = await check_all_request_store_done(
            request=request,
            project_id=project_id,
        )
        if are_all_requests_done:
            await update_existing_request_group(
                request=request,
                project_id=project_id,
                status=RequestStoreStatus.FAILED,
            )
            await send_optis_callback_notification(
                request=request,
                project_id=project_id,
                entity_id=project_id,
                entity=NotificationEntityTypes.Projects,
                message=FIELD_ERROR,
            )


async def get_project_optis_status(
    request: Request, field_md5_id_map: dict[str, int], project_id: int
) -> dict[str, OptisStatusChoices | None]:
    field_response_stores = await get_response_store_by_project_and_fields(
        request=request, project_id=project_id, field_ids=list(field_md5_id_map.values())
    )

    fields_statuses: dict[int, OptisStatusChoices] = {}
    for field_response in field_response_stores:
        # If there is a response in a field store then it's a success.
        if field_response.response:
            fields_statuses[field_response.field_id] = OptisStatusChoices.success
        else:
            fields_statuses[field_response.field_id] = OptisStatusChoices.failed

    field_with_response: set[int] = {field_response.field_id for field_response in field_response_stores}
    request_stores = await get_request_stores_by_project_id(
        request=request,
        project_id=project_id,
    )
    # get all field md5s which are requested from monitor, only those fields will be
    # checked for status. Rest of the fields which are not called through monitor
    # will be marked as success.
    # Fields are still waiting for the response from monitor.
    waiting_for_callback_monitor_md5s: set[str] = set({})
    # fields which are failed or cancelled.
    failed_or_cancelled_md5s: set[str] = set({})
    for request_store in request_stores:
        rs_md5s: list[str] = []
        if request_store.payload is not None:
            rs_md5s = request_store.payload.get("inputs", []).get("boundary_ids", [])
        if request_store.status == RequestStoreStatus.WAITING_FOR_CALLBACK:
            waiting_for_callback_monitor_md5s.update(rs_md5s)
        if request_store.status in (RequestStoreStatus.FAILED, RequestStoreStatus.CANCELLED):
            failed_or_cancelled_md5s.update(rs_md5s)

    wfc_monitor_ids: set[int] = {
        field_md5_id_map[md5] for md5 in waiting_for_callback_monitor_md5s if md5 in field_md5_id_map
    }

    fields_in_progress: set[int] = wfc_monitor_ids - field_with_response
    for field_id in fields_in_progress:
        fields_statuses[field_id] = OptisStatusChoices.in_progress

    for field_md5 in failed_or_cancelled_md5s:
        failed_field_id = field_md5_id_map.get(field_md5)
        if failed_field_id is None:
            continue
        if failed_field_id in field_with_response and failed_field_id not in fields_statuses:
            fields_statuses[failed_field_id] = OptisStatusChoices.success
        elif failed_field_id not in field_with_response:
            fields_statuses[failed_field_id] = OptisStatusChoices.failed

    request_group = await get_latest_request_group_by_project_id(request=request, project_id=project_id)
    if request_group is None:
        logger.info("No request group found for project %s", project_id)
        return {"project": None, "fields": {}}

    if request_group.status == RequestStoreStatus.FAILED:
        return {"fields": fields_statuses, "project": OptisStatusChoices.failed}
    elif request_group.status == RequestStoreStatus.SUCCESS:
        return {"fields": fields_statuses, "project": OptisStatusChoices.success}

    return {"fields": fields_statuses, "project": OptisStatusChoices.in_progress}


async def get_project_contract_docusign(request: Request, project_id: int) -> ProjectDocusignData | None:
    project = await get_project_object(request=request, id=project_id)
    if not project:
        logger.error("project_id: %s", project_id)
        return None

    project_contract = await get_project_contract(request=request, project_id=project_id)
    if not project_contract:
        logger.error("project does not have a docusign contract project_id: %s", project_id)
        return None

    if not project_contract.docusign_envelope_id:
        logger.error("project_id: %s does not have a docusign_envelope_id", project_id)
        return None

    program = await get_program(request=request, program_id=project.program_id)
    if not program:
        logger.error("program_id: %s", project.program_id)
        return None

    if not program.docusign_client_id:
        logger.error(
            "program program_id: %s does not have a docusign_client_id",
            project.program_id,
        )
        return None

    try:
        docusign_obj: Docusign = await get_docusign_object(
            request=request,
            docusign_client_id=program.docusign_client_id,
            program_id=project.program_id,
        )
    except HTTPException:
        logger.error("Unable to get docusign object for program_id: %s", project.program_id)
        return None

    docusign_args = await docusign_obj.get_args(request=request)

    envelope: Envelope = docusign_obj.get_envelope(
        args={
            "base_path": docusign_args.get("base_path"),
            "access_token": docusign_args.get("access_token"),
            "account_id": docusign_args.get("account_id"),
        },
        docusign_envelope_id=project_contract.docusign_envelope_id,
    )

    if envelope is None:
        logger.error(
            "Unable to fetch envelope from docusign with envelope_id: ",
            project_contract.docusign_envelope_id,
        )
        return None

    return ProjectDocusignData(
        program_id=project.program_id,
        project_id=project.id,
        envelope_id=project_contract.docusign_envelope_id,
        metadata=envelope.to_dict(),
    )


async def get_phase_completion_for_project(request: Request, project_id: int, stage_ids: list[int]) -> float:
    """
    Given a project and a list of stages, find the mean completion percentage for the project across those stages

    Args:
        request: FastAPI request object
        project_id: project to get completion percentage for
        stage_ids: list of stages - get mean completion percentage across all these stages

    Returns:
        mean completion percentage
    """
    tasks = Tasks()
    for stage_id in stage_ids:
        tasks.add(get_percentage_completion(request, project_id, stage_id))
    percentages = await tasks.complete_all()
    return sum(percentages) / len(percentages)


async def back_fill_contract_status_for_projects(request: Request, project_ids: list[int] | None = None) -> None:
    """
    Back fill the contract status for contracts and projects
    """
    # update contract status (running sequentially to prevent deadlock)
    await back_fill_generated_contract_status(request=request, project_ids=project_ids)
    await back_fill_signed_contract_status(request=request, project_ids=project_ids)
    await back_fill_voided_contract_status(request=request, project_ids=project_ids)

    # update project contract status based on contract status
    await back_fill_project_contract_status(request=request, project_ids=project_ids)


async def back_fill_project_status_for_projects(request: Request, project_ids: list[int] | None = None) -> None:
    """
    Back fill the project status derived from contract status and user project permissions
    Need to be run after back filling contract status
    """
    project_status_mapping: dict[ContractStatus | None, ProjectStatus] = {
        None: ProjectStatus.created,
        ContractStatus.generated: ProjectStatus.contract_pending,
        ContractStatus.signed: ProjectStatus.enrolled,
        ContractStatus.voided: ProjectStatus.contract_removed,
    }

    # update contract status
    for contract_status, project_status in project_status_mapping.items():
        await back_fill_project_status_from_contract_status(
            request=request, contract_status=contract_status, project_status=project_status, project_ids=project_ids
        )

    await back_fill_deleted_project_status(request=request, project_ids=project_ids)
    # update project status based on phase completion
    await back_fill_enrolled_project_status_from_phase_completion(request=request, project_ids=project_ids)


async def check_field_boundaries_for_project(
    *,
    request: Request,
    program_id: int,
    project_id: int,
    enrolled_only: bool,
    eligible_for_measurement_only: bool,
) -> None:
    """
    Do boundary field checks for all project fields (which haven't been soft-deleted)

    Each field is checked against all active rule configs in this program (see `BoundaryRuleConfig`). Set
    `allow_error_violations == True` so that if an error rule is broken, don't raise an exception. The results
    of all the checks are stored in `BoundaryRuleDeviation`.

    Args:
        request: FastAPI request object
        program_id: program containing this project
        project_id: do boundary check for all non-deleted fields in this program
        enrolled_only: If True, only check fields and projects which are enrolled, where the project has
            reporting enabled and contract signed
        eligible_for_measurement_only: If True, check fields that have flag `measurement_eligibility` set
            to True
    """
    try:
        logger.debug(f"START check_field_boundaries_for_project: project_id={project_id}")
        fields = await get_fields_by_project_id(request=request, project_id=project_id)
        core_fields = [get_core_field_from_mrv_field(field) for field in fields]
        await _check_boundary_rule_violations(
            request=request,
            program_id=program_id,
            project_id=project_id,
            allow_error_violations=True,
            core_fields=core_fields,
            enrolled_only=enrolled_only,
            eligible_for_measurement_only=eligible_for_measurement_only,
        )
        logger.debug(f"COMPLETED check_field_boundaries_for_project: project_id={project_id}")
    except Exception:
        logger.error(f"FAILED check_field_boundaries_for_project: project_id={project_id}")
        logger.error(traceback.format_exc())


async def delete_project_contract(
    request: Request,
    project_id: int,
    hard: bool = False,
    send_email: bool = True,
    docusign_deletion_method: EnvelopeDeletionMethod = EnvelopeDeletionMethod.purge,
) -> None:
    """
    Delete project contract
    Args:
        request: FastAPI request object
        project_id: delete project contract for this project
        hard: if True, hard contract from DB, otherwise soft delete (set deleted_at)
        send_email: if True, send email the user of this project
        docusign_deletion_method: purge or void
                Purge: Delete the envelope after a set retention period (can delete signed documents)
                Void: Void the envelope before document has been signed (cannot delete signed documents)
    """
    project = await get_project_by_id(request=request, project_id=project_id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": "Project not found"})

    contract = await get_project_contract(request=request, project_id=project_id, orm_type=ProjectContractsNoBlob)
    if contract is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail={"message": "Contract not found"})

    enrollment = await get_phase_from_project_id(
        request=request, project_id=project_id, type=phase_enums.PhaseTypes.ENROLMENT
    )
    program = await get_program_by_id(request=request, program_id=project.program_id)

    if (
        program.docusign_client_id
        and contract.docusign_envelope_id
        and enrollment.contract_type == phase_enums.ContractType.DOCUSIGN
    ):
        docusign_obj = await get_docusign_object(
            request=request,
            docusign_client_id=program.docusign_client_id,
            program_id=program.id,
        )

        try:
            docusign_args = await docusign_obj.get_args(request=request)

            if docusign_deletion_method == EnvelopeDeletionMethod.purge:
                await docusign_obj.purge_envelope(
                    args=docusign_args,
                    envelope_id=contract.docusign_envelope_id,
                )
            elif docusign_deletion_method == EnvelopeDeletionMethod.void:
                await docusign_obj.void_envelope(
                    args=docusign_args,
                    envelope_id=contract.docusign_envelope_id,
                )
            else:
                raise ValueError(f"Incorrect docusign deletion method: {docusign_deletion_method}")
        except HTTPException as e:
            # if docusign_args are not found, and the user is a super-admin, continue with deletion
            # https://regrow.atlassian.net/browse/MRV-1465
            caller_role_users = await get_role_users_by_user_id(request, request.state.fs_user_id)
            if not is_super_admin(caller_role_users):
                raise e

    await delete_contract_by_ids(request=request, contract_ids=[contract.id], hard=hard)
    await delete_contract_line_items_for_contract(request=request, contract_id=contract.id)
    await update_project_with_docusign_details_to_null(request=request, project_id=project_id)

    # mark phase as incomplete if contract being deleted
    await upsert_project_phase_completion(
        request=request, project_id=project_id, phase_id=enrollment.id, is_completed=False
    )

    if send_email:
        await send_delete_contract_email_to_user(
            request=request,
            user_id=contract.user,
            enrolment_end_date=enrollment.end_date,
            locale=program.locale.name,
            support_inbox=program.support_inbox,
            support_phone_number=program.support_phone_number,
            program_name=program.name,
            project_id=project_id,
        )
    # update project statuses once contract is deleted
    await back_fill_contract_status_for_projects(request=request, project_ids=[project_id])
    await back_fill_field_status(request, project_ids=[project_id])
    await back_fill_project_status_for_projects(request, project_ids=[project_id])


async def send_delete_contract_email_to_user(
    request: Request,
    user_id: int,
    enrolment_end_date: datetime,
    locale: str,
    support_inbox: str,
    support_phone_number: str,
    program_name: str,
    project_id: int,
) -> None:
    permissions = await get_project_permissions(request, project_id)

    tasks.send_delete_contract_emails.delay(
        support_inbox=support_inbox,
        support_phone_number=support_phone_number,
        program_name=program_name,
        enrolment_end_date=format_datetime(
            datetime=enrolment_end_date,
            format="dd MMM, YYYY",
            locale=locale,
        ),
        emails=[(await get_user_info(i.user)).email for i in permissions],
        user_id=user_id,
        project_id=project_id,
        fs_user_id=request.state.fs_user_id,
        fs_impersonator_user_id=request.state.fs_impersonator_user_id,
    )


async def get_allowed_practice_changes_for_project(request: Request, project_id: int) -> set[PracticeChange]:
    """
    Return the set of permissible practice changes for the program which project belongs to
    """
    program_id = await get_program_id_by_project_id(request, project_id)
    return await get_practice_change_set_by_program_id(request, program_id)


async def get_project_phase_completion(
    request: Request, project_id: int, phase_id: int
) -> ProjectPhaseCompletionResponse:
    """
    Check if a project phase is completed (contract signed or marked as completed by user if it is contractless)
    """
    project_phase_completion = await db_get_project_phase_completion(
        request=request, project_id=project_id, phase_id=phase_id
    )

    return (
        ProjectPhaseCompletionResponse(
            project_id=project_id,
            phase_id=phase_id,
            is_completed=project_phase_completion.is_completed,
            allow_post_close_edit=project_phase_completion.allow_post_close_edit,
        )
        if project_phase_completion
        else ProjectPhaseCompletionResponse(
            project_id=project_id,
            phase_id=phase_id,
            is_completed=False,
            allow_post_close_edit=False,
        )
    )


async def update_project_phase_completion(
    request: Request, project_id: int, phase_id: int, is_completed: bool, allow_post_close_edit: bool = False
) -> ProjectPhaseCompletionResponse:
    """
    Update project phase completion status for a given phase
    """
    await upsert_project_phase_completion(
        request=request,
        project_id=project_id,
        phase_id=phase_id,
        is_completed=is_completed,
        allow_post_close_edit=allow_post_close_edit,
    )
    # update project status after phase completion is modified.
    await back_fill_contract_status_for_projects(request=request, project_ids=[project_id])
    await back_fill_field_status(request, project_ids=[project_id])
    await back_fill_project_status_for_projects(request, project_ids=[project_id])

    if is_completed is False:
        # reconcile event associations if phase changes from completed to not completed
        program_id = await get_program_id_by_project_id(request=request, project_id=project_id)
        await ses_integration.reconcile_event_associations.reconcile_event_associations_for_program(
            request, program_id, project_ids=[project_id], field_ids=None
        )

    return ProjectPhaseCompletionResponse(
        project_id=project_id,
        phase_id=phase_id,
        is_completed=is_completed,
        allow_post_close_edit=allow_post_close_edit,
    )


async def _check_updated_boundary(
    *, request: Request, program_id: int, project_id: int, updating_fields: list[Fields], update: UpdateFieldBoundary
) -> tuple[bool, dict[str, list[FieldBoundaryRuleResult]], dict[int, CoreFieldT]]:
    """
    If we're updating the boundary for a non-deleted MRV field, check the new boundary for boundary rule violations.

    This function needs to upload the geometry in `UpdateFieldBoundary.kml` to Core in order to have a md5 to check
    field boundary violations with.

    It will raise a 400 HTTPException if an error boundary violation is detected.

    Args:
        request: FastAPI request object
        program_id: The program ID for the program the producer is changing the boundary for
        project_id: The project ID for the project the producer is changing the boundary for
        updating_fields: The list of MRV fields we're updating. All these fields point to the same `fs_field_id`, the
            core field whose boundary we're going to update
        update: update request, contains the new geometry we need to check for boundary violations

    Returns:
        A tuple of the following:
            - A bool specifying whether the boundaries were checked
            - A dict mapping md5s to lists of rule configs that were checked for that boundary
            - A dict that maps core field IDs to Core field instances
    """
    boundaries_checked = False
    rule_check_results: dict[str, list[FieldBoundaryRuleResult]] = {}
    core_fields_map: dict[int, CoreFieldT] = {}
    if updating_fields:
        non_deleted_fields = [field for field in updating_fields if field.deleted_at is None]
        # We only do boundary checks on non-deleted fields
        if non_deleted_fields:
            if len(non_deleted_fields) > 1:
                raise ValueError(
                    f"More than one registered field with kml_group_id={update.from_kml_group_id} "
                    f"in project {project_id}"
                )
            # We're adjusting the boundary of a non-deleted field that is currently registered in this program, so
            # we need to check the new boundary.
            #
            # First, upload the geometry to core, without creating a core field, so that we have an md5 to pass to
            # boundary check code.
            response = await external_api.core_upload_kml_file_only(
                boundary=UploadCoreBoundary(kml=update.kml, core_field_name=update.core_field_name),
            )
            created_geometry = UploadKMLResponse.parse_obj(response.json()["result"])
            # If the boundary check results in an error, then it will raise an exception
            rule_check_results, core_fields_map = await _check_boundary_rule_violations(
                request=request,
                program_id=program_id,
                project_id=project_id,
                allow_error_violations=False,
                update_requests=[
                    UpdateFieldRequest(old_kml_group_id=update.from_kml_group_id, new_md5=created_geometry.md5)
                ],
            )
            boundaries_checked = True
    return boundaries_checked, rule_check_results, core_fields_map


async def _update_field_in_core(
    *, request: Request, update: UpdateFieldBoundary, get_core_fields_task: Coroutine[Any, Any, Response]
) -> tuple[CoreFieldT, dict]:
    """
    Upload new field geometry to Core. This will create a new Core field. Also, soft-delete old Core field whose
    boundary we're changing.

    If the field we're trying to update has already been soft-deleted, then this is most likely a caller error. Perhaps
    the caller has already updated this field and it was soft-deleted before? In this case, return status 404.

    Args:
        request: FastAPI request object
        update: update request, contains the new geometry we need to check for boundary violations
        get_core_fields_task: coroutine, that when awaited, will get the kml_groups row that needs to be soft-deleted

    Returns:
        The new core field that was created by uploading the new geometry
    """
    from_core_field = parse_core_fields_response(await get_core_fields_task)[0]
    if from_core_field.deleted_at:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "message": f"You cannot update the boundary of soft-deleted core field: {from_core_field.field_id}"
            },
        )
    is_user_really_super_admin = await is_original_user_super_admin(request)
    response = await external_api.core_upload_kmls(
        request.state.fs_user_id,
        boundaries=[
            UploadCoreField(kml=update.kml, core_farm_id=update.core_farm_id, core_field_name=update.core_field_name)
        ],
        max_area_ha=MAX_AREA_HA_SUPER_ADMIN if is_user_really_super_admin else MAX_AREA_HA,
    )
    full_field = response.json()["result"][0]["field"]
    created_core_field = CreateCoreFieldResponse.parse_obj(full_field)
    if created_core_field.kml_id != from_core_field.kml_id:
        # There's a small possibility that we've uploaded the exact same geometry, in which case we get back the same
        # kml_file ID, which means we don't need to delete the core field (doing so would be an error)
        # Pass settings.CORE_USER_ID here, since this DELETE request is internal to MRV, and not initiated by the user directly
        # So we can pass the permissions check on Core side, and allow producer to edit field, even if they don't have permission to delete it
        await external_api.delete_core_field(int(settings.CORE_USER_ID), update.core_farm_id, from_core_field.kml_id)

    return created_core_field, full_field


async def _update_mrv_fields(
    *,
    request: Request,
    project_id: int,
    updating_fields: list[Fields],
    update: UpdateFieldBoundary,
    created_core_field: CoreFieldT,
) -> FieldBasicResponse:
    """
    Updates rows from mrv_fields that correspond to an updated core field. This will generate a row in
    `mrv_fields_history` to track the change.

    This function will succeed even if there are no MRV fields corresponding to the core field we're updating.

    Args:
        request: FastAPI request object
        project_id: The project the mrv fields belong to
        updating_fields: List of MRV fields corresponding to `update.from_kml_group_id`. There might be many (see
            comments in this function)
        update: Contains `from_kml_group_id` that we want to map from
        created_core_field: Contains `kml_group_id` of core field we're mapping to

    Returns:
        `FieldBasicResponse` - this is returned even if there was no MRV field corresponding to the updated core field
    """
    # Don't do the update if the core field ID hasn't changed, which happens if the boundary is unchanged. Calling
    # _update_fields() in this case causes a spurious row in mrv_fields_history to be created.
    if updating_fields and update.from_kml_group_id != created_core_field.field_id:
        # There might be multiple fields with the same fs_field_id, e.g. this might happen if some of them are deleted
        # I'm not sure if this should happen, but it does, e.g. try the following query:
        #
        #   select count(id),
        #          fs_field_id,
        #          parent_project_id
        #   from mrv_fields
        #   group by fs_field_id, parent_project_id
        #   having count(id) > 1
        field_responses = await _update_fields(
            request,
            project_id=project_id,
            updates={
                field.id: UpdateFieldRequest(
                    old_kml_group_id=update.from_kml_group_id,
                    new_kml_group_id=created_core_field.field_id,
                )
                for field in updating_fields
            },
            do_boundary_check=False,
        )
        return field_responses[0]

    return FieldBasicResponse(
        parent_project_id=project_id, fs_field_id=created_core_field.field_id, farm_id=update.core_farm_id
    )


async def update_field_boundary(
    *, request: Request, project_id: int, update: UpdateFieldBoundary
) -> UpdatedFieldResponse:
    """
    Update one field boundary, which may or may not be registered in MRV.

    If the field has been registered to MRV but has been soft-deleted from MRV, then it will still update the geometry
    in the soft-deleted MRV field.

    If the field _is_ registered to MRV _and_ and it's _not_ soft-deleted, then the new boundary will be checked for
    boundary violations and a 400 error raised if any error level boundary violations are detected.

    If we have only warning-level boundary violations, the function will succeed, and the warning will be in
    `FieldBasicResponse.boundary_rule_violations`

    A new Core field will be created with the new boundary, if there are no boundary violation errors.

    **WARNING**: If _update_fields() fails for any reason and the transaction within it is rolled back, it will point
    to a soft-deleted core field. In the UI it will look as though the field is not enrolled, even though it actually
    is. If this happens we'll need to undo the soft-delete.

    Args:
        request: FastAPI request
        project_id: The project ID for the producer doing this update
        update: Geometry we're updating to, and the field/farm we're updating

    Returns:
        `FieldBasicResponse.fs_field_id` will contain the new Core field id if the boundary was successfully changed.
        `FieldBasicResponse.boundary_rule_violations` might contain boundary violations
    """
    # get the current time to use when polling for SES updates for event reconcilation at the end of this function
    update_time = datetime.now(tz=UTC)

    # All the calls to core in this and nested functions will raise an exception if they return 4xx/5xx
    get_core_fields_task = external_api.core_fields_get_api([update.from_kml_group_id], include_deleted=True)
    program_id = await get_program_id_by_project_id(request, project_id)
    if program_id is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"message": f"No program found for project {project_id}"},
        )

    # Get all mrv fields we're adjusting, if they're part of this project, even if they're deleted.
    updating_fields = await get_fields_by_fs_field_id(request, ids=[update.from_kml_group_id], project_id=project_id)
    boundaries_checked, rule_check_results, core_fields_map = await _check_updated_boundary(
        request=request, program_id=program_id, project_id=project_id, updating_fields=updating_fields, update=update
    )

    # If we got here, it means there were no boundary rule errors, so upload geometry (maybe for the second time),
    # this time creating a core field
    created_core_field, full_field = await _update_field_in_core(
        request=request, update=update, get_core_fields_task=get_core_fields_task
    )

    # Even if there are soft-deleted mrv fields which pointed to the old kml_group.id, call _update_fields() to reassign
    # them to the new kml_group.id - see comments inside _update_fields() for reasons why.
    field_response = await _update_mrv_fields(
        request=request,
        project_id=project_id,
        updating_fields=updating_fields,
        update=update,
        created_core_field=created_core_field,
    )

    if boundaries_checked:
        core_fields_map[created_core_field.field_id] = created_core_field
        _add_boundary_check_results(rule_check_results, [field_response], core_fields_map)

    # Notify the subscribers of a boundary update event
    await event_bus.publish(
        event=FieldBoundaryUpdatedEvent(
            program_id=program_id,
            project_id=project_id,
            field_id_to_old_md5={field.id: field.md5 for field in updating_fields},
            update_time=update_time,
        ),
        request=request,
    )

    return UpdatedFieldResponse(
        core_field=full_field, boundary_rule_violations=field_response.boundary_rule_violations or []
    )


async def rerun_optis_for_project_fields(
    request: Request,
    project_id: int,
    field_ids: list[int] | None = None,
) -> None:
    project = await get_project_with_fields(request=request, project_id=project_id)
    program_id = project.program_id
    notifications = await get_notifications(
        session=request.state.sql_session,
        settings=settings,
        project_id=project_id,
    )
    notification_ids = [notification.id for notification in notifications]
    await mark_dismissed(
        session=request.state.sql_session,
        notification_ids=notification_ids,
        project_id=project_id,
    )
    fields = await get_fields_by_project_id(
        request=request,
        project_id=project_id,
        field_ids=field_ids,
    )
    core_fields = parse_core_fields_response(
        await external_api.core_fields_get_api([field.fs_field_id for field in fields])
    )
    mrv_field_id_map = {field.fs_field_id: field.id for field in fields}
    _, core_fields_map = await _check_boundary_rule_violations(
        request=request,
        program_id=program_id,
        project_id=project_id,
        allow_error_violations=True,
        core_fields=core_fields,
    )

    await run_optis_v2(
        request=request,
        program_id=program_id,
        project_id=project_id,
        core_fields_map=core_fields_map,
        mrv_field_id_map=mrv_field_id_map,
        rerun_optis=False,
    )


async def fetch_missing_values_and_run_validation(
    request: Request,
    project_id: int,
    stage_id: int,
    attr_values: list[ProjStageAttVals],
    entity_type: EntityTypeChoices,
) -> tuple[bool, StageValidationErrors]:
    is_valid = True
    values_data: dict = defaultdict(lambda: defaultdict(list))
    for value in attr_values:
        values_data[value.field_id][value.row_id].append(
            EventValue(
                attribute_type=value.attribute_type,
                value=value.value,
            )
        )

    stages = await get_stages_by_ids(request, [stage_id])
    stage = stages[0]
    phase = (
        await get.generic_get(
            request=request,
            orm_type=Phases,
            filters=[get.Filter(id_field=Phases.id, ids=[stage.phase_id])],
            empty_return=True,
        )
    )[0]

    validation_errors = await AttributeValuesValidator.validate_stages(
        request,
        {stage_id: values_data},
        phase.program_id,
        project_id,
        phase.type_,
        entity_type,
        check_required_fields=False,
        use_db_data=True,
    )
    filtered_errors: dict = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: defaultdict)))

    if validation_errors:
        for entity_id, row_ids in values_data.items():
            for row_id, attrs in row_ids.items():
                for attr in attrs:
                    attr_type = attr.attribute_type
                    if (
                        stage_id in validation_errors
                        and entity_id in validation_errors[stage_id]
                        and row_id in validation_errors[stage_id][entity_id]
                        and attr_type in validation_errors[stage_id][entity_id][row_id]
                    ):
                        is_valid = False
                        filtered_errors[entity_id][row_id][attr_type] = validation_errors[stage_id][entity_id][row_id][
                            attr_type
                        ]

    return is_valid, filtered_errors


async def get_user_id_by_project_id(request: Request, project_id: int) -> int | None:
    """
    Get the user ID of the producer who is associated with the project
    """
    project_permissions = await get.generic_get(
        request=request,
        orm_type=ProjectPermissions,
        type_=ProjectPermission,
        filters=[get.Filter(id_field=ProjectPermissions.project, ids=[project_id])],
        empty_return=True,
    )
    if len(project_permissions) == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"message": f"Project permissions not found for project ID {project_id}"},
        )
    return project_permissions[0].user


async def create_project_with_new_user(
    request: Request, user_data: NewUserProject
) -> ProjectPermissionsCreationResponse | None:
    """
    Allow direct user creation with a project
    If user_data.email is null, create a dummy email associated to this user and create project as normal
    If user_data.email is not null but is not an existing user, create a new user then the associated project and groups
    If user_data.email is not null and is an existing user, only create the project and associated groups
    """

    # get program data
    program = await get_program_by_id(request, user_data.program_id)
    # one of email or phone must exist
    if not user_data.user_details.phone and not user_data.user_details.email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "User email or phone must be provided"},
        )
    # if email is blank, generate a dummy value and set the flag on the project
    # this is to facilitate projects without an email but we need to store one in the backend
    show_email = True
    if not user_data.user_details.email:
        user_data.user_details.email = f"grower+{int(datetime.now(tz=UTC).timestamp() * 1000)}@regrow.ag"
        show_email = False
    user_payload = {
        "id": "new",  # required value for new user creation
        "workspaces": {"mrv": True, "ci": False, "si": False},
        "active": 1,
        "perm": 0,
        "name": user_data.user_details.first_name if user_data.user_details.first_name else "",
        "email": user_data.user_details.email,
        "demoFarms": [],
        "groupIds": [],
        "settings": {
            "phone": user_data.user_details.phone if user_data.user_details.phone else "",
            "si_tool_access": "si_only",
            "timezone": "121t6",  # default pacific time?
            "langLocale": program.locale,
            "measurement": (
                MeasurementUnits.MetricUnits if program.units == UnitsTypes.METRIC else MeasurementUnits.ImperialUnits
            ),
            "company": {
                "city": user_data.user_details.city if user_data.user_details.city else "",
                "country": user_data.user_details.country if user_data.user_details.country else "",
                "state": user_data.user_details.state if user_data.user_details.state else "",
                "street": user_data.user_details.street if user_data.user_details.street else "",
                "zip": user_data.user_details.zip if user_data.user_details.zip else "",
            },
        },
        "surname": user_data.user_details.last_name if user_data.user_details.last_name else "",
        "phone": user_data.user_details.phone if user_data.user_details.phone else "",
    }
    created_user_response = await external_api.core_admin_create_user(user_payload)
    # if response.status_code == 409 then the email already exists, so just create project and associate groups
    user_id = None
    if created_user_response.status_code == status.HTTP_409_CONFLICT:
        user_resp = await external_api.core_user_search_get_api(user_ids=[], search_string=user_data.user_details.email)
        found_users = user_resp.json()["result"]
        for user_id_key in found_users:
            if found_users[user_id_key]["email"] == user_data.user_details.email:
                user_id = int(user_id_key)
    elif created_user_response.status_code == status.HTTP_200_OK:
        created_user = created_user_response.json()["result"]
        user_id = created_user["id"]
    # any other errors should be relayed
    else:
        raise HTTPException(
            status_code=created_user_response.status_code,
            detail={"message": "User could not be created"},
        )
    from programs.router import create_project_with_user_permissions

    project = await create_project_with_user_permissions(
        request,
        user_data.program_id,
        ProjectConfigWithPermissionsRequest(permissions=[ProjectPermissionRequestBasic(user=user_id)]),
    )
    # explicitly turn off showing emails for dummy email users
    if not show_email:
        await set_project_show_email_flag(request, project.id, show_email)

    await update_project_groups(request=request, project_id=project.id, group_ids=user_data.user_groups)

    return project


async def populate_all_project_stage_completion_summaries(request: Request) -> None:
    project_stage_pairs = await db.find_all_project_stage_completion_pairs(request)
    for project_stage_pair in project_stage_pairs:
        await db.build_project_stage_completion_summary(request, project_stage_pair[0], project_stage_pair[1])


async def calculate_project_phase_completion(
    request: Request, project_id: int, phase_id: int, save_result: bool = True
) -> ProjectPhaseCompletionResponse:
    stages = await get_enabled_stages_by_phase_id(request=request, phase_id=phase_id)

    # View Outcomes shares completion percentage with the Contract / Producer Agreement stage,
    # so we exclude it from the completion check to avoid blocking the completion of programs without contract
    stages = [stage for stage in stages if stage.type_ != StageTypes.VIEW_OUTCOMES]

    # Check completion for each required stage
    required_stage_ids = [stage.id for stage in stages if stage.required]
    stage_completion_tasks = [
        get_project_stage_completion(
            request=request,
            project_id=project_id,
            stage_id=stage_id,
        )
        for stage_id in required_stage_ids
    ]
    stages_completion: list[ProjectStageCompletion] = list(await asyncio.gather(*stage_completion_tasks))

    # Only update completion record if is_completed = True. Unclear if this is an explicit requirement.
    if is_completed := all([stage_comp.is_completed for stage_comp in stages_completion]):
        if save_result:
            await update_project_phase_completion(
                request=request, project_id=project_id, phase_id=phase_id, is_completed=True
            )
    return ProjectPhaseCompletionResponse(
        project_id=project_id,
        phase_id=phase_id,
        is_completed=is_completed,
        allow_post_close_edit=False,
    )


def handle_program_specific_autofill_changes(
    program_id: int, autofill_values: dict[EntityTypeChoices, dict[int, list[ValuesRequest]]]
) -> dict[EntityTypeChoices, dict[int, list[ValuesRequest]]]:
    # hard code for program 1208, since we only use uploaded data from HISTORICAL_CROP_ROTATION, HISTORICAL_TILLAGE, NUTRIENT_MGMT and IRRIGATION
    # and autofill for 2024 would cause incorrect ordering
    if program_id == 1208:
        # only for record years from HISTORICAL_CROP_ROTATION, HISTORICAL_TILLAGE, NUTRIENT_MGMT and IRRIGATION
        attribute_ids = {73793, 73797, 73804, 73930}
        field_values = autofill_values[EntityTypeChoices.field]
        for field_id, field_value in field_values.items():
            # remove 2024 values if the attribute is in the list
            field_values[field_id] = [
                v for v in field_value if not (v.attribute_id in attribute_ids and v.value == "2024")
            ]

        # filter out empty values
        autofill_values[EntityTypeChoices.field] = {
            field_id: field_value for field_id, field_value in field_values.items() if len(field_value)
        }
    return autofill_values


async def update_project_fields_values(
    request: Request,
    project_id: int,
    body: dict[int, list[ValuesRequest]],
    completion_stats: bool = Query(False),
    read_only: bool | None = False,
) -> list[Values]:
    # Check if any changing values involve a practice changes and, if so,
    # check it is allowed under that program
    flag_get_project = False
    curr_time = datetime.now()
    await validate_entity_ids(
        request=request,
        entity_ids=set(body.keys()),
        entity_type=EntityTypeChoices.field,
    )
    for values_requests in body.values():
        if any(True for i in values_requests if i.source != ImportDataSources.user):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": """You must leave data source blank or set to user, since
                                    you are a user importing these values"""
                },
            )

        for vr in values_requests:
            if vr.progress == ProgressChoices.enrolment:
                flag_get_project = True
                break
        break
    proj = await _get_project(request, project_id, include_user_info=False, include_core_attributes=False)
    program_id = proj[0].program_id
    practice_allowed = True
    allowed_practice_changes = []
    if not flag_get_project:
        phase_start_date, phase_end_date = await utils.get_monitoring_phase_window(
            request=request, program_id=program_id
        )
        if phase_end_date <= curr_time <= phase_start_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Monitoring window is not open yet",
            )

    else:
        (
            practice_allowed,
            allowed_practice_changes,
        ) = await is_allowed_enrolment_practice(
            request=request,
            project=proj[0],
            values=body.values(),
        )
        if not practice_allowed:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": (
                        f"Requested practice value {vr.value} not"
                        " allowed for this program - allowed"
                        f" values: {allowed_practice_changes}"
                    )
                },
            )
    return await values_crud.update_values(request=request, program_id=program_id, map=body, read_only=read_only)
