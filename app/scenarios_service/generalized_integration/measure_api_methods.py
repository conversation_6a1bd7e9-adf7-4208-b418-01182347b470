import httpx
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from grpc import Status
from scenarios_service_client.measure_api_client import MeasureApiAsyncClient

from config import get_settings
from logger import get_logger
from phases.enums import PhaseTypes
from programs.enums import AccountingMethod, BaselineMethod, Protocols
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration import db, helpers
from scenarios_service.generalized_integration.inventory_api_methods import (
    get_dndc_version_for_inventory_config,
)
from scenarios_service.generalized_integration.schema import (
    ConsumerConfiguration,
)
from scenarios_service.model import DndcTasks

settings = get_settings()
logger = get_logger(__name__)

client = MeasureApiAsyncClient(settings.SCENARIOS_SERVICE_INTERNAL_URL, timeout=600)


async def get_intervention_consumer_configurations(
    consumer_config_id: str | None = None,
) -> list[ConsumerConfiguration]:
    try:
        response = (await client.list_intervention_consumer_configurations(consumer_config_id)).json()
    except httpx.HTTPStatusError as exc:
        response = {
            "error": "Scenarios Service threw error: "
            + exc.response.text
            + " with status code: "
            + str(exc.response.status_code)
        }
    return response


async def get_dndc_version_for_intervention_config(consumer_config_id: str, protocol: Protocols) -> str | None:
    consumer_config = await get_intervention_consumer_configurations(consumer_config_id)
    if consumer_config is None:
        raise HTTPException(
            status_code=Status.HTTP_404_NOT_FOUND,
            detail={"message": "Consumer configuration not found for id {consumer_config_id}"},
        )

    ss_protocol = helpers.convert_protocol_for_ss(protocol)
    for config in consumer_config:
        if config["x_consumer_id"] == consumer_config_id:
            versions_by_protocol = config.get("versions_by_protocol", {})
            if ss_protocol in versions_by_protocol:
                return versions_by_protocol[ss_protocol].get("dndc_version", None)

    logger.error(f"No dndc_version found for consumer_id {consumer_config_id} and protocol {ss_protocol}")
    return None


async def create_dndc_task_with_dndc_version(
    request: Request,
    program_id: int,
    phase: PhaseTypes,
    scenarios_service_api: ScenariosServiceApi,
    is_dry_run: bool,
    ss_consumer_id: str,
    project_id: int | None = None,
    accounting_method: AccountingMethod | None = None,
    baseline_method: BaselineMethod | None = None,
    protocol: Protocols | None = None,
    submitted_field_ids: list[int] | None = None,
) -> DndcTasks:
    dndc_version = None
    if ss_consumer_id is not None:
        if accounting_method == AccountingMethod.intervention and protocol is not None:
            dndc_version = await get_dndc_version_for_intervention_config(ss_consumer_id, protocol)
        elif accounting_method == AccountingMethod.inventory:
            dndc_version = await get_dndc_version_for_inventory_config(ss_consumer_id)

    return await db.create_dndc_task(
        request=request,
        program_id=program_id,
        project_id=project_id,
        phase=phase,
        is_dry_run=is_dry_run,
        scenarios_service_api=scenarios_service_api,
        accounting_method=accounting_method,
        baseline_method=baseline_method,
        protocol=protocol,
        dndc_version=dndc_version,
        submitted_field_ids=submitted_field_ids,
    )
