import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from fields.enums import Field<PERSON>tatus
from scenarios_service.enums import DndcTaskStatusChoices
from scenarios_service.generalized_integration.biofuels_integration import (
    BiofuelsIntegration,
)
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.model import (
    DndcSimulationRequests,
    DndcTasks,
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)


@pytest.mark.parametrize("dry_run", [True, False])
async def test_submit_program_for_biofuels_outcomes(
    dry_run,
    mocker,
    mock_biofuels_api_async_client,
    mock_biofuels_inputs_translator,
    mock_boundary_features,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_validate_fields = mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")
    mock_store_session_input_in_bucket = mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.helpers.store_session_input_in_bucket"
    )

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, status=FieldStatus.enrolled)

    mocker.patch("boundaries_service.client.get_features_for_fields", return_value={field.id: mock_boundary_features})

    integration = BiofuelsIntegration(
        request=app_request,
        biofuels_client=mock_biofuels_api_async_client,
        biofuels_inputs_translator=mock_biofuels_inputs_translator,
    )
    program_submission_result = await integration.submit_program_for_biofuels_outcomes(
        program.id,
        [field.id],
        dry_run=dry_run,
    )

    task = (await orm_select(DndcTasks))[0]
    assert task.status == DndcTaskStatusChoices.finished

    mock_validate_fields.assert_called_once()
    assert mock_validate_fields.call_args_list[0].kwargs["request"] == app_request
    assert mock_validate_fields.call_args_list[0].kwargs["program_id"] == program.id
    assert mock_validate_fields.call_args_list[0].kwargs["submitted_field_ids"] == [field.id]

    mock_biofuels_inputs_translator.get_biofuels_input_for_field.assert_called_once()

    # Persisted DndcSimulationRequest assertions:
    simulation_request = (
        await orm_select(DndcSimulationRequests, where=[DndcSimulationRequests.field_id == field.id])
    )[0]
    assert simulation_request.error_message is None
    assert simulation_request.is_error is False
    assert simulation_request.field_id == field.id
    assert simulation_request.ss_api == ScenariosServiceApi.biofuels_api
    assert simulation_request.ss_field_request_id == 1234567
    assert field.id == simulation_request.field_id
    assert simulation_request.task_id == task.id

    # Submit Biofuels field assertions:
    mock_biofuels_api_async_client.submit_field.assert_called_once()
    assert task.id in mock_biofuels_api_async_client.submit_field.call_args.kwargs["project_name"]
    assert mock_biofuels_api_async_client.submit_field.call_args.kwargs["x_consumer_id"] == "test_consumer"
    assert mock_biofuels_api_async_client.submit_field.call_args.kwargs["biofuels_input"].field_name == str(field.id)

    # Store BiofuelsInput in bucket assertions:
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["request"] is app_request
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["session_input"].field_name == str(field.id)
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["field_id"] == field.id
    assert mock_store_session_input_in_bucket.call_args_list[0].kwargs["task_id"] == task.id

    # Submission result assertions:
    assert program_submission_result.field_submission_results[0].field_id == 12345678
    assert task.id in program_submission_result.field_submission_results[0].project_name
    assert str(field.id) in program_submission_result.field_submission_results[0].session_name
    assert program_submission_result.field_submission_results[0].field_request_id == 1234567
    assert program_submission_result.field_submission_results[0].is_error is False
    assert program_submission_result.finalize_success != dry_run  # Finalize only if not doing a dry_run

    # Finalize endpoint was called, unless we're doing a dry run.
    if dry_run:
        mock_biofuels_api_async_client.finalize_project.assert_not_called()
    else:
        mock_biofuels_api_async_client.finalize_project.assert_called_with(
            project_name=program_submission_result.field_submission_results[0].project_name,
            x_consumer_id="test_consumer",
        )


async def test_submit_program_for_biofuels_outcomes_dont_finalize(
    mocker,
    mock_biofuels_api_async_client,
    mock_biofuels_inputs_translator,
    mock_boundary_features,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    mocker.patch("boundaries_service.client.get_features_for_fields", return_value={field.id: mock_boundary_features})

    # BiofuelsInputsTranslator throws an error
    mock_biofuels_inputs_translator.get_biofuels_input_for_field.side_effect = ScenariosServiceIntegrationError(
        message="Error building BiofuelsInput", code=ScenariosServiceIntegrationErrorCode.invalid_management_data
    )

    integration = BiofuelsIntegration(
        request=app_request,
        biofuels_client=mock_biofuels_api_async_client,
        biofuels_inputs_translator=mock_biofuels_inputs_translator,
    )
    program_submission_result = await integration.submit_program_for_biofuels_outcomes(
        program.id,
        [field.id],
        dry_run=False,
    )

    task = (await orm_select(DndcTasks))[0]
    assert task.status == DndcTaskStatusChoices.finished

    # Submission result assertions:
    assert program_submission_result.field_submission_results[0].field_id == field.id
    assert str(field.id) in program_submission_result.field_submission_results[0].session_name
    assert program_submission_result.field_submission_results[0].field_request_id is None
    assert program_submission_result.field_submission_results[0].is_error is True
    assert (
        program_submission_result.field_submission_results[0].error_code
        == ScenariosServiceIntegrationErrorCode.invalid_management_data
    )
    assert program_submission_result.field_submission_results[0].error_message == "Error building BiofuelsInput"
    assert program_submission_result.finalize_success is False

    # Finalize endpoint was not called since an error occurred
    mock_biofuels_api_async_client.finalize_project.assert_not_called()


async def test_submit_program_for_biofuels_outcomes_validates_inputs(
    mocker,
    mock_biofuels_api_async_client,
    app_request,
    mdl,
    orm_select,
    session_input,
):
    integration = BiofuelsIntegration(request=app_request, biofuels_client=mock_biofuels_api_async_client)

    # If the program doesn't exist, we 404
    with pytest.raises(HTTPException) as exc_info:
        await integration.submit_program_for_biofuels_outcomes(program_id=-2, field_ids=[], dry_run=False)
    assert exc_info.value.status_code == 404

    # If field_validation fails, we allow the 404 to propagate
    program = await mdl.Programs()
    mocker.patch(
        "scenarios_service.generalized_integration.explore_integration.db.get_program_modeling_consumer_id",
        return_value="test_consumer",
    )
    mock_validate_fields = mocker.patch("scenarios_service.generalized_integration.helpers.validate_fields")
    mock_validate_fields.side_effect = HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    with pytest.raises(HTTPException) as exc_info:
        await integration.submit_program_for_biofuels_outcomes(program_id=program.id, field_ids=[1], dry_run=False)
    assert exc_info.value.status_code == 404
    mock_validate_fields.assert_called_once_with(request=app_request, program_id=program.id, submitted_field_ids=[1])
