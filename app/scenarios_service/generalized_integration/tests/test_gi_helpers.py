import pytest
from fastapi import HTT<PERSON>Exception
from starlette import status

from fields.enums import FieldStatus
from scenarios_service.generalized_integration.helpers import validate_fields


async def test_validate_fields(app_request, mdl):
    program_275 = await mdl.Programs(id=275)
    program_276 = await mdl.Programs(id=276)
    project_275 = await mdl.Projects(program_id=program_275.id)
    project_276 = await mdl.Projects(program_id=program_276.id)
    cv_field_275_id = (await mdl.Fields(parent_project_id=project_275.id, status=FieldStatus.contract_voided)).id
    cv_field_276_id = (await mdl.Fields(parent_project_id=project_276.id, status=FieldStatus.contract_voided)).id

    # no validation error
    await validate_fields(app_request, program_id=program_275.id, submitted_field_ids=[cv_field_275_id])

    with pytest.raises(HTTPException) as http_exc:
        await validate_fields(app_request, program_id=program_276.id, submitted_field_ids=[cv_field_276_id])

    assert http_exc.value.status_code == status.HTTP_400_BAD_REQUEST
    assert f"Fields without enrolled+ status: {{{cv_field_276_id}}}" == http_exc.value.detail["message"]
