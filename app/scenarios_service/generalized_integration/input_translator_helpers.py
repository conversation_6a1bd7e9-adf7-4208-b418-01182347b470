import asyncio
import itertools
from operator import attrgetter
from typing import Any

from fastapi import Request
from scenarios_service_schema.public.intervention.autogeneration import (
    BaseIntervention,
)
from scenarios_service_schema.schema import (
    AreaUnitEnum,
    AutoIrrigationEvent,
    AutoIrrigationEvent as SSAutoIrrigationEvent,
    CroppingEvent as SSCroppingEvent,
    CustomReductionEvent,
    FertilizerEvent,
    FertilizerMethodEnum,
    FireEvent as SSFireEvent,
    FloodEvent,
    IrrigationMethodEnum as SSIrrigationMethods,
    MassUnitEnum,
    OrganicAmendmentEvent,
    OrganicAmendmentMethodEnum,
    ReductionEvent as SSReductionEvent,
    ScenarioTypeEnum,
    SessionInput,
    Soil,
    TillEvent as SSTillageEvent,
    VolumeUnitEnum,
)

from config import get_settings
from cultivation_cycles.methods import get_reporting_period_cultivation_cycles
from cultivation_cycles.schema import CultivationCycle
from defaults import defaults_translator
from defaults.attribute_options import (
    APPLICATION_METHODS_WITH_DEPTH,
    APPLICATION_METHODS_WITH_WATER,
    ApplicationMethod,
    ApplicationRateType,
    CropUsage,
    IrrigationMethods,
    NO_ADDITIVES_OPTION,
    PlantingMethod,
)
from defaults.consts import RegrowCropName, RegrowProductName
from defaults.schema import NutrientProductType
from entity_events.event_creators.helpers import harvest_yield_rate_unit_value_to_enums
from entity_events.events.application_event import (
    ApplicationEvent as MRVApplicationEvent,
)
from entity_events.events.cropping_event import (
    CroppingEvent as MRVCroppingEvent,
    PlantingRate,
)
from entity_events.events.fire_event import FireEvent as MRVFireEvent
from entity_events.events.irrigation_event import IrrigationEvent as MRVIrrigationEvent
from entity_events.events.reduction_event import ReductionEvent as MRVReductionEvent
from entity_events.events.schema import ApplicationInput
from entity_events.events.tillage_event import TillageEvent as MRVTillageEvent
from entity_events.events.yield_rate import YieldRate
from entity_events.units import AreaUnit, LengthUnit, VolumeUnit
from fields.schema import Field
from logger import get_logger
from phases.dataclasses import DateRange
from pint_configuration import ureg
from programs.model import Programs
from scenarios_service.data.yields_lookup_155_inventory_rerun import (
    YIELD_LOOKUP_155_INVENTORY_RERUN,
)
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.helpers import FieldWithBoundary
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.model import ScenariosServiceIntegrationErrorCode

Quantity = ureg.Quantity
settings = get_settings()
logger = get_logger(__name__)


async def convert_cultivation_cycles_to_ss_phases(
    request: Request, cultivation_cycles: list[CultivationCycle], field: Field, field_area: float
) -> list[dict]:
    ss_phases = []
    for cycle in cultivation_cycles:
        ss_cropping_events = await asyncio.gather(
            *[
                _translate_cropping_event_to_ss_event(request, ev)
                for ev in cycle.events
                if isinstance(ev, MRVCroppingEvent)
            ]
        )

        ss_tillage_events = [
            _translate_tillage_event_to_ss_event(ev) for ev in cycle.events if isinstance(ev, MRVTillageEvent)
        ]
        nested_ss_application_events = await asyncio.gather(
            *[
                _translate_application_event_to_ss_events(ev, field, field_area)
                for ev in cycle.events
                if isinstance(ev, MRVApplicationEvent)
            ]
        )
        ss_application_events = [event for event_list in nested_ss_application_events for event in event_list]
        ss_fertilizer_events = [event for event in ss_application_events if isinstance(event, FertilizerEvent)]
        ss_organic_events = [event for event in ss_application_events if isinstance(event, OrganicAmendmentEvent)]
        ss_irrigation_events = [
            _translate_irrigation_event_to_ss_event(ev)
            for ev in cycle.events
            if isinstance(ev, MRVIrrigationEvent) and ev.method != IrrigationMethods.flood
        ]
        ss_flood_events = [
            _translate_irrigation_event_to_ss_flood_event(ev)
            for ev in cycle.events
            if isinstance(ev, MRVIrrigationEvent) and ev.method == IrrigationMethods.flood
        ]

        ss_fire_events = [_translate_fire_event_to_ss_event(ev) for ev in cycle.events if isinstance(ev, MRVFireEvent)]
        ss_phases.append(
            {
                "start_date": cycle.start.strftime("%Y-%m-%d"),
                "end_date": cycle.end.strftime("%Y-%m-%d"),
                "management_events": {
                    "cropping": ss_cropping_events,
                    "till": ss_tillage_events,
                    "fertilizer": ss_fertilizer_events,
                    "organic_amendment": ss_organic_events,
                    "irrigation": ss_irrigation_events,
                    "flood": ss_flood_events,
                    "fire": ss_fire_events,
                },
            }
        )
    all_fertilizer_events = [event.json() for phase in ss_phases for event in phase["management_events"]["fertilizer"]]
    logger.info(f"generated ss_fertilizer_events for field {field.id}: {all_fertilizer_events}")
    return ss_phases


def build_session_input(
    field_and_boundary: FieldWithBoundary,
    soil: Soil,
    ss_historical_phases: list[dict],
    ss_monitored_phases: list[dict],
    reporting_info: dict,
    scenarios_service_api: ScenariosServiceApi,
    interventions: list[BaseIntervention],
    ss_session_name: str,
) -> SessionInput:
    location_data = field_and_boundary.get_location_data(allow_multipolygons=False)

    return SessionInput(
        session_name=ss_session_name,
        reporting_information=reporting_info,
        scenarios=[
            {
                "name": f"{field_and_boundary.field.id}_historical_baseline",
                "scenario_type": ScenarioTypeEnum.HISTORICAL_BASELINE,
                "location": location_data,
                "soil": soil,
                "soil_sources": ["SSURGO", "GLOBAL"],
                "weather_sources": ["PRISM", "GLOBAL"],
                "rotations": [{"phases": ss_historical_phases}],
            },
            {
                "name": f"{field_and_boundary.field.id}_intervention",
                "scenario_type": ScenarioTypeEnum.INTERVENTION,
                "location": location_data,
                "soil": soil,
                "soil_sources": ["SSURGO", "GLOBAL"],
                "weather_sources": ["PRISM", "GLOBAL"],
                # Explore API expects the intervention_practices key to be present, even if empty
                "intervention_practices": (
                    interventions if scenarios_service_api == ScenariosServiceApi.explore_api else []
                ),
                # Explore API expects the rotations key to be present, even if empty
                "rotations": [{"phases": ss_monitored_phases}] if ss_monitored_phases else [],
            },
        ],
    )


def get_155_inventory_rerun_crop_yield(field_id: int) -> YieldRate:
    """
    Returns a YieldRate object from a static lookup derived from 155's mrv_field_facts. This is necessary because we're
    using events that were migrated into 1119 long ago without yields, so can't easily be augmented.

    This function can be removed shortly after inventory outcomes are delivered.
    """
    yield_data = YIELD_LOOKUP_155_INVENTORY_RERUN.get(field_id)
    if not yield_data:
        raise ScenariosServiceIntegrationError(
            f"No yield data found for 155 rerun field_id: {field_id}",
            code=ScenariosServiceIntegrationErrorCode.invalid_management_data,
        )
    yield_rate_numerator_unit, yield_rate_denominator_unit = harvest_yield_rate_unit_value_to_enums(
        yield_data["yield_unit"]
    )
    return YieldRate(
        value=yield_data["yield_value"],
        numerator_unit=yield_rate_numerator_unit,
        denominator_unit=yield_rate_denominator_unit,
    )


async def get_reporting_info(
    request: Request,
    cultivation_cycles: list[CultivationCycle],
    field_id: int,
    program: Programs,
    scenarios_service_api: ScenariosServiceApi,
) -> dict:
    """
    Derives the Reporting Period's start date, end date, main commodity crop type, and yield (bu/ac) for the field.

    The reporting info time period dates should be only the most recent reporting period. The reasoning being that is if
    the reporting dates were all the full length of time the field has been involved in the program, credits would be
    regenerated every year for the previous years, which they already got credits for - resulting in double crediting.
    """
    reporting_period_ccs = get_reporting_period_cultivation_cycles(cultivation_cycles, program)
    if len(reporting_period_ccs) == 0:
        raise ScenariosServiceIntegrationError(
            f"No cultivation cycles found for reporting period: {program.reporting_period_start_date} - {program.reporting_period_end_date}",
            ScenariosServiceIntegrationErrorCode.no_cultivation_cycles_after_baseline_year,
        )

    first_cultivation_cycle = reporting_period_ccs[0]
    final_cultivation_cycle = reporting_period_ccs[-1]

    all_events = list(itertools.chain.from_iterable([cc.events for cc in reporting_period_ccs]))
    reporting_period_com_crops = [
        event for event in all_events if isinstance(event, MRVCroppingEvent) and event.crop_usage == CropUsage.COMMODITY
    ]

    if len(reporting_period_com_crops) == 0:
        raise ScenariosServiceIntegrationError(
            f"Final cropping events list empty for reporting: {program.reporting_period_start_date} - {program.reporting_period_end_date}",
            ScenariosServiceIntegrationErrorCode.invalid_management_data,
        )

    reporting_period_crop_types = [event.crop_type for event in reporting_period_com_crops]
    # if this is not for explore api, nor a Vietnam program, don't allow multiple crop types in the reporting period
    if (
        scenarios_service_api != ScenariosServiceApi.explore_api
        and program.id not in [582, 1208, 1666]
        and len(set(reporting_period_crop_types)) > 1
    ):
        raise ScenariosServiceIntegrationError(
            f"Reporting period must contain only one crop type; found {set(reporting_period_crop_types)}",
            ScenariosServiceIntegrationErrorCode.multiple_reporting_period_crop_types,
        )

    final_commodity_cropping_event: MRVCroppingEvent = sorted(
        reporting_period_com_crops, key=attrgetter("interval.end")
    )[-1]

    # only take all crop yields if this is a Vietnam program, otherwise take only the final crop yield
    if program.id in [582, 1208]:
        crop_yields = [event.crop_yield for event in reporting_period_com_crops]
    elif program.id == 1666:
        crop_yields = [get_155_inventory_rerun_crop_yield(field_id)]
    else:
        crop_yields = [final_commodity_cropping_event.crop_yield]

    if program.id in [582, 1208] and final_commodity_cropping_event.crop_type in ["long_rice", "short_rice"]:
        reporting_crop_type = RegrowCropName.rice
    elif program.id == 1666:
        # The 155 inventory rerun is soybeans only, but includes some double crop fields.
        rp_soybean_crops = [ev for ev in reporting_period_com_crops if ev.crop_type == RegrowCropName.soybean]
        assert len(rp_soybean_crops) == 1, "Reporting period should have exactly one soybean crop."
        reporting_crop_type = rp_soybean_crops[0].crop_type
    else:
        reporting_crop_type = final_commodity_cropping_event.crop_type

    crop_yields_bushels_acre = [
        await crop_yield.convert_value_to_unit(VolumeUnit.BUSHEL, AreaUnit.ACRE, reporting_crop_type)
        for crop_yield in crop_yields
        if crop_yield is not None
    ]

    # TODO: Assert that yield is defined for Scope 3 programs, where assets must be tied to a commodity.
    return {
        "start_date": first_cultivation_cycle.start.strftime("%Y-%m-%d"),
        "end_date": final_cultivation_cycle.end.strftime("%Y-%m-%d"),
        "crop_name": reporting_crop_type,
        "crop_yield": sum(crop_yields_bushels_acre) if crop_yields_bushels_acre else None,
    }


def _translate_tillage_event_to_ss_event(tillage_event: MRVTillageEvent) -> SSTillageEvent:
    return SSTillageEvent.parse_obj(
        {
            "date": tillage_event.occurred_at.strftime("%Y-%m-%d"),
            "soil_inverted": tillage_event.soil_inversion,
            "depth": _length_to_cm(tillage_event.depth.value, tillage_event.depth.unit),
            "strip_frac": tillage_event.strip_fraction,
        }
    )


def _translate_irrigation_event_to_ss_event(
    irrigation_event: MRVIrrigationEvent,
) -> AutoIrrigationEvent:
    """
    SS doesn't have the IrrigationEvent working yet, so for the time being we are using AutoIrrigationEvent.
    """
    return SSAutoIrrigationEvent(
        start_date=irrigation_event.interval.start.strftime("%Y-%m-%d"),
        end_date=irrigation_event.interval.end.strftime("%Y-%m-%d"),
        method=_translate_irrigation_method(irrigation_event.method),
        furrow_percentage=(
            irrigation_event.flood_percentage / 100 if irrigation_event.flood_percentage is not None else None
        ),
        depth=(
            _length_to_cm(irrigation_event.subsurface_depth.value, irrigation_event.subsurface_depth.unit)
            if irrigation_event.subsurface_depth is not None
            else 0
        ),
    )


def _translate_irrigation_method(method: IrrigationMethods) -> SSIrrigationMethods:
    if method in [IrrigationMethods.alternating_wet_dry, IrrigationMethods.no_irrigation, IrrigationMethods.flood]:
        raise ValueError(f"Unexpected irrigation method: {method}.")

    translations = {
        IrrigationMethods.drip: SSIrrigationMethods.DRIP,
        IrrigationMethods.furrow: SSIrrigationMethods.FURROW,
        IrrigationMethods.sprinkler: SSIrrigationMethods.SPRINKLER,
        IrrigationMethods.subsurface_drip: SSIrrigationMethods.SUBSURFACE_DRIP,
    }
    return translations.get(method)


async def _translate_cropping_event_to_ss_event(request: Request, cropping_event: MRVCroppingEvent) -> SSCroppingEvent:
    return SSCroppingEvent.parse_obj(
        {
            "start_date": cropping_event.interval.start.strftime("%Y-%m-%d"),
            "end_date": cropping_event.interval.end.strftime("%Y-%m-%d"),
            "name": cropping_event.crop_type,
            "reductions": (
                [await _translate_reduction_event_to_ss_event(reduction) for reduction in cropping_event.reductions]
            ),
            "plant_density": _translate_planting_rate(
                cropping_event.planting_rate, cropping_event.crop_type, cropping_event.planting_method
            ),
        }
    )


def _translate_fire_event_to_ss_event(fire_event: MRVFireEvent) -> SSFireEvent:
    return SSFireEvent.parse_obj(
        {
            "date": fire_event.occurred_at.strftime("%Y-%m-%d"),
            "burn_area_fraction": fire_event.burn_area_fraction,
            "combusted_fraction": fire_event.combusted_fraction,
        }
    )


async def _translate_application_event_to_ss_events(
    application_event: MRVApplicationEvent, field: Field, field_area: float
) -> list[FertilizerEvent | OrganicAmendmentEvent]:
    """
    This function takes a provided MRVApplicationEvent, which can potentially represent an application of multiple
    products (which represents the real-world concept of a "tank-mix"), and converts it to either a FertilizerEvent
    or a OrganicAmendmentEvent.
    These events represent only a single product (plus additive(s)), so multiple events in the response are
    necessary.

    Given that additives can't be added to EENFs, there is potential for invalid events to be created by this
    transformation. Events will need to be validated at data entry to prevent this case from failing SS validation.
    In this function, we simply ignore any additives if the incoming event has any EENF products or the application
    method is fertigation. In this case, we proceed but send an error to APM.

    There's a rule that we were made aware of that (as of current 10/12/2023) events with basic_inorganic and
    eenf_inorganic product_types (Fertilizer) can only use a method in (broadcast, fertigation_furrow,
    fertigation_sprinkler, fertigation_drip, fertigation_subsurface_drip, subsurface). As a result, events with these
    product_types using an "inject" method are translated to "subsurface".
    Similarly, events with organic product_types (Organic Amendment) can only use (broadcast, inject), meaning any
    fertigation-method'd event would have its method translated to "inject". These translations were cleared with Chris
    Dorich on 10/05/2023
    """

    ss_events = []
    for product in application_event.products:
        if application_event.depth is None and application_event.method in APPLICATION_METHODS_WITH_DEPTH:
            # Going to logger.exception this and default to 0 at first in case it's happening often.
            logger.exception(
                ScenariosServiceIntegrationError(
                    "Depth is required for this application method.",
                    ScenariosServiceIntegrationErrorCode.invalid_management_data,
                )
            )

        if application_event.water_amount is None and application_event.method in APPLICATION_METHODS_WITH_WATER:
            # Going to logger.exception this and default to 0 at first in case it's happening often.
            logger.exception(
                ScenariosServiceIntegrationError(
                    "Water amount is required for this application method.",
                    ScenariosServiceIntegrationErrorCode.invalid_management_data,
                )
            )

        # define properties common to both event types
        amount, rate_unit = await _standardize_application_rate(product)
        event = {
            "date": application_event.occurred_at.strftime("%Y-%m-%d"),
            "name": product.product_name,
            "amount": amount,
            "rate_unit": rate_unit,
            "area_unit": AreaUnitEnum.hectare,
            "depth": (
                _length_to_cm(application_event.depth.value, application_event.depth.unit)
                if application_event.depth and application_event.depth.value and application_event.depth.unit
                else 0
            ),
            "custom": None,
            "internal_is_amount_in_N_product": product.application_rate.rate_type == ApplicationRateType.NITROGEN_RATE,
        }

        if await defaults_translator.is_regrow_product_name_valid_fertilizer_product(product.product_name):
            event |= await _translate_fertilizer_event_properties(application_event, field, field_area, product)
            ss_events.append(FertilizerEvent.parse_obj(event))

        elif await defaults_translator.is_regrow_product_name_valid_organic_product(product.product_name):
            event["application_method"] = _translate_organic_amendment_method(application_event.method)
            ss_events.append(OrganicAmendmentEvent.parse_obj(event))
    logger.info(
        f"_translate_application_event_to_ss_events: Field {field.id}: from {application_event} to {[e.json for e in ss_events]}"
    )
    return ss_events


async def _translate_fertilizer_event_properties(
    application_event: MRVApplicationEvent, field: Field, field_area: float, fertilizer_product: ApplicationInput
) -> dict[str, Any]:
    ss_fertilizer_method = _translate_fertilizer_method(application_event.method)

    ss_water_amount = (
        await defaults_translator.translate_water_amount_to_mm(application_event.water_amount, field_area)
        if application_event.water_amount is not None
        else None
    )

    ss_additives = await _get_additives_if_allowed_for_main_product(application_event, fertilizer_product)

    return {
        "application_method": ss_fertilizer_method,
        "additives": ss_additives,
        "water_amount": ss_water_amount,
    }


async def _get_additives_if_allowed_for_main_product(
    application_event: MRVApplicationEvent, fertilizer_product: ApplicationInput
) -> list[str]:
    ss_nutrient_product_type = await defaults_translator.get_product_category_by_regrow_name(
        fertilizer_product.product_name
    )
    # Additives are not allowed when type is EENF or when any fertigation-related application method is used
    if application_event.additives and not (
        ss_nutrient_product_type == NutrientProductType.EENF_INORGANIC
        or application_event.method == ApplicationMethod.FERTIGATION
    ):
        return [add for add in application_event.additives.split(",") if add != NO_ADDITIVES_OPTION]
    else:
        return []


def _translate_irrigation_event_to_ss_flood_event(irrigation_event: MRVIrrigationEvent) -> FloodEvent:
    return FloodEvent(
        start_date=irrigation_event.interval.start.strftime("%Y-%m-%d"),
        end_date=irrigation_event.interval.end.strftime("%Y-%m-%d"),
    )


async def _translate_reduction_event_to_ss_event(reduction_event: MRVReductionEvent) -> SSReductionEvent:
    return SSReductionEvent.parse_obj(
        {
            "date": reduction_event.occurred_at.strftime("%Y-%m-%d"),
            "custom": CustomReductionEvent(
                frac_root_removed=reduction_event.root_removed_fraction,
                frac_root_residue=reduction_event.root_residue_fraction,
                frac_stem_removed=reduction_event.stem_removed_fraction,
                frac_stem_residue=reduction_event.stem_residue_fraction,
                frac_leaf_removed=reduction_event.leaf_removed_fraction,
                frac_leaf_residue=reduction_event.leaf_residue_fraction,
                frac_grain_removed=reduction_event.grain_removed_fraction,
                frac_grain_residue=reduction_event.grain_residue_fraction,
            ),
        }
    )


def _translate_planting_rate(
    planting_rate: PlantingRate | None, crop_type: str, planting_method: PlantingMethod | None
) -> float | None:
    """
    For rice, Scenarios Service will expect planting density in terms of plants per square meter.
    """
    if crop_type in ["rice", "short_rice", "long_rice"] and planting_rate is not None:
        # Assume one seed weighs 0.0241 grams
        # https://docs.google.com/document/d/1sKws3dr9Kqt5NXFhZMjL7DDvCVh2R9JxYC_sngM9WUs
        numerator_in_grams = Quantity(planting_rate.value, planting_rate.numerator_unit.lower()).to("g")
        numerator_in_seeds = numerator_in_grams / 0.0241
        if planting_method == PlantingMethod.TRANSPLANT_WET:
            emergence_rate = 0.6
        elif planting_method in [PlantingMethod.DIRECT_SEEDING_DRY, PlantingMethod.DIRECT_SEEDING_WET]:
            emergence_rate = 0.5
        else:
            emergence_rate = 1.0

        denominator_in_m2 = Quantity(1, planting_rate.denominator_unit.lower()).to("square_meter")
        return ((numerator_in_seeds * emergence_rate) / denominator_in_m2).magnitude
    return None


def _translate_fertilizer_method(mrv_application_method: ApplicationMethod) -> FertilizerMethodEnum:
    if mrv_application_method in [ApplicationMethod.INCORPORATED, ApplicationMethod.AVIATION]:
        raise ValueError(f"Unexpected application method: {mrv_application_method}")
    translations = {
        ApplicationMethod.BROADCASTED: FertilizerMethodEnum.BROADCAST,
        ApplicationMethod.SUBSURFACE: FertilizerMethodEnum.SUBSURFACE,
        ApplicationMethod.FERTIGATION: FertilizerMethodEnum.FERTIGATION_SUBSURFACE_DRIP,
        ApplicationMethod.FERTIGATION_FURROW: FertilizerMethodEnum.FERTIGATION_FURROW,
        ApplicationMethod.FERTIGATION_SPRINKLER: FertilizerMethodEnum.FERTIGATION_SPRINKLER,
        ApplicationMethod.FERTIGATION_DRIP: FertilizerMethodEnum.FERTIGATION_DRIP,
        ApplicationMethod.INJECTED: FertilizerMethodEnum.SUBSURFACE,
    }
    return translations[mrv_application_method]


def _translate_organic_amendment_method(mrv_application_method: ApplicationMethod) -> OrganicAmendmentMethodEnum:
    if mrv_application_method in [
        ApplicationMethod.INCORPORATED,
        ApplicationMethod.AVIATION,
        ApplicationMethod.FERTIGATION_FURROW,
        ApplicationMethod.FERTIGATION_SPRINKLER,
        ApplicationMethod.FERTIGATION_DRIP,
    ]:
        raise ValueError(f"Unexpected application method: {mrv_application_method}")
    translations = {
        ApplicationMethod.BROADCASTED: OrganicAmendmentMethodEnum.BROADCAST,
        ApplicationMethod.INJECTED: OrganicAmendmentMethodEnum.INJECT,
        ApplicationMethod.SUBSURFACE: OrganicAmendmentMethodEnum.INJECT,
        ApplicationMethod.FERTIGATION: OrganicAmendmentMethodEnum.INJECT,
    }
    return translations[mrv_application_method]


async def _standardize_application_rate(product: ApplicationInput) -> tuple[float, MassUnitEnum | VolumeUnitEnum]:
    """
    Scenarios Service doesn't necessarily accept all the units that our events can use, so just convert everything
    that's is_dry to kg/ha and the liquid products to L/ha.
    """
    application_rate = product.application_rate.value
    rate_unit_numerator = product.application_rate.numerator_unit
    rate_unit_denominator = product.application_rate.denominator_unit
    rate_type = product.application_rate.rate_type
    product_is_dry = await defaults_translator.is_regrow_product_name_dry(product.product_name)
    starting_unit_denominator = rate_unit_denominator.value.lower()
    application_rate_q = Quantity(application_rate, rate_unit_numerator.value.lower() + "/" + starting_unit_denominator)
    # Scenarios Service can't accept liquid products with a Nitrogen rate specified in kilograms, so when this
    # occurs we use convert to a volume. This is the method recommended by ES.
    # As a special case, Scenarios Service considers Anhydrous Ammonia to be a liquid product when supplied as a
    # Nitrogen rate while MRV considers it dry, so we convert Anhydrous Ammonia rates to a volume in that case.
    if product.product_name == RegrowProductName.ammonia_anhydrous and rate_type == ApplicationRateType.PRODUCT_RATE:
        # Get liquid density in lbs per gallon
        liquid_density = await defaults_translator.get_regrow_product_name_liquid_density(product.product_name)
        rate_lbs = application_rate_q.to(f"lbs/{starting_unit_denominator}")
        application_rate_q = rate_lbs / Quantity(liquid_density, "lbs/gallon")

    if (not product_is_dry and rate_type == ApplicationRateType.PRODUCT_RATE) or (
        product.product_name == RegrowProductName.ammonia_anhydrous and rate_type == ApplicationRateType.PRODUCT_RATE
    ):
        return application_rate_q.to("liter/ha").magnitude, VolumeUnitEnum.liter
    return application_rate_q.to("kg/ha").magnitude, MassUnitEnum.kilogram


def _length_to_cm(length: float, unit: LengthUnit) -> float:
    return Quantity(length, unit.value.lower()).to("cm").magnitude


def select_cultivation_cycles_for_simulation(
    cultivation_cycles: list[CultivationCycle], reporting_period: DateRange
) -> list[CultivationCycle]:
    return [cc for cc in cultivation_cycles if cc.end.date() <= reporting_period.end_date]
