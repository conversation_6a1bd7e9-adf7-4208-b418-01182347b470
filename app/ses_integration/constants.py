from typing import Type

from ses_client.event import StructuredEvent

from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.tillage_event import TillageEvent
from phases.enums import PhaseTypes, StageTypes

MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE = 90

SES_EVENT_TO_ENTITY_EVENT: dict[str, Type[EntityEvent]] = {
    StructuredEvent.TYPE_APPLICATION_ACTIVITY: ApplicationEvent,
    StructuredEvent.TYPE_FALLOW_PERIOD: FallowPeriod,
    StructuredEvent.TYPE_HARVEST_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_IRRIGATION_ACTIVITY: IrrigationEvent,
    StructuredEvent.TYPE_PLANTING_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_SOWING_ACTIVITY: CroppingEvent,
    StructuredEvent.TYPE_TILLAGE_ACTIVITY: TillageEvent,
}

STAGE_TYPE_TO_SES_EVENT: dict[StageTypes, list[Type[StructuredEvent]]] = {
    StageTypes.IRRIGATION_EVENTS: [StructuredEvent.TYPE_IRRIGATION_ACTIVITY],
    StageTypes.NUTRIENT_EVENTS: [StructuredEvent.TYPE_APPLICATION_ACTIVITY],
    StageTypes.TILLAGE_EVENTS: [StructuredEvent.TYPE_TILLAGE_ACTIVITY],
}

STAGE_TYPE_TO_ENTITY_EVENT: dict[StageTypes, list[Type[StructuredEvent]]] = {
    StageTypes.IRRIGATION_EVENTS: [IrrigationEvent],
    StageTypes.NUTRIENT_EVENTS: [ApplicationEvent],
    StageTypes.TILLAGE_EVENTS: [TillageEvent],
}

EVENT_ASSOCIABLE_PHASE_TYPES = {PhaseTypes.MONITORING, PhaseTypes.ENROLMENT}

NO_PRACTICE_OBSERVATION_STAGE_TYPES = [
    StageTypes.IRRIGATION_EVENTS,
    StageTypes.NUTRIENT_EVENTS,
    StageTypes.TILLAGE_EVENTS,
]

NO_PRACTICE_OBSERVATION_EVENT_TYPES = [
    StructuredEvent.TYPE_HARVEST_ACTIVITY,
    StructuredEvent.TYPE_FALLOW_PERIOD,
]

# Reverse mapping from SES event type to stage type
SES_EVENT_TYPE_TO_STAGE: dict[str, StageTypes] = {
    # Practice events from STAGE_TYPE_TO_SES_EVENT
    StructuredEvent.TYPE_TILLAGE_ACTIVITY: StageTypes.TILLAGE_EVENTS,
    StructuredEvent.TYPE_IRRIGATION_ACTIVITY: StageTypes.IRRIGATION_EVENTS,
    StructuredEvent.TYPE_APPLICATION_ACTIVITY: StageTypes.NUTRIENT_EVENTS,
    # Cropping events (hardcoded in ses_client fetch_cropping_sequences_for_fields)
    StructuredEvent.TYPE_SOWING_ACTIVITY: StageTypes.CROP_EVENTS,
    StructuredEvent.TYPE_PLANTING_ACTIVITY: StageTypes.CROP_EVENTS,
    StructuredEvent.TYPE_HARVEST_ACTIVITY: StageTypes.CROP_EVENTS,
    StructuredEvent.TYPE_TERMINATION_ACTIVITY: StageTypes.CROP_EVENTS,
    StructuredEvent.TYPE_FALLOW_PERIOD: StageTypes.CROP_EVENTS,
}

# Mapping from EntityEventType to StageTypes for optimization
ENTITY_EVENT_TYPE_TO_STAGE_TYPE: dict[EntityEventType, StageTypes | None] = {
    EntityEventType.CROPPING_EVENT: StageTypes.CROP_EVENTS,
    EntityEventType.FALLOW_PERIOD: StageTypes.CROP_EVENTS,
    EntityEventType.TILLAGE_EVENT: StageTypes.TILLAGE_EVENTS,
    EntityEventType.APPLICATION_EVENT: StageTypes.NUTRIENT_EVENTS,
    EntityEventType.IRRIGATION_EVENT: StageTypes.IRRIGATION_EVENTS,
    # GRAZING_EVENT and FIRE_EVENT are not included as they don't currently have SES mappings
    EntityEventType.GRAZING_EVENT: None,
    EntityEventType.FIRE_EVENT: None,
}
