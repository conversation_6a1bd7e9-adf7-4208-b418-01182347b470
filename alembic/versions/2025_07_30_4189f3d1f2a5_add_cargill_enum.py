"""add cargill enum

Revision ID: 4189f3d1f2a5
Revises: eb3afaa2bd83
Create Date: 2025-07-30 10:42:34.543707

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "4189f3d1f2a5"
down_revision = "eb3afaa2bd83"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "mrv_dndc_tasks",
        "baseline_method",
        existing_type=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE"),
        type_=sa.Enum(
            "ROTATIONAL",
            "BLENDED",
            "MATCHED",
            "CLIMATE_MATCHED",
            "CLIMATE_MATCHED_RICE",
            "CARGILL_CUSTOM",
            name="baselinemethod",
        ),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE"),
        type_=sa.Enum(
            "ROTATIONAL",
            "BLENDED",
            "MATCHED",
            "CLIMATE_MATCHED",
            "CLIMATE_MATCHED_RICE",
            "CARGILL_CUSTOM",
            name="baselinemethod",
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "mrv_programs",
        "baseline_method",
        existing_type=sa.Enum(
            "ROTATIONAL",
            "BLENDED",
            "MATCHED",
            "CLIMATE_MATCHED",
            "CLIMATE_MATCHED_RICE",
            "CARGILL_CUSTOM",
            name="baselinemethod",
        ),
        type_=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE"),
        existing_nullable=True,
    )
    op.alter_column(
        "mrv_dndc_tasks",
        "baseline_method",
        existing_type=sa.Enum(
            "ROTATIONAL",
            "BLENDED",
            "MATCHED",
            "CLIMATE_MATCHED",
            "CLIMATE_MATCHED_RICE",
            "CARGILL_CUSTOM",
            name="baselinemethod",
        ),
        type_=mysql.ENUM("ROTATIONAL", "BLENDED", "MATCHED", "CLIMATE_MATCHED", "CLIMATE_MATCHED_RICE"),
        existing_nullable=True,
    )
