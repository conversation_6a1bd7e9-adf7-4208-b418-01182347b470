import asyncio
import logging
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, NoReturn, Optional, Tuple

import grpc
from grpc_health.v1 import health_pb2, health_pb2_grpc
from regrow.ses.context.v1 import context_pb2
from regrow.ses.crop.v1 import crop_pb2
from regrow.ses.event.v1 import event_pb2, event_service_pb2, event_service_pb2_grpc

from ses_client import event, search, sequence
from ses_client import uuid as ses_uuid
from ses_client.application import ApplicationActivity
from ses_client.boundaries import BoundariesClient
from ses_client.event import EventWithContext, StructuredEvent
from ses_client.fallow import FallowPeriod
from ses_client.harvest import HarvestActivity
from ses_client.irrigation import IrrigationActivity
from ses_client.planting import PlantingActivity
from ses_client.sowing import SowingActivity
from ses_client.termination import TerminationActivity
from ses_client.tillage import TillageActivity


class EventNotFoundError(Exception):
    """Custom exception indicating that an event could not be found."""

    def __init__(self, event_id: str):
        super().__init__(f"No result for event_id: {event_id}")
        self.event_id = event_id


@dataclass
class CopyEventsToFieldRequest:
    """Represents a request to copy specific events to a target field."""

    event_ids: List[str]
    field_id: str


@dataclass
class CopyEventToFieldResult:
    """Result of copying a single event to a single field."""

    source_event_id: str
    target_field_id: str
    new_event_id: Optional[str] = None
    error: Optional[str] = None

    @property
    def success(self) -> bool:
        """Returns True if the copy operation was successful."""
        return self.new_event_id is not None and self.error is None


class Client:
    ERR_NO_SEARCH_ADDR = "Search host address not provided"
    ERR_NO_SEARCH_CLIENT = "Search client not initialized"
    ERR_NO_BOUNDARIES_SERVICE = "Boundaries service not initialized"

    # Default minimum overlap percentage for event-field intersections
    DEFAULT_MIN_OVERLAP_PERCENTAGE = 50

    # Default maximum cache age before triggering a refresh after an upsert, delete, archive, or restore operation.
    DEFAULT_CACHE_AGE_SECONDS = 5
    CACHE_CHECK_INTERVAL_SECONDS = 0.5
    CACHE_CHECK_ATTEMPTS = 10

    # gRPC keepalive settings to limit connection stickiness to event-srvc nodes
    KEEP_ALIVE_PING_INTERVAL_MS = 30000
    KEEP_ALIVE_TIMEOUT_MS = 10000

    def __init__(
        self,
        grpc_host_addr: str,
        search_host_addr: str = "",
        boundaries_service_url: str = "",
    ):
        self.grpc_host_addr = grpc_host_addr
        keepalive_options = [
            (
                "grpc.keepalive_time_ms",
                self.KEEP_ALIVE_PING_INTERVAL_MS,
            ),  # Send keepalive ping every 30s
            (
                "grpc.keepalive_timeout_ms",
                self.KEEP_ALIVE_TIMEOUT_MS,
            ),  # Wait 10s for response before closing
        ]
        self.channel = grpc.insecure_channel(grpc_host_addr, options=keepalive_options)
        self.event_srvc = event_service_pb2_grpc.EventServiceStub(self.channel)
        self.health_srvc = health_pb2_grpc.HealthStub(self.channel)

        if search_host_addr:
            self.search_client = search.Search(search_host_addr)

        if boundaries_service_url:
            self.boundaries_service = BoundariesClient(boundaries_service_url)

    def __del__(self):
        """Closes the gRPC channel when the object is deleted."""
        self.channel.close()

    @staticmethod
    def create_deterministic_uuid(data, namespace: str = "default") -> str:
        """Create a deterministic UUID based on input data.

        This is a convenience method that wraps the uuid.create_deterministic_uuid() function,
        making it easily accessible through the Client class.

        Args:
            data: Dictionary or string to generate UUID from. If dict, it will be
                  JSON-serialized with sorted keys for consistency.
            namespace: Optional namespace string to differentiate UUIDs generated
                      from the same data in different contexts.

        Returns:
            A deterministic UUID string in standard format (e.g., "550e8400-e29b-41d4-a716-************")

        Examples:
            >>> client = Client("localhost:50051")
            >>> data = {"id": 123, "eventType": "tillageActivity", "...": "..."}
            >>> uuid1 = client.create_deterministic_uuid(data)
            >>> uuid2 = client.create_deterministic_uuid(data)
            >>> uuid1 == uuid2
            True

            >>> # Different namespace produces different UUID
            >>> uuid3 = client.create_deterministic_uuid(data, namespace="123456")
            >>> uuid1 != uuid3
            True
        """
        return ses_uuid.create_deterministic_uuid(data, namespace)

    async def _execute_with_fieldevent_update(
        self, grpc_call, field_id: str | None = None, event_id: str | None = None
    ):
        """Makes the gRPC request and blocks until the fieldevent record has been updated.

        Args:
            grpc_call (Callable): A function that triggers the gRPC call.
            field_id (Optional[str]): The field ID related to the cache entry.
            event_id (Optional[str]): The event ID related to the cache entry.

        Returns:
            Any: The result of the gRPC call.
        """
        res = await asyncio.to_thread(grpc_call)

        # If we have no field_id, the update of a field_event record will be triggered by the event-srvc.
        # We don't wait but there's no guarantee that the field_event will be updated.
        if not field_id:
            return res

        # Most calls should include a field_id in which case we can run an explicit update
        if field_id and event_id:
            try:
                await self.search_client.update_field_event(field_id, event_id)
                return res
            except Exception as e:
                msg = f"Error updating field_event record for field {field_id} and event {event_id}: {e}"
                logging.error(msg)

        return res

    async def health_check(self):
        """Checks the health of the event service.

        Returns:
            HealthCheckResponse: The response from the health service.
        """
        logging.info("Checking health of event service")
        request = health_pb2.HealthCheckRequest(
            service="regrow.ses.event.v1.EventService"
        )
        return await asyncio.to_thread(self.health_srvc.Check, request)

    async def add_field_event_context(
        self, *, field_id: str, event_id: str, context: context_pb2.EventContext
    ):
        """Adds context to an event and delays the response until the fieldevent record is updated.

        Note:
            There is not much value in the cache delay when adding context for an event. The only piece of context that
            may appear in the cache is the ID of the user who created the event.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to which context is added.
            context (context_pb2.EventContext): The context to be added.

        Returns:
            AddEventContextResponse: The response from adding context to the event.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.AddEventContextRequest(
            event_id=event_id, context=context
        )
        try:
            return await self._execute_with_fieldevent_update(
                grpc_call=lambda: self.event_srvc.AddEventContext(req),
                field_id=field_id,
                event_id=event_id,
            )
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def upsert_field_event(
        self,
        *,
        field_id: str,
        structured_event: StructuredEvent,
        permissive: bool = True,
    ):
        """Upserts an event and delays the response until the cache entry for the specified field is updated.

        Args:
            field_id (str): The ID of the field related to the event.
            structured_event (StructuredEvent): The structured event to upsert.
            permissive (bool): Determines if the upsert can be more lenient with validations.

        Returns:
            UpsertEventResponse: The response from the UpsertEvent gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.UpsertEventRequest(
            event=structured_event.pb_event,
            context=structured_event.pb_context,
            permissive=permissive,
        )
        try:
            return await self._execute_with_fieldevent_update(
                grpc_call=lambda: self.event_srvc.UpsertEvent(req),
                field_id=field_id,
                event_id=structured_event.pb_event.id,
            )
        except grpc.RpcError as err:
            raise_grpc_error(structured_event.pb_event.id, err)

    async def archive_field_event(
        self, *, field_id: str, event_id: str, context: context_pb2.EventContext
    ):
        """Archives an event by setting its archive date on all revisions to now().

        Delays the response until the cache entry for the specified field is updated.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to archive.
            context (context_pb2.EventContext): Context indicating archival metadata.

        Returns:
            ArchiveEventResponse: The response from the ArchiveEvent gRPC call.

        Raises:
            ValueError: If the event does not exist for the specified field.
            grpc.RpcError: If a gRPC error occurs.
        """
        if not await self.field_has_event(field_id=field_id, event_id=event_id):
            raise ValueError(f"Event {event_id} does not exist for field {field_id}")

        req = event_service_pb2.ArchiveEventRequest(id=event_id, context=context)
        try:
            return await self._execute_with_fieldevent_update(
                grpc_call=lambda: self.event_srvc.ArchiveEvent(req),
                field_id=field_id,
                event_id=event_id,
            )
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def archive_field_event_for_user(
        self, *, field_id: str, event_id: str, user_id: str
    ):
        """Archives an event for the user.

        This method is the same as `archive_field_event` but creates the archival context for the specified user ID.
        The response is delayed until the cache entry for the specified field is updated.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to archive.
            user_id (str): The user performing the archival.

        Returns:
            ArchiveEventResponse: The response from the ArchiveEvent gRPC call.
        """
        context = context_pb2.EventContext(
            archival={StructuredEvent.CONTEXT_KEY_REGROW_USER_ID: user_id},
        )
        return await self.archive_field_event(
            field_id=field_id, event_id=event_id, context=context
        )

    async def restore_field_event(
        self, *, field_id: str, event_id: str, context: context_pb2.EventContext
    ):
        """Restores an event by setting the archive date on all revisions of an event to null.

        The response is delayed until the cache entry for the specified field is updated.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to restore.
            context (context_pb2.EventContext): Context indicating restoration metadata.

        Returns:
            RestoreEventResponse: The response from the RestoreEvent gRPC call.

        Raises:
            ValueError: If the event does not exist for the specified field.
            grpc.RpcError: If a gRPC error occurs.
        """
        if not await self.field_has_event(field_id=field_id, event_id=event_id):
            raise ValueError(f"Event {event_id} does not exist for field {field_id}")

        req = event_service_pb2.RestoreEventRequest(id=event_id, context=context)
        try:
            return await self._execute_with_fieldevent_update(
                grpc_call=lambda: self.event_srvc.RestoreEvent(req),
                field_id=field_id,
                event_id=event_id,
            )
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def restore_field_event_for_user(
        self, *, field_id: str, event_id: str, user_id: str
    ):
        """Restores an event for the user.

        This method is the same as `restore_field_event` but creates the restoration context for the specified user ID.
        The response is delayed until the cache entry for the specified field is updated.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to restore.
            user_id (str): The user performing the restoration.

        Returns:
            RestoreEventResponse: The response from the RestoreEvent gRPC call.
        """
        context = context_pb2.EventContext(
            restoration={StructuredEvent.CONTEXT_KEY_REGROW_USER_ID: user_id},
        )
        return await self.restore_field_event(
            field_id=field_id, event_id=event_id, context=context
        )

    async def delete_field_event(self, *, field_id: str, event_id: str):
        """Deletes an event and delays the response until the cache entry for the specified field is updated.

        If the event is successfully hard-deleted, the cache is updated quickly via a fast delete request,
        followed by an async cache update to refresh the entire entry.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to delete.

        Returns:
            DeleteEventResponse: The response from the DeleteEvent gRPC call.

        Raises:
            ValueError: If the event does not exist for the specified field.
            grpc.RpcError: If a gRPC error occurs.
        """
        if not await self.field_has_event(field_id=field_id, event_id=event_id):
            raise ValueError(f"Event {event_id} does not exist for field {field_id}")

        req = event_service_pb2.DeleteEventRequest(id=event_id)
        try:
            res = await asyncio.to_thread(self.event_srvc.DeleteEvent, req)
            if res and field_id and self.search_client:
                await self.search_client.delete_field_event(
                    field_id=field_id, event_id=event_id
                )
                await self.search_client.async_cache_update(field_id)
            return res
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def delete_or_archive_field_event_for_user(
        self, *, field_id: str, event_id: str, user_id: str
    ):
        """Tries to hard-delete an event and, if that fails, archives it.

        Events can be hard-deleted when:
          - they are in draft (revision 0)
          - they are at revision 1 but newly created within a configurable number of hours.

        Args:
            field_id (str): The ID of the field associated with the event.
            event_id (str): The ID of the event to delete or archive.
            user_id (str): The user performing the deletion or archival.

        Returns:
            DeleteEventResponse or ArchiveEventResponse: The response from either DeleteEvent or ArchiveEvent.

        Raises:
            ValueError: If the event does not exist for the specified field.
        """
        if not await self.field_has_event(field_id=field_id, event_id=event_id):
            raise ValueError(f"Event {event_id} does not exist for field {field_id}")

        try:
            return await self.delete_field_event(field_id=field_id, event_id=event_id)
        except ValueError as err:
            msg = f"Event {event_id} cannot be deleted due to {err}, archiving instead."
            logging.info(msg)
            return await self.archive_field_event_for_user(
                field_id=field_id, event_id=event_id, user_id=user_id
            )

    async def upsert_event(
        self, structured_event: StructuredEvent, permissive: bool = True
    ):
        """Upserts an event to SES.

        Args:
            structured_event (StructuredEvent): The structured event to upsert.
            permissive (bool): Determines if the upsert can be more lenient with validations.

        Returns:
            UpsertEventResponse: The response from the UpsertEvent gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.UpsertEventRequest(
            event=structured_event.pb_event,
            context=structured_event.pb_context,
            permissive=permissive,
        )
        try:
            return await asyncio.to_thread(self.event_srvc.UpsertEvent, req)
        except grpc.RpcError as err:
            raise_grpc_error(structured_event.pb_event.id, err)

    async def add_event_context(self, event_id: str, context: context_pb2.EventContext):
        """Adds context to an event.

        Args:
            event_id (str): The ID of the event to which context is added.
            context (context_pb2.EventContext): The context data to add.

        Returns:
            AddEventContextResponse: The response from the AddEventContext gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.AddEventContextRequest(
            event_id=event_id, context=context
        )
        try:
            return await asyncio.to_thread(self.event_srvc.AddEventContext, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def archive_event(self, event_id: str, context: context_pb2.EventContext):
        """Archives an event by setting the archive date on all revisions to now().

        Args:
            event_id (str): The ID of the event to archive.
            context (context_pb2.EventContext): Context indicating archival metadata.

        Returns:
            ArchiveEventResponse: The response from the ArchiveEvent gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.ArchiveEventRequest(id=event_id, context=context)
        try:
            return await asyncio.to_thread(self.event_srvc.ArchiveEvent, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def archive_event_for_user(self, *, event_id: str, user_id: str):
        """Archives an event for a user.

        This method is the same as `archive_event` but creates an archival context for the given user ID.

        Args:
            event_id (str): The ID of the event to archive.
            user_id (str): The user performing the archival.

        Returns:
            ArchiveEventResponse: The response from the ArchiveEvent gRPC call.
        """
        context = context_pb2.EventContext(
            archival={StructuredEvent.CONTEXT_KEY_REGROW_USER_ID: user_id},
        )
        return await self.archive_event(event_id, context)

    async def restore_event(self, event_id: str, context: context_pb2.EventContext):
        """Restores an event by setting the archive date on all revisions to null.

        Args:
            event_id (str): The ID of the event to restore.
            context (context_pb2.EventContext): Context indicating restoration metadata.

        Returns:
            RestoreEventResponse: The response from the RestoreEvent gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.RestoreEventRequest(id=event_id, context=context)
        try:
            return await asyncio.to_thread(self.event_srvc.RestoreEvent, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def restore_event_for_user(self, *, event_id: str, user_id: str):
        """Restores an event for a user.

        This method is the same as `restore_event` but creates a restoration context for the given user ID.

        Args:
            event_id (str): The ID of the event to restore.
            user_id (str): The user performing the restoration.

        Returns:
            RestoreEventResponse: The response from the RestoreEvent gRPC call.
        """
        context = context_pb2.EventContext(
            restoration={StructuredEvent.CONTEXT_KEY_REGROW_USER_ID: user_id},
        )
        return await self.restore_event(event_id, context)

    async def delete_event(self, event_id: str):
        """Attempts to hard-delete an event by ID.

        If the event is not deletable, an error is raised.

        Args:
            event_id (str): The ID of the event to delete.

        Returns:
            DeleteEventResponse: The response from the DeleteEvent gRPC call.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.DeleteEventRequest(id=event_id)
        try:
            return await asyncio.to_thread(self.event_srvc.DeleteEvent, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def delete_or_archive_event_for_user(self, *, event_id: str, user_id: str):
        """Tries to hard-delete an event and, if that fails, archives it.

        Events can be hard-deleted when:
          - they are in draft (revision 0)
          - they are at revision 1 but newly created within a configurable number of hours.

        Args:
            event_id (str): The ID of the event to delete or archive.
            user_id (str): The user performing the deletion or archival.

        Returns:
            DeleteEventResponse or ArchiveEventResponse: The response from either DeleteEvent or ArchiveEvent.
        """
        try:
            return await self.delete_event(event_id=event_id)
        except ValueError as err:
            msg = f"Event {event_id} cannot be deleted due to {err}, archiving instead."
            logging.info(msg)
            return await self.archive_event_for_user(event_id=event_id, user_id=user_id)

    async def field_has_event(self, field_id: str, event_id: str) -> bool:
        """Checks whether a given field's cache entry contains the specified event.

        Args:
            field_id (str): The ID of the field.
            event_id (str): The ID of the event to check.

        Returns:
            bool: True if the field contains the specified event, False otherwise.

        Raises:
            ValueError: If the search client is not initialized.
        """
        if not self.search_client:
            raise ValueError(self.ERR_NO_SEARCH_CLIENT)

        try:
            field_event_ids = await self.search_client.fetch_pb_event_ids_for_fields(
                [field_id]
            )
            return event_id in field_event_ids[field_id]
        except Exception as e:
            msg = f"Error checking if field {field_id} has event {event_id}: {e}"
            logging.error(msg)
            return False

    async def fetch_event(self, event_id: str):
        """Fetches a single event by its ID.

        Args:
            event_id (str): The ID of the event to fetch.

        Returns:
            event_pb2.Event: The requested event.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.FetchEventRequest(id=event_id)
        try:
            res = await asyncio.to_thread(self.event_srvc.FetchEvent, req)
            return res.event
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def fetch_events(self, event_ids: List[str]) -> List[event_pb2.Event]:
        """Fetches multiple events by their IDs.

        Note:
            We fetch them one by one so we can raise an error for the first fetch that fails
            and know which event ID was not found.

        Args:
            event_ids (List[str]): A list of event IDs to fetch.

        Returns:
            List[event_pb2.Event]: The requested list of events.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        events = []
        for event_id in event_ids:
            req = event_service_pb2.FetchEventRequest(id=event_id)
            try:
                res = await asyncio.to_thread(self.event_srvc.FetchEvent, req)
                events.append(res.event)
            except grpc.RpcError as err:
                raise_grpc_error(event_id, err)
        return events

    async def fetch_event_with_context(
        self, event_id: str, merge_context: bool = False
    ) -> event_service_pb2.FetchEventWithContextResponse:
        """Fetches a single event by ID, along with its latest context.

        If merge_context is True, the returned context is a merged record of all contextual data
        associated with the event.

        Args:
            event_id (str): The ID of the event to fetch.
            merge_context (bool): If True, merges all context data associated with the event.

        Returns:
            FetchEventWithContextResponse: The event and its context.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.FetchEventWithContextRequest(
            id=event_id, merge_context=merge_context
        )
        try:
            return await asyncio.to_thread(self.event_srvc.FetchEventWithContext, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def fetch_event_revision_with_context(
        self, event_id: str, revision: str, merge_context: bool = False
    ) -> event_service_pb2.FetchEventWithContextResponse:
        """Fetches a single event by ID and revision ID, along with its latest context.

        If merge_context is True, the returned context is a merged record of all contextual data
        associated with the event.

        Args:
            event_id (str): The ID of the event to fetch.
            revision (str): The revision ID to fetch.
            merge_context (bool): If True, merges all context data associated with the event.

        Returns:
            FetchEventWithContextResponse: The event and its context.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.FetchEventWithContextRequest(
            id=event_id, revision=revision, merge_context=merge_context
        )
        try:
            return await asyncio.to_thread(self.event_srvc.FetchEventWithContext, req)
        except grpc.RpcError as err:
            raise_grpc_error(event_id, err)

    async def fetch_events_with_context(
        self, event_ids: List[str], merge_context: bool = False
    ) -> List[event_service_pb2.FetchEventWithContextResponse]:
        """Fetches multiple events by their IDs, along with their latest contexts.

        If merge_context is True, each returned context is a merged record of all contextual data
        associated with that event.

        Args:
            event_ids (List[str]): The list of event IDs to fetch.
            merge_context (bool): If True, merges all context data for each event.

        Returns:
            List[FetchEventWithContextResponse]: A list of events with their contexts.

        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.FetchEventsWithContextRequest(
            ids=event_ids, merge_context=merge_context
        )
        try:
            res = await asyncio.to_thread(self.event_srvc.FetchEventsWithContext, req)
            return list(res.events)
        except grpc.RpcError as err:
            raise_grpc_error(str(event_ids), err)

    async def fetch_event_revisions_with_context(
        self, fetch_event_requests: list[event_service_pb2.FetchEventWithContextRequest]
    ) -> List[event_service_pb2.FetchEventWithContextResponse]:
        """Fetches multiple events by their IDs and revision IDs, along with their latest contexts.
        If merge_context is True, each returned context is a merged record of all contextual data
        associated with that event.
        Args:
            fetch_event_requests: (List[FetchEventWithContextRequest]): A list of events to fetch.
        Returns:
            List[FetchEventWithContextResponse]: A list of events with their contexts.
        Raises:
            grpc.RpcError: If a gRPC error occurs.
        """
        req = event_service_pb2.FetchEventsWithContextRequest(
            fetch_event_requests=fetch_event_requests,
        )
        try:
            res = await asyncio.to_thread(self.event_srvc.FetchEventsWithContext, req)
            return list(res.events)
        except grpc.RpcError as err:
            raise_grpc_error(str([req.id for req in fetch_event_requests]), err)

    async def get_last_update_for_field(self, field_id: str) -> datetime | None:
        """Fetches the last update timestamp for a specific field.

        Args:
            field_id (str): The ID of the field to check.

        Returns:
            datetime: The last update timestamp for the field.

        Raises:
            ValueError: If the search client is not initialized.
        """
        if not self.search_client:
            raise ValueError(self.ERR_NO_SEARCH_CLIENT)

        return await self.search_client.get_last_update_for_field(field_id)

    async def fetch_events_for_fields(
        self, field_ids: List[str], search_filter: search.Filter
    ) -> Dict[str, List[event_pb2.Event]]:
        """Performs a filtered search to retrieve events for the given fields.

        Args:
            field_ids (List[str]): A list of field IDs to fetch events for.
            search_filter (search.Filter): A filter object defining the criteria for event selection.

        Returns:
            Dict[str, List[event_pb2.Event]]: A dictionary mapping each field ID to a list of matching events.

        Raises:
            ValueError: If the search client is not initialized.
        """
        if not self.search_client:
            raise ValueError(self.ERR_NO_SEARCH_ADDR)

        return await self.search_client.pb_events_for_fields(field_ids, search_filter)

    async def fetch_events_for_fields_with_context(
        self, field_ids: List[str], search_filter: search.Filter
    ) -> Dict[str, List[EventWithContext]]:
        """Performs a filtered search to retrieve events for the given fields, including their contexts.

        Args:
            field_ids (List[str]): A list of field IDs to fetch events for.
            search_filter (search.Filter): A filter object defining the criteria for event selection.

        Returns:
            Dict[str, List[EventWithContext]]: A dictionary mapping each field ID to a list of events with context.
        """
        field_events_map = await self.fetch_events_for_fields(field_ids, search_filter)
        if not field_events_map:
            return {}

        field_events_with_context_map: Dict[str, List[EventWithContext]] = {}

        for field_id, field_events in field_events_map.items():
            field_events_with_context_map[field_id] = []
            for ev in field_events:
                res = await self.fetch_event_with_context(ev.id)
                field_events_with_context_map[field_id].append(
                    EventWithContext(event=res.event, context=res.context)
                )

        return field_events_with_context_map

    async def fetch_cropping_sequences_for_fields(
        self,
        field_ids: List[str],
        from_date: datetime,
        to_date: datetime,
        user_ids: Optional[List[str]] = None,
        owner_ids: Optional[List[str]] = None,
        actor_ids: Optional[List[str]] = None,
        program_ids: Optional[List[str]] = None,
        project_ids: Optional[List[str]] = None,
        min_overlap_percentage: Optional[int] = DEFAULT_MIN_OVERLAP_PERCENTAGE,
        crop_purpose: Optional[List[crop_pb2.CropPurpose]] = None,
        exclude_archived: bool = True,
    ) -> Dict[str, List[tuple]]:
        """Fetches sowing/planting-harvest pairs for a list of fields.

        Args:
            field_ids (List[str]): A list of field IDs to fetch events for.
            from_date (datetime): The start date for the event filter.
            to_date (datetime): The end date for the event filter.
            user_ids (Optional[List[str]]): An optional list of user IDs to filter events by.
            owner_ids (Optional[List[str]]): An optional list of owner IDs to filter events by.
            actor_ids (Optional[List[str]]): An optional list of actor IDs to filter events by.
            program_ids (Optional[List[str]]): An optional list of program IDs to filter events by.
            project_ids (Optional[List[str]]): An optional list of project IDs to filter events by.
            min_overlap_percentage (Optional[int]): An optional overlap percentage for filtering events.
            crop_purpose (Optional[List[crop_pb2.CropPurpose]]): An optional list of crop purposes to filter by.
            exclude_archived (bool): Whether to exclude archived events (default True).

        Returns:
            Dict[str, List[tuple]]: A dictionary mapping field IDs to their cropping sequences.

        Raises:
            ValueError: If the search client is not initialized.
        """
        if not self.search_client:
            raise ValueError(self.ERR_NO_SEARCH_ADDR)

        # Build the search filter
        search_filter = search.Filter(
            event_types=[
                StructuredEvent.TYPE_SOWING_ACTIVITY,
                StructuredEvent.TYPE_PLANTING_ACTIVITY,
                StructuredEvent.TYPE_HARVEST_ACTIVITY,
                StructuredEvent.TYPE_TERMINATION_ACTIVITY,
            ],
            interval_from=from_date,
            interval_to=to_date,
            min_overlap=min_overlap_percentage,
            exclude_archived=exclude_archived,
        )
        if user_ids:
            search_filter.user_ids = user_ids
        if owner_ids:
            search_filter.owner_ids = owner_ids
        if actor_ids:
            search_filter.actor_ids = actor_ids
        if program_ids:
            search_filter.program_ids = program_ids
        if project_ids:
            search_filter.project_ids = project_ids

        # Fetch all events for the specified fields
        field_events_map = await self.search_client.pb_events_for_fields(
            field_ids, search_filter
        )

        # Filter sowing, planting and harvest activities by crop purposes if specified
        if crop_purpose:
            field_events_map = filter_event_list_by_crop_purpose(
                field_events_map, crop_purpose
            )

        # Process events to create cropping sequences
        return {
            field_id: sequence.cropping_sequence(field_events)
            for field_id, field_events in field_events_map.items()
        }

    async def fetch_cropping_sequences_for_fields_with_context(
        self,
        field_ids: List[str],
        from_date: datetime,
        to_date: datetime,
        user_ids: Optional[List[str]] = None,
        owner_ids: Optional[List[str]] = None,
        actor_ids: Optional[List[str]] = None,
        program_ids: Optional[List[str]] = None,
        project_ids: Optional[List[str]] = None,
        min_overlap_percentage: Optional[int] = DEFAULT_MIN_OVERLAP_PERCENTAGE,
        crop_purpose: Optional[List[crop_pb2.CropPurpose]] = None,
        exclude_archived: bool = True,
    ) -> Dict[str, List[tuple]]:
        """Fetches sowing/planting-harvest-termination activity sequences for each of the given fields,
        including the context for each event.

        Args:
            field_ids (List[str]): A list of field IDs to fetch events for.
            from_date (datetime): The start date for the event filter.
            to_date (datetime): The end date for the event filter.
            user_ids (Optional[List[str]]): An optional list of user IDs to filter events by.
            owner_ids (Optional[List[str]]): An optional list of owner IDs to filter events by.
            actor_ids (Optional[List[str]]): An optional list of actor IDs to filter events by.
            program_ids (Optional[List[str]]): An optional list of program IDs to filter events by.
            project_ids (Optional[List[str]]): An optional list of project IDs to filter events by.
            min_overlap_percentage (Optional[int]): An optional overlap percentage for filtering events.
            crop_purpose (Optional[List[crop_pb2.CropPurpose]]): An optional list of crop purposes to filter by.
            exclude_archived (bool): Whether to exclude archived events (default True).

        Returns:
            Dict[str, List[tuple]]:
                A dictionary mapping field IDs to their cropping sequences. Each sequence is a tuple of
                (sowing/planting event, harvest event, termination event), all with context included.

        Raises:
            ValueError: If the search client is not initialized.
        """
        # Fetch the cropping sequences
        cropping_sequences = await self.fetch_cropping_sequences_for_fields(
            field_ids=field_ids,
            from_date=from_date,
            to_date=to_date,
            user_ids=user_ids,
            owner_ids=owner_ids,
            actor_ids=actor_ids,
            program_ids=program_ids,
            project_ids=project_ids,
            min_overlap_percentage=min_overlap_percentage,
            crop_purpose=crop_purpose,
            exclude_archived=exclude_archived,
        )

        field_events_with_context_map: Dict[
            str, List[Tuple[EventWithContext, EventWithContext, EventWithContext]]
        ] = {}

        for field_id, field_event_tuples in cropping_sequences.items():
            field_events_with_context_map[field_id] = []

            for event_tuple in field_event_tuples:
                sowplant: EventWithContext = EventWithContext()
                harvest: EventWithContext = EventWithContext()
                termination: EventWithContext = EventWithContext()

                if event_tuple[0] is not None:
                    res1 = await self.fetch_event_with_context(event_tuple[0].id)
                    sowplant.event = res1.event
                    sowplant.context = res1.context

                if event_tuple[1] is not None:
                    res2 = await self.fetch_event_with_context(event_tuple[1].id)
                    harvest.event = res2.event
                    harvest.context = res2.context

                if event_tuple[2] is not None:
                    res3 = await self.fetch_event_with_context(event_tuple[2].id)
                    termination.event = res3.event
                    termination.context = res3.context

                # Add the tuple of EventWithContext to the list
                field_events_with_context_map[field_id].append(
                    (sowplant, harvest, termination)
                )

        return field_events_with_context_map

    async def fetch_cropping_sequences(
        self,
        event_ids: List[str],
    ) -> List[tuple]:
        """Fetches sowing/planting-harvest-termination activity sequences for the given event IDs.

        Args:
            event_ids (List[str]): The list of event IDs to fetch. Non-cropping events are ignored.

        Returns:
            List[tuple]:
                A list of cropping sequences. Each sequence is a tuple of
                (sowing/planting event, harvest event, termination event).
        """

        req = event_service_pb2.FetchEventsRequest(ids=event_ids)
        try:
            res = await asyncio.to_thread(self.event_srvc.FetchEvents, req)
            # cropping_sequence looks like it will gracefully ignore non-cropping events
            return sequence.cropping_sequence(res.events)
        except grpc.RpcError as err:
            raise_grpc_error(str(event_ids), err)

    async def fetch_cropping_sequences_with_context(
        self,
        event_ids: List[str],
    ) -> List[tuple]:
        """Fetches sowing/planting-harvest-termination activity sequences for the given event IDs,
        including the context for each event.

        Args:
            event_ids (List[str]): The list of event IDs to fetch. Events unrelated to crop sequences are ignored.

        Returns:
            List[tuple]:
                A list of cropping sequences. Each sequence is a tuple of
                (sowing/planting event, harvest event, termination event), all with context included.
        """
        # Fetch the cropping sequences
        cropping_sequences = await self.fetch_cropping_sequences(
            event_ids=event_ids,
        )

        field_events_with_context: List[
            Tuple[EventWithContext, EventWithContext, EventWithContext]
        ] = []

        for event_tuple in cropping_sequences:
            sowplant: EventWithContext = EventWithContext()
            harvest: EventWithContext = EventWithContext()
            termination: EventWithContext = EventWithContext()

            if event_tuple[0] is not None:
                res1 = await self.fetch_event_with_context(event_tuple[0].id)
                sowplant.event = res1.event
                sowplant.context = res1.context

            if event_tuple[1] is not None:
                res2 = await self.fetch_event_with_context(event_tuple[1].id)
                harvest.event = res2.event
                harvest.context = res2.context

            if event_tuple[2] is not None:
                res3 = await self.fetch_event_with_context(event_tuple[2].id)
                termination.event = res3.event
                termination.context = res3.context

            field_events_with_context.append((sowplant, harvest, termination))

        return field_events_with_context

    async def copy_event_to_field(
        self, event_id: str, field_id: str, batch_id: Optional[str] = None
    ) -> str:
        """Copy an event to a different field with updated geometry.

        Creates a new event with the same data as the source event, except with
        updated geometry fetched from the target field. Each call to this method
        creates a new event with a unique ID, even if the same source event has
        been copied to the same target field before.

        Args:
            event_id (str): The ID of the source event to copy.
            field_id (str): The MD5 hash of the target field to copy the event to.

        Returns:
            str: The ID of the newly created event.

        Raises:
            ValueError: If boundaries service is not initialized.
            EventNotFoundError: If the source event is not found.
            grpc.RpcError: If a gRPC error occurs during event operations.
        """
        # Check if boundaries service is available
        if not hasattr(self, "boundaries_service"):
            raise ValueError(self.ERR_NO_BOUNDARIES_SERVICE)

        # Fetch the source event with context
        source_event_with_context = await self.fetch_event_with_context(event_id)
        source_event = source_event_with_context.event
        source_context = source_event_with_context.context

        # Fetch geometry from boundaries service
        target_geometry = await self.boundaries_service.fetch_geometry_str(field_id)

        # Generate unique UUID for each copy operation
        new_event_id = str(uuid.uuid4())

        # Create new event based on source event type
        new_event = self._create_event_copy(
            source_event,
            source_context,
            new_event_id,
            target_geometry,
            field_id,
            batch_id,
        )

        # Upsert the new event and capture the actual persisted event ID
        upsert_response = await self.upsert_event(new_event)
        actual_event_id = upsert_response.event.id

        return actual_event_id

    async def bulk_copy_events(
        self, copy_requests: List[CopyEventsToFieldRequest]
    ) -> List[CopyEventToFieldResult]:
        """Copy multiple events to multiple fields with detailed error reporting.

        This method handles bulk copy operations where each request specifies a set of
        events to copy to a specific target field. Each individual copy operation can
        succeed or fail independently, with detailed results returned for each.

        Args:
            copy_requests (List[CopyEventsToFieldRequest]): List of copy requests,
                where each request specifies event IDs and a target field ID.

        Returns:
            List[CopyEventToFieldResult]: List of results, one for each event-field
                combination across all requests. Results include success/failure status,
                new event IDs for successful copies, and error messages for failures.

        Raises:
            ValueError: If boundaries service is not initialized.

        Examples:
            # One event to many fields
            requests = [
                CopyEventsToFieldRequest(["event1"], "field1"),
                CopyEventsToFieldRequest(["event1"], "field2"),
            ]
            results = await client.bulk_copy_events(requests)

            # Many events to many fields (each field gets different events)
            requests = [
                CopyEventsToFieldRequest(["event1", "event2"], "field1"),
                CopyEventsToFieldRequest(["event3"], "field2"),
            ]
            results = await client.bulk_copy_events(requests)
        """
        # Check if boundaries service is available
        if not hasattr(self, "boundaries_service"):
            raise ValueError(self.ERR_NO_BOUNDARIES_SERVICE)

        # Collect all unique field IDs to batch fetch geometries
        unique_field_ids = list(set(req.field_id for req in copy_requests))

        # Batch fetch geometries for all unique fields
        field_geometries = {}
        for field_id in unique_field_ids:
            try:
                geometry = await self.boundaries_service.fetch_geometry_str(field_id)
                field_geometries[field_id] = geometry
            except Exception as e:
                # If we can't fetch geometry for a field, we'll handle it per-copy below
                field_geometries[field_id] = e

        # Generate a unique batch ID for this bulk copy operation
        import time

        batch_timestamp = str(int(time.time() * 1000000))  # microsecond precision
        batch_id = f"bulk_copy_{batch_timestamp}"

        # Process each copy request
        results = []
        for request in copy_requests:
            # Process each event in the request
            for event_id in request.event_ids:
                result = await self._copy_single_event_to_field(
                    event_id, request.field_id, field_geometries, batch_id
                )
                results.append(result)

        return results

    async def _copy_single_event_to_field(
        self,
        event_id: str,
        field_id: str,
        field_geometries: Dict[str, Exception | str],
        batch_id: Optional[str] = None,
    ) -> CopyEventToFieldResult:
        """Copy a single event to a single field, handling all errors gracefully.

        Args:
            event_id (str): The ID of the source event to copy.
            field_id (str): The ID of the target field.
            field_geometries (Dict[str, any]): Pre-fetched geometries or exceptions.

        Returns:
            CopyEventToFieldResult: Result of the copy operation.
        """
        try:
            # Check if we have geometry for this field
            if field_id not in field_geometries:
                return CopyEventToFieldResult(
                    source_event_id=event_id,
                    target_field_id=field_id,
                    error="Field geometry not available",
                )

            # Check if geometry fetch failed
            geometry_or_error = field_geometries[field_id]
            if isinstance(geometry_or_error, Exception):
                return CopyEventToFieldResult(
                    source_event_id=event_id,
                    target_field_id=field_id,
                    error=f"Failed to fetch field geometry: {str(geometry_or_error)}",
                )

            target_geometry = geometry_or_error

            try:
                # Fetch the source event with context
                source_event_with_context = await self.fetch_event_with_context(
                    event_id
                )
                source_event = source_event_with_context.event
                source_context = source_event_with_context.context
            except EventNotFoundError:
                return CopyEventToFieldResult(
                    source_event_id=event_id,
                    target_field_id=field_id,
                    error=f"Source event not found: {event_id}",
                )

            # Generate unique UUID for each copy operation
            new_event_id = str(uuid.uuid4())

            # Create new event based on source event type
            new_event = self._create_event_copy(
                source_event,
                source_context,
                new_event_id,
                target_geometry,
                field_id,
                batch_id,
            )

            # Upsert the new event and capture the actual persisted event ID
            upsert_response = await self.upsert_event(new_event)
            actual_event_id = upsert_response.event.id

            return CopyEventToFieldResult(
                source_event_id=event_id,
                target_field_id=field_id,
                new_event_id=actual_event_id,
            )

        except EventNotFoundError:
            return CopyEventToFieldResult(
                source_event_id=event_id,
                target_field_id=field_id,
                error=f"Source event not found: {event_id}",
            )
        except Exception as e:
            return CopyEventToFieldResult(
                source_event_id=event_id, target_field_id=field_id, error=str(e)
            )

    def _create_event_copy(
        self,
        source_event: event_pb2.Event,
        source_context: context_pb2.EventContext,
        new_event_id: str,
        target_geometry: str,
        target_field_id: str,
        batch_id: Optional[str] = None,
    ) -> StructuredEvent:
        """Create a copy of an event with new ID and geometry.

        Args:
            source_event (event_pb2.Event): The source event to copy.
            source_context (context_pb2.EventContext): The source event's context.
            new_event_id (str): The ID for the new event.
            target_geometry (str): The target geometry as GeoJSON string.
            target_field_id (str): The target field ID for deterministic cropping period mapping.

        Returns:
            StructuredEvent: A new structured event with copied data.

        Raises:
            ValueError: If the event type is not supported.
        """
        # Extract user_id from source event context (if available)
        user_id = None
        if source_context and "regrowUserId" in source_context.creation:
            user_id = source_context.creation["regrowUserId"]

        # Determine event type and create appropriate StructuredEvent subclass
        event_type_str = event.event_type(source_event)

        # Create a new event with generic type
        new_event: StructuredEvent

        if event_type_str == StructuredEvent.TYPE_TILLAGE_ACTIVITY:
            new_event = TillageActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.tillage_activity.CopyFrom(source_event.tillage_activity)
        elif event_type_str == StructuredEvent.TYPE_SOWING_ACTIVITY:
            new_event = SowingActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.sowing_activity.CopyFrom(source_event.sowing_activity)
        elif event_type_str == StructuredEvent.TYPE_PLANTING_ACTIVITY:
            new_event = PlantingActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.planting_activity.CopyFrom(
                source_event.planting_activity
            )
        elif event_type_str == StructuredEvent.TYPE_APPLICATION_ACTIVITY:
            new_event = ApplicationActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.application_activity.CopyFrom(
                source_event.application_activity
            )
        elif event_type_str == StructuredEvent.TYPE_HARVEST_ACTIVITY:
            new_event = HarvestActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.harvest_activity.CopyFrom(source_event.harvest_activity)
        elif event_type_str == StructuredEvent.TYPE_IRRIGATION_ACTIVITY:
            new_event = IrrigationActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.irrigation_activity.CopyFrom(
                source_event.irrigation_activity
            )
        elif event_type_str == StructuredEvent.TYPE_FALLOW_PERIOD:
            new_event = FallowPeriod(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.fallow_period.CopyFrom(source_event.fallow_period)
        elif event_type_str == StructuredEvent.TYPE_TERMINATION_ACTIVITY:
            new_event = TerminationActivity(event_id=new_event_id, user_id=user_id)
            new_event.pb_event.termination_activity.CopyFrom(
                source_event.termination_activity
            )
        else:
            raise ValueError(f"Unsupported event type: {event_type_str}")

        # Copy common event fields (except ID and geometry which we're replacing)
        new_event.pb_event.interval.CopyFrom(source_event.interval)

        # Set copy tracking fields to ensure each copy is unique
        # This prevents the event service from treating copies as duplicates
        new_event.pb_event.copied_from_event_id = source_event.id
        new_event.pb_event.copied_from_event_revision = source_event.revision
        # Note: copied_at field is intentionally not set - the event-srvc will populate it automatically

        # Generate deterministic cropping_period_identifier based on source and target field
        # This ensures events from the same source cropping sequence maintain their relationship
        new_cropping_period_id = self._generate_target_cropping_period_id(
            source_event.cropping_period_identifier, target_field_id, batch_id
        )
        if new_cropping_period_id:
            new_event.pb_event.cropping_period_identifier = new_cropping_period_id

        # Copy the context from the source event (this includes user_id and other metadata)
        if source_context:
            new_event.pb_context.CopyFrom(source_context)

        # Set the new geometry
        new_event.geom(target_geometry)

        return new_event

    def _generate_target_cropping_period_id(
        self,
        source_cropping_period_id: str,
        target_field_id: str,
        batch_id: Optional[str] = None,
    ) -> Optional[str]:
        """Generate a deterministic cropping period ID for copied events.

        For individual copies (batch_id=None), returns None to remove cropping period grouping.
        For bulk copies, events from the same source sequence get the same target cropping
        period ID within the batch, but different batches get different IDs.

        Args:
            source_cropping_period_id (str): The source event's cropping period ID.
            target_field_id (str): The target field ID.
            batch_id (Optional[str]): Unique identifier for this copy batch operation.

        Returns:
            Optional[str]: The target cropping period ID, or None for individual copies.
        """
        # For individual copies, always return None (no cropping period grouping)
        if batch_id is None:
            return None

        # For bulk copies, only generate if source had a cropping period ID
        if not source_cropping_period_id:
            return None

        # Create batch-specific cropping period ID
        namespace_data = f"{source_cropping_period_id}:{target_field_id}:{batch_id}"
        return ses_uuid.create_deterministic_uuid(
            namespace_data, namespace="cropping_copy"
        )


def raise_grpc_error(event_id: str, err: grpc.RpcError) -> NoReturn:
    """Raises an appropriate exception based on the given gRPC error code.

    Args:
        event_id (str): The ID of the event related to the error.
        err (grpc.RpcError): The gRPC RpcError instance that triggered this function.

    Raises:
        EventNotFoundError: If the gRPC error code is NOT_FOUND.
        ValueError: If the gRPC error code is INVALID_ARGUMENT.
        ConnectionError: If the gRPC error code is UNAVAILABLE.
        RuntimeError: For all other error codes.
    """
    if err.code() == grpc.StatusCode.NOT_FOUND:
        raise EventNotFoundError(event_id)
    elif err.code() == grpc.StatusCode.INVALID_ARGUMENT:
        raise ValueError("Event Service returned INVALID_ARGUMENT error code.")
    elif err.code() == grpc.StatusCode.UNAVAILABLE:
        raise ConnectionError("The event service is currently unavailable.")
    else:
        raise RuntimeError(f"unexpected error: {err.details()}")


def filter_event_list_by_crop_purpose(
    field_events_map: Dict[str, List[event_pb2.Event]],
    crop_purposes: List[crop_pb2.CropPurpose],
) -> Dict[str, List[event_pb2.Event]]:
    """Filters the given field-event map to only include sowing, planting, and harvest activities
    with crops that match the specified purposes.

    Args:
        field_events_map (Dict[str, List[event_pb2.Event]]): A dictionary mapping field IDs to lists of events.
        crop_purposes (List[crop_pb2.CropPurpose]): The crop purposes to filter events by.

    Returns:
        Dict[str, List[event_pb2.Event]]: A filtered dictionary of field events that match the given purposes.
    """
    if not crop_purposes:
        return field_events_map

    filtered_field_events_map: Dict[str, List[event_pb2.Event]] = {
        field_id: [] for field_id in field_events_map.keys()
    }

    for field_id, field_events in field_events_map.items():
        for ev in field_events:
            if not event.has_crop(ev) or event.has_matching_crop_purpose(
                ev, crop_purposes
            ):
                filtered_field_events_map[field_id].append(ev)

    return filtered_field_events_map
