import json
import uuid
from datetime import date, datetime, timedelta, timezone
from typing import Callable
from unittest.mock import ANY, AsyncMock, patch

import pytest
import pytz
from fastapi import status
from matplotlib.dates import relativedelta
from regrow.ses.context.v1.context_pb2 import EventContext
from regrow.ses.event.v1.event_pb2 import Event
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextResponse
from regrow.ses.harvest.v1.harvest_pb2 import HarvestActivity
from ses_client.client import Client as SesClient
from ses_client.event import StructuredEvent
from ses_client.fallow import FallowPeriod as FallowPeriodStructuredEvent
from ses_client.harvest import HarvestActivity as HarvestActivityStructuredEvent
from ses_client.planting import PlantingActivity as PlantingActivityStructuredEvent

from boundaries_service import client as boundaries_client
from defaults.attribute_options import CropUsage, NoCropType
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Interval
from entity_events.validation.rules_scraper import ValidationRulesScraper
from fields.enums import FieldStatus
from fields.schema import Field
from phases.dataclasses import DateRange
from phases.enums import PhaseTypes, StageTypes
from programs.enums import AccountingMethod, ProgramTemplate
from projects.db import get_owner_user_id_for_project
from ses_integration.tests.mocks import (
    get_mock_crop_sequences_cross_year,
    get_mock_field_practice_events,
)
from ui.projects.field_events.constants import DEFAULT_FIELD_EVENT_QUERY_FROM_DATE
from ui.projects.field_events.enums import CopyFieldEventsMode
from ui.projects.field_events.methods import (
    _build_field_cultivation_cycles_response_lookup,
    _create_default_no_practice_observations,
    _delete_events_in_target_field_stage,
    _fetch_event_with_context_or_none,
    _generate_initial_cultivation_cycle_results,
    _get_no_practice_observations_for_empty_cultivation_cycle_result,
    _is_event_overlap_with_intervals,
    _unset_no_practice_observations_if_conflict,
    build_field_cultivation_cycles_for_response,
    copy_field_events,
    create_update_field_events,
    find_overlapping_events,
    get_completion_filter_period,
    get_event_query_range_for_reporting_period,
    get_program_data_collection_required_date_range,
    get_target_field_cultivation_cycles,
    is_cultivation_cycle_in_filter_period,
)
from ui.projects.field_events.router import get_field_event_meta_data
from ui.projects.field_events.schema import (
    CultivationCycleResponse,
    CultivationCycleResponseId,
    CultivationCycleResult,
    FieldEvent,
    FieldEventRequest,
    NoPracticeObservations,
)
from ui.projects.field_events.tests.mocks import get_mock_geom
from ui.projects.methods import (
    extract_acting_user_id,
    get_project_program_phase_and_stage,
)
from values.enums import EntityTypeChoices

field_id = "field-id"


start = datetime(year=2020, month=2, day=1)
end = datetime(year=2022, month=12, day=31)


async def test_get_program_data_collection_required_date_range_for_monitoring_reporting_period_greater_than_one_year(
    mdl,
):
    program = await mdl.Programs(reporting_period_start_date=start, reporting_period_end_date=end)
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.MONITORING, program=program)
    assert result == (end.replace(tzinfo=pytz.utc) - relativedelta(years=2), end.replace(tzinfo=pytz.utc))


async def test_get_program_data_collection_required_date_range_for_monitoring_reporting_period_less_than_one_year(mdl):
    start = datetime(2024, 7, 1)
    end = datetime(2025, 6, 1)
    program = await mdl.Programs(reporting_period_end_date=end, reporting_period_start_date=start)
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.MONITORING, program=program)
    assert result == (end.replace(tzinfo=pytz.utc), end.replace(tzinfo=pytz.utc))


async def test_get_program_data_collection_required_date_range_for_enrolment_multi_phase_data_collection(mdl):
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.ENROLMENT, program=program)
    assert result == (
        start.replace(tzinfo=pytz.utc) - timedelta(days=1) - relativedelta(years=4),
        start.replace(tzinfo=pytz.utc) - timedelta(days=1),
    )


async def test_get_program_data_collection_required_date_range_for_enrolment_multi_phase_data_collection_without_history(
    mdl,
):
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=None,
        is_single_phase_data_collection=False,
    )
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.ENROLMENT, program=program)
    assert result == (
        start.replace(tzinfo=pytz.utc) - timedelta(days=1) - relativedelta(years=2),
        start.replace(tzinfo=pytz.utc) - timedelta(days=1),
    )


async def test_get_program_data_collection_required_date_range_for_enrolment_single_phase_data_collection(mdl):
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=5,
        is_single_phase_data_collection=True,
    )
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.ENROLMENT, program=program)
    assert result == (
        start.replace(tzinfo=pytz.utc) - timedelta(days=1) - relativedelta(years=4),
        end.replace(tzinfo=pytz.utc),
    )


async def test_get_program_data_collection_required_date_range_for_enrolment_single_phase_data_collection_without_history(
    mdl,
):
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        required_years_of_history=None,
        is_single_phase_data_collection=True,
    )
    result = get_program_data_collection_required_date_range(phase_type=PhaseTypes.ENROLMENT, program=program)
    assert result == (
        start.replace(tzinfo=pytz.utc) - timedelta(days=1) - relativedelta(years=2),
        end.replace(tzinfo=pytz.utc),
    )


@pytest.mark.parametrize(
    "phase_type, reporting_period_start, reporting_period_end, is_single_phase, accounting_method, expected_result",
    [
        # Two-phase, Monitoring, intervention
        (
            PhaseTypes.MONITORING,
            datetime(2020, 2, 1),
            datetime(2022, 12, 31),
            False,
            AccountingMethod.intervention,
            (datetime(2020, 2, 1) - relativedelta(years=2), datetime(2022, 12, 31)),
        ),
        # Two-phase, Monitoring, inventory
        (
            PhaseTypes.MONITORING,
            datetime(2020, 2, 1),
            datetime(2022, 12, 31),
            False,
            AccountingMethod.inventory,
            (datetime(2020, 2, 1) - relativedelta(years=1), datetime(2022, 12, 31)),
        ),
        # Two-phase, Enrolment, intervention
        (
            PhaseTypes.ENROLMENT,
            datetime(2020, 2, 1),
            datetime(2022, 12, 31),
            False,
            AccountingMethod.intervention,
            (DEFAULT_FIELD_EVENT_QUERY_FROM_DATE, datetime(2020, 2, 1) - timedelta(days=1)),
        ),
        # Single-phase, Enrolment, intervention
        (
            PhaseTypes.ENROLMENT,
            datetime(2020, 2, 1),
            datetime(2022, 12, 31),
            True,
            AccountingMethod.intervention,
            (DEFAULT_FIELD_EVENT_QUERY_FROM_DATE, datetime(2022, 12, 31)),
        ),
        # Single-phase, Enrolment, inventory
        (
            PhaseTypes.ENROLMENT,
            datetime(2020, 2, 1),
            datetime(2022, 12, 31),
            True,
            AccountingMethod.inventory,
            (DEFAULT_FIELD_EVENT_QUERY_FROM_DATE, datetime(2022, 12, 31)),
        ),
    ],
)
async def test_get_event_query_range_for_reporting_period(
    mdl,
    phase_type: PhaseTypes,
    reporting_period_start: datetime,
    reporting_period_end: datetime,
    is_single_phase: bool,
    accounting_method: AccountingMethod,
    expected_result: tuple,
):
    program = await mdl.Programs(
        reporting_period_start_date=reporting_period_start,
        reporting_period_end_date=reporting_period_end,
        is_single_phase_data_collection=is_single_phase,
        accounting_method=accounting_method,
    )
    result = get_event_query_range_for_reporting_period(phase_type=phase_type, program=program)
    assert result == expected_result


# Copy field events tests


async def mock_fetch_event_success(event_id):
    return type(
        "MockEventWithContext",
        (),
        {
            "event": type(
                "MockEvent",
                (),
                {
                    "id": str(uuid.uuid4()),
                    "revision": 1,
                    "HasField": lambda self, field: field == "tillage_activity",
                },
            )()
        },
    )()


async def mock_fetch_event_none(event_id):
    return None


async def mock_bulk_copy_success(copy_requests):
    from ses_client.client import CopyEventToFieldResult

    results = []
    for request in copy_requests:
        for event_id in request.event_ids:
            results.append(
                CopyEventToFieldResult(
                    source_event_id=event_id,
                    target_field_id="field-md5",
                    new_event_id=str(uuid.uuid4()),
                )
            )
    return results


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("projects.methods.get_project_phase_completion")
@patch("projects.db.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.update_event_associations")
@patch("domain_event_bus.domain_event_bus.event_bus.publish")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_copy_field_events_success(
    mock_parse_ses_events,
    mock_get_locked_intervals,
    mock_publish,
    mock_update_associations,
    mock_get_owner_user_id,
    mock_get_phase_completion,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    faker,
    mdl,
    create_tillage_event_data,
):
    event_ids = [str(uuid.uuid4()), str(uuid.uuid4())]

    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    mock_get_accessible_projects.return_value = None
    mock_get_phase_completion.return_value = type("MockCompletion", (), {"is_completed": False})()
    mock_get_owner_user_id.return_value = faker.unique.random_number(3)
    # Create mock cultivation cycle with no locked events
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,  # Not locked
        stage_cultivation_cycle_is_locked=False,  # Not locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    # Mock parse_ses_events to return a simple tillage event
    mock_tillage_event = TillageEvent.parse_obj(create_tillage_event_data(id=faker.unique.random_number(3)))
    mock_parse_ses_events.return_value = [mock_tillage_event]

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success)

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    assert len(result) == 2
    assert all(r.succeeded for r in result)
    mock_update_associations.assert_called_once()
    mock_publish.assert_called_once()


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
async def test_copy_field_events_source_event_not_found(
    mock_get_locked_intervals, mock_get_accessible_projects, mock_ses_client, app_request, faker, mdl
):
    event_ids = [str(uuid.uuid4())]

    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_none)
    mock_get_accessible_projects.return_value = None  # Unrestricted access
    # Create mock cultivation cycle with no locked events
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,  # Not locked
        stage_cultivation_cycle_is_locked=False,  # Not locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    assert len(result) == 1
    assert not result[0].succeeded
    assert result[0].status_code == status.HTTP_404_NOT_FOUND


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
async def test_copy_field_events_access_denied(
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    mdl,
):
    event_ids = [str(uuid.uuid4())]

    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_get_accessible_projects.return_value = {991}
    app_request.state.allowed_user_program_project_ids = {"project_ids": [992]}

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    assert len(result) == 1
    assert not result[0].succeeded
    assert result[0].status_code == status.HTTP_403_FORBIDDEN


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_copy_field_events_locked_field(
    mock_parse_ses_events,
    mock_get_locked_intervals,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    mdl,
):
    """Test copy_field_events when event is locked - should be silently skipped"""
    event_ids = [str(uuid.uuid4())]

    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_get_accessible_projects.return_value = None
    # Create mock cultivation cycle with locked events that will overlap with any event
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=True,  # Locked
        stage_cultivation_cycle_is_locked=True,  # Locked
        start=datetime(2020, 1, 1, tzinfo=timezone.utc),
        end=datetime(2030, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2025,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    # Mock parse_ses_events to return a simple tillage event with a date that will overlap

    mock_tillage_event = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2025, 6, 15),  # This will overlap with the locked interval
        }
    )
    mock_parse_ses_events.return_value = [mock_tillage_event]

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    # Locked events are now silently skipped, so no results should be returned
    assert len(result) == 0


async def mock_bulk_copy_success_cropping_ids(copy_requests):
    """Mock function for successful bulk copy with CroppingIDs reconstruction"""
    from ses_client.client import CopyEventToFieldResult

    results = []
    for request in copy_requests:
        for event_id in request.event_ids:
            results.append(
                CopyEventToFieldResult(
                    source_event_id=event_id,
                    target_field_id="field-md5",
                    new_event_id=str(uuid.uuid4()),
                )
            )
    return results


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("projects.methods.get_project_phase_completion")
@patch("projects.db.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.update_event_associations")
@patch("domain_event_bus.domain_event_bus.event_bus.publish")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_copy_field_events_with_cropping_ids(
    mock_parse_ses_events,
    mock_get_locked_intervals,
    mock_publish,
    mock_update_associations,
    mock_get_owner_user_id,
    mock_get_phase_completion,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    mdl,
):
    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    cropping_ids = CroppingIDs(
        planting_id=None,
        sowing_id=uuid.UUID("c4825c7e-3bc2-5847-aef0-e38b41f146a2"),
        harvesting_id=uuid.UUID("f0e0a182-3008-5965-bbc0-02fbe73fd170"),
        termination_id=uuid.UUID("0f051cb6-48a6-51ad-9132-376a8c975834"),
        fallow_id=None,
    )

    event_ids = [cropping_ids]

    mock_get_accessible_projects.return_value = None
    mock_get_phase_completion.return_value = type("MockCompletion", (), {"is_completed": False})()
    mock_get_owner_user_id.return_value = 123
    # Create mock cultivation cycle with no locked events
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,  # Not locked
        stage_cultivation_cycle_is_locked=False,  # Not locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    # Mock parse_ses_events to return a simple cropping event
    mock_cropping_event = CroppingEvent.parse_obj({"id": 1, "entity_id": "1", "entity_type": "field"})
    mock_parse_ses_events.return_value = [mock_cropping_event]

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success_cropping_ids)

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    # Should return one result for the composite CroppingIDs
    assert len(result) == 1
    assert result[0].succeeded

    # The source event ID should be the original CroppingIDs
    assert result[0].source_event_id == cropping_ids

    # The new event ID should be a reconstructed CroppingIDs with new UUIDs
    assert isinstance(result[0].new_event_id, CroppingIDs)
    new_cropping_ids = result[0].new_event_id

    # Verify the structure is preserved (None fields stay None, non-None get new UUIDs)
    assert new_cropping_ids.planting_id is None
    assert new_cropping_ids.fallow_id is None
    assert new_cropping_ids.sowing_id is not None
    assert new_cropping_ids.harvesting_id is not None
    assert new_cropping_ids.termination_id is not None

    # Verify the new UUIDs are different from the original ones
    assert new_cropping_ids.sowing_id != cropping_ids.sowing_id
    assert new_cropping_ids.harvesting_id != cropping_ids.harvesting_id
    assert new_cropping_ids.termination_id != cropping_ids.termination_id

    mock_update_associations.assert_called_once()
    mock_publish.assert_called_once()


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("projects.methods.get_project_phase_completion")
@patch("projects.db.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.update_event_associations")
@patch("domain_event_bus.domain_event_bus.event_bus.publish")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
async def test_copy_field_events_with_include_new_field_event(
    mock_get_locked_intervals,
    mock_parse_ses_events,
    mock_publish,
    mock_update_associations,
    mock_get_owner_user_id,
    mock_get_phase_completion,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    faker,
    mdl,
    create_tillage_event_data,
):
    event_ids = [str(uuid.uuid4())]

    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.TILLAGE_EVENTS)

    mock_get_accessible_projects.return_value = None
    mock_get_phase_completion.return_value = type("MockCompletion", (), {"is_completed": False})()
    mock_get_owner_user_id.return_value = faker.unique.random_number(3)
    # Create mock cultivation cycle with no locked events
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,  # Not locked
        stage_cultivation_cycle_is_locked=False,  # Not locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success)

    mock_tillage_event = TillageEvent.parse_obj(create_tillage_event_data(id=faker.unique.random_number(3)))
    mock_parse_ses_events.return_value = [mock_tillage_event]

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
        include_new_field_event=True,
    )

    assert len(result) == 1
    assert result[0].succeeded

    assert result[0].new_event is not None
    assert isinstance(result[0].new_event, FieldEvent)
    assert result[0].new_event.type == EntityEventType.TILLAGE_EVENT

    # parse_ses_events is called twice: once during validation and once during result processing
    assert mock_parse_ses_events.call_count == 2

    mock_update_associations.assert_called_once()
    mock_publish.assert_called_once()


# Tests for new functions and scenarios


async def test_get_target_field_cultivation_cycles_no_locked_events(app_request, mdl):
    """Test get_target_field_cultivation_cycles when no events are locked"""

    # Create test data
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    with (
        patch("ui.projects.field_events.methods.get_owner_user_id_for_project") as mock_get_owner,
        patch("ui.projects.field_events.methods.extract_acting_user_id") as mock_extract_user,
        patch("ui.projects.field_events.methods.get_locked_event_ids_for_user") as mock_get_locked,
        patch("ui.projects.field_events.methods.build_field_cultivation_cycles_for_calculations") as mock_build_cycles,
    ):
        mock_get_owner.return_value = 123
        mock_extract_user.return_value = "456"
        mock_get_locked.return_value = set()  # No locked events
        mock_build_cycles.return_value = {target_field.id: []}  # Empty list

        result = await get_target_field_cultivation_cycles(
            request=app_request,
            target_program=target_program,
            target_phase=target_phase,
            target_stage=target_stage,
            target_field=target_field,
        )

        assert result == []


async def test_get_target_field_cultivation_cycles_with_locked_events(app_request, mdl):
    """Test get_target_field_cultivation_cycles when events are locked"""
    from ui.projects.field_events.schema import (
        CultivationCycleResponseId,
        CultivationCycleResult,
    )

    # Create test data
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    # Mock locked cultivation cycles
    mock_cycle = CultivationCycleResult(
        start=datetime(2023, 1, 1),
        end=datetime(2023, 12, 31),
        crop_event_is_locked=True,
        stage_cultivation_cycle_is_locked=True,
        events=[],
        no_practice_observations=NoPracticeObservations(),
        id=CultivationCycleResponseId(
            phase_id=target_phase.id,
            crop_event_id=None,
            crop_type="corn",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            harvest_year=2023,
        ),
    )

    with (
        patch("ui.projects.field_events.methods.get_owner_user_id_for_project") as mock_get_owner,
        patch("ui.projects.field_events.methods.extract_acting_user_id") as mock_extract_user,
        patch("ui.projects.field_events.methods.get_locked_event_ids_for_user") as mock_get_locked,
        patch("ui.projects.field_events.methods.build_field_cultivation_cycles_for_calculations") as mock_build_cycles,
    ):
        mock_get_owner.return_value = 123
        mock_extract_user.return_value = "456"
        mock_get_locked.return_value = {uuid.uuid4()}  # Has locked events
        mock_build_cycles.return_value = {target_field.id: [mock_cycle]}

        result = await get_target_field_cultivation_cycles(
            request=app_request,
            target_program=target_program,
            target_phase=target_phase,
            target_stage=target_stage,
            target_field=target_field,
        )

        assert len(result) == 1
        assert result[0].start == datetime(2023, 1, 1)
        assert result[0].end == datetime(2023, 12, 31)


def test_is_event_overlap_with_intervals_no_overlap():
    """Test _is_event_overlap_with_intervals when event doesn't overlap"""

    # Create mock event with date outside interval
    mock_event = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2022, 6, 15),  # Before interval
        }
    )

    intervals = [DateRange(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31))]

    result = _is_event_overlap_with_intervals(mock_event, intervals)
    assert result is False


def test_is_event_overlap_with_intervals_with_overlap():
    """Test _is_event_overlap_with_intervals when event overlaps"""

    # Create mock event with date inside interval
    mock_event = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2023, 6, 15),  # Inside interval
        }
    )

    intervals = [DateRange(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31))]

    result = _is_event_overlap_with_intervals(mock_event, intervals)
    assert result is True


def test_is_event_overlap_with_intervals_no_date():
    """Test _is_event_overlap_with_intervals when event has no usable date"""
    from unittest.mock import Mock

    # Create mock event that will return None for get_interval_end_or_occurred_at
    mock_event = Mock()
    mock_event.get_interval_end_or_occurred_at.return_value = None
    mock_event.get_interval_start_or_occurred_at.return_value = None

    intervals = [DateRange(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31))]

    result = _is_event_overlap_with_intervals(mock_event, intervals)
    assert result is False


async def test_fetch_event_with_context_or_none_success():
    """Test _fetch_event_with_context_or_none when event exists"""

    mock_event = await mock_fetch_event_success("test-id")

    with patch("ui.projects.field_events.methods.ses_client") as mock_ses_client:
        mock_ses_client.fetch_event_with_context = AsyncMock(return_value=mock_event)

        result = await _fetch_event_with_context_or_none("test-id")

        assert result == mock_event
        mock_ses_client.fetch_event_with_context.assert_called_once_with(event_id="test-id")


async def test_fetch_event_with_context_or_none_failure():
    """Test _fetch_event_with_context_or_none when event doesn't exist"""

    with patch("ui.projects.field_events.methods.ses_client") as mock_ses_client:
        mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=Exception("Event not found"))

        result = await _fetch_event_with_context_or_none("test-id")

        assert result is None
        mock_ses_client.fetch_event_with_context.assert_called_once_with(event_id="test-id")


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_copy_field_events_partial_locked_events(
    mock_parse_ses_events,
    mock_get_locked_intervals,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    mdl,
):
    """Test copy_field_events when some events are locked and some are not - locked events are silently skipped"""
    event_ids = [str(uuid.uuid4()), str(uuid.uuid4())]

    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_get_accessible_projects.return_value = None
    # Create mock cultivation cycle with locked events that will overlap with only the first event
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=True,  # Locked
        stage_cultivation_cycle_is_locked=True,  # Locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 6, 30, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    # Mock parse_ses_events to return events with different dates
    mock_locked_event = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2023, 3, 15),  # Inside locked interval
        }
    )
    mock_unlocked_event = TillageEvent.parse_obj(
        {
            "id": 2,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2023, 8, 15),  # Outside locked interval
        }
    )
    mock_parse_ses_events.side_effect = [[mock_locked_event], [mock_unlocked_event]]

    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success)

    with (
        patch("ui.projects.field_events.methods.get_owner_user_id_for_project") as mock_get_owner,
        patch("ui.projects.field_events.methods.update_event_associations") as mock_update,
        patch("domain_event_bus.domain_event_bus.event_bus.publish") as mock_publish,
    ):
        mock_get_owner.return_value = 123
        mock_update.return_value = None
        mock_publish.return_value = None

        result = await copy_field_events(
            request=app_request,
            target_program=target_program,
            target_phase=target_phase,
            target_stage=target_stage,
            target_field=target_field,
            event_ids=event_ids,
            copy_mode=CopyFieldEventsMode.COPY,
        )

    # Only the unlocked event should be processed, locked event is silently skipped
    assert len(result) == 1
    assert result[0].succeeded


@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_copy_field_events_cropping_ids_locked(
    mock_parse_ses_events,
    mock_get_locked_intervals,
    mock_get_accessible_projects,
    mock_ses_client,
    app_request,
    mdl,
):
    """Test copy_field_events when CroppingIDs events are locked - should be silently skipped"""
    # Create database objects using mdl
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.CROP_EVENTS)

    cropping_ids = CroppingIDs(
        planting_id=None,
        sowing_id=uuid.UUID("c4825c7e-3bc2-5847-aef0-e38b41f146a2"),
        harvesting_id=uuid.UUID("f0e0a182-3008-5965-bbc0-02fbe73fd170"),
        termination_id=uuid.UUID("0f051cb6-48a6-51ad-9132-376a8c975834"),
        fallow_id=None,
    )

    event_ids = [cropping_ids]

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_get_accessible_projects.return_value = None
    # Create mock cultivation cycle with locked events that will overlap with the cropping event
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=True,  # Locked
        stage_cultivation_cycle_is_locked=True,  # locked
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
        participates_in_phase_completion=None,
    )
    mock_get_locked_intervals.return_value = [mock_cultivation_cycle]

    # Mock parse_ses_events to return cropping event with date inside locked interval
    mock_cropping_event = CroppingEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "crop_type": "corn",
            "interval": Interval(
                start=datetime(2023, 6, 15),  # Inside locked interval
                end=datetime(2023, 10, 15),
            ),
        }
    )
    mock_parse_ses_events.return_value = [mock_cropping_event]

    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.COPY,
    )

    # Locked cropping events are now silently skipped, so no results should be returned
    assert len(result) == 0


def test_is_event_overlap_with_intervals_multiple_intervals():
    """Test _is_event_overlap_with_intervals with multiple intervals"""

    # Create mock event with date that overlaps with second interval
    mock_event = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2024, 6, 15),  # Inside second interval
        }
    )

    intervals = [
        DateRange(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31)),
        DateRange(start_date=date(2024, 1, 1), end_date=date(2024, 12, 31)),
        DateRange(start_date=date(2025, 1, 1), end_date=date(2025, 12, 31)),
    ]

    result = _is_event_overlap_with_intervals(mock_event, intervals)
    assert result is True


def test_is_event_overlap_with_intervals_edge_dates():
    """Test _is_event_overlap_with_intervals with edge dates (start and end of interval)"""

    intervals = [DateRange(start_date=date(2023, 1, 1), end_date=date(2023, 12, 31))]

    # Test event on start date
    mock_event_start = TillageEvent.parse_obj(
        {
            "id": 1,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2023, 1, 1),  # Exactly on start date
        }
    )

    result = _is_event_overlap_with_intervals(mock_event_start, intervals)
    assert result is True

    # Test event on end date
    mock_event_end = TillageEvent.parse_obj(
        {
            "id": 2,
            "entity_id": "1",
            "entity_type": "field",
            "occurred_at": datetime(2023, 12, 31),  # Exactly on end date
        }
    )

    result = _is_event_overlap_with_intervals(mock_event_end, intervals)
    assert result is True


def test_cropping_ids_json_key_generation():
    """Test that CroppingIDs objects can be used as dictionary keys via JSON serialization"""
    from entity_events.events.schema import CroppingIDs

    # Create test CroppingIDs objects
    cropping_ids_1 = CroppingIDs(
        planting_id=None,
        sowing_id=uuid.UUID("c4825c7e-3bc2-5847-aef0-e38b41f146a2"),
        harvesting_id=uuid.UUID("f0e0a182-3008-5965-bbc0-02fbe73fd170"),
        termination_id=uuid.UUID("0f051cb6-48a6-51ad-9132-376a8c975834"),
        fallow_id=None,
    )

    cropping_ids_2 = CroppingIDs(
        planting_id=uuid.UUID("a1111111-1111-1111-1111-111111111111"),
        sowing_id=None,
        harvesting_id=uuid.UUID("b222**************-2222-************"),
        termination_id=None,
        fallow_id=None,
    )

    # Test that JSON serialization works as dictionary keys
    test_dict = {}

    # Use JSON representation as keys
    key1 = cropping_ids_1.json()
    key2 = cropping_ids_2.json()

    test_dict[key1] = (cropping_ids_1, "data1")
    test_dict[key2] = (cropping_ids_2, "data2")

    # Verify we can retrieve the data
    assert len(test_dict) == 2
    assert test_dict[key1][0] == cropping_ids_1
    assert test_dict[key2][0] == cropping_ids_2
    assert test_dict[key1][1] == "data1"
    assert test_dict[key2][1] == "data2"

    # Verify that different CroppingIDs have different JSON representations
    assert key1 != key2

    # Verify that the same CroppingIDs produces the same JSON key
    duplicate_key1 = cropping_ids_1.json()
    assert key1 == duplicate_key1


def test_cropping_ids_expansion_logic():
    """Test the logic for expanding CroppingIDs into individual event IDs"""
    from entity_events.events.schema import CroppingIDs

    # Test CroppingIDs with mixed None and non-None values
    cropping_ids = CroppingIDs(
        planting_id=None,
        sowing_id=uuid.UUID("c4825c7e-3bc2-5847-aef0-e38b41f146a2"),
        harvesting_id=uuid.UUID("f0e0a182-3008-5965-bbc0-02fbe73fd170"),
        termination_id=uuid.UUID("0f051cb6-48a6-51ad-9132-376a8c975834"),
        fallow_id=None,
    )

    # Test the get_ids() method which is used in copy_field_events
    individual_ids = cropping_ids.get_ids()

    # Should return only the non-None IDs as strings
    expected_ids = [
        "c4825c7e-3bc2-5847-aef0-e38b41f146a2",
        "f0e0a182-3008-5965-bbc0-02fbe73fd170",
        "0f051cb6-48a6-51ad-9132-376a8c975834",
    ]

    assert len(individual_ids) == 3
    assert set(individual_ids) == set(expected_ids)

    # Test mapping logic (simulating what copy_field_events does)
    expanded_event_ids = []
    original_to_expanded_mapping = {}

    # Simulate the expansion logic from copy_field_events
    if isinstance(cropping_ids, CroppingIDs):
        individual_ids = cropping_ids.get_ids()
        expanded_event_ids.extend(individual_ids)
        for individual_id in individual_ids:
            original_to_expanded_mapping[individual_id] = cropping_ids

    # Verify the mapping works correctly
    assert len(expanded_event_ids) == 3
    assert len(original_to_expanded_mapping) == 3

    for event_id in individual_ids:
        assert event_id in original_to_expanded_mapping
        assert original_to_expanded_mapping[event_id] == cropping_ids


# most of the tests for build_field_cultivation_cycles
# will be made implicitly by the tests in test_get_phase_cultivation_cycles
# and that get_phase_cultivation_cycles now relies on this function;
# this test just assures we have the interface for other cultivation cycle users
@patch("ses_integration.methods.get_locked_event_ids_for_user")
@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences_cross_year(field_id),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events(field_id),
)
async def test_build_field_cultivation_cycles(mock_get_locked_event_ids_for_user, app_request, mdl):
    # crop event is locked
    mock_get_locked_event_ids_for_user.return_value = {}

    start_date = datetime(year=1990, month=2, day=1, tzinfo=timezone.utc)
    end_date = datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start_date,
        reporting_period_end_date=end_date,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=3,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(
        id=1, md5=field_id, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
    )
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, stages_=[stage])

    program, phase, stage = await get_project_program_phase_and_stage(app_request, project.id, phase.id, stage.id)

    acting_user_id = extract_acting_user_id(request=app_request)
    owner_user_id = await get_owner_user_id_for_project(app_request, project.id)
    result = await build_field_cultivation_cycles_for_response(
        request=app_request,
        program=program,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        phase=phase,
        stage=None,
        fields=[field],
        prefill_monitoring_phase=False,
    )

    result_for_field = result[field.id]
    assert len(result_for_field) > 0

    for cult_cycle in result_for_field:
        assert isinstance(cult_cycle, CultivationCycleResponse)
        if cult_cycle.start.year == 2020 and cult_cycle.end.year == 2021:
            assert len(cult_cycle.events) == 1
            assert cult_cycle.events[0].type == EntityEventType.CROPPING_EVENT
            assert cult_cycle.no_practice_observations == NoPracticeObservations(
                tillage_event=True,
                application_event=True,
                irrigation_event=True,
            )
            assert cult_cycle.crop_event_is_locked is False
        elif cult_cycle.start.year == 2021 and cult_cycle.end.year == 2022:
            assert len(cult_cycle.events) == 4
            assert cult_cycle.events[0].type == EntityEventType.CROPPING_EVENT
            assert cult_cycle.events[1].type == EntityEventType.APPLICATION_EVENT
            assert cult_cycle.events[2].type == EntityEventType.IRRIGATION_EVENT
            assert cult_cycle.events[3].type == EntityEventType.TILLAGE_EVENT
            assert cult_cycle.no_practice_observations == NoPracticeObservations()
            assert cult_cycle.crop_event_is_locked is False
        else:
            assert len(cult_cycle.events) == 0
            assert cult_cycle.no_practice_observations == NoPracticeObservations()
            assert cult_cycle.crop_event_is_locked is False


@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_build_field_cultivation_cycles_response_lookup_unbinned_events(
    mock_parse_ses_events_to_entity_events,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mdl,
    app_request,
):
    mock_parse_ses_events_to_entity_events.return_value = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(year=2022, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2022, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(year=2023, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2023, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        # 1 day before first cultivation cycle
        TillageEvent.parse_obj(
            create_tillage_event_data(id=1, occurred_at=datetime(year=2021, month=6, day=1, tzinfo=timezone.utc))
        ),
        # in cultivation cycle
        TillageEvent.parse_obj(
            create_tillage_event_data(id=2, occurred_at=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc))
        ),
        # after last cultivation cycle
        TillageEvent.parse_obj(
            create_tillage_event_data(id=3, occurred_at=datetime(year=2024, month=12, day=1, tzinfo=timezone.utc))
        ),
    ]

    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc),
        reporting_period_end_date=datetime(year=2025, month=12, day=31, tzinfo=timezone.utc),
        required_years_of_history=3,
        is_single_phase_data_collection=False,
    )
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, stages_=[stage])
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = await _build_field_cultivation_cycles_response_lookup(
        request=app_request,
        program=program,
        acting_user_id=str(user.id),
        owner_user_id=str(user.id),
        phase=phase,
        stage=stage,
        fields=[field],
        ses_crop_events=None,
        ses_events=None,
    )

    cycles = result[field.id]
    assert len(cycles) == 5

    # generated for unbinned event 1 day before first cultivation cycle
    assert cycles[4].start == datetime(year=2021, month=5, day=31, tzinfo=timezone.utc)
    assert cycles[4].end == datetime(year=2021, month=6, day=1, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert cycles[4].id.index == 1
    assert len(cycles[4].events) == 1
    assert cycles[4].events[0].id == 1

    assert cycles[3].start == datetime(year=2021, month=6, day=2, tzinfo=timezone.utc)
    assert cycles[3].end == datetime(year=2022, month=6, day=1, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert cycles[3].id.index is None
    assert len(cycles[3].events) == 0

    assert cycles[2].start == datetime(year=2022, month=6, day=2, tzinfo=timezone.utc)
    assert cycles[2].end == datetime(year=2023, month=6, day=1, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert cycles[2].id.index is None
    assert len(cycles[2].events) == 0

    assert cycles[1].start == datetime(year=2023, month=6, day=2, tzinfo=timezone.utc)
    assert cycles[1].end == datetime(year=2024, month=6, day=1, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert cycles[1].id.index == 1
    assert len(cycles[1].events) == 1
    assert cycles[1].events[0].id == 2

    # generated for unbinned event after last cultivation cycle
    assert cycles[0].start == datetime(year=2024, month=6, day=2, tzinfo=timezone.utc)
    assert cycles[0].end == datetime(year=2024, month=12, day=1, tzinfo=timezone.utc)
    assert cycles[0].id.index == 2
    assert len(cycles[0].events) == 1
    assert cycles[0].events[0].id == 3


@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_build_field_cultivation_cycles_response_lookup_empty_cultivation_cycle_with_cover_crop(
    mock_parse_ses_events_to_entity_events,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mdl,
    app_request,
):
    mock_parse_ses_events_to_entity_events.return_value = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                id=1,
                interval=create_interval_data(
                    start=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2024, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
    ]

    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
        reporting_period_end_date=datetime(year=2025, month=12, day=31, tzinfo=timezone.utc),
    )
    crop_stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, stages_=[crop_stage])
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = await _build_field_cultivation_cycles_response_lookup(
        request=app_request,
        program=program,
        acting_user_id=str(user.id),
        owner_user_id=str(user.id),
        phase=phase,
        stage=None,
        fields=[field],
        ses_crop_events=None,
        ses_events=None,
    )

    cycles = result[field.id]
    assert len(cycles) == 3

    assert cycles[2].id.index == 1
    assert cycles[2].start == datetime(year=2024, month=1, day=1, tzinfo=timezone.utc)
    assert cycles[2].end == datetime(year=2024, month=12, day=31, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert len(cycles[2].events) == 1
    assert cycles[2].events[0].id == 1

    # placeholder
    assert cycles[1].id.index == 2
    assert cycles[1].start == datetime(year=2024, month=1, day=2, tzinfo=timezone.utc)
    assert cycles[1].end == datetime(year=2024, month=12, day=31, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert len(cycles[1].events) == 0

    assert cycles[0].id.index == 1
    assert cycles[0].start == datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    assert cycles[0].end == datetime(year=2025, month=12, day=31, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert len(cycles[0].events) == 0


@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_build_field_cultivation_cycles_response_lookup_partial_cultivation_cycle_with_cover_crop(
    mock_parse_ses_events_to_entity_events,
    create_cropping_event_data,
    create_interval_data,
    mdl,
    app_request,
):
    mock_parse_ses_events_to_entity_events.return_value = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                id=1,
                interval=create_interval_data(
                    start=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2024, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COVER,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(year=2024, month=6, day=1, tzinfo=timezone.utc),
                    end=None,
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
    ]

    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
        reporting_period_end_date=datetime(year=2024, month=12, day=31, tzinfo=timezone.utc),
    )
    crop_stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, stages_=[crop_stage])
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = await _build_field_cultivation_cycles_response_lookup(
        request=app_request,
        program=program,
        acting_user_id=str(user.id),
        owner_user_id=str(user.id),
        phase=phase,
        stage=None,
        fields=[field],
        ses_crop_events=None,
        ses_events=None,
    )

    cycles = result[field.id]
    assert len(cycles) == 1

    assert cycles[0].id.index == 1
    assert cycles[0].start == datetime(year=2024, month=1, day=1, tzinfo=timezone.utc)
    assert cycles[0].end == datetime(year=2024, month=12, day=31, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert len(cycles[0].events) == 2


@pytest.mark.parametrize("accounting_method", [AccountingMethod.intervention, AccountingMethod.inventory])
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_build_field_cultivation_cycles_response_lookup_m_phase(
    mock_parse_ses_events_to_entity_events,
    accounting_method,
    create_cropping_event_data,
    create_tillage_event_data,
    create_interval_data,
    mdl,
    app_request,
):
    mock_parse_ses_events_to_entity_events.return_value = [
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2024, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        CroppingEvent.parse_obj(
            create_cropping_event_data(
                interval=create_interval_data(
                    start=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc),
                    end=datetime(year=2025, month=6, day=1, tzinfo=timezone.utc),
                ),
                crop_usage=CropUsage.COMMODITY,
                reductions=[],
            )
        ),
        TillageEvent.parse_obj(
            create_tillage_event_data(id=1, occurred_at=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc))
        ),
        TillageEvent.parse_obj(
            create_tillage_event_data(id=2, occurred_at=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc))
        ),
    ]

    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc),
        reporting_period_end_date=datetime(year=2025, month=12, day=31, tzinfo=timezone.utc),
        accounting_method=accounting_method,
    )
    stage = await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, stages_=[stage])
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = await _build_field_cultivation_cycles_response_lookup(
        request=app_request,
        program=program,
        acting_user_id=str(user.id),
        owner_user_id=str(user.id),
        phase=phase,
        stage=stage,
        fields=[field],
        ses_crop_events=None,
        ses_events=None,
    )

    cycles = result[field.id]
    assert len(cycles) == (2 if accounting_method == AccountingMethod.intervention else 1)

    assert cycles[0].start == datetime(year=2024, month=6, day=2, tzinfo=timezone.utc)
    assert cycles[0].end == datetime(year=2025, month=6, day=1, hour=23, minute=59, second=59, tzinfo=timezone.utc)
    assert len(cycles[0].events) == 1
    assert cycles[0].events[0].id == 2


@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
async def test_build_field_cultivation_cycles_response_lookup_default_no_irrigation(
    mock_parse_ses_events_to_entity_events,
    create_irrigation_event_data,
    create_interval_data,
    mdl,
    app_request,
):
    mock_parse_ses_events_to_entity_events.return_value = [
        IrrigationEvent.parse_obj(
            create_irrigation_event_data(
                interval=create_interval_data(start=datetime(2025, 1, 1), end=datetime(2025, 1, 2))
            )
        )
    ]

    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2024, month=1, day=1, tzinfo=timezone.utc),
        reporting_period_end_date=datetime(year=2025, month=12, day=31, tzinfo=timezone.utc),
        accounting_method=AccountingMethod.intervention,
    )
    stage = await mdl.Stage(type_=StageTypes.IRRIGATION_EVENTS, default_no_practice_observation=True)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, stages_=[stage])
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(parent_project_id=project.id)

    result = await _build_field_cultivation_cycles_response_lookup(
        request=app_request,
        program=program,
        acting_user_id=str(user.id),
        owner_user_id=str(user.id),
        phase=phase,
        stage=stage,
        fields=[field],
        ses_crop_events=None,
        ses_events=None,
    )

    cycles = result[field.id]
    assert len(cycles) == 2
    assert cycles[0].no_practice_observations == NoPracticeObservations()
    assert cycles[1].no_practice_observations == NoPracticeObservations(irrigation_event=True)


async def test_get_field_events_meta_data(mdl, app_request):
    # create user
    user_id = (await mdl.Users()).id
    # create program
    program_id = (await mdl.Programs()).id
    # create project
    project_id = (await mdl.Projects(program_id=program_id)).id

    # Created phase
    phase_id = (await mdl.Phases(program_id=program_id)).id

    # Create project permissions recorded for user
    await mdl.ProjectPermissions(user=user_id, project=project_id)

    # Create project phase completion record with completion False
    await mdl.ProjectPhaseCompletion(project_id=project_id, phase_id=phase_id, is_completed=False)

    lookup = await get_field_event_meta_data(app_request, project_id=project_id, phase_id=phase_id)

    assert len(lookup) == 0


def build_cultivation_cycle_result(
    phase_id: int,
    create_interval_data: Callable,
    create_cropping_event_data: Callable,
    create_reduction_event_data: Callable,
) -> CultivationCycleResult:
    sowing_date_2021 = datetime(2021, 4, 1)
    harvest_date_2021 = datetime(2021, 10, 1)
    return CultivationCycleResult(
        start=sowing_date_2021,
        end=harvest_date_2021,
        id=CultivationCycleResponseId(
            phase_id=phase_id,
            crop_event_id=None,  # I don't think we will need to have any dependency on this
            crop_type="barley",
            start_date=sowing_date_2021,
            end_date=harvest_date_2021,
            harvest_year=harvest_date_2021.year,
        ),
        events=[
            CroppingEvent.parse_obj(
                create_cropping_event_data(
                    entity_id=1,
                    interval=create_interval_data(start=sowing_date_2021, end=harvest_date_2021),
                    crop_type="barley",
                    reductions=[create_reduction_event_data(occurred_at=harvest_date_2021)],
                )
            )
        ],
        no_practice_observations=NoPracticeObservations(),
    )


def test_is_cultivation_cycle_in_filter_period(
    create_interval_data, create_cropping_event_data, create_reduction_event_data
):
    cultivation_cycle = build_cultivation_cycle_result(
        1, create_interval_data, create_cropping_event_data, create_reduction_event_data
    )
    # period is before cultivation cycle, false
    filter_period = DateRange(start_date=date(2021, 1, 1), end_date=date(2021, 3, 15))
    assert is_cultivation_cycle_in_filter_period(cultivation_cycle, filter_period) is False

    # period overlaps start of cycle, true
    filter_period = DateRange(start_date=date(2021, 3, 15), end_date=date(2021, 4, 15))
    assert is_cultivation_cycle_in_filter_period(cultivation_cycle, filter_period) is True

    # period compasses cycle, true
    filter_period = DateRange(start_date=date(2021, 3, 15), end_date=date(2021, 10, 15))
    assert is_cultivation_cycle_in_filter_period(cultivation_cycle, filter_period) is True

    # period overlaps end of cycle, true
    filter_period = DateRange(start_date=date(2021, 9, 15), end_date=date(2021, 10, 15))
    assert is_cultivation_cycle_in_filter_period(cultivation_cycle, filter_period) is True

    # period is after cycle, false
    filter_period = DateRange(start_date=date(2021, 10, 15), end_date=date(2021, 12, 15))
    assert is_cultivation_cycle_in_filter_period(cultivation_cycle, filter_period) is False


async def test_get_completion_filter_period(mdl):
    start = datetime(year=2023, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    two_phase_program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=3,
        is_single_phase_data_collection=False,
    )
    e_phase_two_phase_program = await mdl.Phases(
        program_id=two_phase_program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True
    )

    e_two_filter_period = get_completion_filter_period(two_phase_program, e_phase_two_phase_program, 2022)
    assert e_two_filter_period.start_date == date(year=2020, month=1, day=2)
    assert e_two_filter_period.end_date == date(year=2023, month=1, day=1)
    e_two_filter_period = get_completion_filter_period(two_phase_program, e_phase_two_phase_program, 2021)
    assert e_two_filter_period.start_date == date(year=2019, month=1, day=2)
    assert e_two_filter_period.end_date == date(year=2023, month=1, day=1)

    m_phase_two_phase_program = await mdl.Phases(
        program_id=two_phase_program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True
    )
    m_two_filter_period = get_completion_filter_period(two_phase_program, m_phase_two_phase_program, 2022)
    assert m_two_filter_period.start_date == date(year=2023, month=1, day=1)
    assert m_two_filter_period.end_date == date(year=2023, month=12, day=31)
    m_two_filter_period = get_completion_filter_period(two_phase_program, m_phase_two_phase_program, 2021)
    assert m_two_filter_period.start_date == date(year=2023, month=1, day=1)
    assert m_two_filter_period.end_date == date(year=2023, month=12, day=31)

    one_phase_program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=3,
        is_single_phase_data_collection=True,
    )
    e_phase_one_phase_program = await mdl.Phases(
        program_id=one_phase_program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True
    )
    e_one_filter_period = get_completion_filter_period(one_phase_program, e_phase_one_phase_program, 2022)
    assert e_one_filter_period.start_date == date(year=2020, month=1, day=2)
    assert e_one_filter_period.end_date == date(year=2023, month=12, day=31)
    e_one_filter_period = get_completion_filter_period(one_phase_program, e_phase_one_phase_program, 2021)
    assert e_one_filter_period.start_date == date(year=2019, month=1, day=2)
    assert e_one_filter_period.end_date == date(year=2023, month=12, day=31)


async def test_generate_initial_cultivation_cycle_results(mdl):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)

    starting_cycle_end_date = datetime(2025, 1, 1)
    ending_cycle_end_date = datetime(2025, 1, 1)
    results = _generate_initial_cultivation_cycle_results(
        starting_cycle_end_date=starting_cycle_end_date,
        ending_cycle_end_date=ending_cycle_end_date,
        default_no_practice_observations=NoPracticeObservations(),
        phase=phase,
        stage=None,
    )
    expected_results = [
        CultivationCycleResult(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2025,
                index=1,
                phase_id=phase.id,
                stage_id=None,
            ),
            start=datetime(2024, 1, 2),
            end=datetime(2025, 1, 1, 23, 59, 59),
            events=[],
            no_practice_observations=NoPracticeObservations(),
            crop_event_is_locked=False,
            participates_in_phase_completion=None,
        )
    ]
    assert results == expected_results

    starting_cycle_end_date = datetime(2023, 6, 1)
    ending_cycle_end_date = datetime(2025, 1, 1, 23, 59, 59)
    results = _generate_initial_cultivation_cycle_results(
        starting_cycle_end_date=starting_cycle_end_date,
        ending_cycle_end_date=ending_cycle_end_date,
        default_no_practice_observations=NoPracticeObservations(),
        phase=phase,
        stage=None,
    )
    expected_results = [
        CultivationCycleResult(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2025,
                index=1,
                phase_id=phase.id,
                stage_id=None,
            ),
            start=datetime(2024, 1, 2),
            end=datetime(2025, 1, 1, 23, 59, 59),
            events=[],
            no_practice_observations=NoPracticeObservations(),
            crop_event_is_locked=False,
            participates_in_phase_completion=None,
        ),
        CultivationCycleResult(
            id=CultivationCycleResponseId(
                crop_event_id=None,
                crop_type=None,
                start_date=None,
                end_date=None,
                harvest_year=2024,
                index=1,
                phase_id=phase.id,
                stage_id=None,
            ),
            start=datetime(2023, 1, 2),
            end=datetime(2024, 1, 1, 23, 59, 59),
            events=[],
            no_practice_observations=NoPracticeObservations(),
            crop_event_is_locked=False,
            participates_in_phase_completion=None,
        ),
    ]
    assert results == expected_results


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_event_with_context")
async def test_unset_no_practice_observations_if_conflict(
    mock_fetch_event_with_context,
    mock_add_field_event_context,
    create_tillage_event_data,
    mdl,
    app_request,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    crop_event_id = uuid.uuid4()
    event = Event(id=str(crop_event_id), harvest_activity=HarvestActivity())
    context = EventContext(
        association={
            StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                {
                    EntityEventType.TILLAGE_EVENT: True,
                    EntityEventType.APPLICATION_EVENT: True,
                }
            ),
            CONTEXT_KEY_REGROW_OWNING_USER: "1",
            CONTEXT_KEY_REGROW_ACTING_USER: "1",
        }
    )
    mock_fetch_event_with_context.return_value = FetchEventWithContextResponse(event=event, context=context)

    tillage_event = TillageEvent.parse_obj(
        create_tillage_event_data(occurred_at=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc))
    )

    res = await _unset_no_practice_observations_if_conflict(
        request=app_request,
        no_practice_observations=NoPracticeObservations(
            tillage_event=True, application_event=True, irrigation_event=False
        ),
        cultivation_cycle_events=[tillage_event],
        crop_event_id=crop_event_id,
        acting_user_id="1",
        owner_user_id="1",
        phase=phase,
        field=Field.from_orm(field),
    )
    assert res == NoPracticeObservations(tillage_event=False, application_event=True, irrigation_event=False)

    mock_add_field_event_context.assert_called_with(
        field_id=field.md5,
        event_id=str(crop_event_id),
        context=EventContext(
            association={
                StructuredEvent.CONTEXT_KEY_NO_PRACTICE_OBSERVATION: json.dumps(
                    {
                        EntityEventType.TILLAGE_EVENT: False,
                        EntityEventType.APPLICATION_EVENT: True,
                    }
                ),
                CONTEXT_KEY_REGROW_OWNING_USER: "1",
                CONTEXT_KEY_REGROW_ACTING_USER: "1",
            }
        ),
    )


@patch.object(SesClient, "add_field_event_context")
@patch.object(SesClient, "fetch_event_with_context")
async def test_unset_no_practice_observations_if_conflict_no_conflict(
    mock_fetch_event_with_context,
    mock_add_field_event_context,
    create_tillage_event_data,
    mdl,
    app_request,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    crop_event_id = uuid.uuid4()
    tillage_event = TillageEvent.parse_obj(
        create_tillage_event_data(occurred_at=datetime(year=2025, month=1, day=1, tzinfo=timezone.utc))
    )

    res = await _unset_no_practice_observations_if_conflict(
        request=app_request,
        no_practice_observations=NoPracticeObservations(
            tillage_event=False, application_event=True, irrigation_event=True
        ),
        cultivation_cycle_events=[tillage_event],
        crop_event_id=crop_event_id,
        acting_user_id="1",
        owner_user_id="1",
        phase=phase,
        field=Field.from_orm(field),
    )
    assert res == NoPracticeObservations(tillage_event=False, application_event=True, irrigation_event=True)

    mock_fetch_event_with_context.assert_not_called()
    mock_add_field_event_context.assert_not_called()


def test_get_no_practice_observations_for_empty_cultivation_cycle_result(create_irrigation_event_data):
    assert _get_no_practice_observations_for_empty_cultivation_cycle_result(
        default_no_practice_observations=NoPracticeObservations(irrigation_event=True), events=[]
    ) == NoPracticeObservations(irrigation_event=True)
    assert (
        _get_no_practice_observations_for_empty_cultivation_cycle_result(
            default_no_practice_observations=NoPracticeObservations(irrigation_event=True),
            events=[IrrigationEvent.parse_obj(create_irrigation_event_data())],
        )
        == NoPracticeObservations()
    )


validation_rules_scraper_process_events = ValidationRulesScraper()


@patch("ui.projects.field_events.methods.get_field_with_parent_project")
async def test_create_update_field_events_field_not_found(
    mock_get_field,
    app_request,
    mdl,
    create_interval_data,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    mock_get_field.return_value = None

    interval = create_interval_data(
        start=datetime(2020, 9, 1, tzinfo=timezone.utc),
        end=datetime(2020, 10, 1, tzinfo=timezone.utc),
    )

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )

    field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)
    field_event_request = FieldEventRequest(**field_event.dict())

    result = await create_update_field_events(
        request=app_request,
        events=[field_event_request],
        project_id=project.id,
        phase_id=phase.id,
        field_id=field.id,
        acting_user_id="123",
    )

    assert len(result) == 1
    assert result[0].succeeded is False
    assert result[0].error_detail == f"Field {field.id} does not exist for project {project.id}"
    assert result[0].status_code == status.HTTP_404_NOT_FOUND
    assert result[0].project_id == project.id
    assert result[0].phase_id == phase.id
    assert result[0].field_id == field.id

    mock_get_field.assert_called_once()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch("ui.projects.field_events.methods.get_field_with_parent_project")
@patch("ui.projects.field_events.methods.get_project_program_phase_and_stage")
@patch("ui.projects.field_events.methods.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.can_bypass_event_lock")
@patch("ui.projects.field_events.methods.get_locked_event_ids_for_user")
async def test_create_update_field_events_event_locked(
    mock_get_locked_event_ids,
    mock_can_bypass_lock,
    mock_get_owner_user_id,
    mock_get_program_phase_stage,
    mock_get_field,
    app_request,
    mdl,
    create_interval_data,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    mock_get_field.return_value = field
    mock_get_program_phase_stage.return_value = (program, phase, None)
    mock_get_owner_user_id.return_value = "123"
    mock_can_bypass_lock.return_value = False

    interval = create_interval_data(
        start=datetime(2020, 9, 1, tzinfo=timezone.utc),
        end=datetime(2020, 10, 1, tzinfo=timezone.utc),
    )

    event_id = uuid.uuid4()
    mock_get_locked_event_ids.return_value = {event_id}  # Event is locked
    fallow_event = CroppingEvent.parse_obj(
        {
            "id": {
                "fallow_id": event_id,
                "sowing_id": None,
                "harvesting_id": None,
                "planting_id": None,
                "termination_id": None,
            },
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )

    field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)
    field_event_request = FieldEventRequest(**field_event.dict())

    result = await create_update_field_events(
        request=app_request,
        events=[field_event_request],
        project_id=project.id,
        phase_id=phase.id,
        field_id=field.id,
        acting_user_id="123",
    )

    assert len(result) == 1
    assert result[0].succeeded is False
    assert result[0].error_detail == f"Event {fallow_event.id} is locked and cannot be edited at this time"
    assert result[0].status_code == status.HTTP_400_BAD_REQUEST
    assert result[0].project_id == project.id
    assert result[0].phase_id == phase.id
    assert result[0].field_id == field.id

    mock_get_field.assert_called_once()
    mock_get_locked_event_ids.assert_called_once()


@patch("ui.projects.field_events.methods.FROM_UI_DICT_EVENT_CREATOR_TYPE_LOOKUP", {})
@patch("ui.projects.field_events.methods.get_field_with_parent_project")
@patch("ui.projects.field_events.methods.get_project_program_phase_and_stage")
@patch("ui.projects.field_events.methods.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.can_bypass_event_lock")
@patch("ses_integration.methods.get_locked_event_ids_for_user")
async def test_create_update_field_events_unsupported_event_type(
    mock_get_locked_event_ids,
    mock_can_bypass_lock,
    mock_get_owner_user_id,
    mock_get_program_phase_stage,
    mock_get_field,
    app_request,
    mdl,
    create_interval_data,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    mock_get_field.return_value = field
    mock_get_program_phase_stage.return_value = (program, phase, None)
    mock_get_owner_user_id.return_value = "123"
    mock_can_bypass_lock.return_value = False
    mock_get_locked_event_ids.return_value = set()  # No events locked

    interval = create_interval_data(
        start=datetime(2020, 9, 1, tzinfo=timezone.utc),
        end=datetime(2020, 10, 1, tzinfo=timezone.utc),
    )

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )

    field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)
    field_event_request = FieldEventRequest(**field_event.dict())

    result = await create_update_field_events(
        request=app_request,
        events=[field_event_request],
        project_id=project.id,
        phase_id=phase.id,
        field_id=field.id,
        acting_user_id="123",
    )

    assert len(result) == 1
    assert result[0].succeeded is False
    assert result[0].error_detail == f"Unsupported event creator type: {EntityEventType.CROPPING_EVENT}"
    assert result[0].status_code == status.HTTP_400_BAD_REQUEST
    assert result[0].project_id == project.id
    assert result[0].phase_id == phase.id
    assert result[0].field_id == field.id

    mock_get_field.assert_called_once()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch(
    "entity_events.validation.rules_scraper.validation_rules_scraper",
    validation_rules_scraper_process_events,
)
@patch(
    "entity_events.validation.validator.validation_rules_scraper",
    validation_rules_scraper_process_events,
)
@patch("ui.projects.field_events.methods.get_field_with_parent_project")
@patch("ui.projects.field_events.methods.get_project_program_phase_and_stage")
@patch("ui.projects.field_events.methods.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.can_bypass_event_lock")
@patch("ses_integration.methods.get_locked_event_ids_for_user")
@patch("ui.projects.field_events.methods.AttributeValuesValidator.validate_event")
async def test_create_update_field_events_validation_error(
    mock_validate_event,
    mock_get_locked_event_ids,
    mock_can_bypass_lock,
    mock_get_owner_user_id,
    mock_get_program_phase_stage,
    mock_get_field,
    app_request,
    mdl,
    create_interval_data,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    mock_get_field.return_value = field
    mock_get_program_phase_stage.return_value = (program, phase, None)
    mock_get_owner_user_id.return_value = "123"
    mock_can_bypass_lock.return_value = False
    mock_get_locked_event_ids.return_value = set()  # No events locked

    mock_validate_event.return_value = {"errors": {"crop_type": ["Invalid crop type selected."]}}

    interval = create_interval_data(
        start=datetime(2020, 9, 1, tzinfo=timezone.utc),
        end=datetime(2020, 10, 1, tzinfo=timezone.utc),
    )

    fallow_event = CroppingEvent.parse_obj(
        {
            "id": None,
            "entity_id": field.id,
            "entity_type": EntityTypeChoices.field,
            "interval": interval,
            "crop_type": NoCropType.FALLOW,
        }
    )

    field_event = FieldEvent.from_event(event=fallow_event, event_type=EntityEventType.CROPPING_EVENT)
    field_event_request = FieldEventRequest(**field_event.dict())

    result = await create_update_field_events(
        request=app_request,
        events=[field_event_request],
        project_id=project.id,
        phase_id=phase.id,
        field_id=field.id,
        acting_user_id="123",
    )

    assert len(result) == 1
    assert result[0].succeeded is False
    assert result[0].error_detail == {"validation_errors": {"crop_type": ["Invalid crop type selected."]}}
    assert result[0].status_code == status.HTTP_400_BAD_REQUEST
    assert result[0].project_id == project.id
    assert result[0].phase_id == phase.id
    assert result[0].field_id == field.id

    mock_get_field.assert_called_once()
    mock_validate_event.assert_called_once()


@patch("ui.projects.field_events.methods.update_no_practice_observations_for_event")
async def test_create_default_no_practice_observations(mock_update_no_practice_observations_for_event, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    planting_activity = PlantingActivityStructuredEvent(event_id=str(uuid.uuid4()), user_id="1").start(
        datetime(year=2025, month=1, day=1)
    )
    harvest_activity = HarvestActivityStructuredEvent(event_id=str(uuid.uuid4()), user_id="1").start(
        datetime(year=2025, month=1, day=1)
    )
    fallow_period = FallowPeriodStructuredEvent(event_id=str(uuid.uuid4()), user_id="1").start(
        datetime(year=2025, month=1, day=1)
    )

    # harvest activity
    await _create_default_no_practice_observations(
        default_no_practice_observations=NoPracticeObservations(irrigation_event=True),
        field=Field.from_orm(field),
        events=[harvest_activity],
        acting_user_id="1",
        owner_user_id="1",
    )
    mock_update_no_practice_observations_for_event.assert_called_with(
        event_id=harvest_activity.pb_event.id,
        no_practice_observations={EntityEventType.IRRIGATION_EVENT: True},
        field=Field.from_orm(field),
        acting_user_id="1",
        owner_user_id="1",
        ses_client=ANY,
    )
    mock_update_no_practice_observations_for_event.reset_mock()

    # fallow period
    await _create_default_no_practice_observations(
        default_no_practice_observations=NoPracticeObservations(irrigation_event=True),
        field=Field.from_orm(field),
        events=[fallow_period],
        acting_user_id="1",
        owner_user_id="1",
    )
    mock_update_no_practice_observations_for_event.assert_called_with(
        event_id=fallow_period.pb_event.id,
        no_practice_observations={EntityEventType.IRRIGATION_EVENT: True},
        field=Field.from_orm(field),
        acting_user_id="1",
        owner_user_id="1",
        ses_client=ANY,
    )
    mock_update_no_practice_observations_for_event.reset_mock()

    # planting activity
    await _create_default_no_practice_observations(
        default_no_practice_observations=NoPracticeObservations(irrigation_event=True),
        field=field,
        events=[planting_activity],
        acting_user_id=None,
        owner_user_id="1",
    )
    mock_update_no_practice_observations_for_event.assert_not_called()
    mock_update_no_practice_observations_for_event.reset_mock()

    # no acting user id
    await _create_default_no_practice_observations(
        default_no_practice_observations=NoPracticeObservations(irrigation_event=True),
        field=field,
        events=[harvest_activity],
        acting_user_id=None,
        owner_user_id="1",
    )
    mock_update_no_practice_observations_for_event.assert_not_called()


async def test_find_overlapping_events():
    """Test finding overlapping events between source and target events."""
    # Create overlapping tillage events (same date)
    source_tillage = TillageEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, 10, 0, tzinfo=timezone.utc),
    )
    target_tillage = TillageEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, 14, 0, tzinfo=timezone.utc),  # Same date, different time
    )

    # Create overlapping cropping and fallow events
    source_cropping = CroppingEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )
    target_fallow = FallowPeriod(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    # Create non-overlapping event
    target_tillage_no_overlap = TillageEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 6, 20, tzinfo=timezone.utc),  # Different date
    )

    # Test data - flatten all target events into a single list
    source_events = [source_tillage, source_cropping]
    target_field_events = [target_tillage, target_tillage_no_overlap, target_fallow]

    # Test function
    overlapping_events = await find_overlapping_events(source_events, target_field_events)

    # Assertions
    assert len(overlapping_events) == 2
    assert target_tillage in overlapping_events
    assert target_fallow in overlapping_events
    assert target_tillage_no_overlap not in overlapping_events


async def test_delete_events_in_target_field_stage(app_request, mdl):
    """Test deletion of events in target field stage."""
    # Create test data
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    stage = await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS)

    # Create mock events
    tillage_event = TillageEvent(
        id=uuid.uuid4(),
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, tzinfo=timezone.utc),
    )

    cropping_event = CroppingEvent(
        id=CroppingIDs(planting_id=uuid.uuid4(), harvesting_id=uuid.uuid4()),
        entity_id=field.id,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )

    overlapping_events = [tillage_event, cropping_event]

    # Mock SES client and delete_event_associations
    with (
        patch("ui.projects.field_events.methods.ses_client") as mock_ses,
        patch("ui.projects.field_events.methods.delete_event_associations") as mock_delete_assoc,
        patch("ui.projects.field_events.methods.get_locked_event_ids_for_user") as mock_get_locked,
    ):

        mock_ses.delete_or_archive_field_event_for_user = AsyncMock()
        mock_delete_assoc.return_value = None
        mock_get_locked.return_value = []  # No locked events

        # Test function
        await _delete_events_in_target_field_stage(
            request=app_request,
            field=field,
            phase=phase,
            stage=stage,
            owner_user_id=123,
            events=overlapping_events,
        )

        # Assertions
        # Should delete 3 SES events: 1 tillage + 2 cropping (planting + harvesting)
        assert mock_ses.delete_or_archive_field_event_for_user.call_count == 3

        # Should call delete_event_associations with 3 UUID event IDs
        mock_delete_assoc.assert_called_once()
        call_args = mock_delete_assoc.call_args
        assert len(call_args[1]["event_ids"]) == 3


@patch("ui.projects.field_events.methods.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.update_event_associations")
@patch("domain_event_bus.domain_event_bus.event_bus.publish")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
@patch("ui.projects.field_events.methods.find_overlapping_events")
@patch("ui.projects.field_events.methods._delete_events_in_target_field_stage")
@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
async def test_copy_field_events_overwrite_overlapping_mode(
    mock_get_accessible_projects,
    mock_ses_client,
    mock_delete_events,
    mock_find_overlapping,
    mock_parse_ses_events,
    mock_get_cultivation_cycles,
    mock_publish,
    mock_update_associations,
    mock_get_owner_user_id,
    app_request,
    mdl,
):
    """Test copy_field_events with OVERWRITE_OVERLAPPING mode."""
    event_ids = [str(uuid.uuid4())]

    # Create test data
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.TILLAGE_EVENTS)

    # Create mock events
    source_tillage = TillageEvent(
        id=uuid.uuid4(),
        entity_id=target_field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, tzinfo=timezone.utc),
    )

    overlapping_tillage = TillageEvent(
        id=uuid.uuid4(),
        entity_id=target_field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, tzinfo=timezone.utc),
    )

    # Setup mocks
    mock_get_accessible_projects.return_value = None  # Unrestricted access
    mock_get_owner_user_id.return_value = "123"  # Should be string like existing tests

    # Create mock cultivation cycle containing the overlapping event
    mock_cultivation_cycle = CultivationCycleResult(
        events=[overlapping_tillage],
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,
        stage_cultivation_cycle_is_locked=False,
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
    )

    # Return tuple of (locked_intervals, cultivation_cycles)
    mock_get_cultivation_cycles.return_value = [mock_cultivation_cycle]
    mock_parse_ses_events.return_value = [source_tillage]
    mock_find_overlapping.return_value = [overlapping_tillage]
    mock_delete_events.return_value = None

    # Mock SES client responses
    async def mock_fetch_event_success(event_id):
        return FetchEventWithContextResponse(event=Event(id=event_id), context=EventContext())

    async def mock_bulk_copy_success(copy_requests):
        from ses_client.client import CopyEventToFieldResult

        results = []
        for request in copy_requests:
            for event_id in request.event_ids:
                results.append(
                    CopyEventToFieldResult(
                        source_event_id=event_id,
                        target_field_id=request.field_id,
                        new_event_id=str(uuid.uuid4()),
                    )
                )
        return results

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success)

    # Test function
    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.OVERWRITE_OVERLAPPING,
        include_new_field_event=False,
    )

    # Assertions
    assert len(result) == 1
    assert result[0].succeeded is True

    # Verify that cultivation cycles were fetched and overlap detection was called
    mock_get_cultivation_cycles.assert_called_once()
    mock_find_overlapping.assert_called_once_with(
        source_events=[source_tillage], target_field_events=[overlapping_tillage]
    )
    mock_delete_events.assert_called_once_with(
        request=app_request,
        field=target_field,
        phase=target_phase,
        stage=target_stage,
        owner_user_id="123",
        events=[overlapping_tillage],
    )

    # Verify that bulk copy was performed
    mock_ses_client.bulk_copy_events.assert_called_once()
    mock_update_associations.assert_called_once()
    mock_publish.assert_called_once()


@patch("ui.projects.field_events.methods.get_owner_user_id_for_project")
@patch("ui.projects.field_events.methods.update_event_associations")
@patch("domain_event_bus.domain_event_bus.event_bus.publish")
@patch("ui.projects.field_events.methods.get_target_field_cultivation_cycles")
@patch("ui.projects.field_events.methods.parse_ses_events_to_entity_events")
@patch("ui.projects.field_events.methods.find_overlapping_events")
@patch("ui.projects.field_events.methods._delete_events_in_target_field_stage")
@patch("ui.projects.field_events.methods.ses_client")
@patch("ui.projects.field_events.methods.get_accessible_projects")
async def test_copy_field_events_overwrite_overlapping_no_overlaps(
    mock_get_accessible_projects,
    mock_ses_client,
    mock_delete_events,
    mock_find_overlapping,
    mock_parse_ses_events,
    mock_get_cultivation_cycles,
    mock_publish,
    mock_update_associations,
    mock_get_owner_user_id,
    app_request,
    mdl,
):
    """Test copy_field_events with OVERWRITE_OVERLAPPING mode when no overlaps are found."""
    event_ids = [str(uuid.uuid4())]

    # Create test data
    target_program = await mdl.Programs()
    target_project = await mdl.Projects(program_id=target_program.id)
    target_field = await mdl.Fields(parent_project_id=target_project.id)
    target_phase = await mdl.Phases(program_id=target_program.id, type_=PhaseTypes.ENROLMENT)
    target_stage = await mdl.Stage(phase_id=target_phase.id, type_=StageTypes.TILLAGE_EVENTS)

    # Create mock events
    source_tillage = TillageEvent(
        id=uuid.uuid4(),
        entity_id=target_field.id,
        entity_type=EntityTypeChoices.field,
        occurred_at=datetime(2023, 5, 15, tzinfo=timezone.utc),
    )

    # Setup mocks - no overlapping events found
    mock_get_accessible_projects.return_value = None
    mock_get_owner_user_id.return_value = "123"

    # Create empty cultivation cycle (no events)
    mock_cultivation_cycle = CultivationCycleResult(
        events=[],  # No events in cultivation cycle
        no_practice_observations=NoPracticeObservations(),
        crop_event_is_locked=False,
        stage_cultivation_cycle_is_locked=False,
        start=datetime(2023, 1, 1, tzinfo=timezone.utc),
        end=datetime(2023, 12, 31, tzinfo=timezone.utc),
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=2023,
            index=1,
            phase_id=target_phase.id,
            stage_id=target_stage.id,
        ),
    )

    # Return tuple of (locked_intervals, cultivation_cycles)
    mock_get_cultivation_cycles.return_value = [mock_cultivation_cycle]
    mock_parse_ses_events.return_value = [source_tillage]
    mock_find_overlapping.return_value = []  # No overlapping events

    # Mock SES client responses
    async def mock_fetch_event_success(event_id):
        return FetchEventWithContextResponse(event=Event(id=event_id), context=EventContext())

    async def mock_bulk_copy_success(copy_requests):
        from ses_client.client import CopyEventToFieldResult

        results = []
        for request in copy_requests:
            for event_id in request.event_ids:
                results.append(
                    CopyEventToFieldResult(
                        source_event_id=event_id,
                        target_field_id=request.field_id,
                        new_event_id=str(uuid.uuid4()),
                    )
                )
        return results

    mock_ses_client.fetch_event_with_context = AsyncMock(side_effect=mock_fetch_event_success)
    mock_ses_client.bulk_copy_events = AsyncMock(side_effect=mock_bulk_copy_success)

    # Test function
    result = await copy_field_events(
        request=app_request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
        event_ids=event_ids,
        copy_mode=CopyFieldEventsMode.OVERWRITE_OVERLAPPING,
        include_new_field_event=False,
    )

    # Assertions
    assert len(result) == 1
    assert result[0].succeeded is True

    # Verify that cultivation cycles were fetched and overlap detection was called but no deletion occurred
    mock_get_cultivation_cycles.assert_called_once()
    mock_find_overlapping.assert_called_once()
    mock_delete_events.assert_not_called()  # Should not be called when no overlaps found

    # Verify that bulk copy was still performed
    mock_ses_client.bulk_copy_events.assert_called_once()


async def test_copy_field_events_overwrite_overlapping_cropping_fallow_cross_overlap():
    """Test that cropping events can overlap with fallow periods in OVERWRITE_OVERLAPPING mode."""
    # Create overlapping cropping and fallow events
    source_cropping = CroppingEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )

    target_fallow = FallowPeriod(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    # Test source cropping overlapping with target fallow
    source_events = [source_cropping]
    target_field_events = [target_fallow]

    overlapping_events = await find_overlapping_events(source_events, target_field_events)
    assert len(overlapping_events) == 1
    assert target_fallow in overlapping_events

    # Test source fallow overlapping with target cropping
    source_fallow = FallowPeriod(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 6, 1, tzinfo=timezone.utc), end=datetime(2023, 10, 1, tzinfo=timezone.utc)
        ),
    )

    target_cropping = CroppingEvent(
        id=uuid.uuid4(),
        entity_id=1,
        entity_type=EntityTypeChoices.field,
        interval=Interval(
            start=datetime(2023, 4, 1, tzinfo=timezone.utc), end=datetime(2023, 8, 15, tzinfo=timezone.utc)
        ),
        crop_type="corn",
    )

    source_events = [source_fallow]
    target_field_events = [target_cropping]

    overlapping_events = await find_overlapping_events(source_events, target_field_events)
    assert len(overlapping_events) == 1
    assert target_cropping in overlapping_events
