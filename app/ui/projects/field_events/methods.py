from __future__ import annotations

import asyncio
from collections import defaultdict
from datetime import date, datetime, timedelta, timezone
from itertools import chain
from typing import List
from uuid import UUID

from dateutil.relativedelta import relativedelta
from fastapi import Request, status
from regrow.ses.event.v1.event_service_pb2 import (
    FetchEventWithContextResponse,
    UpsertEventResponse,
)
from ses_client.client import Client, CopyEventsToFieldRequest, CopyEventToFieldResult
from ses_client.event import event_type, EventWithContext, StructuredEvent

from boundaries_service import client as boundaries_client
from config import get_settings
from cultivation_cycles.methods import (
    get_cultivation_cycles,
    get_unique_index_for_next_empty_cultivation_cycle,
    get_unique_index_for_previous_empty_cultivation_cycle,
)
from cultivation_cycles.schema import CultivationCycle, CultivationCycleGenerationConfig
from defaults.attribute_options import CropUsage
from domain_event_bus.domain_events import FieldEventsChanged
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.enums import EntityEventType
from entity_events.events.fallow_period import FallowPeriod
from entity_events.events.irrigation_event import IrrigationEvent
from entity_events.events.schema import CroppingIDs
from entity_events.events.tillage_event import TillageEvent
from entity_events.validation.annotations import (
    ValidationErrorResponse,
    ValidationResultsResponse,
)
from entity_events.validation.validator import AttributeValuesValidator
from fields.baselines.methods import get_or_create_fields_baselines
from fields.db import get_field_facts_for_fields
from fields.enums import EntityDataType, FieldDataState
from fields.schema import Field
from logger import get_logger
from permissions.methods import get_accessible_projects, get_original_user_id
from phases.dataclasses import DateRange
from phases.enums import PhaseTypes, StageTypes
from phases.methods import get_enabled_stages_by_phase_id
from phases.model import Phases, Stage
from programs.db import get_program_series
from programs.enums import AccountingMethod
from programs.model import Programs
from projects.db import (
    get_owner_user_id_for_project,
)
from ses_integration.constants import (
    ENTITY_EVENT_TYPE_TO_STAGE_TYPE,
    SES_EVENT_TYPE_TO_STAGE,
)
from ses_integration.db import get_programs_required_stage_types
from ses_integration.event_associations import (
    delete_event_associations,
    update_event_associations,
)
from ses_integration.methods import (
    get_locked_event_ids_for_user,
    get_ses_field_events,
    parse_ses_events_to_entity_events,
    upsert_events_to_ses,
)
from ses_integration.no_practice_observations import (
    get_no_practice_observations_from_ses_structured_event_context,
    update_no_practice_observations_for_event,
)
from ses_integration.schema import EventIdWithStageType
from ui.projects.completion.db import get_per_field_stage_completions_for_project
from ui.projects.field_events.constants import (
    DEFAULT_FIELD_EVENT_QUERY_FROM_DATE,
    DEFAULT_REQUIRED_YEARS_OF_HISTORY,
    FROM_UI_DICT_EVENT_CREATOR_TYPE_LOOKUP,
)
from ui.projects.field_events.enums import CopyFieldEventsMode
from ui.projects.field_events.event_overlap import do_events_overlap
from ui.projects.field_events.schema import (
    AdminBulkUpdateFieldResponse,
    BulkCopyEventResponse,
    CultivationCycleResponse,
    CultivationCycleResponseId,
    CultivationCycleResult,
    FieldEvent,
    FieldEventRequest,
    FrozenCroppingIDs,
    NoPracticeObservations,
)
from ui.projects.methods import (
    can_bypass_event_lock,
    extract_acting_user_id,
    get_field_with_parent_project,
    get_project_program_phase_and_stage,
)
from values.enums import EntityTypeChoices

logger = get_logger(__name__)

settings = get_settings()

ses_client = Client(
    settings.SES_INTERNAL_URL_BASE,
    settings.SES_SEARCH_INTERNAL_URL_BASE,
    settings.BOUNDARIES_SERVICE_INTERNAL_URL_BASE.rstrip("/"),
)


def _get_e_phase_query_range_for_reporting_period(program: Programs) -> tuple[datetime, datetime]:
    # for E phase
    # if single phase data collection is ON
    # query for any events between 2015 and reporting period end (inclusive)
    if program.is_single_phase_data_collection:
        to_date = program.reporting_period_end_date
    # if single phase data collection is OFF
    # query for any events between 2015 and reporting period start (exclusive)
    else:
        to_date = program.reporting_period_start_date - timedelta(days=1)

    return DEFAULT_FIELD_EVENT_QUERY_FROM_DATE, to_date


def get_event_query_range_for_reporting_period(program: Programs, phase_type: PhaseTypes) -> tuple[datetime, datetime]:
    # for M phase
    # query for events between reporting period start - 2 years and reporting period end date
    if phase_type == PhaseTypes.MONITORING:
        num_years = 2 if program.accounting_method == AccountingMethod.intervention else 1
        return program.reporting_period_start_date - relativedelta(years=num_years), program.reporting_period_end_date

    return _get_e_phase_query_range_for_reporting_period(program)


def get_completion_filter_period(program: Programs, phase: Phases, baseline_year: int) -> DateRange:
    """
    Find the periods over which, if a cultivation cycle is present, it is
    flagged as participating in completion.

    At this point we know the cultivation cycles, so we can say exactly what
    the overlapping cycles are; this is different than then amount of data needing to
    be fetched ot determine the cycles
    """
    if phase.type_ == PhaseTypes.MONITORING:
        return program.get_reporting_period()

    historical_period = program.get_historical_period()
    historical_start_date = historical_period.start_date

    baseline_date = date(
        year=baseline_year + 1 - program.required_years_of_history,
        month=historical_start_date.month,
        day=historical_start_date.day,
    )

    if baseline_date < historical_start_date:
        start_date = baseline_date
    else:
        start_date = historical_start_date

    if phase.type_ == PhaseTypes.ENROLMENT:
        if program.is_single_phase_data_collection:
            reporting_period = program.get_reporting_period()
            return DateRange(start_date=start_date, end_date=reporting_period.end_date)
        else:
            return DateRange(start_date=start_date, end_date=historical_period.end_date)
    else:
        # assume everything in the program is relevant
        return DateRange(start_date=start_date, end_date=program.get_reporting_period().end_date)


def _get_reporting_period_length_years(program: Programs) -> int:
    """
    Returns the length of the program reporting period in years, rounded up to the
    next year. For example, if the length is 1 day, then return 1 year. If the length
    is 1 year and 1 day, then return 2 years.
    """
    reporting_period_length = relativedelta(program.reporting_period_end_date, program.reporting_period_start_date)
    if reporting_period_length.months or reporting_period_length.days:
        return reporting_period_length.years + 1
    return reporting_period_length.years


def get_program_data_collection_required_date_range(
    phase_type: PhaseTypes, program: Programs
) -> tuple[datetime, datetime]:
    reporting_period_start_date = program.reporting_period_start_date.replace(tzinfo=timezone.utc)
    reporting_period_end_date = program.reporting_period_end_date.replace(tzinfo=timezone.utc)
    if phase_type == PhaseTypes.MONITORING:
        # we use the reporting period length, rather than the reporting period start and
        # end years, to calculate the required year range, because a reporting period can
        # be less than 1 year, but span across 2 different years - in which case we would
        # only require data for 1 year.
        reporting_period_length_years = _get_reporting_period_length_years(program=program)
        starting_cycle_end_date = reporting_period_end_date - relativedelta(years=reporting_period_length_years - 1)
        ending_cycle_end_date = reporting_period_end_date
    else:
        required_years_of_history = program.required_years_of_history
        if not required_years_of_history:
            logger.warning(f"Program {program.id} is missing required years of history.")
            required_years_of_history = DEFAULT_REQUIRED_YEARS_OF_HISTORY

        before_reporting_period_date = reporting_period_start_date - timedelta(days=1)
        starting_cycle_end_date = before_reporting_period_date - relativedelta(years=required_years_of_history - 1)
        ending_cycle_end_date = (
            reporting_period_end_date if program.is_single_phase_data_collection else before_reporting_period_date
        )
    return starting_cycle_end_date, ending_cycle_end_date


def is_cultivation_cycle_in_filter_period(cc: CultivationCycleResult, filter_period: DateRange) -> bool:
    """
    If a range ends before another begins, there is no overlap; otherwise there is
    """
    return not ((cc.end.date() < filter_period.start_date) or (filter_period.end_date < cc.start.date()))


async def _get_default_no_practice_observations(
    request: Request, phase: Phases, stage: Stage | None
) -> NoPracticeObservations:
    no_practice_obs = NoPracticeObservations()
    if stage:
        stages = [stage]
    else:
        stages = await get_enabled_stages_by_phase_id(request=request, phase_id=phase.id)
    for stage in stages:
        if stage.type_ == StageTypes.TILLAGE_EVENTS and stage.default_no_practice_observation:
            no_practice_obs.tillage_event = True
        if stage.type_ == StageTypes.NUTRIENT_EVENTS and stage.default_no_practice_observation:
            no_practice_obs.application_event = True
        if stage.type_ == StageTypes.IRRIGATION_EVENTS and stage.default_no_practice_observation:
            no_practice_obs.irrigation_event = True
    return no_practice_obs


def _get_no_practice_observations_for_empty_cultivation_cycle_result(
    default_no_practice_observations: NoPracticeObservations, events: list[EntityEvent]
) -> NoPracticeObservations:
    no_practice_obs = default_no_practice_observations.copy()
    if default_no_practice_observations.tillage_event and any([isinstance(event, TillageEvent) for event in events]):
        no_practice_obs.tillage_event = False
    if default_no_practice_observations.application_event and any(
        [isinstance(event, ApplicationEvent) for event in events]
    ):
        no_practice_obs.application_event = False
    if default_no_practice_observations.irrigation_event and any(
        [isinstance(event, IrrigationEvent) for event in events]
    ):
        no_practice_obs.irrigation_event = False
    return no_practice_obs


def _generate_empty_cultivation_cycle_result(
    events: list[EntityEvent],
    start_date: datetime,
    end_date: datetime,
    default_no_practice_observations: NoPracticeObservations,
    index: int,
    phase_id: int,
    stage_id: int | None = None,
) -> CultivationCycleResult:
    no_practice_observations = _get_no_practice_observations_for_empty_cultivation_cycle_result(
        default_no_practice_observations=default_no_practice_observations, events=events
    )
    return CultivationCycleResult(
        events=events,
        no_practice_observations=no_practice_observations,
        start=start_date,
        end=end_date,
        stage_cultivation_cycle_is_locked=False,
        id=CultivationCycleResponseId(
            crop_event_id=None,
            crop_type=None,
            start_date=None,
            end_date=None,
            harvest_year=end_date.year,
            index=index,
            phase_id=phase_id,
            stage_id=stage_id,
        ),
    )


def _map_harvest_event_to_no_practice_observations(
    ses_crop_events: dict[str, list[tuple[EventWithContext, EventWithContext]]] | None,
) -> dict[str, NoPracticeObservations]:
    harvest_event_to_no_practice_observations: dict[str, NoPracticeObservations] = {}
    if not ses_crop_events:
        return harvest_event_to_no_practice_observations
    all_crop_events = list(chain(*ses_crop_events.values()))
    for crop_event in all_crop_events:
        harvest_event = crop_event[1]
        if not harvest_event.context:
            continue
        no_practice_observations = get_no_practice_observations_from_ses_structured_event_context(harvest_event.context)
        if not no_practice_observations:
            continue
        harvest_event_to_no_practice_observations[harvest_event.event.id] = no_practice_observations
    return harvest_event_to_no_practice_observations


def _map_fallow_period_to_no_practice_observations(
    ses_field_practice_events: dict[str, list[EventWithContext]] | None,
) -> dict[str, NoPracticeObservations]:
    fallow_period_to_no_practice_observations: dict[str, NoPracticeObservations] = {}
    if not ses_field_practice_events:
        return fallow_period_to_no_practice_observations
    all_field_practice_events = list(chain(*ses_field_practice_events.values()))
    for field_practice_event in all_field_practice_events:
        if not event_type(field_practice_event.event) == StructuredEvent.TYPE_FALLOW_PERIOD:
            continue
        if not field_practice_event.context:
            continue
        no_practice_observations = get_no_practice_observations_from_ses_structured_event_context(
            field_practice_event.context
        )
        if not no_practice_observations:
            continue
        fallow_period_to_no_practice_observations[field_practice_event.event.id] = no_practice_observations
    return fallow_period_to_no_practice_observations


async def _unset_no_practice_observations_if_conflict(
    request: Request,
    no_practice_observations: NoPracticeObservations,
    cultivation_cycle_events: list[EntityEvent],
    crop_event_id: UUID,
    acting_user_id: str | None,
    owner_user_id: str,
    phase: Phases,
    field: Field,
) -> NoPracticeObservations:
    to_update_no_practice_observations = {}
    for event in cultivation_cycle_events:
        if isinstance(event, TillageEvent) and no_practice_observations.tillage_event:
            to_update_no_practice_observations[EntityEventType.TILLAGE_EVENT] = False
        elif isinstance(event, ApplicationEvent) and no_practice_observations.application_event:
            to_update_no_practice_observations[EntityEventType.APPLICATION_EVENT] = False
        elif isinstance(event, IrrigationEvent) and no_practice_observations.irrigation_event:
            to_update_no_practice_observations[EntityEventType.IRRIGATION_EVENT] = False
    if to_update_no_practice_observations and acting_user_id:
        updated_no_practice_observations = await update_no_practice_observations_for_event(
            event_id=str(crop_event_id),
            no_practice_observations=to_update_no_practice_observations,
            field=field,
            acting_user_id=acting_user_id,
            owner_user_id=owner_user_id,
            ses_client=ses_client,
        )
        from domain_event_bus.domain_event_bus import event_bus

        await event_bus.publish(
            event=FieldEventsChanged(
                program_id=phase.program_id, phase_id=phase.id, project_id=field.parent_project_id, field_id=field.id
            ),
            request=request,
        )
        return updated_no_practice_observations
    return no_practice_observations


def _bin_unbinned_events_into_empty_cultivation_cycles(
    unbinned_events: list[EntityEvent],
    cultivation_cycles: list[CultivationCycleResult],
    default_no_practice_observations: NoPracticeObservations,
    phase_id: int,
    stage_id: int | None = None,
) -> list[CultivationCycleResult]:
    """
    The provided unbinned_events and cultivation_cycles should be sorted in reverse order.
    """
    cycles_start = min(cycle.start for cycle in cultivation_cycles)
    cycles_end = max(cycle.end for cycle in cultivation_cycles)

    events_before_cycles = []
    new_start_date = datetime.max.replace(tzinfo=timezone.utc)
    events_after_cycles = []
    new_end_date = datetime.min.replace(tzinfo=timezone.utc)
    for event in unbinned_events:
        event_binning_date = event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()
        event_binning_date = event_binning_date.replace(tzinfo=timezone.utc)
        if event_binning_date < cycles_start:
            events_before_cycles.append(event)
            new_start_date = min(new_start_date, event_binning_date)
            continue
        if event_binning_date > cycles_end:
            events_after_cycles.append(event)
            new_end_date = max(new_end_date, event_binning_date)
            continue
        for cultivation_cycle in cultivation_cycles:
            if cultivation_cycle.start <= event_binning_date <= cultivation_cycle.end:
                cultivation_cycle.events.append(event)
                cultivation_cycle.no_practice_observations = (
                    _get_no_practice_observations_for_empty_cultivation_cycle_result(
                        default_no_practice_observations=default_no_practice_observations,
                        events=cultivation_cycle.events,
                    )
                )
                break
    if events_before_cycles:
        start_date = min(new_start_date, cycles_start - timedelta(days=2))
        end_date = cycles_start - timedelta(seconds=1)
        start_cycle = _generate_empty_cultivation_cycle_result(
            events=events_before_cycles,
            start_date=start_date,
            end_date=end_date,
            default_no_practice_observations=default_no_practice_observations,
            index=get_unique_index_for_previous_empty_cultivation_cycle(
                harvest_year=end_date.year, next_cultivation_cycle=cultivation_cycles[-1]
            ),
            phase_id=phase_id,
            stage_id=stage_id,
        )
        cultivation_cycles = cultivation_cycles + [start_cycle]
    if events_after_cycles:
        start_date = cycles_end + timedelta(seconds=1)
        end_date = max(new_end_date, cycles_end + timedelta(days=2))
        end_cycle = _generate_empty_cultivation_cycle_result(
            events=events_after_cycles,
            start_date=start_date,
            end_date=end_date,
            default_no_practice_observations=default_no_practice_observations,
            index=get_unique_index_for_next_empty_cultivation_cycle(
                harvest_year=end_date.year, previous_cultivation_cycle=cultivation_cycles[0]
            ),
            phase_id=phase_id,
            stage_id=stage_id,
        )
        cultivation_cycles = [end_cycle] + cultivation_cycles

    return cultivation_cycles


def _filter_m_phase_cultivation_cycles(
    phase_type: PhaseTypes, program: Programs, cultivation_cycles: list[CultivationCycle]
) -> list[CultivationCycle]:
    if phase_type != PhaseTypes.MONITORING:
        return cultivation_cycles
    reporting_period_start_date = program.reporting_period_start_date.replace(tzinfo=timezone.utc)
    num_preceding_years = 1 if program.accounting_method == AccountingMethod.intervention else 0
    return [
        cycle
        for cycle in cultivation_cycles
        if cycle.end >= reporting_period_start_date - relativedelta(years=num_preceding_years)
    ]


def _generate_placeholders_for_empty_cultivation_cycles_with_cover_crops(
    cultivation_cycles: list[CultivationCycleResult],
    default_no_practice_observations: NoPracticeObservations,
) -> list[CultivationCycleResult]:
    """
    If an empty cultivation cycle contains a cover crop, then it will not be displayed
    in the UI as an empty row - the cover crop will occupy the row. To address this,
    this method generates an additional empty cultivation cycle, that will be displayed
    in the UI as an empty row.
    """
    all_cultivation_cycles = []
    for cult_cycle in cultivation_cycles:
        has_commodity_crop_or_fallow = False
        has_cover_crop = False
        for event in cult_cycle.events:
            if (
                isinstance(event, CroppingEvent)
                and event.crop_usage == CropUsage.COMMODITY
                or isinstance(event, FallowPeriod)
            ):
                has_commodity_crop_or_fallow = True
                break
            elif isinstance(event, CroppingEvent) and event.crop_usage == CropUsage.COVER:
                has_cover_crop = True
        if has_commodity_crop_or_fallow or not has_cover_crop:
            all_cultivation_cycles.append(cult_cycle)
            continue
        empty_cultivation_cycle = _generate_empty_cultivation_cycle_result(
            events=[],
            start_date=cult_cycle.start + timedelta(days=1),
            end_date=cult_cycle.end,
            default_no_practice_observations=default_no_practice_observations,
            index=get_unique_index_for_next_empty_cultivation_cycle(
                harvest_year=cult_cycle.end.year, previous_cultivation_cycle=cult_cycle
            ),
            phase_id=cult_cycle.id.phase_id,
            stage_id=cult_cycle.id.stage_id,
        )
        all_cultivation_cycles.append(empty_cultivation_cycle)
        all_cultivation_cycles.append(cult_cycle)
    return all_cultivation_cycles


def _flag_cultivation_cycle_participation_in_phase_completion(
    cultivation_cycles: list[CultivationCycleResult], program: Programs, phase: Phases, baseline_year: int
) -> list[CultivationCycleResult]:
    filter_period = get_completion_filter_period(program, phase, baseline_year)
    for cc in cultivation_cycles:
        cc.participates_in_phase_completion = is_cultivation_cycle_in_filter_period(cc, filter_period)
    return cultivation_cycles


def _generate_initial_cultivation_cycle_results(
    starting_cycle_end_date: datetime,
    ending_cycle_end_date: datetime,
    default_no_practice_observations: NoPracticeObservations,
    phase: Phases,
    stage: Stage | None,
) -> list[CultivationCycleResult]:
    """
    Returns the list of empty cultivation cycles that is initially displayed before
    any events have been entered.
    """

    cult_cycle_results = []
    cycle_end_date = ending_cycle_end_date
    while cycle_end_date >= starting_cycle_end_date:
        standardized_cycle_end_date = cycle_end_date.replace(hour=23, minute=59, second=59)
        cult_cycle_results.append(
            _generate_empty_cultivation_cycle_result(
                events=[],
                start_date=standardized_cycle_end_date - relativedelta(years=1) + timedelta(seconds=1),
                end_date=standardized_cycle_end_date,
                default_no_practice_observations=default_no_practice_observations,
                index=1,
                phase_id=phase.id,
                stage_id=stage.id if stage else None,
            )
        )
        cycle_end_date = cycle_end_date - relativedelta(years=1)
    return cult_cycle_results


def _get_all_ses_event_id_with_stage_type(
    ses_crop_events: dict[str, list[tuple[EventWithContext, EventWithContext]]] | None,
    ses_events: dict[str, list[EventWithContext]] | None,
) -> list[EventIdWithStageType]:
    """Extract all event IDs with their stage types from both ses_crop_events and ses_events."""
    all_events_with_stage: list[EventIdWithStageType] = []
    # Add cropping event IDs
    if ses_crop_events:
        for events in ses_crop_events.values():
            for event_tuple in events:
                for event in event_tuple:
                    if event and event.event and event.event.id:
                        try:
                            event_id = UUID(event.event.id)
                            ses_event_type = event_type(event.event)
                            stage_type = SES_EVENT_TYPE_TO_STAGE.get(ses_event_type)
                            if stage_type:
                                all_events_with_stage.append(
                                    EventIdWithStageType(event_id=event_id, stage_type=stage_type)
                                )
                        except (ValueError, TypeError):
                            # Skip invalid UUIDs
                            continue
    # Add practice event IDs
    if ses_events:
        for events in ses_events.values():
            for event in events:
                if event and event.event and event.event.id:
                    try:
                        event_id = UUID(event.event.id)
                        ses_event_type = event_type(event.event)
                        stage_type = SES_EVENT_TYPE_TO_STAGE.get(ses_event_type)
                        if stage_type:
                            all_events_with_stage.append(EventIdWithStageType(event_id=event_id, stage_type=stage_type))
                    except (ValueError, TypeError):
                        # Skip invalid UUIDs
                        continue
    return all_events_with_stage


def _get_ses_event_ids_from_events(
    events: list[EntityEvent] | list[FieldEvent],
) -> list[UUID]:
    """Extract SES event IDs from entity events or field events."""
    event_ids: list[UUID] = []
    for event in events:
        if event.id is not None:
            if isinstance(event.id, CroppingIDs):
                # CroppingIDs.get_ids() returns list[str], so we need to convert
                for id_str in event.id.get_ids():
                    try:
                        event_ids.append(UUID(id_str))
                    except (ValueError, TypeError):
                        # Skip invalid UUIDs
                        continue
            else:
                # event.id should be UUID already, but let's be safe
                if isinstance(event.id, UUID):
                    event_ids.append(event.id)
                else:
                    try:
                        event_ids.append(UUID(str(event.id)))
                    except (ValueError, TypeError):
                        # Skip invalid UUIDs
                        continue
    return event_ids


async def _build_field_cultivation_cycles_result_lookup(
    request: Request,
    program: Programs,
    acting_user_id: str | None,
    owner_user_id: str,
    phase: Phases,
    stage: Stage | None,
    fields: list[Field],
    ses_crop_events: dict[str, list[tuple[EventWithContext, EventWithContext]]] | None,
    ses_events: dict[str, list[EventWithContext]] | None,
) -> dict[str, list[CultivationCycleResult]]:
    result: dict[str, list[CultivationCycleResult]] = {}

    starting_cycle_end_date, ending_cycle_end_date = get_program_data_collection_required_date_range(
        phase_type=phase.type_, program=program
    )

    # Optimize locking call when stage context is known
    events_with_stage_type = _get_all_ses_event_id_with_stage_type(ses_crop_events, ses_events)

    event_locks: set[UUID] = await get_locked_event_ids_for_user(
        ses_client=ses_client,
        request=request,
        owner_user_id=int(owner_user_id),
        acting_phase_id=phase.id,
        field_md5s=[f.md5 for f in fields],
        events=events_with_stage_type,
    )
    default_no_practice_obs = await _get_default_no_practice_observations(request=request, phase=phase, stage=stage)
    baseline_lookup = await get_or_create_fields_baselines(request, [f.id for f in fields])

    # determine if the stage is required in current or previous programs
    stage_is_required_in_any_program = True
    if stage:
        program_series = await get_program_series(request, phase.program_id)
        previous_and_current_required_stage_types = await get_programs_required_stage_types(
            request=request,
            phase_type=phase.type_,
            program_ids=program_series[: program_series.index(phase.program_id) + 1],
        )
        stage_is_required_in_any_program = stage.type_ in previous_and_current_required_stage_types

    for field in fields:
        baseline_year = baseline_lookup[field.id].baseline_year
        crop_events = ses_crop_events.get(field.md5, []) if ses_crop_events is not None else None
        field_practice_events = ses_events.get(field.md5, []) if ses_events is not None else None
        entity_events = parse_ses_events_to_entity_events(
            cropping_events=crop_events, field_practice_events=field_practice_events, field=field
        )
        entity_events = _filter_events_to_end_dates(entity_events, ending_cycle_end_date)

        event_to_no_practice_observations = {
            **_map_harvest_event_to_no_practice_observations(ses_crop_events=ses_crop_events),
            **_map_fallow_period_to_no_practice_observations(ses_field_practice_events=ses_events),
        }

        cult_cycle_results = []
        unbinned_events = []
        if len(entity_events) > 0:
            cultivation_cycles, unbinned_events = get_cultivation_cycles(
                events=entity_events,
                generation_config=CultivationCycleGenerationConfig(
                    start_year=starting_cycle_end_date.year,
                    end_year=ending_cycle_end_date.year,
                    autogenerate_gap_cycles=True,
                    stage_type=stage.type_ if stage else None,
                ),
            )
            for cult_cycle in cultivation_cycles:
                crop_event_composite_id = cult_cycle.id.crop_event_id
                if not crop_event_composite_id:
                    crop_event_is_locked = False
                    stage_cultivation_cycle_is_locked = False
                    no_practice_obs = _get_no_practice_observations_for_empty_cultivation_cycle_result(
                        default_no_practice_observations=default_no_practice_obs, events=cult_cycle.events
                    )
                else:
                    cycle_crop_event_id: UUID = (
                        crop_event_composite_id.harvesting_id
                        if crop_event_composite_id.is_non_fallow_cropping_event()
                        else crop_event_composite_id.fallow_id
                    )
                    crop_event_is_locked = cycle_crop_event_id in event_locks

                    # Determine stage_cultivation_cycle_is_locked
                    stage_cultivation_cycle_is_locked = crop_event_is_locked
                    if stage is not None:
                        # If stage not required in any program, don't lock
                        # If stage required and crop event is locked, then lock
                        stage_cultivation_cycle_is_locked = stage_is_required_in_any_program and crop_event_is_locked

                    no_practice_obs = event_to_no_practice_observations.get(
                        str(cycle_crop_event_id), NoPracticeObservations()
                    )
                    if no_practice_obs != NoPracticeObservations():
                        no_practice_obs = await _unset_no_practice_observations_if_conflict(
                            request=request,
                            no_practice_observations=no_practice_obs,
                            cultivation_cycle_events=cult_cycle.events,
                            crop_event_id=cycle_crop_event_id,
                            acting_user_id=acting_user_id,
                            owner_user_id=owner_user_id,
                            phase=phase,
                            field=field,
                        )
                cult_cycle_result = CultivationCycleResult(
                    events=sorted(
                        cult_cycle.events,
                        key=lambda x: x.get_interval_end_or_occurred_at() or x.get_interval_start_or_occurred_at(),
                        reverse=True,
                    ),
                    no_practice_observations=no_practice_obs,
                    crop_event_is_locked=crop_event_is_locked,
                    stage_cultivation_cycle_is_locked=stage_cultivation_cycle_is_locked,
                    start=cult_cycle.start,
                    end=cult_cycle.end,
                    id=CultivationCycleResponseId(
                        **cult_cycle.id.dict(), phase_id=phase.id, stage_id=stage.id if stage else None
                    ),
                )
                cult_cycle_results.append(cult_cycle_result)
        if not cult_cycle_results:
            cult_cycle_results = _generate_initial_cultivation_cycle_results(
                starting_cycle_end_date=starting_cycle_end_date,
                ending_cycle_end_date=ending_cycle_end_date,
                default_no_practice_observations=default_no_practice_obs,
                phase=phase,
                stage=stage,
            )
        cult_cycle_results = _bin_unbinned_events_into_empty_cultivation_cycles(
            unbinned_events=sorted(
                unbinned_events,
                key=lambda x: x.get_interval_end_or_occurred_at() or x.get_interval_start_or_occurred_at(),
                reverse=True,
            ),
            cultivation_cycles=sorted(cult_cycle_results, key=lambda x: x.start, reverse=True),
            default_no_practice_observations=default_no_practice_obs,
            phase_id=phase.id,
            stage_id=stage.id if stage else None,
        )
        cult_cycle_results = _filter_m_phase_cultivation_cycles(
            phase_type=phase.type_,
            program=program,
            cultivation_cycles=cult_cycle_results,
        )
        cult_cycle_results = _generate_placeholders_for_empty_cultivation_cycles_with_cover_crops(
            cultivation_cycles=cult_cycle_results,
            default_no_practice_observations=default_no_practice_obs,
        )
        cult_cycle_results = _flag_cultivation_cycle_participation_in_phase_completion(
            cultivation_cycles=cult_cycle_results,
            program=program,
            phase=phase,
            baseline_year=baseline_year,
        )
        result[field.id] = cult_cycle_results
    return result


async def _prefill_monitoring_phase_cultivation_cycles(
    request: Request,
    field_id_to_cultivation_cycle_responses: dict[str, list[CultivationCycleResponse]],
    stage: Stage,
    fields: list[Field],
    program: Programs,
) -> dict[str, list[CultivationCycleResponse]]:
    field_stage_completion_results = await get_per_field_stage_completions_for_project(
        request=request, stage_id=stage.id, project_id=fields[0].parent_project_id
    )
    field_id_to_stage_completion = {
        result.field_id: result.completion_result.completion_status for result in field_stage_completion_results
    }
    field_id_to_assigned_practices = await get_field_facts_for_fields(
        request=request,
        field_ids=[field.id for field in fields],
        state=FieldDataState.assigned,
        data_type=EntityDataType.entity_practices,
    )
    field_id_to_assigned_practices = {
        field_id: assigned_practices[0] for field_id, assigned_practices in field_id_to_assigned_practices.items()
    }
    # TODO: move create_update_field_events to dedicated file. then, move this import to the top.
    from ui.projects.field_events.monitoring_phase_prefill import (
        prefill_monitoring_phase_cover_crops_or_tillage_for_field,
    )

    return {
        field_id: prefill_monitoring_phase_cover_crops_or_tillage_for_field(
            field_id=field_id,
            cultivation_cycles=cc_responses,
            stage_type=stage.type_,
            program=program,
            field_id_to_stage_completion=field_id_to_stage_completion,
            field_id_to_assigned_practices=field_id_to_assigned_practices,
        )
        for field_id, cc_responses in field_id_to_cultivation_cycle_responses.items()
    }


def _convert_cultivation_cycle_result_to_response(result: CultivationCycleResult) -> CultivationCycleResponse:
    return CultivationCycleResponse(
        id=result.id,
        events=[
            FieldEvent(
                event_values=event.to_ui_dict(),
                id=event.id,
                type=EntityEventType(event.__class__.__name__),
            )
            for event in result.events
        ],
        no_practice_observations=result.no_practice_observations,
        crop_event_is_locked=result.crop_event_is_locked,
        stage_cultivation_cycle_is_locked=result.stage_cultivation_cycle_is_locked,
        start=result.start,
        end=result.end,
        participates_in_phase_completion=result.participates_in_phase_completion,
        contains_prefilled_monitoring_phase_events=result.contains_prefilled_monitoring_phase_events,
    )


def _filter_events_to_end_dates(events: list[EntityEvent], end_datetime: datetime) -> list[EntityEvent]:
    filtered_events = []
    end_date = end_datetime.date()
    for ev in events:
        ev_datetime = ev.get_interval_end_or_occurred_at() or ev.get_interval_start_or_occurred_at()
        if ev_datetime is None:
            continue
        if ev_datetime.date() <= end_date:
            filtered_events.append(ev)
    return filtered_events


async def _build_field_cultivation_cycles_response_lookup(
    request: Request,
    program: Programs,
    acting_user_id: str | None,
    owner_user_id: str,
    phase: Phases,
    stage: Stage | None,
    fields: list[Field],
    ses_crop_events: dict[str, list[tuple[EventWithContext, EventWithContext]]] | None,
    ses_events: dict[str, list[EventWithContext]] | None,
) -> dict[str, list[CultivationCycleResult]]:
    result_lookup = await _build_field_cultivation_cycles_result_lookup(
        request=request,
        program=program,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        phase=phase,
        stage=stage,
        fields=fields,
        ses_crop_events=ses_crop_events,
        ses_events=ses_events,
    )
    return {
        field_id: list(map(_convert_cultivation_cycle_result_to_response, cc_result))
        for field_id, cc_result in result_lookup.items()
    }


async def build_field_cultivation_cycles_for_calculations(
    request: Request,
    program: Programs,
    acting_user_id: str | None,
    owner_user_id: str,
    phase: Phases,
    stage: Stage | None,
    fields: list[Field],
) -> dict[str, list[CultivationCycleResult]]:
    if len(fields) == 0:
        return {}
    from_date, to_date = get_event_query_range_for_reporting_period(program=program, phase_type=phase.type_)
    # we need to extend the date to be sure we get all the components of an event
    # we remove those that end after the reporting period later
    extended_to_date = to_date + timedelta(weeks=52)
    ses_crop_events, ses_events = await get_ses_field_events(
        ses_client=ses_client,
        field_md5s=[f.md5 for f in fields],
        from_date=from_date,
        to_date=extended_to_date,
        owner_user_id=owner_user_id,
        phase=phase,
        stage_type=stage.type_ if stage else None,
    )
    return await _build_field_cultivation_cycles_result_lookup(
        request=request,
        fields=fields,
        ses_crop_events=ses_crop_events,
        ses_events=ses_events,
        program=program,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        phase=phase,
        stage=stage,
    )


async def build_field_cultivation_cycles_for_response(
    request: Request,
    program: Programs,
    acting_user_id: str,
    owner_user_id: str,
    phase: Phases,
    stage: Stage | None,
    fields: list[Field],
    prefill_monitoring_phase: bool,
) -> dict[str, list[CultivationCycleResponse]]:
    result_lookup = await build_field_cultivation_cycles_for_calculations(
        request=request,
        program=program,
        acting_user_id=acting_user_id,
        owner_user_id=owner_user_id,
        phase=phase,
        stage=stage,
        fields=fields,
    )
    response_lookup = {
        field_id: list(map(_convert_cultivation_cycle_result_to_response, cc_results))
        for field_id, cc_results in result_lookup.items()
    }
    if prefill_monitoring_phase and phase.type_ == PhaseTypes.MONITORING and stage:
        response_lookup = await _prefill_monitoring_phase_cultivation_cycles(
            request=request,
            field_id_to_cultivation_cycle_responses=response_lookup,
            stage=stage,
            fields=fields,
            program=program,
        )
    return response_lookup


async def _create_default_no_practice_observations(
    default_no_practice_observations: NoPracticeObservations,
    field: Field,
    events: list[StructuredEvent],
    acting_user_id: str | None,
    owner_user_id: str,
) -> None:
    if not acting_user_id:
        return

    no_practice_observations_dict = default_no_practice_observations.to_ses_dict()
    if not no_practice_observations_dict:
        return

    for event in events:
        if event_type(event.pb_event) in [StructuredEvent.TYPE_HARVEST_ACTIVITY, StructuredEvent.TYPE_FALLOW_PERIOD]:
            await update_no_practice_observations_for_event(
                event_id=str(event.pb_event.id),
                no_practice_observations=no_practice_observations_dict,
                field=field,
                acting_user_id=acting_user_id,
                owner_user_id=owner_user_id,
                ses_client=ses_client,
            )


async def create_update_field_events(
    request: Request,
    events: List[FieldEventRequest],
    project_id: int,
    phase_id: int,
    field_id: int,
    acting_user_id: str | None,
) -> List[AdminBulkUpdateFieldResponse]:
    """
    Process a list of events for a specific field, phase, and project.
    Returns a list of FieldEventResponse objects indicating success or failure for each event.
    """
    results: List[AdminBulkUpdateFieldResponse] = []

    def succeeded_event_response(field_event: FieldEvent) -> AdminBulkUpdateFieldResponse:
        return AdminBulkUpdateFieldResponse(
            project_id=project_id,
            phase_id=phase_id,
            field_id=field_id,
            **field_event.dict(exclude={"project_id", "phase_id", "field_id"}),
            succeeded=True,
            error_detail=None,
            status_code=None,
        )

    def failed_event_response(
        field_event: FieldEvent,
        error_detail: str | dict[str, ValidationErrorResponse | ValidationResultsResponse] | None,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ) -> AdminBulkUpdateFieldResponse:
        return AdminBulkUpdateFieldResponse(
            project_id=project_id,
            phase_id=phase_id,
            field_id=field_id,
            **field_event.dict(exclude={"project_id", "phase_id", "field_id"}),
            succeeded=False,
            error_detail=error_detail,
            status_code=status_code,
        )

    field = await get_field_with_parent_project(request, project_id, field_id)
    if field is None:
        # if field does not exist, mark all events in this group as failed
        for field_event in events:
            results.append(
                failed_event_response(
                    field_event,
                    f"Field {field_id} does not exist for project {project_id}",
                    status.HTTP_404_NOT_FOUND,
                )
            )
        return results

    # geom loading in background, since it is a time consuming operation
    geom_task = asyncio.create_task(boundaries_client.get_geometry_for_md5(field.md5))

    program, phase, _ = await get_project_program_phase_and_stage(request, project_id, phase_id, None)
    owner_user_id = await get_owner_user_id_for_project(request, project_id)

    # user with BYPASS_PROJECT_FIELD_EVENT_LOCK permission can bypass the lock
    bypass_lock = await can_bypass_event_lock(request, program_id=program.id)

    default_no_practice_obs = await _get_default_no_practice_observations(request=request, phase=phase, stage=None)

    if not bypass_lock:
        # Extract event IDs and types for optimization
        events_with_stage_type = []
        for field_event in events:
            if field_event.id:
                stage_type = ENTITY_EVENT_TYPE_TO_STAGE_TYPE.get(field_event.type)
                if isinstance(field_event.id, CroppingIDs):
                    for event_id in field_event.id.get_ids():
                        events_with_stage_type.append(
                            EventIdWithStageType(event_id=UUID(event_id), stage_type=stage_type)
                        )
                else:
                    events_with_stage_type.append(EventIdWithStageType(event_id=field_event.id, stage_type=stage_type))

        # get locked events should be called at field level since it calls external service
        locked_event_ids = await get_locked_event_ids_for_user(
            ses_client=ses_client,
            request=request,
            owner_user_id=int(owner_user_id),
            acting_phase_id=phase.id,
            events=events_with_stage_type,
        )

    # Process each event in the group
    for field_event in events:
        try:
            any_events_locked = False
            if not bypass_lock:
                event_ids = (
                    [UUID(crop_id) for crop_id in field_event.id.get_ids()]
                    if isinstance(field_event.id, CroppingIDs)
                    else [field_event.id] if field_event.id else []
                )
                any_events_locked = any(event_id in locked_event_ids for event_id in event_ids)
            if any_events_locked:
                results.append(
                    failed_event_response(
                        field_event,
                        f"Event {field_event.id} is locked and cannot be edited at this time",
                        status.HTTP_400_BAD_REQUEST,
                    )
                )
                continue

            event_type = field_event.type
            create_event = FROM_UI_DICT_EVENT_CREATOR_TYPE_LOOKUP.get(event_type)

            if create_event is None:
                results.append(
                    failed_event_response(
                        field_event,
                        f"Unsupported event creator type: {event_type}",
                        status.HTTP_400_BAD_REQUEST,
                    )
                )
                continue

            event_values = field_event.event_values
            event: EntityEvent = create_event(
                entity_id=field.id,
                entity_type=EntityTypeChoices.field,
                attribute_type_to_value=event_values,
                event_id=field_event.id,
                event_creation_specification=EventCreationSpecification(
                    error_handling=EventCreatorErrorHandling.RAISE_ERROR
                ),
            )

            if event is None:
                results.append(
                    failed_event_response(
                        field_event,
                        f"No EntityEvents of type {event_type} were able to be created from event data: {event_values}",
                        status.HTTP_400_BAD_REQUEST,
                    )
                )
                continue

            # Validate event
            validation_results = await AttributeValuesValidator.validate_event(
                event=event.to_ui_dict(),
                event_id=event.id,
                entity_event_name=event_type,
                run_required_attrs_check=True,
                run_required_values_check=False,
                program_id=program.id,
                request=request,
                field_id=field_id,
                phase_type=phase.type_,
            )

            if validation_results["errors"]:
                results.append(
                    failed_event_response(
                        field_event,
                        {"validation_errors": validation_results["errors"]},
                        status.HTTP_400_BAD_REQUEST,
                    )
                )
                continue

            # Get geometry (await the background task if unfinished)
            geom = await geom_task

            # Convert to SES events
            new_events, ids_to_delete = await event.to_ses_events(
                owner_user_id=owner_user_id,
                acting_user_id=acting_user_id,
                geom=geom,
            )

            if len(new_events) == 0:
                results.append(
                    failed_event_response(
                        field_event,
                        f"No SES events were created from event data: {event_values}",
                        status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
                )
                continue

            # Some MRV events map on to multiple structured events, and sometimes the SES events they map to change, e.g. a
            # CroppingEvent might start off needing a SowingActivity, but then we need to change that to a PlantingActivity,
            # and when that happens, we need to delete the original SowingActivity as it's no longer needed.
            for event_id in ids_to_delete:
                res = await ses_client.delete_or_archive_field_event_for_user(
                    field_id=field.md5, event_id=event_id, user_id=owner_user_id
                )
                if not res or getattr(res, "id", None) != event_id:
                    logger.error(f"Error deleting SES event: {event_id}")

            if ids_to_delete:
                await delete_event_associations(
                    request,
                    event_ids=[UUID(del_id) for del_id in ids_to_delete],
                    owner_user_id=owner_user_id,
                )

            upserted_events: list[UpsertEventResponse] = await upsert_events_to_ses(
                field, event, new_events, ses_client
            )

            if default_no_practice_obs != NoPracticeObservations():
                await _create_default_no_practice_observations(
                    default_no_practice_observations=default_no_practice_obs,
                    field=field,
                    events=new_events,
                    acting_user_id=acting_user_id,
                    owner_user_id=owner_user_id,
                )

            await update_event_associations(
                request=request,
                field=field,
                phase_id=phase_id,
                upserted_events=[upsert.event for upsert in upserted_events],
                owner_user_id=owner_user_id,
            )

            created_event = event.from_ses_events(events=upserted_events, entity_id=field.id)
            field_event = FieldEvent.from_event(event=created_event, event_type=event.__class__.__name__)

            results.append(succeeded_event_response(field_event))

        except Exception as e:
            logger.error(f"Error processing event for field {field_id}: {str(e)}")
            results.append(
                failed_event_response(
                    field_event,
                    f"Internal error: {str(e)}",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            )

    return results


async def copy_field_events(
    request: Request,
    target_program: Programs,
    target_phase: Phases,
    target_stage: Stage,
    target_field: Field,
    event_ids: list[str | CroppingIDs],
    copy_mode: CopyFieldEventsMode,
    include_new_field_event: bool = False,
) -> List[BulkCopyEventResponse]:
    """
    Copy field events to another field
    """
    logger.info(f"copy_mode: {copy_mode}, source_events: {event_ids}, target_field: {target_field.id}")

    def succeeded_copy_response(
        source_event_id: str | CroppingIDs,
        new_event_id: str | CroppingIDs,
        new_event: FieldEvent | None = None,
    ) -> BulkCopyEventResponse:
        return BulkCopyEventResponse(
            source_event_id=source_event_id,
            target_field_id=target_field.id,
            new_event_id=new_event_id,
            new_event=new_event,
            succeeded=True,
            error_detail=None,
            status_code=200,
        )

    def failed_copy_response(
        source_event_id: str | CroppingIDs,
        error_detail: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ) -> BulkCopyEventResponse:
        return BulkCopyEventResponse(
            source_event_id=source_event_id,
            target_field_id=target_field.id,
            new_event_id=None,
            new_event=None,
            succeeded=False,
            error_detail=error_detail,
            status_code=status_code,
        )

    # validate user has access to target project
    # admin accessible projects
    accessible_project_ids = await get_accessible_projects(
        request=request,
        program_id=target_program.id,
        user_id=get_original_user_id(request),
        return_none_for_unrestricted=True,
    )

    # producer accessible projects
    if accessible_project_ids is not None:
        accessible_project_ids = accessible_project_ids.union(
            request.state.allowed_user_program_project_ids["project_ids"]
            if hasattr(request.state, "allowed_user_program_project_ids")
            else set()
        )

    if accessible_project_ids is not None and target_field.parent_project_id not in accessible_project_ids:
        return [
            failed_copy_response(
                source_event_id=event_id,
                error_detail=(
                    f"User does not have access to target field {target_field.id}  in project {target_field.parent_project_id}"
                ),
                status_code=status.HTTP_403_FORBIDDEN,
            )
            for event_id in event_ids
        ]

    target_field_cultivation_cycles = await get_target_field_cultivation_cycles(
        request=request,
        target_program=target_program,
        target_phase=target_phase,
        target_stage=target_stage,
        target_field=target_field,
    )

    # Extract locked intervals from cultivation cycles
    target_field_locked_intervals = [
        cycle.get_date_range() for cycle in target_field_cultivation_cycles if cycle.stage_cultivation_cycle_is_locked
    ]

    results: list[BulkCopyEventResponse] = []
    ses_events: list[str] = []
    cropping_events: list[CroppingIDs] = []

    for event_id in event_ids:
        if isinstance(event_id, CroppingIDs):
            cropping_events.append(event_id)
        else:
            ses_events.append(str(event_id))

    source_events_by_id: dict[str | FrozenCroppingIDs, EntityEvent] = {}

    # Validate ses events
    ses_event_ids_to_copy: list[str] = []
    for event_id in ses_events:
        ses_event = await _fetch_event_with_context_or_none(event_id)
        if ses_event is not None:
            ses_event_ids_to_copy.append(event_id)
            source_events_by_id[str(event_id)] = parse_ses_events_to_entity_events(
                field=target_field,
                cropping_events=None,
                field_practice_events=[ses_event],
            )[0]
        else:
            results.append(
                failed_copy_response(
                    source_event_id=event_id,
                    error_detail=f"Source event {event_id} not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                )
            )

    #  validate CroppingIDs events
    ses_event_ids_to_cropping_id: dict[str, FrozenCroppingIDs] = {}
    for cropping_id in cropping_events:
        cropping_ses_events = await asyncio.gather(
            *[_fetch_event_with_context_or_none(event_id) for event_id in cropping_id.get_ids()]
        )
        if None not in cropping_ses_events:
            frozen_cropping_id = FrozenCroppingIDs.from_cropping_ids(cropping_id)
            for ses_event_id in cropping_id.get_ids():
                ses_event_ids_to_cropping_id[ses_event_id] = frozen_cropping_id
            source_events_by_id[frozen_cropping_id] = parse_ses_events_to_entity_events(
                field=target_field,
                cropping_events=[tuple(cropping_ses_events)],
                field_practice_events=None,
            )[0]
        else:
            results.append(
                failed_copy_response(
                    source_event_id=cropping_id,
                    error_detail="One or more SES events in cropping event not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                )
            )

    # If copying events to locked interval, hard skip copying those events
    # As we should not touch any locked cultivation cycle in any case
    if target_field_locked_intervals:
        for event_id in list(ses_event_ids_to_copy):
            event = source_events_by_id[event_id]
            if _is_event_overlap_with_intervals(event, target_field_locked_intervals):
                ses_event_ids_to_copy.remove(event_id)
                # Not adding to results as we don't want to show this error to the user

        for cropping_id in set(ses_event_ids_to_cropping_id.values()):
            event = source_events_by_id[cropping_id]
            if _is_event_overlap_with_intervals(event, target_field_locked_intervals):
                for ses_event_id in cropping_id.get_ids():
                    ses_event_ids_to_cropping_id.pop(ses_event_id, None)
                # Not adding to results as we don't want to show this error to the user

    # Combine all SES event IDs for bulk copy
    all_ses_event_ids = ses_event_ids_to_copy + list(ses_event_ids_to_cropping_id.keys())

    logger.info(f"source ses event ids: {all_ses_event_ids}")
    if not all_ses_event_ids:
        return results

    owner_user_id = await get_owner_user_id_for_project(request=request, project_id=target_field.parent_project_id)

    if copy_mode == CopyFieldEventsMode.OVERWRITE:
        # Delete all events in target field and stage for other overwrite modes
        await _delete_events_in_target_field_stage(
            request=request,
            field=target_field,
            phase=target_phase,
            stage=target_stage,
            owner_user_id=owner_user_id,
            events=[event for cycle in target_field_cultivation_cycles for event in cycle.events],
        )
    elif copy_mode == CopyFieldEventsMode.OVERWRITE_OVERLAPPING:
        # Only delete overlapping events
        source_events = list(source_events_by_id.values())

        overlapping_events = await find_overlapping_events(
            source_events=source_events,
            target_field_events=[event for cycle in target_field_cultivation_cycles for event in cycle.events],
        )

        if overlapping_events:
            await _delete_events_in_target_field_stage(
                request=request,
                field=target_field,
                phase=target_phase,
                stage=target_stage,
                owner_user_id=owner_user_id,
                events=overlapping_events,
            )

    # Perform bulk copy
    copy_request = CopyEventsToFieldRequest(event_ids=all_ses_event_ids, field_id=target_field.md5)
    logger.info(f"bulk copy request: {copy_request}")
    bulk_copy_results = await ses_client.bulk_copy_events(copy_requests=[copy_request])

    logger.info(f"bulk copy results: {bulk_copy_results}")

    # Process results for SES events
    for copy_result in bulk_copy_results:
        if copy_result.source_event_id in ses_event_ids_to_copy:
            if copy_result.success and copy_result.new_event_id:
                results.append(
                    succeeded_copy_response(
                        source_event_id=copy_result.source_event_id,
                        new_event_id=copy_result.new_event_id,
                    )
                )
            else:
                results.append(
                    failed_copy_response(
                        source_event_id=copy_result.source_event_id,
                        error_detail=copy_result.error or "Unknown error during copy operation",
                    )
                )

    # Process results for CroppingIDs events
    cropping_id_to_copy_results: dict[FrozenCroppingIDs, list[CopyEventToFieldResult]] = defaultdict(list)
    for copy_result in bulk_copy_results:
        if copy_result.source_event_id in ses_event_ids_to_cropping_id:
            cropping_id = ses_event_ids_to_cropping_id[copy_result.source_event_id]
            cropping_id_to_copy_results[cropping_id].append(copy_result)

    for (
        cropping_id,
        copy_results_for_cropping,
    ) in cropping_id_to_copy_results.items():
        if all(copy_result.success for copy_result in copy_results_for_cropping):
            results.append(
                succeeded_copy_response(
                    source_event_id=cropping_id,
                    new_event_id=_build_cropping_ids_from_copy_results(
                        source_cropping_id=cropping_id,
                        copy_results=copy_results_for_cropping,
                    ),
                )
            )
        else:
            results.append(
                failed_copy_response(
                    source_event_id=cropping_id,
                    error_detail="Failed to copy all events: "
                    + "; ".join([copy_result.error for copy_result in copy_results_for_cropping if copy_result.error]),
                )
            )

    # Extract new event IDs from successful copy results
    new_ses_events: list[EventWithContext] = []

    # Use AsyncIO to fetch and process new events in parallel
    async def _async_fetch_and_process_events(result: BulkCopyEventResponse) -> None:
        if isinstance(result.new_event_id, CroppingIDs):
            cropping_ses_events = await asyncio.gather(
                *[ses_client.fetch_event_with_context(event_id=event_id) for event_id in result.new_event_id.get_ids()]
            )
            new_ses_events.extend(cropping_ses_events)

            if include_new_field_event:
                entity_event = parse_ses_events_to_entity_events(
                    field=target_field,
                    cropping_events=[tuple(cropping_ses_events)],
                    field_practice_events=None,
                )[0]
                result.new_event = FieldEvent.from_event(event=entity_event, event_type=entity_event.__class__.__name__)

        else:
            practice_ses_events = [await ses_client.fetch_event_with_context(event_id=result.new_event_id)]
            new_ses_events.extend(practice_ses_events)

            if include_new_field_event:
                entity_event = parse_ses_events_to_entity_events(
                    field=target_field,
                    cropping_events=None,
                    field_practice_events=practice_ses_events,
                )[0]
                result.new_event = FieldEvent.from_event(
                    event=entity_event,
                    event_type=entity_event.__class__.__name__,
                )

    await asyncio.gather(
        *[_async_fetch_and_process_events(result) for result in results if result.succeeded and result.new_event_id]
    )

    # update event associations
    if not new_ses_events:
        return results

    await update_event_associations(
        request=request,
        field=target_field,
        phase_id=target_phase.id,
        upserted_events=[event.event for event in new_ses_events],
        owner_user_id=owner_user_id,
    )

    # Publish domain event for field
    from domain_event_bus.domain_event_bus import event_bus

    await event_bus.publish(
        event=FieldEventsChanged(
            program_id=target_program.id,
            phase_id=target_phase.id,
            project_id=target_field.parent_project_id,
            field_id=target_field.id,
        ),
        request=request,
    )

    return results


async def get_target_field_cultivation_cycles(
    request: Request,
    target_program: Programs,
    target_phase: Phases,
    target_stage: Stage,
    target_field: Field,
) -> list[CultivationCycleResult]:
    """
    Get cultivation cycles for the target field with all events.

    Returns:
        List of cultivation cycles containing events and locked status
    """

    target_project_owner_id = await get_owner_user_id_for_project(
        request=request, project_id=target_field.parent_project_id
    )
    acting_user_id = extract_acting_user_id(request=request)

    target_field_cultivation_cycles = await build_field_cultivation_cycles_for_calculations(
        request=request,
        program=target_program,
        acting_user_id=acting_user_id,
        owner_user_id=target_project_owner_id,
        phase=target_phase,
        stage=target_stage,
        fields=[target_field],
    )

    return target_field_cultivation_cycles[target_field.id]


def _is_event_overlap_with_intervals(event: EntityEvent, intervals: list[DateRange]) -> bool:
    """
    Check if the event overlaps with given intervals.
    """
    event_binning_date = event.get_interval_end_or_occurred_at() or event.get_interval_start_or_occurred_at()
    if event_binning_date is None:
        return False
    event_binning_date = event_binning_date.date()

    return any(interval.start_date <= event_binning_date <= interval.end_date for interval in intervals)


def _build_cropping_ids_from_copy_results(
    source_cropping_id: FrozenCroppingIDs, copy_results: list[CopyEventToFieldResult]
) -> CroppingIDs:
    """
    Build a new CroppingIDs object from copy results by mapping source event IDs to new event IDs.
    """
    source_to_new_id = {
        copy_result.source_event_id: copy_result.new_event_id
        for copy_result in copy_results
        if copy_result.new_event_id
    }

    def get_new_id(id_as_str: str | None) -> UUID | None:
        if id_as_str and id_as_str in source_to_new_id:
            return UUID(source_to_new_id[id_as_str])
        return None

    return CroppingIDs(
        planting_id=get_new_id(source_cropping_id.planting_id_as_str()),
        sowing_id=get_new_id(source_cropping_id.sowing_id_as_str()),
        harvesting_id=get_new_id(source_cropping_id.harvesting_id_as_str()),
        termination_id=get_new_id(source_cropping_id.termination_id_as_str()),
        fallow_id=get_new_id(source_cropping_id.fallow_id_as_str()),
    )


async def _fetch_event_with_context_or_none(
    event_id: str,
) -> FetchEventWithContextResponse | None:
    """Validate that a SES event exists."""
    try:
        return await ses_client.fetch_event_with_context(event_id=event_id)
    except Exception:
        return None


async def _delete_events_in_target_field_stage(
    request: Request,
    field: Field,
    phase: Phases,
    stage: Stage,
    owner_user_id: int,
    events: list[EntityEvent],
) -> None:
    """
    Delete specified events from the target field, excluding locked events.

    Args:
        request: FastAPI request object
        program: Program containing the field
        field: Field containing the events
        phase: Phase for locked event checking
        stage: Stage for events
        owner_user_id: Owner user ID for event access
        events: List of events to delete
    """
    # Extract event IDs first to optimize locking check
    event_ids = _get_ses_event_ids_from_events(events)

    # Optimize by providing the known stage type for all events
    # Since all events are from the same stage, they all have the same stage type
    events_with_stage_type = [EventIdWithStageType(event_id=event_id, stage_type=stage.type_) for event_id in event_ids]

    target_field_locked_events = await get_locked_event_ids_for_user(
        ses_client=ses_client,
        request=request,
        owner_user_id=owner_user_id,
        acting_phase_id=phase.id,
        field_md5s=[field.md5],
        events=events_with_stage_type,
    )
    target_field_locked_events = {str(event_id) for event_id in target_field_locked_events}

    # do not delete locked events
    event_ids = [event_id for event_id in event_ids if str(event_id) not in target_field_locked_events]

    logger.info(f"Deleting events {event_ids} in target field {field.md5} and stage {stage.id} before overwriting")

    for event_id in event_ids:
        await ses_client.delete_or_archive_field_event_for_user(
            field_id=field.md5, event_id=event_id, user_id=owner_user_id
        )

    await delete_event_associations(request=request, event_ids=event_ids, owner_user_id=owner_user_id)


async def find_overlapping_events(
    source_events: List[EntityEvent], target_field_events: List[EntityEvent]
) -> List[EntityEvent]:
    """
    Find target events that overlap with source events.

    Args:
        source_events: Events being copied
        target_field_events: All existing target events in the field

    Returns:
        List of target events that should be deleted due to overlap
    """
    overlapping_events = []

    # Check each target event against all source events
    for target_event in target_field_events:
        for source_event in source_events:
            if do_events_overlap(source_event, target_event):
                overlapping_events.append(target_event)
                break

    return overlapping_events
