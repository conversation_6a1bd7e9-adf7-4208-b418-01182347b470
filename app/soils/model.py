from __future__ import annotations

from sqlalchemy import (
    CheckConstraint,
    Column,
    Date,
    DateTime,
    Float,
    ForeignKey,
    func,
    Grouping,
    Index,
    Integer,
    String,
)

from db.mysql import Base


class SoilsOverride(Base):
    __tablename__ = "mrv_soils_override"
    id = Column(Integer, nullable=False, primary_key=True, index=True)
    md5 = Column(String(32), nullable=False, index=True)
    field_id = Column(Integer, nullable=True)
    program_id = Column(Integer, ForeignKey("mrv_programs.id"), nullable=True)
    sampling_date = Column(Date, nullable=False)
    pred_soc = Column(Float, nullable=False)
    pred_bd = Column(Float, nullable=False)
    percent_valid = Column(Float, nullable=False)
    lower90_soc = Column(Float, nullable=False)
    upper90_soc = Column(Float, nullable=False)
    lower90_bd = Column(Float, nullable=False)
    upper90_bd = Column(Float, nullable=False)
    ph = Column(Float)
    clay_fraction = Column(Float)
    updated_at = Column(
        DateTime,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        nullable=False,
    )
    deleted_at = Column(DateTime, nullable=True)

    __table_args__ = (
        Index(
            "mrv_soils_override_unique_undeleted_field_id",
            "field_id",
            Grouping(func.IF(deleted_at.is_(None), 1, None)),
            unique=True,
            info={"skip_autogenerate": True},
        ),
        CheckConstraint("pred_soc >= 0.0", name="ck_mrv_soils_override_pred_soc_ge_0"),
        CheckConstraint("pred_soc <= 100.0", name="ck_mrv_soils_override_pred_soc_le_100"),
        CheckConstraint("pred_bd >= 0.1", name="ck_mrv_soils_override_pred_bd_ge_0_1"),
        CheckConstraint("pred_bd <= 2.65", name="ck_mrv_soils_override_pred_bd_le_2_65"),
        CheckConstraint("ph >= 3.0", name="ck_mrv_soils_override_ph_ge_3"),
        CheckConstraint("ph <= 11.0", name="ck_mrv_soils_override_ph_le_11"),
        CheckConstraint("clay_fraction >= 0.0", name="ck_mrv_soils_override_clay_fraction_ge_0"),
        CheckConstraint("clay_fraction <= 1.0", name="ck_mrv_soils_override_clay_fraction_le_1"),
    )
