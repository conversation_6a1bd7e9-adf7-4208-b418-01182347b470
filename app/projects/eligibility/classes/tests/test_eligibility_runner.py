from datetime import datetime, timezone
from unittest.mock import patch
from uuid import uuid4

import pytest

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from entity_events.events.cropping_event import CroppingEvent
from entity_events.measures import Interval
from programs.enums import ProgramTemplate
from projects.eligibility.classes.eligibility_runner import EligibilityRunner
from values.enums import EntityTypeChoices


@pytest.mark.asyncio
async def test_set_cultivation_cycles_event_based_program(app_request, mdl):
    # Create test data
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        is_single_phase_data_collection=False,
    )
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields()
    field2 = await mdl.Fields()

    # Mock cultivation cycles
    cultivation_cycle1 = CultivationCycle(
        id=CultivationCycleId(
            crop_event_id=uuid4(),
            harvest_year=2024,
            index=0,
        ),
        start=datetime(2024, 3, 1, tzinfo=timezone.utc),
        end=datetime(2024, 10, 31, tzinfo=timezone.utc),
        events=[],
    )
    cultivation_cycle2 = CultivationCycle(
        id=CultivationCycleId(
            crop_event_id=uuid4(),
            harvest_year=2024,
            index=1,
        ),
        start=datetime(2024, 11, 1, tzinfo=timezone.utc),
        end=datetime(2025, 2, 28, tzinfo=timezone.utc),
        events=[],
    )

    # Create EligibilityRunner instance
    runner = EligibilityRunner(
        project_id=project.id,
        assign_practice_stage_id=1,
        phase_id=1,
        entity_type=EntityTypeChoices.field,
        request=app_request,
        default_baseline_year=2023,
    )

    # Mock dependencies
    mock_events = [
        CroppingEvent(
            id=uuid4(),
            entity_id=field1.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2024, 3, 1, tzinfo=timezone.utc),
                end=datetime(2024, 10, 31, tzinfo=timezone.utc),
            ),
            is_intended=False,
        ),
        CroppingEvent(
            id=uuid4(),
            entity_id=field1.id,
            entity_type=EntityTypeChoices.field,
            interval=Interval(
                start=datetime(2024, 3, 1, tzinfo=timezone.utc),
                end=datetime(2024, 10, 31, tzinfo=timezone.utc),
            ),
            is_intended=True,  # This should be filtered out
        ),
    ]

    with patch("projects.eligibility.classes.eligibility_runner.get_fields_by_project_id") as mock_get_fields, patch(
        "projects.eligibility.classes.eligibility_runner.get_program_by_project_id"
    ) as mock_get_program, patch(
        "projects.eligibility.classes.eligibility_runner.fetch_events_for_field_phase"
    ) as mock_fetch_events, patch(
        "projects.eligibility.classes.eligibility_runner.get_cultivation_cycles"
    ) as mock_get_cycles:

        mock_get_fields.return_value = [field1, field2]
        mock_get_program.return_value = program
        mock_fetch_events.return_value = mock_events
        mock_get_cycles.return_value = ([cultivation_cycle1, cultivation_cycle2], None)

        await runner._set_cultivation_cycles()

        # Verify results
        assert len(runner.cultivation_cycles) == 4  # 2 cycles per field, 2 fields
        assert runner.cultivation_cycles == [
            cultivation_cycle1,
            cultivation_cycle2,
            cultivation_cycle1,
            cultivation_cycle2,
        ]

        # Verify fetch was called for each field
        assert mock_fetch_events.call_count == 2
        mock_fetch_events.assert_any_call(
            request=app_request,
            field_id=field1.id,
            enrollment_phase_only=True,
            is_single_phase_program=False,
        )
        mock_fetch_events.assert_any_call(
            request=app_request,
            field_id=field2.id,
            enrollment_phase_only=True,
            is_single_phase_program=False,
        )
