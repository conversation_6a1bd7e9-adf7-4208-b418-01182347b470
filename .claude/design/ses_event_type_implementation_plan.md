# SES Event Type Implementation Plan

## Problem Statement
Currently, `get_stage_types_for_event_ids()` in `/app/ses_integration/methods.py` makes SES client calls to fetch event details every time it needs to determine stage types for events. This is inefficient when we already have event type information available in other parts of the system.

## Solution Overview
Add a new `ses_event_type` column to `mrv_field_event_associations` table to store SES event types locally. Propagate event types from available SES events down the call chain to avoid additional SES fetches, optimizing `get_stage_types_for_event_ids` performance.

## Background Context

### Key Files and Their Roles:
- **`/app/ses_integration/model.py`**: Contains `FieldEventAssociation` SQLAlchemy model for `mrv_field_event_associations` table
- **`/app/ses_integration/schema.py`**: Contains Pydantic models including `EventRevision` class
- **`/app/ses_integration/event_associations.py`**: Main logic for creating/updating field-event associations
- **`/app/ses_integration/reconcile_event_associations.py`**: Background reconciliation that syncs events from SES
- **`/app/ses_integration/methods.py`**: Contains `get_stage_types_for_event_ids()` function that needs optimization
- **`/app/ses_integration/constants.py`**: Contains `SES_EVENT_TYPE_TO_STAGE` mapping

### Current Data Flow:
1. **Reconciliation**: SES events → `EventRevision` objects → `FieldEventAssociation` records
2. **Normal Association**: Upserted SES events → `EventRevision` objects → `FieldEventAssociation` records
3. **Stage Type Resolution**: Event IDs → SES client fetch → Stage types

### New Data Flow:
1. **Enhanced Reconciliation**: SES events → extract types → `EventRevisionWithType` objects → `FieldEventAssociation` records with `ses_event_type`
2. **Enhanced Association**: Upserted SES events → extract types → `EventRevisionWithType` objects → `FieldEventAssociation` records with `ses_event_type`
3. **Optimized Stage Type Resolution**: Event IDs → query database first → use local mapping → fallback to SES client only if needed

## Tasks

### Task 1: Schema Extension
**File**: `/app/ses_integration/schema.py`
**Description**: Create new `EventRevisionWithType` class extending `EventRevision`
**Context**: The existing `EventRevision` class (line 22-25) only contains `event_id` and `revision`. We need to extend it to also carry the event type.
**Location**: Add after the existing `EventRevision` class
**Implementation**:
```python
class EventRevisionWithType(EventRevision):
    ses_event_type: str | None  # Allow None for database compatibility and migration
```
**Imports needed**: None (inherits from existing `EventRevision`)
**Key point**: ses_event_type allows None to handle existing NULL values in database during migration

### Task 2: Model Updates
**File**: `/app/ses_integration/model.py`
**Description**: Add `ses_event_type` column to `FieldEventAssociation` model
**Context**: The `FieldEventAssociation` class (line 14-32) currently has columns like `ses_event_id`, `field_md5`, etc. We need to add the new column.
**Location**: Add after line 31 (after `deleted_at` column, before the closing of the class)
**Implementation**:
```python
ses_event_type = Column(String(255), nullable=True)
```
**Imports needed**: None (`String` and `Column` already imported)

### Task 3: Update Reconciliation Flow
**File**: `/app/ses_integration/reconcile_event_associations.py`
**Function**: `_reconcile_event_associations_for_md5` (around line 181-183)
**Description**: Extract event types from `event_pb2.Event` objects when creating event revisions
**Context**: This function receives `events: list[event_pb2.Event]` from SES and creates `EventRevision` objects. We need to extract event types and create `EventRevisionWithType` objects instead.
**Current code** (lines 181-183):
```python
event_revisions = [
    EventRevision(event_id=UUID(ev.id), revision=ev.revision) for ev in events if not ev.HasField("archived")
]
```
**New implementation**:
```python
from ses_client.event import event_type

event_revisions = [
    EventRevisionWithType(
        event_id=UUID(ev.id), 
        revision=ev.revision,
        ses_event_type=event_type(ev)
    ) 
    for ev in events if not ev.HasField("archived")
]
```
**Imports needed**: Add `from ses_client.event import event_type` to top of file
**Additional import**: Add `from ses_integration.schema import EventRevisionWithType`

### Task 4: Update Normal Association Flow
**File**: `/app/ses_integration/event_associations.py`
**Function**: `update_event_associations` (around line 37-44)
**Description**: Extract event types from `upserted_events` when creating event revisions
**Context**: This function receives `upserted_events: list[Event]` (protobuf Event objects) and creates `EventRevision` objects. We need to extract event types.
**Current code** (lines 41-42):
```python
event_revisions=[EventRevision(event_id=UUID(ev.id), revision=ev.revision) for ev in upserted_events],
```
**New implementation**:
```python
from ses_client.event import event_type

event_revisions = [
    EventRevisionWithType(
        event_id=UUID(ev.id), 
        revision=ev.revision,
        ses_event_type=event_type(ev)
    ) 
    for ev in upserted_events
]
```
**Imports needed**: Add `from ses_client.event import event_type` to top of file
**Note**: `EventRevisionWithType` import will be added as part of schema changes

### Task 5: Update associate_events_with_fields_of_md5
**File**: `/app/ses_integration/event_associations.py`
**Function**: `associate_events_with_fields_of_md5` (line 47)
**Description**: Update function signature to accept `list[EventRevisionWithType]` instead of `list[EventRevision]`
**Context**: This function is called by both reconciliation and normal association flows. It needs to accept the new type and pass it down the chain.
**Current signature** (line 47):
```python
async def associate_events_with_fields_of_md5(
    request: Request,
    event_revisions: list[EventRevision],
    field_md5: str,
    owner_user_id: str,
    project_id: int | None = None,
    include_completed_phases: bool = False,
) -> None:
```
**New signature**:
```python
async def associate_events_with_fields_of_md5(
    request: Request,
    event_revisions: list[EventRevisionWithType],
    field_md5: str,
    owner_user_id: str,
    project_id: int | None = None,
    include_completed_phases: bool = False,
) -> None:
```
**Implementation**: Only the type annotation changes; function body remains the same

### Task 6: Update _associate_events_with_fields_for_phases
**File**: `/app/ses_integration/event_associations.py`
**Function**: `_associate_events_with_fields_for_phases` (line 211)
**Description**: Update function signature to accept `list[EventRevisionWithType]`
**Context**: This is a private function that orchestrates the association process. It calls `_upsert_associations_between_events_and_fields`.
**Current signature** (line 211):
```python
async def _associate_events_with_fields_for_phases(
    request: Request,
    event_revisions: list[EventRevision],
    associable_phases_per_field: list[tuple[AssociationField, list[PhasesWithoutRelationships]]],
) -> None:
```
**New signature**:
```python
async def _associate_events_with_fields_for_phases(
    request: Request,
    event_revisions: list[EventRevisionWithType],
    associable_phases_per_field: list[tuple[AssociationField, list[PhasesWithoutRelationships]]],
) -> None:
```
**Implementation**: Update the call to `_upsert_associations_between_events_and_fields` (line 220) to pass the new type

### Task 7: Update _upsert_associations_between_events_and_fields
**File**: `/app/ses_integration/event_associations.py`
**Function**: `_upsert_associations_between_events_and_fields` (line 251)
**Description**: Use event types when creating FieldEventAssociation records
**Context**: This is the core function that creates `FieldEventAssociation` database records. Currently it only uses basic event information.
**Current signature** (line 251):
```python
async def _upsert_associations_between_events_and_fields(
    request: Request, event_ids: list[uuid.UUID], fields: list[AssociationField]
) -> list[FieldEventAssociation]:
```
**New signature**:
```python
async def _upsert_associations_between_events_and_fields(
    request: Request, event_revisions: list[EventRevisionWithType], fields: list[AssociationField]
) -> list[FieldEventAssociation]:
```
**Key changes**:
1. **Parameter change**: Replace `event_ids: list[uuid.UUID]` with `event_revisions: list[EventRevisionWithType]`
2. **Create event type mapping**: Extract event types from revisions
3. **Update FieldEventAssociation creation** (around line 289):

**Current creation** (line 289):
```python
FieldEventAssociation(
    ses_event_id=str(ses_event_id),
    field_md5=field.field_md5,
    field_id=field.field_id,
    project_id=field.project_id,
    program_id=field.program_id,
)
```
**New creation**:
```python
FieldEventAssociation(
    ses_event_id=str(ses_event_id),
    field_md5=field.field_md5,
    field_id=field.field_id,
    project_id=field.project_id,
    program_id=field.program_id,
    ses_event_type=event_type_mapping.get(ses_event_id)
)
```
**Additional logic needed**: Create mapping at start of function:
```python
event_type_mapping = {rev.event_id: rev.ses_event_type for rev in event_revisions}
event_ids = [rev.event_id for rev in event_revisions]
```

### Task 8: Update Bulk Update Function
**File**: `/app/ses_integration/db.py`
**Function**: `bulk_update_and_undelete_field_event_associations` (line 100)
**Description**: Add support for updating `ses_event_type` during bulk operations
**Context**: This function undeletes field event associations and optionally updates the `field_md5`. We need to also support updating the event type.
**Current signature** (line 100):
```python
async def bulk_update_and_undelete_field_event_associations(
    request: Request, field_event_association_ids: list[int], field_md5: Optional[str] = None
) -> list[FieldEventAssociation]:
```
**New signature**:
```python
async def bulk_update_and_undelete_field_event_associations(
    request: Request, field_event_association_ids: list[int], field_md5: Optional[str] = None, ses_event_type: Optional[str] = None
) -> list[FieldEventAssociation]:
```
**Current update logic** (lines 111-118):
```python
update_query = (
    update(FieldEventAssociation)
    .where(FieldEventAssociation.id.in_(field_event_association_ids))
    .values(deleted_at=None)
    .execution_options(synchronize_session="fetch")
)
if field_md5 is not None:
    update_query = update_query.values(field_md5=field_md5)
```
**New update logic**:
```python
update_query = (
    update(FieldEventAssociation)
    .where(FieldEventAssociation.id.in_(field_event_association_ids))
    .values(deleted_at=None)
    .execution_options(synchronize_session="fetch")
)
if field_md5 is not None:
    update_query = update_query.values(field_md5=field_md5)
if ses_event_type is not None:
    update_query = update_query.values(ses_event_type=ses_event_type)
```

### Task 9: Update Existing Records During Normal Flow
**File**: `/app/ses_integration/event_associations.py`
**Function**: `_upsert_associations_between_events_and_fields` (line 251)
**Description**: Update existing FieldEventAssociation records to populate ses_event_type if currently NULL
**Context**: When processing events through normal flows, if we find existing FieldEventAssociation records that don't have event types, we should update them with the newly available type information.
**Location**: After existing record lookup logic (around line 280-290)
**Implementation**: Add logic to update existing records with NULL event types:
```python
# After finding existing associations, update any with missing event types
for existing_association in existing_associations:
    event_type = event_type_mapping.get(UUID(existing_association.ses_event_id))
    if existing_association.ses_event_type is None and event_type is not None:
        existing_association.ses_event_type = event_type
        # The session will automatically track this change for commit
```
**Benefits**: Gradually populates event types for existing records without requiring a separate migration

### Task 10: Optimize Stage Type Resolution
**File**: `/app/ses_integration/methods.py`
**Function**: `get_stage_types_for_event_ids` (line 49)
**Description**: Query database first for events with stored types, only fetch missing ones from SES
**Context**: This function currently fetches all events from SES client. We want to first check our database for stored event types.
**Current function** (lines 49-87):
```python
async def get_stage_types_for_event_ids(ses_client: Client, event_ids: list[str]) -> dict[str, StageTypes | None]:
    result = {}
    missing_event_ids = []

    # Check cache for existing mappings
    for event_id in event_ids:
        if event_id in _event_stage_cache:
            result[event_id] = _event_stage_cache[event_id]
        else:
            missing_event_ids.append(event_id)

    # Fetch missing events from SES in a single batch call (only if needed)
    if missing_event_ids:
        ses_events = await ses_client.fetch_events(event_ids=missing_event_ids)
        # ... rest of function
```
**New implementation strategy**:
1. **Add database query step** after cache check but before SES fetch
2. **Query FieldEventAssociation** for events with non-NULL `ses_event_type`
3. **Use SES_EVENT_TYPE_TO_STAGE mapping** to resolve stage types locally
4. **Only fetch remaining events** from SES client
5. **Optionally update database** with newly resolved event types

**New imports needed**:
```python
from ses_integration.model import FieldEventAssociation
from ses_integration.constants import SES_EVENT_TYPE_TO_STAGE
from sqlalchemy import select
```

**New logic to add** (after line 70, before SES fetch):
```python
# Query database for events with stored types
if missing_event_ids:
    async with ses_client.request.state.sql_session.begin() as session:
        stmt = select(FieldEventAssociation.ses_event_id, FieldEventAssociation.ses_event_type).where(
            FieldEventAssociation.ses_event_id.in_(missing_event_ids),
            FieldEventAssociation.ses_event_type.is_not(None),
            FieldEventAssociation.deleted_at.is_(None)
        ).distinct()
        db_results = await session.execute(stmt)
        
        # Process database results
        still_missing_event_ids = []
        for event_id, event_type in db_results:
            stage_type = SES_EVENT_TYPE_TO_STAGE.get(event_type)
            _event_stage_cache[event_id] = stage_type
            result[event_id] = stage_type
            
        # Find events still not resolved
        for event_id in missing_event_ids:
            if event_id not in result:
                still_missing_event_ids.append(event_id)
        
        missing_event_ids = still_missing_event_ids
```

### Task 11: Update Test Model Factory
**File**: `/app/helper/pytest_models_factory.py`
**Function**: `FieldEventAssociation` (line 920)
**Description**: Add `ses_event_type` parameter to test data factory
**Context**: The model factory generates test data for `FieldEventAssociation`. We need to add the new column to support testing.
**Current function signature** (around line 920):
```python
async def FieldEventAssociation(
    self,
    ses_event_id: str = ...,
    field_md5: str = ...,
    field_id: int = ...,
    project_id: int = ...,
    program_id: int = ...,
    created_at: datetime = ...,
    deleted_at: datetime = None,
    **kwargs
) -> FieldEventAssociation:
```
**New function signature**:
```python
async def FieldEventAssociation(
    self,
    ses_event_id: str = ...,
    field_md5: str = ...,
    field_id: int = ...,
    project_id: int = ...,
    program_id: int = ...,
    created_at: datetime = ...,
    deleted_at: datetime = None,
    ses_event_type: str | None = None,
    **kwargs
) -> FieldEventAssociation:
```
**Implementation**: Add `ses_event_type=ses_event_type` to the model creation call

### Task 12: Handle Manual Association Cases
**File**: `/app/ses_integration/event_associations.py`
**Function**: `manually_associate_event_with_phase` (line 140)
**Description**: Update manual associations to work with new schema including event types
**Context**: This function is called from the migration process where we DO have access to the full SES event object and its type.

**Caller location**: `/app/ses_integration/migration/migrate_mrv_values_to_ses.py` (line 351)
```python
# The caller has access to the full event:
event_being_upserted = me.event  # Full Event object
created_event: UpsertEventResponse = await _upsert_event_with_retries(event_being_upserted)
# Currently calls without event type
await event_associations.manually_associate_event_with_phase(...)
```

**Implementation strategy**:
1. **Update function signature** to accept event type:
```python
async def manually_associate_event_with_phase(
    request: Request, 
    event_id: UUID, 
    revision: int, 
    field_id: int, 
    phase_id: int,
    ses_event_type: str | None  # Accept None for compatibility
) -> None:
```

2. **Update function body** to use EventRevisionWithType:
```python
await _associate_events_with_fields_for_phases(
    request=request,
    event_revisions=[EventRevisionWithType(event_id=event_id, revision=revision, ses_event_type=ses_event_type)],
    associable_phases_per_field=[(assoc_field, [phase])],
)
```

3. **Update the production caller** to pass event type:
```python
await event_associations.manually_associate_event_with_phase(
    request=request,
    event_id=uuid.UUID(created_event.event.id),
    revision=created_event.event.revision,
    field_id=me.field_id,
    phase_id=me.phase_id,
    ses_event_type=created_event.event.type,  # Pass the actual event type
)
```

4. **Update test callers** to provide mock event types:
```python
# In tests, use a mock event type like:
await manually_associate_event_with_phase(
    request=app_request, 
    event_id=event_id, 
    field_id=field.id, 
    phase_id=phase.id, 
    revision=revision,
    ses_event_type="tillageActivity"  # Or whatever mock type is appropriate for the test
)
```

**Key point**: ses_event_type can be None for compatibility with existing data and migration scenarios

## Event Type Constants
Use existing StructuredEvent constants:
- `StructuredEvent.TYPE_TILLAGE_ACTIVITY` = "tillageActivity"
- `StructuredEvent.TYPE_SOWING_ACTIVITY` = "sowingActivity" 
- `StructuredEvent.TYPE_PLANTING_ACTIVITY` = "plantingActivity"
- `StructuredEvent.TYPE_APPLICATION_ACTIVITY` = "applicationActivity"
- `StructuredEvent.TYPE_HARVEST_ACTIVITY` = "harvestActivity"
- `StructuredEvent.TYPE_IRRIGATION_ACTIVITY` = "irrigationActivity"
- `StructuredEvent.TYPE_FALLOW_PERIOD` = "fallowPeriod"
- `StructuredEvent.TYPE_TERMINATION_ACTIVITY` = "terminationActivity"

## Coverage
- ✅ **Reconciliation flows**: Event types extracted from `event_pb2.Event` objects
- ✅ **Normal association flows**: Event types extracted from `upserted_events`
- ✅ **Existing record updates**: Populate event types for existing NULL records during normal flow
- ✅ **Manual association flows**: Gracefully handle NULL event types
- ✅ **Stage type optimization**: Use stored types to avoid SES calls
- ✅ **Bulk operations**: Support updating event types
- ✅ **Testing**: Update model factory for tests

## Implementation Order

**Phase 1: Foundation (Tasks 1-2)**
1. Task 1: Create `EventRevisionWithType` schema
2. Task 2: Add `ses_event_type` column to model

**Phase 2: Data Flow Updates (Tasks 3-7)**
3. Task 3: Update reconciliation flow
4. Task 4: Update normal association flow  
5. Task 5: Update `associate_events_with_fields_of_md5`
6. Task 6: Update `_associate_events_with_fields_for_phases`
7. Task 7: Update `_upsert_associations_between_events_and_fields`

**Phase 3: Optimization and Support (Tasks 8-12)**
8. Task 8: Update bulk update function
9. Task 9: Update existing records during normal flow
10. Task 12: Handle manual association cases
11. Task 11: Update test model factory
12. Task 10: Optimize stage type resolution (do this last to verify everything works)

## Key Dependencies
- Tasks 1-2 must be completed before any other tasks
- Tasks 3-4 can be done in parallel
- Tasks 5-7 must be done in order (5 → 6 → 7)
- Task 7 must be completed before Tasks 8-9
- Task 9 must be completed before Task 10 (stage type optimization)
- Task 10 should be done last as it's the main performance optimization

## Testing Strategy
1. **After Phase 1**: Ensure models can be created with the new column
2. **After Phase 2**: Test that event types are being stored correctly in reconciliation and normal flows
3. **After Phase 3**: Test that the optimization works and performance improves

## Troubleshooting

### Common Issues and Solutions

**Import Errors**:
- If `EventRevisionWithType` import fails, ensure Task 1 is completed
- If `event_type` import fails, check ses_client version and import path

**Database Errors**:
- If column doesn't exist errors occur, ensure the model (Task 2) is updated and database migration is run
- If data too long errors occur, consider increasing varchar length from 255

**Type Errors**:
- If function signature mismatches occur, ensure all functions in the call chain are updated (Tasks 5-7)
- Use union types if mixing EventRevision and EventRevisionWithType is needed

**Performance Issues**:
- If Task 9 doesn't improve performance, verify that database queries are using indexes
- Consider adding index on `ses_event_type` column if queries are slow

**Data Consistency Issues**:
- If event types are missing, check that SES events have the expected format
- If event types are incorrect, verify the `event_type()` function is working correctly

### Database Migration Note
While not included in this implementation plan, you'll eventually need to create an Alembic migration to add the column to production:
```bash
make alembic_create_automigration MESSAGE="Add ses_event_type to field_event_associations"
```

## Benefits
- **No additional SES fetches** - Event types extracted from already-available SES events
- **Clean propagation** - Event types flow naturally down the call chain
- **Backward compatible** - Existing flows without event types continue to work (NULL values)
- **Performance improvement** - Reduces SES client calls in `get_stage_types_for_event_ids`
- **Single source of truth** - Event types extracted once where SES events are available