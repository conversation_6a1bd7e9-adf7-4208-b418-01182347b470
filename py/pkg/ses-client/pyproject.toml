[tool.poetry]
name = "ses-client"
version = "1.2.12"
description = ""
authors = [
    "<PERSON> <mi<PERSON><PERSON>@regrow.ag>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>"
]
readme = "README.md"


exclude = [
    "tests/*",
]

[[tool.poetry.source]]
name = "PyPI"

[[tool.poetry.source]]
name = "regrow-python"
url = "https://us-west1-python.pkg.dev/flurosat-154904/regrow-python/simple"
priority = "supplemental"

[tool.poetry.dependencies]
python = ">=3.11,<3.13"
httpx = "*"
grpcio = "*"
geojson = "*"
grpcio-health-checking = "*"
protobuf = "<6.0"
regrow-ses = { version = "0.1.65", source = "regrow-python" }

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
shapely = "^2.0.6"
geopy = "^2.4.1"
pydoc-markdown = "^4.8.2"
pytest-asyncio = "^1.0.0"
mypy = "^1.16.0"
ruff = "^0.11.12"
bump2version = "^1.0.1"

[tool.pytest.ini_options]
markers = [
    "integration: marks tests as integration (deselect with '-m \"not integration\"')",
]
asyncio_mode = "auto"

[tool.ruff.lint.per-file-ignores]
"tests/*.py" = ["T201"] # Ignore T201 print() in tests

[tool.mypy]
warn_return_any = true
warn_unused_configs = true

[[tool.mypy.overrides]]
module = "httpx"
ignore_missing_imports = true

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
