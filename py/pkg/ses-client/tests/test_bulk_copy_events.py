import json
import pytest
from unittest.mock import AsyncMock, MagicMock

from ses_client.client import <PERSON><PERSON>, CopyEventsToFieldRequest
from ses_client.boundaries import BoundariesClient


def create_mock_upsert_response(event_id: str):
    """Helper function to create a mock upsert response."""
    mock_response = MagicMock()
    mock_event = MagicMock()
    mock_event.id = event_id
    mock_response.event = mock_event
    return mock_response


@pytest.mark.integration
class TestBulkCopyEvents:
    """Test cases for the bulk_copy_events functionality."""

    @pytest.mark.asyncio
    async def test_bulk_copy_events_no_boundaries_service(self):
        """Test that bulk_copy_events raises error when boundaries service is not initialized."""

        client = Client("localhost:50051")
        requests = [CopyEventsToFieldRequest(["event1"], "field1")]

        with pytest.raises(ValueError, match="Boundaries service not initialized"):
            await client.bulk_copy_events(requests)

    @pytest.mark.asyncio
    async def test_bulk_copy_events_empty_requests(self):
        """Test bulk copy with empty request list."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        client.boundaries_service = mock_boundaries

        results = await client.bulk_copy_events([])
        assert results == []

    @pytest.mark.asyncio
    async def test_copy_event_creates_unique_ids(self):
        """Test that copying an event multiple times creates unique IDs."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Create mock event
        event_id = "test_event_id"
        field_id = "test_field_id"

        mock_event = MagicMock()
        mock_event.id = event_id
        mock_event.tillage_activity.depth.value = 5

        # Mock context
        mock_context = MagicMock()

        # Create response object
        mock_response = MagicMock()
        mock_response.event = mock_event
        mock_response.context = mock_context

        # Setup fetch_event_with_context to return the same event
        client.fetch_event_with_context = AsyncMock(return_value=mock_response)

        # Mock other methods - return different IDs for each upsert
        client._create_event_copy = MagicMock()
        client.upsert_event = AsyncMock(
            side_effect=[
                create_mock_upsert_response("unique_id_1"),
                create_mock_upsert_response("unique_id_2"),
            ]
        )

        # Test first copy
        new_id1 = await client.copy_event_to_field(event_id, field_id)

        # Test second copy
        new_id2 = await client.copy_event_to_field(event_id, field_id)

        # Should generate different UUIDs each time
        assert new_id1 != new_id2
        assert new_id1 == "unique_id_1"
        assert new_id2 == "unique_id_2"

    @pytest.mark.asyncio
    async def test_bulk_copy_events_single_request(self):
        """Test bulk copy with a single request containing one event."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("unique_event_id")
        )

        # Test
        requests = [CopyEventsToFieldRequest(["event1"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 1
        result = results[0]
        assert result.source_event_id == "event1"
        assert result.target_field_id == "field1"
        assert result.new_event_id == "unique_event_id"
        assert result.error is None
        assert result.success is True

        # Verify boundaries service was called
        mock_boundaries.fetch_geometry_str.assert_called_once_with("field1")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_multiple_events_to_one_field(self):
        """Test bulk copy with multiple events to a single field."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to return an event with proper pb_event structure
        def mock_create_event_copy(
            source_event,
            source_context,
            new_event_id,
            target_geometry,
            target_field_id,
            batch_id=None,
        ):
            mock_new_event = MagicMock()
            mock_new_event.pb_event = MagicMock()
            mock_new_event.pb_event.id = new_event_id
            return mock_new_event

        client._create_event_copy = MagicMock(side_effect=mock_create_event_copy)

        # Mock upsert_event to return actual event IDs
        upsert_call_count = 0

        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test - multiple events to one field
        requests = [CopyEventsToFieldRequest(["event1", "event2", "event3"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 3

        for i, result in enumerate(results):
            expected_event_id = f"event{i + 1}"
            assert result.source_event_id == expected_event_id
            assert result.target_field_id == "field1"
            # Should return the actual event ID from upsert
            assert result.new_event_id == f"actual_event_{i + 1}"
            assert result.error is None
            assert result.success is True

        # Verify boundaries service was called only once (batched)
        mock_boundaries.fetch_geometry_str.assert_called_once_with("field1")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_many_to_many(self):
        """Test bulk copy with many events to many fields (each field gets different events)."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy to return an event with proper pb_event structure
        def mock_create_event_copy(
            source_event,
            source_context,
            new_event_id,
            target_geometry,
            target_field_id,
            batch_id=None,
        ):
            mock_new_event = MagicMock()
            mock_new_event.pb_event = MagicMock()
            mock_new_event.pb_event.id = new_event_id
            return mock_new_event

        client._create_event_copy = MagicMock(side_effect=mock_create_event_copy)

        # Mock upsert_event to return actual event IDs
        upsert_call_count = 0

        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test - many events to many fields (each field gets different events)
        requests = [
            CopyEventsToFieldRequest(["event1", "event2"], "field1"),
            CopyEventsToFieldRequest(["event3"], "field2"),
            CopyEventsToFieldRequest(["event1", "event4", "event5"], "field3"),
        ]
        results = await client.bulk_copy_events(requests)

        # Verify results - should have 6 total results (2 + 1 + 3)
        assert len(results) == 6

        # Check specific results
        expected_combinations = [
            ("event1", "field1"),
            ("event2", "field1"),
            ("event3", "field2"),
            ("event1", "field3"),
            ("event4", "field3"),
            ("event5", "field3"),
        ]

        for i, (expected_event, expected_field) in enumerate(expected_combinations):
            result = results[i]
            assert result.source_event_id == expected_event
            assert result.target_field_id == expected_field
            # Should return the actual event ID from upsert
            assert result.new_event_id == f"actual_event_{i + 1}"
            assert result.error is None
            assert result.success is True

        # Verify boundaries service was called for each unique field (batched)
        assert mock_boundaries.fetch_geometry_str.call_count == 3
        mock_boundaries.fetch_geometry_str.assert_any_call("field1")
        mock_boundaries.fetch_geometry_str.assert_any_call("field2")
        mock_boundaries.fetch_geometry_str.assert_any_call("field3")

    @pytest.mark.asyncio
    async def test_bulk_copy_events_with_errors(self):
        """Test bulk copy with some operations failing."""
        from unittest.mock import MagicMock

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock the boundaries service to fail for field2
        mock_boundaries = AsyncMock(spec=BoundariesClient)

        def mock_fetch_geometry(field_id):
            if field_id == "field2":
                raise ValueError("Field not found")
            return json.dumps(
                {
                    "type": "Polygon",
                    "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
                }
            )

        mock_boundaries.fetch_geometry_str.side_effect = mock_fetch_geometry
        client.boundaries_service = mock_boundaries

        # Mock other dependencies for successful cases
        mock_source_event = MagicMock()
        mock_source_context = MagicMock()
        mock_source_context.creation = {"regrowUserId": "test_user_id"}
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)
        client.upsert_event = AsyncMock(
            return_value=create_mock_upsert_response("unique_event_id")
        )

        # Test - mix of successful and failing operations
        requests = [
            CopyEventsToFieldRequest(["event1"], "field1"),  # Should succeed
            CopyEventsToFieldRequest(
                ["event2"], "field2"
            ),  # Should fail (geometry fetch fails)
            CopyEventsToFieldRequest(["event3"], "field1"),  # Should succeed
        ]
        results = await client.bulk_copy_events(requests)

        # Verify results
        assert len(results) == 3

        # First result should succeed
        assert results[0].source_event_id == "event1"
        assert results[0].target_field_id == "field1"
        assert results[0].new_event_id == "unique_event_id"
        assert results[0].error is None
        assert results[0].success is True

        # Second result should fail
        assert results[1].source_event_id == "event2"
        assert results[1].target_field_id == "field2"
        assert results[1].new_event_id is None
        assert "Failed to fetch field geometry" in results[1].error
        assert results[1].success is False

        # Third result should succeed
        assert results[2].source_event_id == "event3"
        assert results[2].target_field_id == "field1"
        assert results[2].new_event_id == "unique_event_id"
        assert results[2].error is None
        assert results[2].success is True

    @pytest.mark.asyncio
    async def test_bulk_copy_events_returns_actual_upserted_event_ids(self):
        """Test that bulk_copy_events returns actual event IDs from upsert responses, not deterministic UUIDs."""

        client = Client(
            "localhost:50051", boundaries_service_url="http://test-boundaries"
        )

        # Mock boundaries service
        mock_boundaries = AsyncMock(spec=BoundariesClient)
        target_geometry = json.dumps(
            {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]],
            }
        )
        mock_boundaries.fetch_geometry_str.return_value = target_geometry
        client.boundaries_service = mock_boundaries

        # Mock source event
        mock_source_event = MagicMock()
        mock_source_event.id = "source_event_id"
        mock_source_context = MagicMock()
        mock_event_with_context = MagicMock()
        mock_event_with_context.event = mock_source_event
        mock_event_with_context.context = mock_source_context

        client.fetch_event_with_context = AsyncMock(
            return_value=mock_event_with_context
        )

        # Mock _create_event_copy
        mock_new_event = MagicMock()
        client._create_event_copy = MagicMock(return_value=mock_new_event)

        # Mock upsert_event to return different actual event IDs
        upsert_call_count = 0

        def mock_upsert_side_effect(event):
            nonlocal upsert_call_count
            upsert_call_count += 1
            actual_id = f"actual_event_{upsert_call_count}"
            return create_mock_upsert_response(actual_id)

        client.upsert_event = AsyncMock(side_effect=mock_upsert_side_effect)

        # Test
        requests = [CopyEventsToFieldRequest(["event1", "event2"], "field1")]
        results = await client.bulk_copy_events(requests)

        # Verify we got the actual event IDs
        assert len(results) == 2
        assert results[0].new_event_id == "actual_event_1"
        assert results[1].new_event_id == "actual_event_2"

        # Verify both operations succeeded
        assert results[0].success is True
        assert results[1].success is True
        assert results[0].error is None
        assert results[1].error is None
