# Migration 155 to 1665

## We are re-using this migration to get some more data from 155 into the system so that we can re-quantify 155 as in inventory program


For this migration, we need to look at and collect data from multiple programs.
This means that we will need to perform multiple migrations into the same program.
This should be on a per field basis. If there is every any missing data in any of the
following migrations, just leave it for the producer to fill in.

Step 1: For all producers that have a contract and not a regrow email in program 155,
migrate name and Cargill ID.


https://regrow.atlassian.net/browse/MRV-1229


```SQL
select
*
from mrv_service.mrv_programs
order by id desc
limit 5;
```

# write query to read last 10 rows of projects
```SQL
select
*
from mrv_service.mrv_projects
order by id desc
limit 10;
```




# Inspect a project

## using curl - source program strcuture
```
curl 'http://localhost:8000/projects/migration/inspect/8099' \
-H 'accept: application/json' \
-H 'fs-user-id: 13' \
-H 'Content-Type: application/json' \
| jq -r '.text_block'
```

## using curl - target program structure
```
curl 'http://localhost:8000/projects/migration/inspect/20515' \
-H 'accept: application/json' \
-H 'fs-user-id: 13' \
-H 'Content-Type: application/json' \
| jq -r '.text_block'
```




# Query to list all projects to migrate
For all producers that have a contract and not a regrow.ag email…

see file `migrate_full.txt`, with a list of all id's that will be used in the migration

```SQL local
select62869
    mp.id as project_id,
    mupp.user as user_id,
    u.email as user_email,
    mpc.id as contract_id,
    mpc.docusign_status as contract_status
from mrv_projects mp
left join mrv_user_project_permissions mupp on mupp.project = mp.id
left join users u on u.id = mupp.user
left join mrv_project_contracts mpc on mpc.project = mp.id
where mp.program_id = 155
and mp.deleted_at is null
and mupp.deleted_at is null
and (u.email not like "%@regrow.ag" and  u.email not like "%flurosat%")
and mpc.deleted_at is null
and mpc.docusign_status = 'completed'
order by mp.id
```


https://regrow.atlassian.net/browse/MRV-1478

as part of this we would like to bring over values from ENROLMENT


Summer Crop 2020,


2020

Phase: Enrollment

Stage: Historical_crop_rotation

Attribute: summer_crop

Record Year: 2020







Summer Crop 2019



2021
