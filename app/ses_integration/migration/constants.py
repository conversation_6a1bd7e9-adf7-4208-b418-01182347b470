from phases.enums import StageTypes

EARLIEST_ADJUSTED_FALLOW_END_MONTH = 7
EARLIEST_ADJUSTED_FALLOW_END_DAY = 1

NPO_STAGES_BY_PROGRAM = {
    253: {
        StageTypes.TILLAGE_EVENTS,
    },
    254: {
        StageTypes.TILLAGE_EVENTS,
    },
    257: {
        StageTypes.TILLAGE_EVENTS,
    },
    258: {
        StageTypes.TILLAGE_EVENTS,
    },
    259: {
        StageTypes.TILLAGE_EVENTS,
    },
    1119: {
        StageTypes.TILLAGE_EVENTS,
    },
    # 112X programs have an optional Nutrient stage, so completion is not assured and NPOs are not safe to derive.
    1126: {
        StageTypes.TILLAGE_EVENTS,
    },
    1127: {
        StageTypes.TILLAGE_EVENTS,
    },
    1128: {
        StageTypes.TILLAGE_EVENTS,
    },
    1129: {
        StageTypes.TILLAGE_EVENTS,
    },
    1130: {
        StageTypes.TILLAGE_EVENTS,
    },
    1131: {
        StageTypes.TILLAGE_EVENTS,
    },
    1150: {  # Regenconnect UK CY25
        StageTypes.TILLAGE_EVENTS,
    },
    1178: {  # Bartlett Soy
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
    },
    1202: {  # Bartlett Corn
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
    },
    1216: {  # Anchor Nutro
        StageTypes.TILLAGE_EVENTS,
        StageTypes.IRRIGATION_EVENTS,
        StageTypes.NUTRIENT_EVENTS,
    },
    71143: {  # Dev program for 155 inventory testing https://regrow.atlassian.net/browse/MRV-6140
        StageTypes.TILLAGE_EVENTS,
    },
    1665: {  # Prod program for 155 inventory
        StageTypes.TILLAGE_EVENTS,
    },
}
