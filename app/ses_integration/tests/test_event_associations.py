from uuid import UUID, uuid4

import pytest

from phases.enums import PhaseTypes
from programs.enums import ProgramTemplate
from root_crud import get
from ses_integration.event_associations import (
    associate_events_with_fields_of_md5,
    delete_event_associations,
    get_event_revisions_for_fields_phase,
    manually_associate_event_with_phase,
)
from ses_integration.model import FieldEventAssociation, PhaseEventAssociation
from ses_integration.schema import EventRevision


async def test_update_event_field_phase_associations_field_in_one_phase(mdl, app_request):
    """Field associated with one phase"""
    field_md5 = "test_md5"
    owner_user_id = "12"
    event_revisions = [EventRevision(event_id=UUID("1234************1234************"), revision=1)]
    program_id = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id = (await mdl.Projects(program_id=program_id)).id
    field = await mdl.Fields(md5=field_md5, parent_project_id=project_id)
    phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id, user=int(owner_user_id))

    different_field_md5 = "test_md5_2"
    different_program_id = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    different_project_id = (await mdl.Projects(program_id=different_program_id)).id
    different_field = await mdl.Fields(md5=different_field_md5, parent_project_id=different_project_id)
    different_phase = await mdl.Phases(program_id=different_program_id, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=different_project_id, user=int(owner_user_id))

    # Neither Phase has been completed
    await mdl.ProjectPhaseCompletion(project_id=field.parent_project_id, phase_id=phase.id, is_completed=False)
    await mdl.ProjectPhaseCompletion(
        project_id=different_field.parent_project_id, phase_id=different_phase.id, is_completed=False
    )

    await associate_events_with_fields_of_md5(app_request, event_revisions, field_md5, owner_user_id=owner_user_id)
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(ev.event_id) for ev in event_revisions])
        ],
    )
    assert len(associations) == 1
    assert associations[0].ses_event_id == str(event_revisions[0].event_id)
    assert associations[0].field_md5 == field_md5
    assert associations[0].field_id == field.id
    assert associations[0].program_id == program_id

    # Verify PhaseEventAssociation
    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[associations[0].id]),
        ],
    )
    assert len(phase_associations) == 1
    assert phase_associations[0].phase_id == phase.id


async def test_update_event_field_phase_associations_field_in_three_phases(mdl, app_request):
    """Field associated with three uncompleted phases, two in one program and one in another program"""
    field_md5 = "test_md5"
    owner_user_id = "12"
    event_revisions = [EventRevision(event_id=UUID("1234************1234************"), revision=1)]

    # Program 1 setup
    program_id_1 = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id_1 = (await mdl.Projects(program_id=program_id_1)).id
    field_1 = await mdl.Fields(md5=field_md5, parent_project_id=project_id_1)
    phase_1_1 = await mdl.Phases(program_id=program_id_1, type_=PhaseTypes.ENROLMENT)
    phase_1_2 = await mdl.Phases(program_id=program_id_1, type_=PhaseTypes.MONITORING)
    await mdl.ProjectPermissions(project=project_id_1, user=int(owner_user_id))

    # Program 2 setup
    program_id_2 = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id_2 = (await mdl.Projects(program_id=program_id_2)).id
    field_2 = await mdl.Fields(md5=field_md5, parent_project_id=project_id_2)
    phase_2_1 = await mdl.Phases(program_id=program_id_2, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id_2, user=int(owner_user_id))

    # No Phase has been completed
    await mdl.ProjectPhaseCompletion(project_id=field_1.parent_project_id, phase_id=phase_1_1.id, is_completed=False)
    await mdl.ProjectPhaseCompletion(project_id=field_1.parent_project_id, phase_id=phase_1_2.id, is_completed=False)
    await mdl.ProjectPhaseCompletion(project_id=field_2.parent_project_id, phase_id=phase_2_1.id, is_completed=False)

    await associate_events_with_fields_of_md5(app_request, event_revisions, field_md5, owner_user_id=owner_user_id)

    # Verify FieldEventAssociation
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(ev.event_id) for ev in event_revisions]),
            get.Filter(id_field=FieldEventAssociation.field_md5, ids=[field_md5]),
        ],
    )
    assert len(associations) == 2
    assert any(assoc.field_id == field_1.id for assoc in associations)
    assert any(assoc.field_id == field_2.id for assoc in associations)

    # Verify PhaseEventAssociation for Program 1
    phase_associations_1 = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id,
                ids=[assoc.id for assoc in associations if assoc.field_id == field_1.id],
            ),
        ],
    )
    assert len(phase_associations_1) == 2
    assert any(phase_assoc.phase_id == phase_1_1.id for phase_assoc in phase_associations_1)
    assert any(phase_assoc.phase_id == phase_1_2.id for phase_assoc in phase_associations_1)

    # Verify PhaseEventAssociation for Program 2
    phase_associations_2 = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id,
                ids=[assoc.id for assoc in associations if assoc.field_id == field_2.id],
            ),
        ],
    )
    assert len(phase_associations_2) == 1
    assert phase_associations_2[0].phase_id == phase_2_1.id


async def test_update_event_field_phase_associations_field_in_two_phases_e_completed(mdl, app_request):
    """Field associated with two phases, one completed and one open.
    When an E Phase is completed, only the M Phase's events are updated."""
    field_md5 = "test_md5"
    owner_user_id = "12"
    event_id = "12345678-1234-5678-1234-************"
    previous_revision = 1
    new_revision = 2
    event_revisions = [EventRevision(event_id=UUID(event_id), revision=new_revision)]

    # Program setup
    program_id = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id = (await mdl.Projects(program_id=program_id)).id
    field = await mdl.Fields(md5=field_md5, parent_project_id=project_id)
    completed_phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.ENROLMENT)
    open_phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.MONITORING)
    await mdl.ProjectPermissions(project=project_id, user=int(owner_user_id))

    # Create existing association for the completed phase
    field_event_association = await mdl.FieldEventAssociation(
        ses_event_id=event_id, field_md5=field_md5, field_id=field.id, project_id=project_id, program_id=program_id
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=completed_phase.id, revision=previous_revision
    )

    await mdl.ProjectPhaseCompletion(project_id=project_id, phase_id=completed_phase.id, is_completed=True)
    await mdl.ProjectPhaseCompletion(project_id=project_id, phase_id=open_phase.id, is_completed=False)

    await associate_events_with_fields_of_md5(app_request, event_revisions, field_md5, owner_user_id=owner_user_id)

    # Verify FieldEventAssociation
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[event_id]),
            get.Filter(id_field=FieldEventAssociation.field_md5, ids=[field_md5]),
        ],
    )
    assert len(associations) == 1
    assert associations[0].ses_event_id == event_id
    assert associations[0].field_md5 == field_md5
    assert associations[0].field_id == field.id
    assert associations[0].program_id == program_id

    # Verify PhaseEventAssociation
    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[associations[0].id]),
        ],
    )
    assert len(phase_associations) == 2
    assert any(
        phase_assoc.phase_id == completed_phase.id and phase_assoc.revision == previous_revision
        for phase_assoc in phase_associations
    )
    assert any(
        phase_assoc.phase_id == open_phase.id and phase_assoc.revision == new_revision
        for phase_assoc in phase_associations
    )


async def test_update_event_field_phase_associations_field_in_two_phases_m_completed(mdl, app_request):
    """
    Field associated with two phases, one completed and one open.
    When an M Phase is completed, the E and M Phase are still updated so E can't get ahead of M.
    """
    field_md5 = "test_md5"
    owner_user_id = "12"
    event_id = "12345678-1234-5678-1234-************"
    previous_revision = 1
    new_revision = 2
    event_revisions = [EventRevision(event_id=UUID(event_id), revision=new_revision)]

    # Program setup
    program_id = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id = (await mdl.Projects(program_id=program_id)).id
    field = await mdl.Fields(md5=field_md5, parent_project_id=project_id)
    completed_m_phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.MONITORING)
    open_e_phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id, user=int(owner_user_id))

    # Create existing association for the completed phase
    field_event_association = await mdl.FieldEventAssociation(
        ses_event_id=event_id, field_md5=field_md5, field_id=field.id, project_id=project_id, program_id=program_id
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=completed_m_phase.id, revision=previous_revision
    )

    await mdl.ProjectPhaseCompletion(project_id=project_id, phase_id=completed_m_phase.id, is_completed=True)
    await mdl.ProjectPhaseCompletion(project_id=project_id, phase_id=open_e_phase.id, is_completed=False)

    await associate_events_with_fields_of_md5(app_request, event_revisions, field_md5, owner_user_id=owner_user_id)

    # Verify FieldEventAssociation
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[event_id]),
            get.Filter(id_field=FieldEventAssociation.field_md5, ids=[field_md5]),
        ],
    )
    assert len(associations) == 1
    assert associations[0].ses_event_id == event_id
    assert associations[0].field_md5 == field_md5
    assert associations[0].field_id == field.id
    assert associations[0].program_id == program_id

    # Verify PhaseEventAssociations
    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[associations[0].id]),
        ],
    )
    assert len(phase_associations) == 2
    assert any(
        phase_assoc.phase_id == completed_m_phase.id and phase_assoc.revision == new_revision
        for phase_assoc in phase_associations
    )
    assert any(
        phase_assoc.phase_id == open_e_phase.id and phase_assoc.revision == new_revision
        for phase_assoc in phase_associations
    )


async def test_update_event_field_phase_associations_field_owning_user_access(mdl, app_request):
    """Ensure associations are only set up for fields if the owner_user_id has access to the field's project"""
    shared_field_md5 = "test_md5"
    owner_user_id = "12"
    other_user_id = "34"
    event_revisions = [EventRevision(event_id=UUID("1234************1234************"), revision=1)]

    # Program and Project setup for owner_user_id
    program_id_1 = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id_1 = (await mdl.Projects(program_id=program_id_1)).id
    field_1 = await mdl.Fields(md5=shared_field_md5, parent_project_id=project_id_1)
    phase_1 = await mdl.Phases(program_id=program_id_1, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id_1, user=int(owner_user_id))

    # Program and Project setup for other_user_id
    program_id_2 = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id_2 = (await mdl.Projects(program_id=program_id_2)).id
    field_2 = await mdl.Fields(md5=shared_field_md5, parent_project_id=project_id_2)
    await mdl.Phases(program_id=program_id_2, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id_2, user=int(other_user_id))

    await associate_events_with_fields_of_md5(
        app_request, event_revisions, shared_field_md5, owner_user_id=owner_user_id
    )

    # Verify FieldEventAssociation for owner_user_id
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(ev.event_id) for ev in event_revisions]),
            get.Filter(id_field=FieldEventAssociation.field_md5, ids=[shared_field_md5]),
        ],
    )
    assert len(associations) == 1
    assert associations[0].field_id == field_1.id
    assert associations[0].program_id == program_id_1

    # Verify no FieldEventAssociation for other_user_id
    assert not any(assoc.field_id == field_2.id for assoc in associations)

    # Verify PhaseEventAssociation for owner_user_id
    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[associations[0].id]),
        ],
    )
    assert len(phase_associations) == 1
    assert phase_associations[0].phase_id == phase_1.id


async def test_update_event_field_phase_associations_field_in_zero_phases(mdl, app_request):
    """Field associated with one phase"""
    field_md5 = "test_md5"
    owner_user_id = "12"
    event_revisions = [EventRevision(event_id=UUID("1234************1234************"), revision=1)]
    program_id = (await mdl.Programs(program_template=ProgramTemplate.event_based)).id
    project_id = (await mdl.Projects(program_id=program_id)).id
    field = await mdl.Fields(md5=field_md5, parent_project_id=project_id)
    phase = await mdl.Phases(program_id=program_id, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project_id, user=int(owner_user_id))

    # The only Phase has been completed
    await mdl.ProjectPhaseCompletion(project_id=field.parent_project_id, phase_id=phase.id, is_completed=True)

    await associate_events_with_fields_of_md5(app_request, event_revisions, field_md5, owner_user_id=owner_user_id)
    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[
            get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(ev.event_id) for ev in event_revisions])
        ],
        empty_return=True,
    )
    assert len(associations) == 0


async def test_delete_event_associations_simple(app_request, mdl):
    """
    One Phase is linked to the event, and the phase is not completed. The PhaseEventAssociation and the
    FieldEventAssociation should be deleted.
    """
    owner_user_id = 1
    event_id = uuid4()
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=owner_user_id)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(event_id), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=phase.id, revision=1
    )

    await delete_event_associations(app_request, event_ids=[event_id], owner_user_id=owner_user_id)

    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(event_id)])],
        filter_deleted=False,
    )
    assert len(associations) == 1
    assert associations[0].deleted_at is not None

    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id])
        ],
        filter_deleted=False,
    )
    assert len(phase_associations) == 1
    assert phase_associations[0].deleted_at is not None


async def test_delete_event_associations_other_user_field_event_associations(app_request, mdl):
    """
    FieldEventAssociations owned by another user shouldn't be considered for deletion.
    """
    owner_user_id = "1"
    other_user_id = "2"
    event_id = uuid4()

    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(md5="test_md5", parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    other_program = await mdl.Programs()
    other_phase = await mdl.Phases(program_id=other_program.id)
    other_project = await mdl.Projects(program_id=other_program.id)
    other_field = await mdl.Fields(md5=field.md5, parent_project_id=other_project.id)
    await mdl.ProjectPermissions(project=other_project.id, user=int(other_user_id))

    # FieldEventAssociation for owner_user_id
    field_event_association_owner = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(event_id), field_md5=field.md5, project_id=project.id, program_id=program.id
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association_owner.id, phase_id=phase.id, revision=1
    )

    # FieldEventAssociation for other_user_id
    field_event_association_other = await mdl.FieldEventAssociation(
        field_id=other_field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=other_project.id,
        program_id=other_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association_other.id, phase_id=other_phase.id, revision=1
    )

    await delete_event_associations(app_request, event_ids=[event_id], owner_user_id=owner_user_id)

    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(event_id)])],
        filter_deleted=True,
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_other.id
    assert associations[0].deleted_at is None

    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association_other.id]
            )
        ],
        filter_deleted=True,
    )
    assert len(phase_associations) == 1
    assert phase_associations[0].deleted_at is None
    assert phase_associations[0].field_event_association_id == associations[0].id


async def test_delete_event_associations_non_matching_event_ids(app_request, mdl):
    owner_user_id = "1"
    event_id = uuid4()
    non_matching_event_id = uuid4()

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(non_matching_event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await delete_event_associations(app_request, event_ids=[event_id], owner_user_id=owner_user_id)

    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(non_matching_event_id)])],
    )
    assert len(associations) == 1
    assert associations[0].deleted_at is None


async def test_delete_event_associations_multiple_events(app_request, mdl):
    owner_user_id = "1"
    event_1_id = uuid4()
    event_2_id = uuid4()

    program = await mdl.Programs()
    phase_id = (await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)).id
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    for event_id in [event_1_id, event_2_id]:
        event_assoc = await mdl.FieldEventAssociation(
            field_id=field.id,
            ses_event_id=str(event_id),
            field_md5=field.md5,
            project_id=project.id,
            program_id=program.id,
        )
        await mdl.PhaseEventAssociation(field_event_association_id=event_assoc.id, phase_id=phase_id, revision=1)

    await delete_event_associations(app_request, event_ids=[event_1_id, event_2_id], owner_user_id=owner_user_id)

    event_associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(event_1_id), str(event_2_id)])],
        filter_deleted=False,
    )
    assert len(event_associations) == 2
    assert all(assoc.deleted_at is not None for assoc in event_associations)


async def test_delete_event_associations_phase_event_association_not_deleted(app_request, mdl):
    """
    FieldEventAssociation shouldn't be deleted if there's a PhaseEventAssociation we didn't delete because it was completed already.
    """
    owner_user_id = "2"
    event_id = uuid4()

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    phase_completed = await mdl.Phases(program_id=program.id)
    await mdl.ProjectPhaseCompletion(phase_id=phase_completed.id, project_id=project.id, is_completed=True)
    phase_uncompleted = await mdl.Phases(program_id=program.id)
    await mdl.ProjectPhaseCompletion(phase_id=phase_uncompleted.id, project_id=project.id, is_completed=False)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(event_id), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    # PhaseEventAssociation for completed phase
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=phase_completed.id, revision=1
    )

    # PhaseEventAssociation for uncompleted phase
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id, phase_id=phase_uncompleted.id, revision=2
    )

    await delete_event_associations(app_request, event_ids=[event_id], owner_user_id=owner_user_id)

    associations = await get.generic_get(
        request=app_request,
        orm_type=FieldEventAssociation,
        filters=[get.Filter(id_field=FieldEventAssociation.ses_event_id, ids=[str(event_id)])],
        filter_deleted=True,
    )
    assert len(associations) == 1
    assert associations[0].deleted_at is None

    phase_associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id])
        ],
        filter_deleted=False,
    )
    assert len(phase_associations) == 2
    assert any(
        phase_assoc.phase_id == phase_completed.id and phase_assoc.deleted_at is None
        for phase_assoc in phase_associations
    )
    assert any(
        phase_assoc.phase_id == phase_uncompleted.id and phase_assoc.deleted_at is not None
        for phase_assoc in phase_associations
    )


async def test_manually_associate_event_with_phase(mdl, app_request, orm_select):
    """
    The other module entrypoints for updating event associations test their behavior pretty thoroughly, so this testing
    chooses not to retest some functionality.
    """
    user_id = 1
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(phase_id=phase.id, project_id=project.id, is_completed=True)
    event_id = uuid4()
    revision = 2

    await manually_associate_event_with_phase(
        request=app_request, event_id=event_id, field_id=field.id, phase_id=phase.id, revision=revision
    )

    field_event_association = (await orm_select(FieldEventAssociation))[0]
    assert field_event_association.field_id == field.id
    assert field_event_association.project_id == project.id
    assert field_event_association.program_id == program.id

    phase_event_association = (await orm_select(PhaseEventAssociation))[0]
    assert phase_event_association.phase_id == phase.id
    assert phase_event_association.field_event_association_id == field_event_association.id
    assert phase_event_association.revision == revision


async def test_manually_associate_event_with_phase_invalid_phase_type(mdl, app_request, orm_select):
    """Error raised and no association is created if phase type not in EVENT_ASSOCIABLE_PHASE_TYPES."""
    user_id = 1
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    non_associable_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.SOIL_SAMPLING)
    await mdl.ProjectPermissions(project=project.id, user=user_id)
    event_id = uuid4()
    revision = 1

    with pytest.raises(ValueError):
        await manually_associate_event_with_phase(
            request=app_request,
            event_id=event_id,
            field_id=field.id,
            phase_id=non_associable_phase.id,
            revision=revision,
        )
    assert len(await orm_select(FieldEventAssociation)) == 0
    assert len(await orm_select(PhaseEventAssociation)) == 0


async def test_manually_associate_event_with_phase_field_different_programs(mdl, app_request, orm_select):
    """Error raised and no association is created if field and phase are in different programs."""
    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field = await mdl.Fields(parent_project_id=project_1.id)
    program_2 = await mdl.Programs()
    phase = await mdl.Phases(program_id=program_2.id, type_=PhaseTypes.ENROLMENT)
    event_id = uuid4()
    revision = 1

    with pytest.raises(ValueError) as exc:
        await manually_associate_event_with_phase(
            request=app_request,
            event_id=event_id,
            field_id=field.id,
            phase_id=phase.id,
            revision=revision,
        )
        assert exc.value.args[0] == f"Field {field.id} and phase {phase.id} are in different programs."

    assert len(await orm_select(FieldEventAssociation)) == 0
    assert len(await orm_select(PhaseEventAssociation)) == 0


async def test_get_event_revisions_for_fields_phase(mdl, app_request, orm_select):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, md5="test_md5")
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    event_id = uuid4()
    revision = 1
    revision2 = 2

    field_event_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=field.md5,
        field_id=field.id,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=e_phase.id,
        revision=revision,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=m_phase.id,
        revision=revision2,
    )

    event_revisions = (
        await get_event_revisions_for_fields_phase(
            request=app_request,
            field_ids=[field.id],
            phase_type=PhaseTypes.ENROLMENT,
        )
    )[field.id]

    assert len(event_revisions) == 1
    assert event_revisions[0].event_id == event_id
    assert event_revisions[0].revision == revision


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_multiple_fields(mdl, app_request):
    """Test get_event_revisions_for_fields_phase with multiple fields."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    field1 = await mdl.Fields(parent_project_id=project.id, md5="test_md5_1")
    field2 = await mdl.Fields(parent_project_id=project.id, md5="test_md5_2")
    field3 = await mdl.Fields(parent_project_id=project.id, md5="test_md5_3")

    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)

    event_id1 = uuid4()
    event_id2 = uuid4()
    event_id3a = uuid4()
    event_id3b = uuid4()

    field1_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id1),
        field_md5=field1.md5,
        field_id=field1.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field1_association.id,
        phase_id=e_phase.id,
        revision=1,
    )

    field2_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id2),
        field_md5=field2.md5,
        field_id=field2.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field2_association.id,
        phase_id=m_phase.id,
        revision=2,
    )

    field3_association1 = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id3a),
        field_md5=field3.md5,
        field_id=field3.id,
        project_id=project.id,
        program_id=program.id,
    )
    field3_association2 = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id3b),
        field_md5=field3.md5,
        field_id=field3.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field3_association1.id,
        phase_id=e_phase.id,
        revision=3,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field3_association2.id,
        phase_id=m_phase.id,
        revision=4,
    )

    enrollment_revisions = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=[field1.id, field2.id, field3.id],
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert len(enrollment_revisions[field1.id]) == 1
    assert enrollment_revisions[field1.id][0].event_id == event_id1
    assert enrollment_revisions[field1.id][0].revision == 1

    assert len(enrollment_revisions[field2.id]) == 0

    assert len(enrollment_revisions[field3.id]) == 1
    assert enrollment_revisions[field3.id][0].event_id == event_id3a
    assert enrollment_revisions[field3.id][0].revision == 3

    monitoring_revisions = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=[field1.id, field2.id, field3.id],
        phase_type=PhaseTypes.MONITORING,
    )

    assert len(monitoring_revisions[field1.id]) == 0

    assert len(monitoring_revisions[field2.id]) == 1
    assert monitoring_revisions[field2.id][0].event_id == event_id2
    assert monitoring_revisions[field2.id][0].revision == 2

    assert len(monitoring_revisions[field3.id]) == 1
    assert monitoring_revisions[field3.id][0].event_id == event_id3b
    assert monitoring_revisions[field3.id][0].revision == 4


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_empty_field_ids(app_request):
    """Test get_event_revisions_for_fields_phase with empty field_ids list."""
    result = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=[],
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert result == {}


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_nonexistent_fields(app_request):
    """Test get_event_revisions_for_fields_phase with nonexistent field IDs."""
    nonexistent_field_ids = [99999, 99998, 99997]

    result = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=nonexistent_field_ids,
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert len(result) == 3
    assert result[99999] == []
    assert result[99998] == []
    assert result[99997] == []


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_mixed_existing_nonexistent(mdl, app_request):
    """Test get_event_revisions_for_fields_phase with mix of existing and nonexistent fields."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    real_field = await mdl.Fields(parent_project_id=project.id, md5="test_md5_real")
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    event_id = uuid4()
    field_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=real_field.md5,
        field_id=real_field.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_association.id,
        phase_id=e_phase.id,
        revision=1,
    )

    mixed_field_ids = [real_field.id, 99999, 99998]

    result = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=mixed_field_ids,
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert len(result[real_field.id]) == 1
    assert result[real_field.id][0].event_id == event_id
    assert result[real_field.id][0].revision == 1

    assert result[99999] == []
    assert result[99998] == []


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_same_event_different_fields(mdl, app_request):
    """Test get_event_revisions_for_fields_phase when same event is associated with multiple fields."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)

    field1 = await mdl.Fields(parent_project_id=project.id, md5="test_md5_1")
    field2 = await mdl.Fields(parent_project_id=project.id, md5="test_md5_2")

    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    event_id = uuid4()

    field1_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=field1.md5,
        field_id=field1.id,
        project_id=project.id,
        program_id=program.id,
    )
    field2_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=field2.md5,
        field_id=field2.id,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field1_association.id,
        phase_id=e_phase.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field2_association.id,
        phase_id=e_phase.id,
        revision=2,
    )

    result = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=[field1.id, field2.id],
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert len(result[field1.id]) == 1
    assert result[field1.id][0].event_id == event_id
    assert result[field1.id][0].revision == 1

    assert len(result[field2.id]) == 1
    assert result[field2.id][0].event_id == event_id
    assert result[field2.id][0].revision == 2


@pytest.mark.asyncio
async def test_get_event_revisions_for_fields_phase_single_field_compatibility(mdl, app_request):
    """Test that the batch function works correctly with single field (backward compatibility)."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, md5="test_md5")
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    event_id = uuid4()
    field_association = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=field.md5,
        field_id=field.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_association.id,
        phase_id=e_phase.id,
        revision=5,
    )

    result = await get_event_revisions_for_fields_phase(
        request=app_request,
        field_ids=[field.id],
        phase_type=PhaseTypes.ENROLMENT,
    )

    assert len(result) == 1
    assert field.id in result
    assert len(result[field.id]) == 1
    assert result[field.id][0].event_id == event_id
    assert result[field.id][0].revision == 5


async def test_associate_events_with_fields_of_md5_include_completed_phases_false(mdl, app_request):
    """Test that completed phases are excluded when include_completed_phases=False (default behavior)"""
    event_id = uuid4()
    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    permission = await mdl.ProjectPermissions(project=project.id)

    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    await associate_events_with_fields_of_md5(
        request=app_request,
        event_revisions=event_revisions,
        field_md5=field.md5,
        owner_user_id=permission.user,
        include_completed_phases=False,
    )

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase.id])],
        empty_return=True,
    )
    assert len(associations) == 0, "Should not associate with completed phases when include_completed_phases=False"


async def test_associate_events_with_fields_of_md5_include_completed_phases_true(mdl, app_request):
    """Test that completed phases are included when include_completed_phases=True"""
    event_id = uuid4()
    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    permission = await mdl.ProjectPermissions(project=project.id)

    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    await associate_events_with_fields_of_md5(
        request=app_request,
        event_revisions=event_revisions,
        field_md5=field.md5,
        owner_user_id=permission.user,
        include_completed_phases=True,
    )

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase.id])],
    )
    assert len(associations) == 1, "Should associate with completed phases when include_completed_phases=True"
    assert associations[0].phase_id == phase.id
