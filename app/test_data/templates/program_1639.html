<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
    <head>
        <meta charset="utf-8"/>
        <meta name="generator" content="pandoc"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes"/>
        <title>Regrow - Contract program 1639 France</title>
        <style>
            html {
                line-height: 1.5;
                font-family: Georgia, serif;
                font-size: 20px;
                color: #1a1a1a;
                background-color: #fdfdfd;
            }

            body {
                margin: 0 auto;
                max-width: 48em;
                padding: 50px;
                hyphens: auto;
                overflow-wrap: break-word;
                text-rendering: optimizeLegibility;
                font-kerning: normal;
            }

            @media (max-width: 600px) {
                body {
                    font-size: 0.9em;
                    padding: 1em;
                }

                h1 {
                    font-size: 1.8em;
                }
            }

            @media print {
                body {
                    background-color: transparent;
                    color: black;
                    font-size: 12pt;
                }

                h2,
                h3,
                p {
                    orphans: 3;
                    widows: 3;
                }

                h2,
                h3,
                h4 {
                    page-break-after: avoid;
                }
            }

            p {
                margin: 1em 0;
                text-align: justify;
            }

            a {
                color: #1a1a1a;
            }

            a:visited {
                color: #1a1a1a;
            }

            img {
                max-width: 100%;
            }

            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 1.4em;
            }

            h5,
            h6 {
                font-size: 1em;
                font-style: italic;
            }

            h6 {
                font-weight: normal;
            }

            ol,
            ul {
                padding-left: 1.7em;
                margin-top: 1em;
            }

            li > ol,
            li > ul {
                margin-top: 0;
            }

            blockquote {
                margin: 1em 0 1em 1.7em;
                padding-left: 1em;
                border-left: 2px solid #e6e6e6;
                color: #606060;
            }

            code {
                font-family: Menlo, Monaco, 'Lucida Console', Consolas, monospace;
                font-size: 85%;
                margin: 0;
            }

            pre {
                margin: 1em 0;
                overflow: auto;
            }

            pre code {
                padding: 0;
                overflow: visible;
                overflow-wrap: normal;
            }

            .sourceCode {
                background-color: transparent;
                overflow: visible;
            }

            hr {
                background-color: #1a1a1a;
                border: none;
                height: 1px;
                margin: 1em 0;
            }

            table {
                margin: 1em 0;
                border-collapse: collapse;
                width: 100%;
                overflow-x: auto;
                font-variant-numeric: lining-nums tabular-nums;
            }

            table caption {
                margin-bottom: 0.75em;
            }

            tbody {
                margin-top: 0.5em;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }

            th {
                border-top: 1px solid #1a1a1a;
                padding: 0.25em 0.5em;
            }

            td {
                padding: 0.125em 0.5em 0.25em;
            }

            header {
                margin-bottom: 4em;
                text-align: center;
            }

            #TOC li {
                list-style: none;
            }

            #TOC ul {
                padding-left: 1.3em;
            }

            #TOC > ul {
                padding-left: 0;
            }

            #TOC a:not(:hover) {
                text-decoration: none;
            }

            code {
                white-space: pre-wrap;
            }

            span.smallcaps {
                font-variant: small-caps;
            }

            span.underline {
                text-decoration: underline;
            }

            div.column {
                display: inline-block;
                vertical-align: top;
                width: 50%;
            }

            div.hanging-indent {
                margin-left: 1.5em;
                text-indent: -1.5em;
            }

            ul.task-list {
                list-style: none;
            }

            .display.math {
                display: block;
                text-align: center;
                margin: 0.5rem auto;

            }
            li::marker {
                width: 2.4cm;
                text-align: left !important;
            }
            .dash-list {
                list-style-type: none;
                padding-left: 1em;
            }
            .dash-list li::before {
                content: "- ";
                margin-right: 0.5em;
            }
        </style>
    </head>
    <body>
        <!-- custom program inputs. -->
        {% set ns1 = namespace(company_name_key="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith("Nom de l'entreprise") %}
                {% set ns1.company_name_key = key %}
            {% endif %}
        {% endfor %}
        {% set legal_name = custom_inputs[ns1.company_name_key] | default("") %}
        {% if legal_name == "" %}
            {% set legal_name = user_fname + " " + user_lname %}
        {% endif %}

        {% set SIRET = custom_inputs["SIRET"] | default("") %}
        {% if SIRET == "" %}
            {% set SIRET = custom_inputs["SIREN"] | default("") %}
        {% endif %}

        {% set ns2 = namespace(cicoop="") %}
        {% for key, value in custom_inputs.items() %}
            {% if key.startswith('Coopérative ou négoce') %}
                {% set ns2.cicoop = key %}
            {% endif %}
        {% endfor %}

        {# Template Variables for CO-OP #}
        {% set coop = custom_inputs[ns2.cicoop] | default("") %}
        {% set coop_data = lookups['eu_coop'][coop] | default({}) %}
        {% set coop_cargill_id = coop_data["ID"] | default("") %}
        {% set coop_name = coop_data["Name_contract"] | default("") %}
        {% set coop_addr1 = coop_data["House_Number"] | default("") %}
        {% set coop_addr2 = coop_data["Street"] | default("") %}
        {% set coop_addr3 = coop_data["City"] | default("") %}
        {% set coop_addr4 = coop_data["Postal_Code"] | default("") %}
        {% set coop_RCS   = coop_data["RCS"] | default("") %}
        {% set coop_addr = coop_addr1 + " " + coop_addr2 + " " + coop_addr3 + " " + coop_addr4 %}
        {% set ns = namespace(has_pollinator_engagement=false, has_agroforestry=false) %}
        {% for field in fields %}
            {% if field.note.ASSIGN_PRACTICES and ("pollinator" in field.note.ASSIGN_PRACTICES|lower or "mise en place de ruches" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_pollinator_engagement = true %}
            {% endif %}
            {% if field.note.ASSIGN_PRACTICES and ("agroforestry" in field.note.ASSIGN_PRACTICES|lower or "agroforesterie" in field.note.ASSIGN_PRACTICES|lower) %}
                {% set ns.has_agroforestry = true %}
            {% endif %}
        {% endfor %}
        {% set has_pollinator_engagement = ns.has_pollinator_engagement %}
        {% set has_agroforestry = ns.has_agroforestry %}

        {% set CARGILL_ACCOUNT_ID_CUSTOM_INPUT = custom_inputs["Cargill account ID"] | default("") %}
        {% if CARGILL_ACCOUNT_ID_CUSTOM_INPUT != "" %}
            {% set cargill_account_id =  CARGILL_ACCOUNT_ID_CUSTOM_INPUT %}
        {% endif %}

        <table cellspacing="0" cellpadding="0" style="border: none; border-collapse: separate;">
            <tr>
                <!-- Top right corner -->
                <td style="text-align: right;">
                    {{ user_fname }} {{ user_lname }}<br>
                    {{ user_email }}<br>
                    {{ cargill_account_id }}<br>
                    {% if user_phone_number %}
                    {{ user_phone_number }}<br />
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;" >
                    <!-- Contract heading -->
                    <p><strong>Annexe A au Contrat Agriculteur Cargill RegenConnect</strong><sup>®</sup></p>
                </td>
            </tr>
        </table>

        <!-- Contract 1st paragraph -->
        <p>
            La présente Annexe A, en vigueur à compter du {{ current_date.strftime('%d') }}/{{ current_date.strftime('%m') }}/{{ current_date.strftime('%Y') }}, fait partie intégrante du Contrat Agriculteur Cargill RegenConnect<strong>®</strong> (« <strong>Contrat</strong> »), entre {{ legal_name }} situé à {{ user_address }} {{ user_city }} {{ user_state }} {{ user_postal_code }} et immatriculé sous le numéro de RCS ou SIRET {{ SIRET }}, (« <strong>Participant</strong> ») et {{ coop_name }}, situé à {{ coop_addr }} et immatriculé sous le numéro de RCS {{ coop_RCS }} (« <strong>Collecteur</strong> »).
        </p>

        <!-- Contract 2nd paragraph -->
        <p>
            <strong>ATTENDU QUE</strong> tous les termes commençant par une majuscule et qui ne sont pas définis dans la présente Annexe A ont la signification qui leur est attribuée dans le Contrat Agriculteur ;
        </p>

        <!-- Contract 3rd paragraph  -->
        <p>
            <strong>ATTENDU QUE</strong> le Portail est le principal moyen de mesurer, de quantifier et de vérifier les changements de Pratiques Agricoles du Participant et leurs impacts environnementaux, et que Regrow est l'administrateur du Portail qui collaborera étroitement avec le Participant, Cargill et le Collecteur pour (i) vérifier la mise en œuvre des pratiques agricoles régénératrices sur les Parcelles du Participant, conformément aux termes du Contrat (ii) déterminer l’amélioration de la séquestration de carbone, l'amélioration de la santé des sols et autres résultats environnementaux positifs.
        </p>

        <!-- Contract 4th paragraph -->
        <p><strong>MAINTENANT, PAR CONSÉQUENT,</strong> le Participant et le Collecteur conviennent de ce qui suit :</p>

        <ol type="1">

            <li><!-- 1. Term -->
                <p>L’Annexe A présente les détails de l'engagement du Participant pour la Campagne 2026.</p>
            </li>

            <li style="page-break-before:always"><!-- 2. Appendix A heading-->
            </li>

            <!-- Regenerative practices selection-->
            <!-- <blockquote> -->
                <!-- <p>Pratiques Régénératives sélectionnées</p> -->
                <p><strong>Pratiques régénératrices déclarées pour la saison <br>2025-2026</strong></p>
            <!-- </blockquote> -->

            <!-- Table of regenerative practices -->
            <table width="100%" style="table-layout: fixed;">
                <thead style="vertical-align: middle;">
                    <tr class="header">
                        <th scope="col" style="padding-left:10px;text-align:left;">Exploitation
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Parcelle
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Campagne de référence
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Surface (ha)
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Pratiques Régénératrices 2025-2026
                        </th>
                        <th scope="col" style="padding-left:10px;text-align:left;">Paiement estimé (€)
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in fields %}
                    <tr>
                        <td style="padding-top:10px;">{{ row.note["farm_name"]}}</td>
                        <td style="padding-top:10px;">{{ row.note["field_name"]}}</td>
                        <td style="padding-top:10px;">{{ "" if row.note["baseline_year"] == None else row.note["baseline_year"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.field_area == None else "%.2f"|format(row.field_area) }}</td>
                        <td style="padding-top:10px;">{{ row.note["ASSIGN_PRACTICES"] }}</td>
                        <td style="padding-top:10px;">{{ "" if row.value == None else "%.2f"|format(row.value) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- total estimation-->
            <!-- <blockquote> -->
                <div style="text-align:left;">
                    <div style="margin-bottom:7px;">
                        Estimation de la séquestration totale
                    </div>

                    <div style="font-size:12px;color:#6f6f6f;margin:7px 0;">
                        pour parcelles, {{ "%.2f"|format(total_area) }} {{ user_units }}
                    </div>

                    <table cellpadding="0" cellspacing="0" border="0" style="padding:10px;text-align:left;line-height:1;width:100%;box-sizing:border-box;border:1px solid #e0e2e3;">
                        <tbody>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Intérêt déclaré pour l'agroforesterie
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Oui" if has_agroforestry else "Non" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="width: 70%;">
                                    Engagement pollinisateurs
                                </td>
                                <td style="text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "Oui (250" + currency_char + ")" if has_pollinator_engagement else "Non" }}</b>
                                </td>
                            </tr>
                            <tr style="vertical-align:top">
                                <td style="padding-top:10px; width: 70%;">
                                    Estimation du paiement total (37 EUR / t CO2 / an)
                                </td>
                                <td style="padding-top:10px;text-align:right; width: 30%; word-wrap: break-word; word-break: break-all;">
                                    <b>{{ "%.2f"|format(payment) }}{{ currency_char }}</b>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <!-- </blockquote> -->

            <ol start="3" type="1">
                <li><!-- 3. Historical data -->
                    <p><u>Utilisation du Portail :</u> Le Participant doit télécharger sur le Portail (i) ses Parcelles ainsi que (ii) l’historique de l’itinéraire cultural pour chaque Parcelle. L’historique de l’itinéraire cultural comprend les éléments suivants pour les cinq (5) années précédant la Campagne 2026 : cultures principales (date de semis et de récolte), couverts végétaux le cas échéant (date de semis et de destruction) et type de travail du sol. Le Portail génère ensuite des estimations de séquestration et de paiement du carbone séquestré pour chaque Parcelle.</p>
                </li>

                <li><!-- 4. Measurements-->
                    <p><u>Mesure :</u> À la fin de la saison, le Participant doit confirmer via le Portail la mise en œuvre et la réalisation complètes des Pratiques Agricoles énoncées à l'article 2 ci-dessus. </p>
                </li>

                <li><!-- 5. Verification-->
                    <p><u>Vérification :</u> La télédétection (via la technologie satellitaire) sera réalisée par Cargill/Regrow pour vérifier que les Pratiques Agricoles convenues ont été mises en œuvre par le participant sur les Parcelles inscrites. Les conflits au niveau de la Parcelle sont résolus en vérifiant les registres du Participant. A la demande de Cargill, si une Pratique Agricole ne peut être confirmée par télédétection, le Participant est tenu de fournir les documents ou les registres qui confirment la mise en œuvre des Pratiques Agricoles contractuelles</p>
                </li>

                <li><!-- 6. Final results-->
                    <p><u>Résultats finaux :</u> Sur la base des points 2 à 5 ci-dessus, Regrow et Cargill calculeront le changement net final du taux de séquestration du carbone organique dans le sol, des émissions de méthane et de N2O, converties en CO2 équivalent, entre les pratiques de référence et les nouvelles interventions.</p>
                </li>

                <li><!-- Measurements requirements-->
                    <p><u>Exigences relatives à la Mesure :</u> Pendant la phase de mesure, le Participant doit renseigner sur le Portail plusieurs informations pour la campagne concernée. Cela inclut, sans s'y limiter, les éléments suivants :</p>
                    <ul>
                        <li>
                            <p>Confirmer la culture principale</p>
                        </li>
                        <li>
                            <p>Confirmer les changements de pratiques mis en œuvre (travail du sol et cultures de couverture)</p>
                        </li>
                        <li>
                            <p>Rendement des cultures</p>
                        </li>
                        <li>
                            <p>Gestion des résidus de cultures</p>
                        </li>
                        <li>
                            <p>Détails sur le travail du sol (profondeur du travail du sol)</p>
                        </li>
                        <li>
                            <p>Méthode d'irrigation</p>
                        </li>
                    </ul>
                </li>

                <li><!-- Discrepancy-->
                    <p><u>Bénéfices additionnels :</u> Lors des phases de Mesures et de vérification des Pratiques Culturales engagées, Cargill effectuera un calcul d’indicateurs de potentiels bénéfices environnementaux additionnels concernant la diversité des cultures, la durée de couverture du sol et l'impact du semis-direct sur la rétention hydrique dans les zones à faible ressource en eau (<strong>« Bénéfices Additionnels »</strong>). Sur la base de ces calculs, Cargill pourra décider d'accorder au Participant une rémunération additionnelle par hectare pour les Bénéfices Additionnels (<strong>« Rémunération Additionnelle »</strong>)</p>
                    <p>Pour la Campagne 2026, les Bénéfices Additionnels comprendront les éléments suivants :
                        <ul class="dash-list">
                            <li>Prime à la diversité des cultures : selon le nombre d'espèces mesurées sur les Parcelles engagées, jusqu'à 2 €/ha de Rémunération Additionnelle.</li>
                            <li>Taux de couverture du sol : selon le nombre de jours sous couverture végétale au cours des 365 jours précédant la récolte de la Campagne 2026 sur les Parcelles engagées, jusqu'à 1 €/ha de Rémunération Additionnelle.</li>
                            <li>Semis-direct sur les zones à faibles ressources en eau : selon la localisation des Parcelles mesurées en semis -direct, jusqu'à 1 €/ha de Rémunération Additionnelle.</li>
                        </ul>
                    </p>
                </li>

                <li>
                    <p><u>Prime de pollinisation :</u> À compter de la Campagne 2026, le Participant pourra s'engager à mettre en œuvre une pratique régénératrice supplémentaire visant à soutenir les populations de pollinisateurs (incluant les pollinisateurs sauvages) en grandes cultures. Cette pratique comprend la mise en place d'une coopération avec des apiculteurs professionnels agréés localement, autour d’actions telles que la facilitation de l'accès aux champs, l'installation d'abris, de zones d'ombrage, etc. Dans le cadre de la mesure, le participant devra fournir le numéro d'enregistrement de l’apiculteur concerné (NAPI). Sur la base d’un tel numéro d’enregistrement, Cargill, par l’intermédiaire du collecteur, fournira au Participant une prime de 250 EUR sous forme de paiement annuel forfaitaire par exploitation participante (<strong>« Prime de Pollinisation »</strong>).</p>
                </li>

                <li>
                    <p><u>Campagne agricole 2026 :</u> La Rémunération Additionnelle et la Prime de Pollinisation s'ajoutent à la Rémunération (telle que définie dans le Contrat Agriculteur) et sont payables comme partie du Montant Total de Rémunération. La Rémunération Additionnelle et la Prime de Pollinisation s'appliquent uniquement à la Campagne 2026 et Cargill se réserve le droit de réexaminer la question du versement d’une Rémunération Additionnelle et d’une Prime de Pollinisation pour les campagnes agricoles futures.</p>
                </li>

                <li>
                    <p><u>Agroforesterie :</u> Dans le cadre de la déclaration de ses pratiques, le Participant pourra manifester sa volonté de mettre en œuvre des pratiques régénératrices supplémentaires sous forme d'agroforesterie. Cette manifestation d'intérêt ne donnera lieu à aucune prime Rémunération pour le Participant. Cargill s'efforcera d'établir des liens avec d'autres tiers intéressés, ce qui pourrait conduire à une mise en place collaborative de la pratique, à définir dans un contrat distinct.</p>
                </li>

                <li>
                    <p><u>Travail du sol Adaptatif :</u> Par dérogation au point 3 du Contrat agriculteur, concernant la phase de Mesures pour la Campagne 2025 et les Campagnes suivantes, les règles suivantes s'appliquent au travail du sol conventionnel :</p>
                    <ul>
                        <li>
                            <p>Le travail du sol conventionnel ne pourra pas être déclaré comme pratique prévue pour une Campagne donnée (les Parcelles pour lesquelles un travail du sol conventionnel est initialement prévu ne seront pas enregistrées). Toutefois,</p>
                        </li>
                        <li>
                            <p>La déclaration du travail du sol conventionnel, sous réserve de confirmation par Cargill, pourra être exceptionnellement acceptée (travail du sol adaptatif) lors de la phase de Mesures (c'est-à-dire après la récolte d'une Campagne donnée), au maximum une fois sur les trois années d’engagement pour une Parcelle donnée si une exception (comme indiquée ci-dessous) s’applique ;</p>
                        </li>
                        <li>
                            <p>La liste des exceptions tolérées est la suivante : des conditions météorologiques défavorables telles qu'inondations ou précipitations excessives, une nécessité phytosanitaire en cas d’infestation par des adventices, des conditions défavorables lors de la récolte de la culture précédente (par exemple, un tassement important) ;</p>
                        </li>
                        <li>
                            <p>Si le travail du sol conventionnel est déclaré lors de la troisième Campagne de participation de l'agriculteur, ce dernier accepte de prolonger les droits de surveillance (tels que décrits dans le contrat Agriculteur) à Cargill pour une Campagne supplémentaire ; </p>
                        </li>
                        <li>
                            <p>Le Participant est tenu d'informer Cargill au plus vite par écrit ou par email chaque fois qu'il est nécessaire d'appliquer un travail du sol conventionnel exceptionnel.</p>
                        </li>
                    </ul>
                    <p>L'application d'un travail du sol conventionnel exceptionnel peut entraîner une baisse, voire une valeur négative de la Rémunération Totale pour une Campagne donnée.</p>
                </li>

                <li>
                    <p>À l'exception de ce qui est indiqué ci-dessus, toutes les conditions générales contenues dans le Contrat resteront pleinement en vigueur. En cas de conflit entre la présente Annexe A et le Contrat, les conditions de la présente Annexe A prévaudront.</p>
                </li>

            </ol>

        </ol>
        <br>

        <table width="100%">
            <tbody style="border: none;">
            <tr>
                <td >
                    <strong>Le Participant</strong>
                    <p>Nom/Prénom : {{ user_fname }} {{ user_lname }}</p>
                    <p>Signature : <span style="display: inline-block; color: white;">/sn1/</span></p>
                </td>
            </tr>
            <tr>
                <td >
                    <strong>Le Collecteur</strong>
                    <p>Nom/Prénom : {{ coop_name }}</p>
                    <!-- No co-signer for this program.
                    <p>Signature : <span style="display: inline-block; color: white;">/co_signature/</span></p>
                    -->
                </td>
            </tr>
            </tbody>
        </table>
    </body>
</html>
