from csv import Dict<PERSON><PERSON>er
from datetime import datetime, timezone
from io import <PERSON><PERSON>
from typing import Any, Iterator, Optional
from uuid import UUID

import google.auth
from google.cloud import storage

from config import get_settings
from defaults.attribute_options import (
    <PERSON>ropUsage,
    ResidueHarvested,
    TerminationMethods,
    TillagePractice,
)
from defaults.consts import COVER_CROP_USAGES
from entity_events.data_classes import EventCreationSpecification
from entity_events.event_creators.base_event_creator import (
    base_create_cropping_event,
    base_create_reduction_event,
    base_create_tillage_event,
)
from entity_events.event_creators.cargill_regenconnect_2024_event_creator import (
    default_termination_method_by_harvest_date,
)
from entity_events.event_creators.helpers import harvest_yield_rate_unit_value_to_enums
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.reduction_event import ReductionEvent
from entity_events.events.tillage_event import TillageEvent
from entity_events.measures import Depth
from entity_events.units import LengthUnit
from helper import list_helper
from helper.datetime_helper import parse_datetime_as_utc
from phases.enums import AttributeTypes, PhaseTypes, StageTypes
from values.enums import EntityTypeChoices

settings = get_settings()
env = settings.env if settings.env == "prod" else "dev"

GAPFILLING_BUCKET_NAME = f"flurosense-us-west1-mrv-service-private-{env}"
GAPFILLING_CSV_PREFIX = "cargill-eu-y2-gapfilling"
ENROLLMENT_PHASE_CSV_VALUE = "enrollment"
MONITORING_PHASE_CSV_VALUE = "measurement"

"""
Programs 253, 254, 257, 258, 259

* E Phase CroppingEvent + ReductionEvent
- Crop events for 2019 cultivation cycle and earlier are generated from MRV Values

* E Phase TillageEvent
- Till events for 2019 cultivation cycle and earlier are generated from MRV Values

* E Phase ApplicationEvent
- Not collected. Gapfilled using integration with SS.

* E Phase IrrigationEvent
- Not collected and not gapfilled.
--

* M Phase CroppingEvent + ReductionEvent
- Gapfilled using CSV.

* M Phase TillageEvent
- Gapfilled using CSV.

* M Phase ApplicationEvent
- Not collected. Gapfilled using integration with SS.

* M Phase IrrigationEvent
- Generated using base event creator.
"""


def create_cropping_event_eu_25x_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> None:
    record_year = attribute_type_to_value.get(AttributeTypes.record_year)
    # Only create events for 2019 harvested crops (or earlier), in service of SES migration.
    if not record_year or int(record_year) > 2019:
        return None
    cover_crop_mix = attribute_type_to_value.get(AttributeTypes.cover_crop_mix)
    if not cover_crop_mix:
        return None
    crop_usage = (
        CropUsage.COVER
        if attribute_type_to_value.get(AttributeTypes.cover_crop_mix) in COVER_CROP_USAGES
        else CropUsage.COMMODITY
    )
    attribute_type_to_value[AttributeTypes.crop_usage] = crop_usage
    # Ref https://regrow.atlassian.net/browse/MRV-5235
    if crop_usage == CropUsage.COMMODITY:
        attribute_type_to_value[AttributeTypes.residue_harvested] = ResidueHarvested.percent_00

    if crop_usage == CropUsage.COVER:
        if attribute_type_to_value.get(AttributeTypes.harvest_date):
            attribute_type_to_value[AttributeTypes.winter_crop_termination] = (
                default_termination_method_by_harvest_date(
                    parse_datetime_as_utc(attribute_type_to_value.get(AttributeTypes.harvest_date))
                )
            )
    return base_create_cropping_event(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def create_tillage_event_eu_25x_enrollment(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> None:
    record_year = attribute_type_to_value.get(AttributeTypes.record_year)
    # Only create events for 2019 cycle (or earlier), in service of SES migration.
    if not record_year or int(record_year) > 2019:
        return None
    return base_create_tillage_event(
        attribute_type_to_value=attribute_type_to_value,
        entity_id=entity_id,
        entity_type=entity_type,
        event_id=event_id,
        event_creation_specification=event_creation_specification,
    )


def create_cropping_event_eu_25x_monitoring(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> None:
    """The events will be generated using the gapfilling CSV, so return None."""
    return


def create_tillage_event_eu_25x_monitoring(
    attribute_type_to_value: dict[AttributeTypes, str],
    entity_id: int,
    entity_type: EntityTypeChoices,
    event_id: UUID | None,
    event_creation_specification: EventCreationSpecification,
) -> None:
    """The events will be generated using the gapfilling CSV, so return None."""
    return


def post_create_eu_25x_events(
    events_by_phase_stage: dict[PhaseTypes, dict[StageTypes, list[EntityEvent]]],
    values_data: dict[PhaseTypes, dict[int, dict[StageTypes, dict[UUID | int, dict[AttributeTypes, str]]]]],
) -> dict[PhaseTypes, dict[StageTypes, list[EntityEvent]]]:
    field_ids: set[int] = set()
    for phase_type in values_data:
        for entity_id in values_data[phase_type]:
            field_ids.add(entity_id)
    return _create_events_from_gapfilling_csv(events_by_phase_stage=events_by_phase_stage, field_ids=field_ids)


def _set_ids_on_gapfilled_events(events_by_phase_stage: dict[PhaseTypes, dict[StageTypes, list[EntityEvent]]]) -> None:
    """
    Set a row_id-like ID on the events in the dict. This is necessary because the events are created from a CSV file
    so don't have IDs set. This allows the SES migration to generate consistent unique event IDs for the events.
    """
    for events_by_stage in events_by_phase_stage.values():
        for events in events_by_stage.values():
            events_by_field = list_helper.group_by(events, lambda ev: ev.entity_id)
            for events in events_by_field.values():
                curr_row_id = 0
                for ev in events:
                    ev.id = curr_row_id
                    curr_row_id += 1


def _create_events_from_gapfilling_csv(
    events_by_phase_stage: dict[PhaseTypes, dict[StageTypes, list[EntityEvent]]], field_ids: set[int]
) -> dict[PhaseTypes, dict[StageTypes, list[EntityEvent]]]:
    gapfilling_csv = _get_gapfilling_csv()
    reader = DictReader(gapfilling_csv)
    for row in reader:
        if int(row["mrv_field_id"]) not in field_ids:
            continue
        if row["phase"] not in [ENROLLMENT_PHASE_CSV_VALUE, MONITORING_PHASE_CSV_VALUE]:
            continue
        phase_type = PhaseTypes.ENROLMENT if row["phase"] == ENROLLMENT_PHASE_CSV_VALUE else PhaseTypes.MONITORING
        if row["crop_name"] != "":
            stage_type = StageTypes.HISTORICAL_CROP_ROTATION
            if cropping_event := _create_cropping_event_from_gapfilling_csv(row):
                events_by_phase_stage[phase_type][stage_type].append(cropping_event)
        else:
            stage_type = StageTypes.HISTORICAL_TILLAGE if phase_type == PhaseTypes.ENROLMENT else StageTypes.TILLAGE
            if tillage_event := _create_tillage_event_from_gapfilling_csv(row):
                events_by_phase_stage[phase_type][stage_type].append(tillage_event)
    _set_ids_on_gapfilled_events(events_by_phase_stage)
    return events_by_phase_stage


def _get_gapfilling_csv() -> StringIO:
    gcp_credentials, gcp_project = google.auth.default()
    gcs_client = storage.Client(gcp_project, credentials=gcp_credentials)
    blobs = gcs_client.list_blobs(GAPFILLING_BUCKET_NAME, prefix=GAPFILLING_CSV_PREFIX)
    latest_csv_blob = _get_latest_csv_blob(blobs)
    return StringIO(latest_csv_blob.download_as_text())


def _get_latest_csv_blob(blobs: Iterator[storage.Blob]) -> storage.Blob:
    latest_blob = None
    latest_created = None
    for blob in blobs:
        if not blob.name.endswith(".csv"):
            continue
        if latest_created is None or blob.time_created > latest_created:
            latest_blob = blob
            latest_created = blob.time_created
    return latest_blob


def _create_cropping_event_from_gapfilling_csv(cropping_row: dict[str, Any]) -> Optional[CroppingEvent]:
    planting_datetime = datetime.strptime(cropping_row["plant_date"], "%Y-%m-%d")
    harvest_datetime = datetime.strptime(cropping_row["harvest_date"], "%Y-%m-%d")
    yield_rate = None
    if cropping_row["yield"] != "":
        yield_num_unit, yield_denom_unit = harvest_yield_rate_unit_value_to_enums(cropping_row["yield_unit"])
        yield_rate = {
            "value": cropping_row["yield"],
            "numerator_unit": yield_num_unit,
            "denominator_unit": yield_denom_unit,
        }
    if not cropping_row["crop_usage"]:
        # Some of the primary data for ineligible fields has no crop_usage, so isn't usable
        return None
    crop_usage = (
        CropUsage.COVER
        if cropping_row["crop_usage"] in {"Basic cover crop", "Premium cover crop"}
        else CropUsage.COMMODITY
    )
    if crop_usage == CropUsage.COVER:
        cropping_row[AttributeTypes.residue_harvested] = None
        if cropping_row["termination_method"] == "":
            cropping_row[AttributeTypes.winter_crop_termination] = default_termination_method_by_harvest_date(
                harvest_datetime
            )
        else:
            cropping_row[AttributeTypes.winter_crop_termination] = TerminationMethods(
                cropping_row["termination_method"]
            )
    else:  # COMMODITY
        cropping_row[AttributeTypes.winter_crop_termination] = None
        if cropping_row["residue_harvested"] == "":
            cropping_row[AttributeTypes.residue_harvested] = ResidueHarvested.percent_00
        else:
            cropping_row[AttributeTypes.residue_harvested] = ResidueHarvested(cropping_row["residue_harvested"])
    reductions = []
    if reduction_event := _create_reduction_event_from_gapfilling_csv(cropping_row):
        reductions.append(reduction_event)
    return CroppingEvent.parse_obj(
        {
            "entity_id": cropping_row["mrv_field_id"],
            "entity_type": EntityTypeChoices.field,
            "interval": {
                "start": planting_datetime.replace(tzinfo=timezone.utc),
                "end": harvest_datetime.replace(tzinfo=timezone.utc),
            },
            "crop_type": cropping_row["crop_name"],
            "crop_usage": crop_usage,
            "crop_yield": yield_rate,
            "termination_method": cropping_row.get(AttributeTypes.winter_crop_termination),
            "residue_harvested": cropping_row.get(AttributeTypes.residue_harvested),
            "reductions": reductions,
        }
    )


def _create_reduction_event_from_gapfilling_csv(cropping_row: dict[str, Any]) -> ReductionEvent | None:
    harvest_datetime = datetime.strptime(cropping_row["harvest_date"], "%Y-%m-%d")
    attribute_type_to_value = {
        AttributeTypes.harvest_date: harvest_datetime.replace(tzinfo=timezone.utc),
        AttributeTypes.crop_usage: CropUsage(cropping_row["crop_usage"]),
        AttributeTypes.winter_crop_termination: cropping_row.get(AttributeTypes.winter_crop_termination),
        AttributeTypes.residue_harvested: cropping_row.get(AttributeTypes.residue_harvested),
    }
    return base_create_reduction_event(attribute_type_to_value)


def _create_tillage_event_from_gapfilling_csv(tillage_row: dict[str, Any]) -> TillageEvent | None:
    tillage_datetime = datetime.strptime(tillage_row["tillage_date"], "%Y-%m-%d")
    return TillageEvent.parse_obj(
        {
            "entity_id": tillage_row["mrv_field_id"],
            "entity_type": EntityTypeChoices.field,
            "occurred_at": tillage_datetime.replace(tzinfo=timezone.utc),
            "depth": (
                Depth(value=tillage_row["tillage_depth"], unit=LengthUnit.CENTIMETRE)
                if tillage_row["tillage_depth"]
                else None
            ),
            "soil_inversion": tillage_row["tillage_inversion"],
            "strip_fraction": None,
            "tillage_practice": TillagePractice(tillage_row["tillage_practice"]),
        }
    )
