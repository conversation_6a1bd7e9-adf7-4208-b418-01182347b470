from datetime import datetime, timezone
from typing import Any

import pytest
from scenarios_service_schema.internal.soil import Soil
from scenarios_service_schema.schema import FieldLocation

from cultivation_cycles.schema import CultivationCycle, CultivationCycleId
from defaults.tests import mock_defaults_translator
from entity_events.tests.helpers import create_structured_program_and_field
from fields.model import Fields
from fields.schema import Field
from programs.enums import ProgramTemplate
from programs.model import Programs
from scenarios_service.generalized_integration.helpers import FieldWithBoundary
from scenarios_service.generalized_integration.inventory_inputs_translator import (
    InventoryInputsTranslator,
)


@pytest.fixture
def inventory_inputs_translator(app_request):
    return InventoryInputsTranslator(
        app_request,
    )


async def mock_are_crops_enabled(regrow_crop_names: list[str]) -> dict[str, bool]:
    return {crop_name: True for crop_name in regrow_crop_names}


def identity_fn(arg1: Any) -> Any:
    return arg1


async def test_get_inventory_input_for_field(
    mdl,
    mdl_factory_ignore_ids,
    orm_select,
    inventory_inputs_translator,
    mock_field_geometry_and_area,
    comprehensive_mrv_values_lookup,
    mocker,
):
    # First set up a Structured Stages program so the config is compatible with the Structured Events Facade
    program_id, _, field_id = await create_structured_program_and_field(
        mdl, mdl_factory_ignore_ids, comprehensive_mrv_values_lookup
    )

    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == field_id]))[0])
    await mdl.SoilsOverride(field_id=field.id, program_id=program_id, md5=field.md5, pred_soc=0.89, pred_bd=1.2)
    program: Programs = (await orm_select(Programs, where=[Programs.id == program_id]))[0]
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
    )
    mock_get_start_year = mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.get_historical_data_start_year_for_field",
        return_value=2019,
    )

    session_input = await inventory_inputs_translator.get_inventory_input_for_field(
        FieldWithBoundary(field, mock_field_geometry_and_area[0], mock_field_geometry_and_area[1]), program
    )

    mock_get_start_year.assert_called()

    assert session_input.field_name == str(field_id)
    assert isinstance(session_input.location, FieldLocation)
    assert len(session_input.cultivation_cycles) == 6
    assert session_input.reporting_information.start_date == "2022-07-16"
    assert session_input.reporting_information.end_date == "2023-07-14"
    assert session_input.reporting_information.crop_name == "corn"
    assert session_input.reporting_information.crop_yield == 150.0
    assert session_input.soil == Soil(soc=0.0089, bulk_density=1.2)
    assert session_input.start_year == 2018  # Earliest event date is used if less than start_year


async def test_get_inventory_input_with_cycle_after_reporting_period(
    mock_reporting_info,
    mock_field_geometry_and_area,
    cultivation_cycles,
    mdl,
    inventory_inputs_translator,
    app_request,
    mocker,
):
    reporting_period_end = datetime(2021, 12, 31)
    # Ensure that at least one cultivation cycle ends after the reporting period end
    assert any(cc.end.date() > reporting_period_end.date() for cc in cultivation_cycles)
    # Create field, project, and program
    program = await mdl.Programs(
        program_template=ProgramTemplate.event_based,
        reporting_period_start_date=datetime(2021, 1, 1),
        reporting_period_end_date=reporting_period_end,
    )
    project = await mdl.Projects(program_id=program.id)
    field = Field.from_orm(await mdl.Fields(parent_project_id=project.id))

    mock_event_based_dependencies(mocker, mock_reporting_info)
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.get_cultivation_cycles",
        return_value=(cultivation_cycles, []),
    )

    inv_input = await inventory_inputs_translator.get_inventory_input_for_field(
        FieldWithBoundary(field, mock_field_geometry_and_area[0], mock_field_geometry_and_area[1]), program=program
    )

    # session_input shouldn't have any phases ending after the reporting period
    assert all(datetime.strptime(cc.end_date, "%Y-%m-%d") < reporting_period_end for cc in inv_input.cultivation_cycles)


async def test_get_inventory_input_earlier_start_year(
    mdl,
    mdl_factory_ignore_ids,
    orm_select,
    inventory_inputs_translator,
    mock_field_geometry_and_area,
    comprehensive_mrv_values_lookup,
    mocker,
):
    # First set up a Structured Stages program so the config is compatible with the Structured Events Facade
    program_id, _, field_id = await create_structured_program_and_field(
        mdl, mdl_factory_ignore_ids, comprehensive_mrv_values_lookup
    )

    field: Field = Field.from_orm((await orm_select(Fields, where=[Fields.id == field_id]))[0])
    program: Programs = (await orm_select(Programs, where=[Programs.id == program_id]))[0]
    mocker.patch(
        "scenarios_service.generalized_integration.helpers.defaults_translator.translate_core_crop_name_to_regrow_name",
        side_effect=mock_defaults_translator.mock_translate_core_crop_name_to_regrow_name,
    )
    mock_get_start_year = mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.helpers.get_historical_data_start_year_for_field",
        return_value=2017,
    )

    session_input = await inventory_inputs_translator.get_inventory_input_for_field(
        FieldWithBoundary(field, mock_field_geometry_and_area[0], mock_field_geometry_and_area[1]), program
    )

    mock_get_start_year.assert_called()

    assert session_input.field_name == str(field_id)
    assert isinstance(session_input.location, FieldLocation)
    assert len(session_input.cultivation_cycles) == 7
    assert session_input.reporting_information.start_date == "2022-07-16"
    assert session_input.reporting_information.end_date == "2023-07-14"
    assert session_input.reporting_information.crop_name == "corn"
    assert session_input.reporting_information.crop_yield == 150.0
    assert session_input.start_year == 2017  # Crop stage start year is used if earlier than events


async def test_get_cultivation_cycles_min_event_date_no_events():
    start = datetime(2022, 1, 1)
    end = datetime(2022, 12, 1)
    min_date = InventoryInputsTranslator.get_cultivation_cycles_min_event_date(
        [CultivationCycle(id=CultivationCycleId(start_date=start, end_date=end), start=start, end=end)],
    )
    assert min_date == datetime.max.replace(tzinfo=timezone.utc)


# helper functions
def mock_event_based_dependencies(mocker, mock_reporting_info):
    # The other session_inputs_translator tests should use this and should read EntityEvents instead of MRV Values.
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.fetch_events_for_simulations.get_events",
        return_value=[],
    )
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.get_cultivation_cycles",
        return_value=([], []),
    )
    mocker.patch("scenarios_service.generalized_integration.session_inputs_translator.verify_crops_are_api_enabled")
    mocker.patch(
        "scenarios_service.generalized_integration.inventory_inputs_translator.input_translator_helpers.get_reporting_info",
        return_value=mock_reporting_info,
    )
