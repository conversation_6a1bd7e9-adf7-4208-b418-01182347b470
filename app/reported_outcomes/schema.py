from datetime import date, datetime
from typing import Optional

from pydantic import BaseModel

from helper.helper import optional
from reported_outcomes.enums import OutcomeApprovalStatusType


@optional
class FieldLevelOutcomesRequest(BaseModel):
    program_id: int
    project_id: int
    field_id: int
    start_date: date
    end_date: date
    crop_name: Optional[str]
    crop_yield: Optional[float]
    credit_share: float
    reversible_credit_share: Optional[float]
    reversible_dsoc_baseline: float
    reversible_dsoc_practice_change: float
    reversible_dsoc_preliminary_credit: float
    reversible_dsoc_mean: Optional[float]
    reversible_dsoc_standard_deviation: Optional[float]
    non_reversible_credit_share: float
    non_reversible_mean: float
    non_reversible_standard_deviation: float
    non_reversible_direct_n2o_baseline: float
    non_reversible_direct_n2o_practice_change: float
    non_reversible_direct_n2o_preliminary_credit: float
    non_reversible_direct_n2o_mean: float
    non_reversible_direct_n2o_standard_deviation: float
    non_reversible_indirect_n2o_baseline: float
    non_reversible_indirect_n2o_practice_change: float
    non_reversible_indirect_n2o_credit: float
    non_reversible_soil_ch4_baseline: Optional[float]
    non_reversible_soil_ch4_practice_change: Optional[float]
    non_reversible_soil_ch4_credit: Optional[float]
    non_reversible_fossil_fuel_co2_baseline: Optional[float]
    non_reversible_fossil_fuel_co2_practice_change: Optional[float]
    non_reversible_fossil_fuel_co2_credit: Optional[float]
    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class FieldLevelOutcomesResponse(FieldLevelOutcomesRequest):
    id: int
    updated_at: datetime


class CropLevelOutcomesRequest(BaseModel):
    program_id: int
    crop_type: str
    start_date: date
    end_date: date
    total_yield: float
    number_of_fields: int
    number_of_acres: float
    total_reversible_credit: float
    total_non_reversible_credit: float
    total_credit: float
    reversible_credit_mean: float
    reversible_credit_standard_deviation: float
    total_reversible_emissions_reductions: float
    non_reversible_credit_mean: float
    non_reversible_credit_standard_deviation: float
    total_non_reversible_emissions_reductions: float
    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class CropLevelOutcomesResponse(CropLevelOutcomesRequest):
    id: int
    updated_at: datetime


class ProgramLevelOutcomesRequest(BaseModel):
    program_id: int
    start_date: date
    end_date: date
    number_of_fields: int
    number_of_acres: float
    total_reversible_credit: Optional[float]
    total_non_reversible_credit: float
    total_credit: float
    reversible_credit_mean: Optional[float]
    reversible_credit_standard_deviation: Optional[float]
    total_reversible_emissions_reductions: Optional[float]
    non_reversible_credit_mean: float
    non_reversible_credit_standard_deviation: float
    total_non_reversible_emissions_reductions: float
    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class ProgramLevelOutcomesResponse(ProgramLevelOutcomesRequest):
    id: int
    updated_at: datetime


class RawProgramOutcomes(BaseModel):
    """
    The field and crop outcomes per program
    """

    program_id: int
    field_outcomes: dict[int, FieldLevelOutcomesRequest]
    crop_outcomes: dict[str, CropLevelOutcomesRequest]
    program_outcomes: Optional[ProgramLevelOutcomesRequest]


class ReportedProgramOutcomes(RawProgramOutcomes):
    """
    The program results with metainformation about the run they were gathered from
    """

    task_id: Optional[str]
    is_test_run: bool


class OutcomeApprovalStatus(BaseModel):
    program_id: int
    task_id: str
    outcome_approval_status: OutcomeApprovalStatusType
    created_at: datetime
    updated_at: datetime


@optional
class FieldLevelInventoryOutcomesRequest(BaseModel):
    program_id: int
    project_id: int
    field_id: int
    crop_name: str
    mapped_acres: float
    total_yield: float
    net_emissions_percentage: float
    net_emissions_factor: float
    ghg_emissions_factor: float
    soc_emissions_factor: float
    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class FieldLevelInventoryOutcomesResponse(FieldLevelInventoryOutcomesRequest):
    id: int
    updated_at: datetime


class CropLevelInventoryOutcomesRequest(BaseModel):
    program_id: int
    crop_type: str
    number_of_fields: int
    mapped_acres: float
    total_yield: float
    total_emissions: float
    total_emissions_per_bushel: float
    direct_n2o_emissions: float
    indirect_n2o_emissions: float
    soil_ch4_emissions: float
    soc: float
    soc_per_bushel: float
    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class CropLevelInventoryOutcomesResponse(CropLevelInventoryOutcomesRequest):
    id: int
    updated_at: datetime


class RawInventoryProgramOutcomes(BaseModel):
    """
    The field and crop outcomes per inventory program
    """

    program_id: int
    field_inventory_outcomes: dict[int, FieldLevelInventoryOutcomesRequest]
    crop_inventory_outcomes: dict[str, CropLevelInventoryOutcomesRequest]


class ReportedInventoryProgramOutcomes(RawInventoryProgramOutcomes):
    """
    The inventory program results with metainformation about the run they were gathered from
    """

    task_id: Optional[str]
    is_test_run: bool


class FieldLevelBiofuelsOutcomesRequest(BaseModel):
    program_id: int
    project_id: int
    field_id: int

    created_at: datetime
    task_id: Optional[str]

    class Config:
        orm_mode = True


class FieldLevelBiofuelsOutcomesResponse(CropLevelInventoryOutcomesRequest):
    id: int
    updated_at: datetime


class ReportedBiofuelsProgramOutcomes(BaseModel):
    """
    The inventory program results with metainformation about the run they were gathered from
    """

    program_id: int
    # TO-DO: actual biofuels outcome stuff here
    field_inventory_outcomes: dict[int, FieldLevelBiofuelsOutcomesRequest]
    task_id: Optional[str]
    is_test_run: bool
