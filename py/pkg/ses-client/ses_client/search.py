import json
from dataclasses import asdict, dataclass
from datetime import datetime, timezone
from typing import Any, Dict, Iterable, List, Optional, Union

import httpx  # type: ignore[import-not-found]
from google.protobuf.json_format import ParseDict
from regrow.ses.event.v1 import event_pb2


@dataclass
class Filter:
    exclude_archived: bool = True
    event_types: List[str] | None = None
    interval_from: datetime | None = None
    interval_to: datetime | None = None
    min_overlap: float | None = None
    user_ids: List[str] | None = None
    owner_ids: List[str] | None = None
    actor_ids: List[str] | None = None
    program_ids: List[str] | None = None
    project_ids: List[str] | None = None

    def _normalize_overlap_value(self, min_overlap: float) -> float:
        """
        Normalize overlap value to percentage format (1-100).

        Auto-detects format:
        - Values 0.0-1.0 are treated as fractions and converted to percentages
        - Values 1.0-100.0 are treated as percentages and used as-is
        - Edge case: 1.0 is ambiguous but treated as 100% (reasonable default)

        Args:
            min_overlap: Overlap value in either fraction (0.0-1.0) or percentage (1-100) format

        Returns:
            Overlap value in percentage format (1-100)

        Raises:
            ValueError: If value is outside valid ranges
        """
        if min_overlap < 0:
            raise ValueError("min_overlap cannot be negative")
        elif min_overlap > 100:
            raise ValueError("min_overlap cannot exceed 100%")
        elif min_overlap <= 1.0:
            # Treat as fraction, convert to percentage
            return min_overlap * 100.0
        else:
            # Already in percentage format
            return min_overlap

    def as_dict(self) -> dict[str, Any]:
        data_dict = asdict(self)
        if self.interval_from is not None:
            data_dict["interval_from"] = (
                self.interval_from.astimezone(timezone.utc)
                .isoformat()
                .replace("+00:00", "Z")
            )
        if self.interval_to is not None:
            data_dict["interval_to"] = (
                self.interval_to.astimezone(timezone.utc)
                .isoformat()
                .replace("+00:00", "Z")
            )

        # Auto-detect and normalize min_overlap format
        if self.min_overlap is not None:
            data_dict["min_overlap"] = self._normalize_overlap_value(self.min_overlap)

        filtered_dict = {k: v for k, v in data_dict.items() if v is not None}
        return filtered_dict

    def as_json(self) -> str:
        def serialize(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()  # Convert datetime to ISO 8601 string
            raise TypeError("Type not serializable")

        filtered_dict = self.as_dict()
        return json.dumps(filtered_dict, default=serialize)


class Search:
    """Handles calls to the event-search service."""

    RETURN_EVENT_IDS = "ids"
    RETURN_SHORT_EVENTS = "cache"
    RETURN_EVENTS = ""
    RETURN_EVENTS_WITH_GEOM = "complete"

    def __init__(self, search_service_url: str):
        self.url = search_service_url

    async def _upsert_field_events(self, field_md5: str, field_events: List[dict]):
        """Upsert a key in the event cache. Note this is not for general consumption and should only be used for testing.
        The /keys endpoint should be disabled in a proper deployment.
        """
        body = field_events
        async with httpx.AsyncClient() as client:
            res = await client.post(f"{self.url}/fieldevents/{field_md5}", json=body)
        if res.status_code != 201:
            raise ValueError(f"Failed to upsert field events: {res.text}")

    def _event_search_payload(
        self,
        field_ids: Union[str, List[str]],  # Allow a single string or a list of strings
        search_filter: Optional[Filter] = None,
        return_type: str = RETURN_SHORT_EVENTS,
    ) -> dict:
        """
        Build the payload for an event search request.

        :param field_ids: A single field ID or a list of field IDs.
        :param search_filter: An optional filter to apply to the search.
        :param return_type: The type of response to return (e.g., short or full events).
        :return: The payload dictionary for the search request.
        """
        if not isinstance(field_ids, Iterable) or isinstance(field_ids, str):
            field_ids = [field_ids]

        if not isinstance(field_ids, list):
            raise ValueError("field_ids must be a list or a single string.")

        if not all(isinstance(fid, str) for fid in field_ids):
            raise ValueError("All elements of field_ids must be strings.")

        payload: Dict[str, Any] = {
            "field_ids": field_ids,
            "return": return_type,
        }
        if search_filter is not None:
            payload["filter"] = search_filter.as_dict()

        return payload

    async def _events_raw_response(self, request_body: dict) -> httpx.Response:
        """
        Make a search request to the external service.

        :param request_body: The payload for the POST request.
        :return: Either the raw response object or the parsed JSON response.
        :raises ValueError: If the request fails with a non-200 status code.
        """
        try:
            async with httpx.AsyncClient() as client:
                res = await client.post(f"{self.url}/events", json=request_body)
            res.raise_for_status()  # Raises an HTTPError for non-2xx responses
        except httpx.RequestError as e:
            raise ValueError(f"Failed to search events: {e}") from e

        return res

    async def _events_json_response(self, request_body: dict) -> Dict[str, Any]:
        """
        Make a search request to the external service and return the parsed JSON response.

        :param request_body: The payload for the POST request.
        :return: The JSON response.
        """
        res = await self._events_raw_response(request_body)
        return res.json()

    def _parse_events(self, json_response: dict) -> Dict[str, List[event_pb2.Event]]:
        """
        Parse a JSON search response into a dictionary mapping field IDs
        to lists of Protobuf Event objects.

        :param json_response: The JSON response from the search request.
        :return: A dictionary mapping field IDs to lists of Event objects.
        """
        parsed_events = {}

        for field_id, field_data in json_response.items():
            events = field_data.get("events", [])
            parsed_events[field_id] = [
                ParseDict(event, event_pb2.Event(), ignore_unknown_fields=True)
                for event in events
            ]

        return parsed_events

    async def pb_events_for_fields(
        self, field_ids: List[str], search_filter: Filter | None = None
    ) -> Dict[str, List[event_pb2.Event]]:
        """
        Fetch events for a list of fields and return them in a dictionary mapping
        field IDs to lists of Event protobuf objects.

        :param field_ids: A list of field IDs to fetch events for.
        :param search_filter: An optional filter to apply to the search.
        :return: A dictionary where keys are field IDs and values are lists of Event protobuf objects.
        """
        body = self._event_search_payload(
            field_ids, search_filter=search_filter, return_type=self.RETURN_EVENTS
        )
        res: Dict[Any, Any] = await self._events_json_response(body)
        # parse the response to list of event_pb2.Event
        field_events: Dict[str, List[event_pb2.Event]] = {}
        for field_id in res:
            if "events" in res[field_id]:
                for event in res[field_id]["events"]:
                    if field_id not in field_events:
                        field_events[field_id] = []
                    field_events[field_id].append(
                        ParseDict(event, event_pb2.Event(), ignore_unknown_fields=True)
                    )

        return field_events

    async def fetch_pb_event_ids_for_fields(
        self, field_ids: List[str], search_filter: Filter | None = None
    ) -> Dict[str, List[str]]:
        """
        Fetches a list of event ids for each of the specified fields.

        :param field_ids: A list of field IDs to fetch events for.
        :param search_filter: An optional filter to apply to the search.
        :return: A dictionary where keys are field ID and value is a list of event ids.
        """
        body = self._event_search_payload(
            field_ids, search_filter=search_filter, return_type=self.RETURN_EVENT_IDS
        )
        res: Dict[str, List[str]] = await self._events_json_response(body)
        field_event_ids: Dict[str, List[str]] = {}
        for field_id, event_ids in res.items():
            field_event_ids[field_id] = event_ids

        return field_event_ids

    async def field_events(
        self,
        field_ids: List[str],
        search_filter: Filter | None = None,
        return_type: str = RETURN_EVENTS,
    ):
        """Run the search for the given fields and apply the search filter."""
        if field_ids is None or len(field_ids) == 0:
            return []

        body = self._event_search_payload(field_ids, search_filter, return_type)
        async with httpx.AsyncClient() as client:
            res = await client.post(f"{self.url}/events", json=body)
        if res.status_code != 200:
            raise ValueError(f"Failed to search events: {res.text}")
        return res.json()

    async def async_cache_update(self, field_id: str):
        """Trigger an async cache update task for the given field."""
        body = {"field_ids": [field_id]}
        async with httpx.AsyncClient() as client:
            res = await client.post(f"{self.url}/cache/async", json=body)
        if res.status_code != 202:
            raise ValueError(f"Failed to trigger async cache update: {res.text}")

    async def update_field_event(self, field_id: str, event_id: str) -> dict:
        """Update the cache with a new event."""
        async with httpx.AsyncClient() as client:
            res = await client.post(
                f"{self.url}/fieldevents/{field_id}/{event_id}", json={}
            )
        if res.status_code != 201:
            raise ValueError(f"Failed to fast update cache: {res.text}")
        return res.json()

    async def delete_field_event(self, field_id: str, event_id: str) -> dict:
        """Delete an event from the cache."""
        async with httpx.AsyncClient() as client:
            res = await client.delete(f"{self.url}/fieldevents/{field_id}/{event_id}")
        if res.status_code != 200:
            raise ValueError(f"Failed to fast delete cache: {res.text}")
        return res.json()

    async def get_last_update_for_field(self, field_id: str) -> datetime | None:
        """Get the last update timestamp for a field."""
        async with httpx.AsyncClient() as client:
            res = await client.get(f"{self.url}/fields/{field_id}/last-update")
        if res.status_code == 404:
            return None
        if res.status_code != 200:
            raise ValueError(f"Failed to get last update for field {field_id}: {res}")
        if "last_update" not in res.json():
            return None
        return datetime.fromisoformat(res.json()["last_update"])
