import itertools
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>, timezone
from typing import Any, Optional

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from gcloud.aio.storage import Storage
from geojson_pydantic import MultiPolygon, Polygon
from scenarios_service_schema.assets_base import BaselineMethodEnum
from scenarios_service_schema.biofuels import BiofuelsInput
from scenarios_service_schema.explore_api import ExploreAPIInput
from scenarios_service_schema.inventory import InventoryInput
from scenarios_service_schema.public.units import AreaUnitEnum
from scenarios_service_schema.schema import (
    ProtocolEnum,
    SessionInput,
    Soil,
)

import scenarios_service.methods as ss_methods
from boundaries_service import client as boundaries_client
from config import get_settings
from cultivation_cycles.schema import CultivationCycle
from defaults import defaults_translator
from defaults.attribute_options import CropUsage
from defaults.consts import (
    BIOFUELS_API_ENABLED,
    MEASURE_API_ENABLED,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fire_event import FireEvent
from fields.baselines.methods import get_or_create_fields_baseline
from fields.db import get_fields_status_and_program
from fields.enums import FieldStatus
from fields.schema import Field
from logger import get_logger
from phases import db as phases_db
from phases.enums import PhaseTypes, StageTypes
from phases.model import Stage
from programs import db as programs_db
from programs.enums import AccountingMethod, BaselineMethod, Protocols
from programs.model import Programs
from root_crud import get
from scenarios_service.enums import MeasureProtocolResponseTypes
from scenarios_service.generalized_integration.schema import (
    ScenariosServiceIntegrationError,
)
from scenarios_service.model import (
    DndcSimulationRequests,
    ScenariosServiceApi,
    ScenariosServiceIntegrationErrorCode,
)
from scenarios_service.utils import slugify
from soils.model import SoilsOverride

logger = get_logger(__name__)
settings = get_settings()

# some programs should allow us to simulate fields that have voided contracts
CONTRACT_VOIDED_ELIGIBLE_PROGRAMS = [275]


def convert_protocol_for_ss(protocol: Optional[Protocols]) -> ProtocolEnum:
    if protocol is None:
        raise NotImplementedError("Program must set a protocol")
    if protocol == Protocols.SUSTAINCERT:
        raise NotImplementedError("Measure API integration behavior for Cargill Sustaincert protocol is not defined.")
    protocols_lookup = {
        Protocols.CAR_SEP: ProtocolEnum.SEP,
        Protocols.VERRA: ProtocolEnum.VM0042,
        Protocols.GENERAL_SCOPE_3: ProtocolEnum.GENERAL_SCOPE_3,
    }
    return protocols_lookup.get(protocol)


def lookup_measure_protocol_response_type(protocol: Optional[Protocols]) -> MeasureProtocolResponseTypes:
    if protocol is None:
        logger.warn("Protocol was not set; assuming an older Scope 3 program")
        return MeasureProtocolResponseTypes.scope_3
    if protocol in [Protocols.GENERAL_SCOPE_3, Protocols.SUSTAINCERT]:
        return MeasureProtocolResponseTypes.scope_3
    if protocol in [Protocols.CAR_SEP, Protocols.VERRA]:
        return MeasureProtocolResponseTypes.scope_1
    raise NotImplementedError(f"Output response type is not defined for protocol {protocol}")


def get_baseline_method_for_protocol(protocol: Protocols, baseline_method: BaselineMethod) -> BaselineMethodEnum:
    baseline_method_map: dict[BaselineMethod, BaselineMethodEnum] = {
        BaselineMethod.ROTATIONAL: BaselineMethodEnum.ROTATIONAL,
        BaselineMethod.BLENDED: BaselineMethodEnum.BLENDED,
        BaselineMethod.MATCHED: BaselineMethodEnum.MATCHED,
        BaselineMethod.CLIMATE_MATCHED: BaselineMethodEnum.CLIMATE_MATCHED,
        BaselineMethod.CLIMATE_MATCHED_RICE: BaselineMethodEnum.CLIMATE_MATCHED_RICE,
    }
    if baseline_method is None:
        raise ValueError("Program must have a baseline method configured to use baseline autogeneration.")
    # Protocols SUSTAINCERT and GENERAL_SCOPE_3 can use any baseline method
    elif protocol == Protocols.VERRA:
        if baseline_method not in (
            BaselineMethod.ROTATIONAL,
            BaselineMethod.CLIMATE_MATCHED,
            BaselineMethod.CLIMATE_MATCHED_RICE,
        ):
            raise ValueError(
                "Rotational, climate matched, and climate matched rice are the only allowed baseline methods for under Verra VM0043."
            )
    elif protocol == Protocols.CAR_SEP:
        if baseline_method not in (BaselineMethod.BLENDED, BaselineMethod.MATCHED):
            raise ValueError('CAR SEP programs can only use the "blended" and "matched" baseline methods.')
    return baseline_method_map[baseline_method]


def get_baseline_method(program: Programs) -> BaselineMethodEnum:
    return get_baseline_method_for_protocol(program.protocol, program.baseline_method)


async def get_historical_data_start_year_for_field(request: Request, field_id: int) -> int:
    """Uses the program crediting year, the start year of the E Phase cropping stage, and the field's
    baseline year to figure out when the field's historical period should begin."""

    program = await programs_db.get_program_from_field_id(request, field_id)

    """Regenconnect US has repeatedly dropped the oldest year when migrating from one program year to the next.
    This is really something we should stop doing, but in the meantime we need to hard-code the start dates for those
    programs."""
    # It's unclear whether 155 will use the generalized integration for final outcomes, so this is just in case.
    if program.id == 155:
        return 2018
    elif program.id == 275:
        return 2021
    elif program.id in [1119, 253, 254, 257, 258, 259]:
        return 2019

    num_historical_years = await get_program_num_historical_years(request, program)

    # A better solution would be to use mrv_fields_history which won't reflect baseline resets.
    # The result is not using original entire history if we reset baseline, but that's ok for now.
    baseline = await get_or_create_fields_baseline(request, field_id)
    if not baseline:
        raise ValueError(f"Field {field_id} has no baseline data.")
    return baseline.baseline_year - num_historical_years + 1


async def get_program_num_historical_years(request: Request, program: Programs) -> int:
    """
    Returns the number of years we expect to be collected in a program's E Phase cropping stage, calculated from the
    program's crediting year and the cropping stage start year.

    Raises:
        ValueError if the program has no E Phase HISTORICAL_CROP_ROTATION stage.
        ValueError if the E Phase HISTORICAL_CROP_ROTATION stage has no year_start defined.
    """
    try:
        crop_stage: Stage = (
            await phases_db.get_stages_from_program_and_phase_types(
                request, program.id, [PhaseTypes.ENROLMENT], [StageTypes.HISTORICAL_CROP_ROTATION]
            )
        )[0]
    except IndexError:
        raise ValueError(f"Couldn't find an E Phase cropping stage to derive start_year from in program {program.id}.")
    if crop_stage.year_start is None:
        raise ValueError(
            f"E Phase cropping stage {crop_stage.id} has no year_start, which is needed to generate "
            f"cultivation cycles."
        )
    return program.reporting_period_start_date.year - crop_stage.year_start


async def store_session_input_in_bucket(
    request: Request,
    task_id: str,
    field_id: int,
    session_input: ExploreAPIInput | SessionInput | BiofuelsInput | InventoryInput,
) -> str | None:
    if settings.env == "local":
        return None

    storage: Storage = request.state.gcloud_storage_client()
    if isinstance(session_input, InventoryInput):
        directory = "inventory-inputs"
    elif isinstance(session_input, BiofuelsInput):
        directory = "biofuels-inputs"
    else:
        directory = "dndc-session-inputs"
    object_path = f"{directory}/{task_id}/{field_id}.json"

    await storage.upload(
        bucket=settings.GCLOUD_STORAGE_PRIVATE_BUCKET, object_name=object_path, file_data=session_input.json()
    )
    return f"gs://{settings.GCLOUD_STORAGE_PRIVATE_BUCKET}/{object_path}"


async def filter_out_already_submitted_fields_for_dndc_task(
    request: Request, task_id: str, field_ids: list[int]
) -> list[int]:
    """
    Filters out fields that have already been submitted to the Measure API for the given task.
    This is necessary to avoid resubmitting fields that have already been processed.
    """
    successful_requests = await get.generic_get(
        request=request,
        orm_type=DndcSimulationRequests,
        filters=[
            get.Filter(id_field=DndcSimulationRequests.task_id, ids=[task_id]),
            get.Filter(id_field=DndcSimulationRequests.is_error, ids=[0]),
            get.Filter(id_field=DndcSimulationRequests.field_id, ids=field_ids),
        ],
        empty_return=True,
    )
    fields_already_submitted = {req.field_id for req in successful_requests}
    return [field_id for field_id in field_ids if field_id not in fields_already_submitted]


async def validate_fields(request: Request, program_id: int, submitted_field_ids: list[int]) -> None:
    """
    Validate that the provided field_ids
       * exist in mrv_fields
       * aren't deleted
       * belong to the provided program
       * have status enrolled

    Raise a 400 if any of these conditions are not met.
    """
    field_status_and_program = await get_fields_status_and_program(request, submitted_field_ids)
    nonexistent_field_ids = {fid for fid in submitted_field_ids if fid not in field_status_and_program}
    deleted_field_ids = set()
    unenrolled_fields = set()
    field_ids_not_in_program = set()
    for fid, (fld_status, field_program_id) in field_status_and_program.items():
        if fld_status == FieldStatus.deleted:
            deleted_field_ids.add(fid)
        elif (fld_status != FieldStatus.enrolled) and (
            fld_status != FieldStatus.contract_voided or program_id not in CONTRACT_VOIDED_ELIGIBLE_PROGRAMS
        ):
            unenrolled_fields.add(fid)
        if field_program_id != program_id:
            field_ids_not_in_program.add(fid)
    if nonexistent_field_ids or deleted_field_ids or unenrolled_fields or field_ids_not_in_program:
        error_message_parts = []
        if nonexistent_field_ids:
            error_message_parts.append(f"Nonexistent field IDs: {nonexistent_field_ids}")
        if deleted_field_ids:
            error_message_parts.append(f"Deleted field IDs: {deleted_field_ids}")
        if unenrolled_fields:
            error_message_parts.append(f"Fields without enrolled+ status: {unenrolled_fields}")
        if field_ids_not_in_program:
            error_message_parts.append(f"Fields from a different program: {field_ids_not_in_program}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": ", ".join(error_message_parts)},
        )


async def validate_fire_event_dates(events: list[EntityEvent]) -> None:
    for fire_event in [fire_event for fire_event in events if isinstance(fire_event, FireEvent)]:
        for cropping_event in [
            cropping_event for cropping_event in events if isinstance(cropping_event, CroppingEvent)
        ]:
            if (
                cropping_event.get_interval_start_or_occurred_at().date()
                <= fire_event.occurred_at.date()
                <= cropping_event.get_interval_end_or_occurred_at().date()
            ):
                raise ScenariosServiceIntegrationError(
                    f"A fire event occurred on {fire_event.occurred_at.date()}, between the planting and harvesting dates of a cropping event ({cropping_event.get_interval_start_or_occurred_at().date()}, {cropping_event.get_interval_end_or_occurred_at().date()}).",
                    ScenariosServiceIntegrationErrorCode.fire_event_during_cropping_event,
                )


async def verify_crops_are_api_enabled(
    request: Request, cultivation_cycles: list[CultivationCycle], scenarios_service_api: ScenariosServiceApi
) -> None:
    """Check all crops in the CultivationCycles to make sure they are marked enabled for modeling on the Defaults
    Service. Raise an integration error if not. The way Defaults Service models this information is expected
    to change in the future to consider regions and other factors.

    Args:
        request
        cultivation_cycles: cycles containing the crops to be verified
        scenarios_service_api: which API we need crops to be enabled for
    Raises:
        ScenariosServiceIntegrationError if any crops fail the check
    """
    crop_events = [ev for cc in cultivation_cycles for ev in cc.events if isinstance(ev, CroppingEvent)]
    # Biofuels API doesn't accept any cover crop events so they don't need to be enabled.
    if scenarios_service_api == ScenariosServiceApi.biofuels_api:
        crop_events = [ev for ev in crop_events if ev.crop_usage == CropUsage.COMMODITY]

    crop_types = {crop_event.crop_type for crop_event in crop_events}

    parameters_by_api = {
        ScenariosServiceApi.explore_api: {
            "enabled_function": defaults_translator.are_crops_explore_api_enabled,
            "api_name": "Explore API",
        },
        ScenariosServiceApi.measure_api: {
            "enabled_function": defaults_translator.are_crops_measure_api_enabled,
            "api_name": "Measure API",
        },
        ScenariosServiceApi.biofuels_api: {
            "enabled_function": defaults_translator.are_crops_biofuels_api_enabled,
            "api_name": "Biofuels API",
        },
    }
    enabled_function = parameters_by_api[scenarios_service_api]["enabled_function"]
    api_name = parameters_by_api[scenarios_service_api]["api_name"]
    enabled_by_crop_name: dict[str, bool] = await enabled_function(regrow_crop_names=crop_types)
    crops_not_enabled = [crop for crop, enabled in enabled_by_crop_name.items() if not enabled]
    if crops_not_enabled:
        error_message = f"Crops can't be submitted for modeling: {crops_not_enabled} are not supported by {api_name}."
        raise ScenariosServiceIntegrationError(error_message, ScenariosServiceIntegrationErrorCode.unsupported_crop)


def adjust_shared_plant_harvest_dates(
    events_by_phase: dict[PhaseTypes, list[EntityEvent]],
) -> dict[PhaseTypes, list[EntityEvent]]:
    """
    It's agronomically reasonable for a field to harvest a certain crop and plant the following crop on the same day,
    but because Scenarios Service requires its Phases (analogous to our cultivation cycles) to be non-overlapping, we
    aren't able to send such cropping events.

    The solution is that when two neighboring cropping events share a harvest and planting date, we push the planting
    date up by one day so that the latter cropping event is entirely contained within its cultivation cycle.
    """
    cropping_events = [
        ev for ev in itertools.chain.from_iterable(events_by_phase.values()) if isinstance(ev, CroppingEvent)
    ]
    harvest_dates = {ev.interval.end.date() for ev in cropping_events}

    for event in cropping_events:
        if event.interval.start.date() in harvest_dates:
            event.interval.start += timedelta(days=1)
            logger.debug(
                f"A cropping event for field {event.entity_id} had its planting date adjusted for modeling "
                f"because the previous harvest happened on the same day."
            )
    return events_by_phase


async def group_historical_and_monitored_cultivation_cycles(
    request: Request, cultivation_cycles: list[CultivationCycle], field: Field, program: Programs
) -> tuple[list[CultivationCycle], list[CultivationCycle]]:
    """
    Determine which cultivation cycles should be included in the historical baseline scenario, and which should be
    in the intervention scenario. This depends on when the field was first enrolled in the program (as persisted in
    mrv_fields_baseline) because any cultivation cycle after that year is considered part of a reporting period.
    Therefore any cultivation cycle that ends after the end of the baseline year is included in the intervention
    scenario.

    The advantage of this approach (vs. saying that if a field is in its 3rd year of the program, we always include 3
    cultivation cycles), is that even if we end up with multiple cultivation cycles in a year (something we're seeing
    increasingly as programs become more diverse), we won’t end up skipping the initial cultivation cycles that
    should be in the monitored phase because some field reported multiple cultivation cycles in a program year.
    The drawback to this approach is that if/when we make the switch to defining program reporting periods as an
    explicit date range rather than just a year, this code will need to change. In that case our mrv_fields_baseline
    table can just point directly to the program year that the field was first enrolled in, or record the exact
    enrollment date.
    """
    if len(cultivation_cycles) == 0:
        return [], []

    baseline = await get_or_create_fields_baseline(request, field.id)
    historical_ccs, monitored_ccs = [], []
    for cultivation_cycle in cultivation_cycles:
        if cultivation_cycle.end.year > baseline.baseline_year or (
            cultivation_cycle.end.year == baseline.baseline_year
            and program.reporting_period_start_date
            and cultivation_cycle.end > program.reporting_period_start_date.replace(tzinfo=timezone.utc)
        ):
            monitored_ccs.append(cultivation_cycle)
        else:
            historical_ccs.append(cultivation_cycle)
    if not monitored_ccs:
        error_message = f"No cultivation cycles exist after the field baseline year. Check if mrv_fields_baseline is set correctly for field {field.id}."
        raise ScenariosServiceIntegrationError(
            error_message, ScenariosServiceIntegrationErrorCode.no_cultivation_cycles_after_baseline_year
        )
    monitored_ccs = remove_intended_commodity_crop_cultivation_cycles(monitored_ccs)
    return historical_ccs, monitored_ccs


def remove_intended_commodity_crop_cultivation_cycles(
    cultivation_cycles: list[CultivationCycle],
) -> list[CultivationCycle]:
    return [cycle for cycle in cultivation_cycles if not cycle_has_intended_commodity_crop(cycle)]


def cycle_has_intended_commodity_crop(cycle: CultivationCycle) -> bool:
    """Are all the commodity crops in the cycle marked as intended? If somehow a cycle has multiple commodities and
    only some are intended, it's probably best to keep the cycle in."""
    com_crop_events = [
        event for event in cycle.events if isinstance(event, CroppingEvent) and event.crop_usage == CropUsage.COMMODITY
    ]
    return len(com_crop_events) > 0 and all(ev.is_intended for ev in com_crop_events)


@dataclass
class FieldWithBoundary:
    field: Field
    geometry: Polygon | MultiPolygon
    area_hectares: float

    def get_location_data(self, allow_multipolygons: bool) -> dict[str, Any]:
        geometry = dict(self.geometry)
        if geometry["type"] != "MultiPolygon" or allow_multipolygons:
            return {"boundary": geometry}
        # Does it actually contain multiple Polygons?
        is_real_multipolygon = len(geometry["coordinates"]) > 1
        if not is_real_multipolygon:
            #  Convert to a single Polygon
            return {
                "boundary": {
                    "type": "Polygon",
                    "coordinates": geometry["coordinates"][0],
                }
            }
        # If it's a real MP and we aren't allowing such, use lat + lng and area instead
        return {
            "latlon": ss_methods.get_latlong_from_geometry(dict(geometry)),
            "area": self.area_hectares,
            "area_unit": AreaUnitEnum.hectare,
        }


async def get_field_boundaries(fields: list[Field]) -> dict[int, FieldWithBoundary]:
    field_features = await boundaries_client.get_features_for_fields(fields)
    return {
        field.id: FieldWithBoundary(
            field=field,
            geometry=field_features[field.id].geometry,
            area_hectares=field_features[field.id].properties.area_m2 / 10000,
        )
        for field in fields
    }


def get_api_for_program_method(method: AccountingMethod) -> str | None:
    if method in [AccountingMethod.inventory, AccountingMethod.intervention]:
        return MEASURE_API_ENABLED
    if method == AccountingMethod.biofuels:
        return BIOFUELS_API_ENABLED
    return None


def populate_soil_using_soil_override(soil: Soil, soil_override: SoilsOverride) -> None:
    soil.bulk_density = soil_override.pred_bd
    soil.soc = soil_override.pred_soc / 100
    if (ph := soil_override.ph) is not None:
        soil.ph = ph
    if (clay_fraction := soil_override.clay_fraction) is not None:
        soil.clay_fraction = clay_fraction


def build_ss_project_name(program: Programs, task_id: str) -> str:
    return f"{slugify(program.name)}_{task_id}"
