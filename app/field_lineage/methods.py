from collections import defaultdict

from fastapi import Request
from sqlalchemy import func, select

from boundaries_service.client import feature_intersections
from boundaries_service.schema import Intersection
from field_lineage.annotations import MD5To<PERSON>atched<PERSON>ields, MD5ToMatchedFieldsList
from field_lineage.db import (
    create_field_lineages,
    create_field_relationships,
    get_field_id_to_baseline_field,
    get_field_lineage_by_baseline_field_id,
    get_field_lineage_by_id,
    get_fields_by_field_lineage_id_asc,
    update_field_relationship,
    update_previous_field_lineage_ids,
)
from field_lineage.enums import FieldLineageStatus, FieldRelationshipType
from field_lineage.model import FieldRelationship
from field_lineage.schema import (
    FieldLineageRequest,
    FieldRelationshipRequest,
    FieldRelationshipUpdate,
    MatchedFields,
)
from fields.baselines.db import (
    bulk_create_or_update_field_baselines,
    get_field_baselines_by_field_ids,
    update_or_create_field_baseline_data,
)
from fields.db import (
    get_fields_by_program,
    get_fields_orm_by_field_ids,
    update_field_lineage_id_or_status,
)
from fields.enums import FieldStatus
from fields.model import Fields
from fields.schema import FieldBaselineUpdateRequest
from helper.async_tools import batch_list
from helper.helper import run_query
from programs.db import get_program_by_id, get_program_by_project_id
from projects.model import Projects

BOUNDARY_ID_BATCH_SIZE = 100


async def set_field_baselines(
    request: Request,
    program_id: int,
    project_id: int,
    field_ids: list[int],
    set_field_lineage: bool = False,
    set_field_relationship: bool = False,
    set_field_baseline: bool = True,
) -> None:
    program = await get_program_by_id(request=request, program_id=program_id)
    program_enrollment_year = program.reporting_period_start_date.year - 1
    if not program.previous_program_id:
        if set_field_lineage:
            await _set_field_lineage_for_new_fields(request=request, field_ids=field_ids)
        if set_field_baseline:
            await bulk_create_or_update_field_baselines(
                request=request, field_ids=field_ids, baseline_year=program_enrollment_year, is_returning=False
            )
        return

    previous_fields = [
        field
        for field in await get_fields_by_program(request=request, program_id=program.previous_program_id)
        if field.status == FieldStatus.enrolled
    ]
    if not previous_fields:
        if set_field_lineage:
            await _set_field_lineage_for_new_fields(request=request, field_ids=field_ids)
        if set_field_baseline:
            await bulk_create_or_update_field_baselines(
                request=request, field_ids=field_ids, baseline_year=program_enrollment_year, is_returning=False
            )
        await _update_field_lineage_and_relationships_for_skipped_fields(
            request=request,
            unmatched_field_ids=field_ids,
            previous_program_id=program.previous_program_id,
            set_field_lineage=set_field_lineage,
            set_field_relationship=set_field_relationship,
        )
        return
    md5_to_previous_field = {field.md5: field for field in previous_fields}

    fields = await get_fields_orm_by_field_ids(request=request, field_ids=field_ids)
    md5_to_field = {field.md5: field for field in fields}

    unmatched_field_md5s = await _set_field_baselines_and_lineage_by_md5(
        request=request,
        md5_to_field=md5_to_field,
        md5_to_previous_field=md5_to_previous_field,
        set_field_lineage=set_field_lineage,
        set_field_relationship=set_field_relationship,
        set_field_baseline=set_field_baseline,
    )
    if unmatched_field_md5s:
        md5_to_low_matched_fields, unmatched_field_md5s = await _set_field_baselines_and_lineage_by_overlap(
            request=request,
            md5_to_field={md5: field for md5, field in md5_to_field.items() if md5 in unmatched_field_md5s},
            md5_to_previous_field=md5_to_previous_field,
            set_field_lineage=set_field_lineage,
            set_field_relationship=set_field_relationship,
            set_field_baseline=set_field_baseline,
        )
        if md5_to_low_matched_fields or unmatched_field_md5s:
            if set_field_lineage:
                if md5_to_low_matched_fields:
                    await _set_field_lineage_for_reset_fields(
                        request=request,
                        md5_to_low_matched_fields=md5_to_low_matched_fields,
                    )
                if unmatched_field_md5s:
                    field_ids = [md5_to_field[md5].id for md5 in unmatched_field_md5s]
                    await _set_field_lineage_for_new_fields(request=request, field_ids=field_ids)
            if set_field_baseline:
                low_and_unmatched_field_md5s = list(md5_to_low_matched_fields.keys()) + unmatched_field_md5s
                await bulk_create_or_update_field_baselines(
                    request=request,
                    field_ids=[md5_to_field[md5].id for md5 in low_and_unmatched_field_md5s],
                    baseline_year=program_enrollment_year,
                    is_returning=False,
                )

    await _update_field_lineage_and_relationships_for_skipped_fields(
        request=request,
        unmatched_field_ids=[field.id for field in md5_to_field.values() if field.md5 in unmatched_field_md5s],
        previous_program_id=program.previous_program_id,
        set_field_lineage=set_field_lineage,
        set_field_relationship=set_field_relationship,
    )
    if set_field_relationship:
        await _update_field_relationships_for_split_fields(
            request=request, program_id=program_id, field_ids=[field.id for field in fields]
        )


async def _set_field_baselines_and_lineage_by_md5(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    set_field_lineage: bool,
    set_field_relationship: bool,
    set_field_baseline: bool,
) -> list[str]:
    matched_fields, unmatched_field_md5s = await _get_and_save_previous_fields_by_md5(
        request=request,
        md5_to_field=md5_to_field,
        md5_to_previous_field=md5_to_previous_field,
        set_field_relationship=set_field_relationship,
    )
    if matched_fields:
        await _set_field_baselines_and_lineage_for_exact_matched_fields(
            request=request,
            matched_fields=matched_fields,
            set_field_lineage=set_field_lineage,
            set_field_baseline=set_field_baseline,
        )
    return unmatched_field_md5s


async def _set_field_baselines_and_lineage_by_overlap(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    set_field_lineage: bool,
    set_field_relationship: bool,
    set_field_baseline: bool,
) -> tuple[MD5ToMatchedFieldsList, list[str]]:
    md5_to_matched_fields, unmatched_field_md5s = await _get_and_save_previous_fields_by_overlap(
        request=request,
        md5_to_field=md5_to_field,
        md5_to_previous_field=md5_to_previous_field,
        set_field_relationship=set_field_relationship,
    )
    md5_to_high_matched_field, md5_to_low_matched_fields = await _filter_md5_to_matched_fields(
        md5_to_matched_fields=md5_to_matched_fields
    )
    if md5_to_high_matched_field:
        matched_fields = list(md5_to_high_matched_field.values())
        # this method cannot be used to set field baselines until field lineage data
        # (specifically, the baseline field for each previous field) has been backfilled.
        if set_field_lineage:
            await _set_field_baselines_and_lineage_for_high_matched_fields(
                request=request,
                matched_fields=matched_fields,
                set_field_lineage=set_field_lineage,
                set_field_baseline=False,
            )
        # in the meantime, set field baselines WITHOUT taking into consideration whether
        # a field has drifted from the baseline field.
        if set_field_baseline:
            await _set_baselines_for_returning_fields(
                request=request,
                matched_fields=matched_fields,
            )
    return md5_to_low_matched_fields, unmatched_field_md5s


async def _get_and_save_previous_fields_by_md5(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    set_field_relationship: bool,
) -> tuple[list[MatchedFields], list[str]]:
    matched_fields: list[MatchedFields] = []
    unmatched_field_md5s: list[str] = []
    field_relationship_requests: list[FieldRelationshipRequest] = []
    for md5, field in md5_to_field.items():
        previous_field = md5_to_previous_field.get(md5)
        if not previous_field:
            unmatched_field_md5s.append(md5)
        else:
            field_relationship_requests.append(
                FieldRelationshipRequest(
                    field_id=field.id,
                    previous_field_id=previous_field.id,
                    percent_intersection=100,
                    area_intersection=field.area,
                    relationship=FieldRelationshipType.MATCH,
                )
            )
            matched_fields.append(
                MatchedFields(
                    field_id=field.id,
                    md5=md5,
                    previous_field_id=previous_field.id,
                    previous_md5=md5,
                    previous_field_lineage_id=previous_field.field_lineage_id,
                    percent_intersection_of_field_by_previous=100,
                    percent_intersection_of_previous_by_field=100,
                )
            )
    if set_field_relationship:
        await create_field_relationships(request=request, field_relationship_requests=field_relationship_requests)
    return matched_fields, unmatched_field_md5s


async def _get_and_save_previous_fields_by_overlap(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_previous_field: dict[str, Fields],
    set_field_relationship: bool,
) -> tuple[MD5ToMatchedFieldsList, list[str]]:
    md5s = list(md5_to_field.keys())
    previous_md5s = list(md5_to_previous_field.keys())

    md5_to_matched_fields: MD5ToMatchedFieldsList = defaultdict(list)
    unmatched_field_md5s: list[str] = []
    fields_to_field_relationship_requests: dict[tuple[str, str], FieldRelationshipRequest] = defaultdict(list)
    for md5_batch in batch_list(full_list=md5s, batch_size=BOUNDARY_ID_BATCH_SIZE):
        all_intersections = await feature_intersections(boundary_ids=md5_batch, comparison_boundary_ids=previous_md5s)
        for md5 in md5_batch:
            intersections = all_intersections.feature_intersections.get(md5, [])
            if not intersections:
                unmatched_field_md5s.append(md5)
            else:
                field_id = md5_to_field[md5].id
                merge_candidates: list[Intersection] = []
                for intersection in intersections:
                    intersecting_field = md5_to_previous_field[intersection.intersecting_id]
                    fields_to_field_relationship_requests[(md5, intersection.intersecting_id)] = (
                        FieldRelationshipRequest(
                            field_id=field_id,
                            previous_field_id=intersecting_field.id,
                            percent_intersection=intersection.percent_intersection_first,
                            area_intersection=intersection.area_intersection_m2 / 10000,
                            relationship=(
                                FieldRelationshipType.MATCH
                                if intersection.percent_intersection_first >= 90
                                else FieldRelationshipType.NON_MATCH
                            ),
                        )
                    )
                    md5_to_matched_fields[md5].append(
                        MatchedFields(
                            field_id=field_id,
                            md5=md5,
                            previous_field_id=intersecting_field.id,
                            previous_md5=intersection.intersecting_id,
                            previous_field_lineage_id=intersecting_field.field_lineage_id,
                            percent_intersection_of_field_by_previous=intersection.percent_intersection_first,
                            percent_intersection_of_previous_by_field=intersection.percent_intersection_second,
                        )
                    )
                    if intersection.percent_intersection_first >= 90 or intersection.percent_intersection_second >= 90:
                        merge_candidates.append(intersection)
                if len(merge_candidates) > 1:
                    await _update_field_relationship_requests_for_merged_fields(
                        md5=md5,
                        merged_intersections=merge_candidates,
                        fields_to_field_relationship_requests=fields_to_field_relationship_requests,
                    )
    if set_field_relationship:
        await create_field_relationships(
            request=request,
            field_relationship_requests=fields_to_field_relationship_requests.values(),
        )
    return md5_to_matched_fields, unmatched_field_md5s


async def _filter_md5_to_matched_fields(
    md5_to_matched_fields: MD5ToMatchedFieldsList,
) -> tuple[MD5ToMatchedFields, MD5ToMatchedFieldsList]:
    md5_to_high_matched_field: MD5ToMatchedFields = {}
    md5_to_low_matched_fields: MD5ToMatchedFieldsList = defaultdict(list)
    for md5, matched_fields in md5_to_matched_fields.items():
        merge_candidates: list[MatchedFields] = []
        max_matched_field = None
        for matched_field in matched_fields:
            if (
                matched_field.percent_intersection_of_field_by_previous >= 90
                or matched_field.percent_intersection_of_previous_by_field >= 90
            ):
                merge_candidates.append(matched_field)
            if (
                max_matched_field is None
                or matched_field.percent_intersection_of_field_by_previous
                > max_matched_field.percent_intersection_of_field_by_previous
            ):
                max_matched_field = matched_field
        # if the field is a merge of 2+ fields, then it cannot be matched to any one field.
        if len(merge_candidates) > 1:
            md5_to_low_matched_fields[md5] = matched_fields
            continue
        if max_matched_field and max_matched_field.percent_intersection_of_field_by_previous >= 90:
            md5_to_high_matched_field[md5] = max_matched_field
        else:
            md5_to_low_matched_fields[md5] = matched_fields
    return md5_to_high_matched_field, md5_to_low_matched_fields


async def _set_field_lineage_for_new_fields(request: Request, field_ids: list[int]) -> None:
    for field_id in field_ids:
        field_lineage_id = await _get_or_create_field_lineage_for_current_field(
            request=request, field_id=field_id, immediate_previous_field_lineage_ids=[]
        )
        await update_field_lineage_id_or_status(
            request=request,
            field_id=field_id,
            field_lineage_id=field_lineage_id,
            field_lineage_status=FieldLineageStatus.new,
        )


async def _set_field_lineage_for_reset_fields(
    request: Request, md5_to_low_matched_fields: MD5ToMatchedFieldsList
) -> None:
    for low_matched_fields in md5_to_low_matched_fields.values():
        field_id = low_matched_fields[0].field_id
        immediate_previous_field_lineage_ids = list(
            {matched_field.previous_field_lineage_id for matched_field in low_matched_fields}
        )
        field_lineage_id = await _get_or_create_field_lineage_for_current_field(
            request=request,
            field_id=field_id,
            immediate_previous_field_lineage_ids=immediate_previous_field_lineage_ids,
        )
        await update_field_lineage_id_or_status(
            request=request,
            field_id=field_id,
            field_lineage_id=field_lineage_id,
            field_lineage_status=FieldLineageStatus.reset,
        )


async def _set_field_baselines_and_lineage_for_exact_matched_fields(
    request: Request, matched_fields: list[MatchedFields], set_field_lineage: bool, set_field_baseline: bool
) -> None:
    if set_field_lineage:
        for matched_field in matched_fields:
            await update_field_lineage_id_or_status(
                request=request,
                field_id=matched_field.field_id,
                field_lineage_id=matched_field.previous_field_lineage_id,
                field_lineage_status=FieldLineageStatus.returning,
            )
    if set_field_baseline:
        await _set_baselines_for_returning_fields(request=request, matched_fields=matched_fields)


async def _set_field_baselines_and_lineage_for_high_matched_fields(
    request: Request, matched_fields: list[MatchedFields], set_field_lineage: bool, set_field_baseline: bool
) -> None:
    previous_field_id_to_baseline_field = await get_field_id_to_baseline_field(
        request=request, field_ids=[matched_field.previous_field_id for matched_field in matched_fields]
    )
    all_intersections = await feature_intersections(
        boundary_ids=[matched_field.md5 for matched_field in matched_fields],
        comparison_boundary_ids=[baseline_field.md5 for baseline_field in previous_field_id_to_baseline_field.values()],
    )
    for matched_field in matched_fields:
        baseline_field = previous_field_id_to_baseline_field[matched_field.previous_field_id]
        # baseline field = previous field
        if baseline_field.md5 == matched_field.previous_md5:
            if set_field_lineage:
                await update_field_lineage_id_or_status(
                    request=request,
                    field_id=matched_field.field_id,
                    field_lineage_id=baseline_field.field_lineage_id,
                    field_lineage_status=FieldLineageStatus.returning,
                )
            if set_field_baseline:
                await _set_baselines_for_returning_fields(
                    request=request,
                    matched_fields=[matched_field],
                )
        # baseline field overlaps field by 90% or more
        elif any(
            intersection.intersecting_id == baseline_field.md5 and intersection.percent_intersection_first >= 90
            for intersection in all_intersections.feature_intersections.get(matched_field.md5, [])
        ):
            if set_field_lineage:
                await update_field_lineage_id_or_status(
                    request=request,
                    field_id=matched_field.field_id,
                    field_lineage_id=baseline_field.field_lineage_id,
                    field_lineage_status=FieldLineageStatus.returning,
                )
            if set_field_baseline:
                await _set_baselines_for_returning_fields(
                    request=request,
                    matched_fields=[matched_field],
                )
        # baseline field does not overlap field by 90% or more
        else:
            await _set_field_baseline_and_lineage_for_returning_reset_field(
                request=request,
                matched_field=matched_field,
                baseline_field=baseline_field,
                set_field_lineage=set_field_lineage,
                set_field_baseline=set_field_baseline,
            )


async def _set_field_baseline_and_lineage_for_returning_reset_field(
    request: Request,
    matched_field: MatchedFields,
    baseline_field: Fields,
    set_field_lineage: bool,
    set_field_baseline: bool,
) -> None:
    previous_fields_asc = await get_fields_by_field_lineage_id_asc(
        request=request, field_lineage_id=baseline_field.field_lineage_id, enrolled_only=True
    )
    all_intersections = await feature_intersections(
        boundary_ids=[matched_field.md5],
        comparison_boundary_ids=[previous_field.md5 for previous_field in previous_fields_asc],
    )
    intersecting_id_to_intersection = {
        intersection.intersecting_id: intersection
        for intersection in all_intersections.feature_intersections.get(matched_field.md5, [])
    }
    field_lineage_id_assigned = False
    for previous_field in previous_fields_asc:
        intersection = intersecting_id_to_intersection.get(previous_field.md5)
        if not intersection:
            continue
        if intersection.percent_intersection_first >= 90:
            if set_field_lineage:
                field_lineage_id = await _get_or_create_field_lineage_for_previous_field(
                    request=request, previous_field=previous_field
                )
                await update_field_lineage_id_or_status(
                    request=request,
                    field_id=matched_field.field_id,
                    field_lineage_id=field_lineage_id,
                    field_lineage_status=FieldLineageStatus.returning_reset,
                )
            if set_field_baseline:
                previous_field_program = await get_program_by_project_id(
                    request=request, project_id=previous_field.parent_project_id
                )
                previous_field_program_enrollment_year = previous_field_program.reporting_period_start_date.year - 1
                await bulk_create_or_update_field_baselines(
                    request=request,
                    field_ids=[matched_field.field_id],
                    baseline_year=previous_field_program_enrollment_year,
                    is_returning=True,
                )
            field_lineage_id_assigned = True
            break
    if not field_lineage_id_assigned:
        raise Exception(f"Field lineage ID could not be assigned to field {matched_field.field_id}.")


async def _get_or_create_field_lineage_for_current_field(
    request: Request, field_id: int, immediate_previous_field_lineage_ids: list[int]
) -> int:
    if len(immediate_previous_field_lineage_ids) == 1:
        previous_field_lineage = await get_field_lineage_by_id(
            request=request, field_lineage_id=immediate_previous_field_lineage_ids[0]
        )
        previous_field_lineage_ids = [previous_field_lineage.id] + previous_field_lineage.previous_lineage_ids
    else:
        previous_field_lineage_ids = immediate_previous_field_lineage_ids
    return await _get_or_create_field_lineage_for_field(
        request=request, field_id=field_id, previous_field_lineage_ids=previous_field_lineage_ids
    )


async def _get_or_create_field_lineage_for_previous_field(request: Request, previous_field: Fields) -> int:
    previous_field_lineage = await get_field_lineage_by_id(
        request=request, field_lineage_id=previous_field.field_lineage_id
    )
    previous_field_lineage_ids = [previous_field_lineage.id] + previous_field_lineage.previous_lineage_ids
    return await _get_or_create_field_lineage_for_field(
        request=request, field_id=previous_field.id, previous_field_lineage_ids=previous_field_lineage_ids
    )


async def _get_or_create_field_lineage_for_field(
    request: Request, field_id: int, previous_field_lineage_ids: list[int]
) -> int:
    field_lineage = await get_field_lineage_by_baseline_field_id(request=request, baseline_field_id=field_id)
    if field_lineage and field_lineage.previous_lineage_ids != previous_field_lineage_ids:
        await update_previous_field_lineage_ids(
            request=request,
            field_lineage_id=field_lineage.id,
            previous_field_lineage_ids=previous_field_lineage_ids,
        )
    if not field_lineage:
        field_lineages = await create_field_lineages(
            request=request,
            field_lineage_requests=[
                FieldLineageRequest(baseline_field_id=field_id, previous_lineage_ids=previous_field_lineage_ids)
            ],
        )
        field_lineage = field_lineages[0]
    return field_lineage.id


async def _set_baselines_for_returning_fields(request: Request, matched_fields: list[MatchedFields]) -> None:
    previous_field_baselines = await get_field_baselines_by_field_ids(
        request=request,
        field_ids=[matched_field.previous_field_id for matched_field in matched_fields],
    )
    previous_field_to_baseline_year = {
        baseline.field_id: baseline.baseline_year for baseline in previous_field_baselines
    }
    for matched_field in matched_fields:
        baseline_year = previous_field_to_baseline_year[matched_field.previous_field_id]
        await update_or_create_field_baseline_data(
            request=request,
            update_baseline_data=[
                FieldBaselineUpdateRequest(
                    field_id=matched_field.field_id, baseline_year=baseline_year, is_returning=True
                )
            ],
        )


async def _update_field_relationship_requests_for_merged_fields(
    md5: str,
    merged_intersections: list[Intersection],
    fields_to_field_relationship_requests: dict[tuple[str, str], FieldRelationshipRequest],
) -> None:
    for intersection in merged_intersections:
        fields_to_field_relationship_requests[(md5, intersection.intersecting_id)].relationship = (
            FieldRelationshipType.MERGE
        )


async def _get_previous_field_id_to_split_field_ids(
    request: Request, program_id: int, field_ids: list[int]
) -> dict[int, list[int]]:
    previous_field_ids_subquery = (
        select(FieldRelationship.previous_field_id)
        .where(FieldRelationship.field_id.in_(field_ids))
        .where(FieldRelationship.deleted_at.is_(None))
        .distinct()
        .subquery()
    )
    query = (
        select(FieldRelationship.previous_field_id, func.group_concat(FieldRelationship.field_id))
        .join(
            previous_field_ids_subquery,
            FieldRelationship.previous_field_id == previous_field_ids_subquery.c.previous_field_id,
        )
        .join(Fields, FieldRelationship.field_id == Fields.id)
        .join(Projects, Fields.parent_project_id == Projects.id)
        .where(Projects.program_id == program_id)
        .where(FieldRelationship.percent_intersection >= 90)
        .where(FieldRelationship.deleted_at.is_(None))
        .group_by(FieldRelationship.previous_field_id)
        .having(func.count(FieldRelationship.field_id) > 1)
    )
    async with request.state.sql_session() as s:
        res = (await run_query(query=query, s=s)).all()
    return {single_res[0]: list(map(int, single_res[1].split(","))) for single_res in res}


async def _update_field_relationships_for_split_fields(request: Request, program_id: int, field_ids: list[int]) -> None:
    previous_field_id_to_split_field_ids = await _get_previous_field_id_to_split_field_ids(
        request=request, program_id=program_id, field_ids=field_ids
    )
    for previous_field_id, split_field_ids in previous_field_id_to_split_field_ids.items():
        for split_field_id in split_field_ids:
            await update_field_relationship(
                request=request,
                field_id=split_field_id,
                previous_field_id=previous_field_id,
                field_relationship_update=FieldRelationshipUpdate(relationship=FieldRelationshipType.SPLIT),
            )


async def _update_field_lineage_and_relationships_for_skipped_fields(
    request: Request,
    unmatched_field_ids: list[int],
    previous_program_id: int,
    set_field_lineage: bool,
    set_field_relationship: bool,
) -> None:
    if not unmatched_field_ids:
        return

    unmatched_fields = await get_fields_orm_by_field_ids(request=request, field_ids=unmatched_field_ids)
    md5_to_field = {field.md5: field for field in unmatched_fields}

    previous_program = await get_program_by_id(request=request, program_id=previous_program_id)
    if not previous_program.previous_program_id:
        return

    two_year_ago_fields = [
        field
        for field in await get_fields_by_program(request=request, program_id=previous_program.previous_program_id)
        if field.status == FieldStatus.enrolled
    ]
    if not two_year_ago_fields:
        return
    md5_to_two_year_ago_field = {field.md5: field for field in two_year_ago_fields}

    unmatched_field_md5s = await _update_field_lineages_and_relationships_for_skipped_fields_by_md5(
        request=request,
        md5_to_field=md5_to_field,
        md5_to_two_year_ago_field=md5_to_two_year_ago_field,
        set_field_lineage=set_field_lineage,
        set_field_relationship=set_field_relationship,
    )
    if not unmatched_field_md5s:
        return
    await _update_field_lineages_and_relationships_for_skipped_fields_by_overlap(
        request=request,
        md5_to_field={md5: field for md5, field in md5_to_field.items() if md5 in unmatched_field_md5s},
        md5_to_two_year_ago_field=md5_to_two_year_ago_field,
        set_field_lineage=set_field_lineage,
        set_field_relationship=set_field_relationship,
    )


async def _update_field_lineages_and_relationships_for_skipped_fields_by_md5(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_two_year_ago_field: dict[str, Fields],
    set_field_lineage: bool,
    set_field_relationship: bool,
) -> list[str]:
    unmatched_field_md5s: list[str] = []
    for md5, field in md5_to_field.items():
        two_year_ago_field = md5_to_two_year_ago_field.get(md5)
        if two_year_ago_field:
            if set_field_relationship:
                await create_field_relationships(
                    request=request,
                    field_relationship_requests=[
                        FieldRelationshipRequest(
                            field_id=field.id,
                            previous_field_id=two_year_ago_field.id,
                            percent_intersection=100,
                            area_intersection=field.area,
                            relationship=FieldRelationshipType.SKIP,
                        )
                    ],
                )
            if set_field_lineage:
                await update_previous_field_lineage_ids(
                    request=request,
                    field_lineage_id=field.field_lineage_id,
                    previous_field_lineage_ids=[two_year_ago_field.field_lineage_id],
                )
                await update_field_lineage_id_or_status(
                    request=request, field_id=field.id, field_lineage_status=FieldLineageStatus.skipped_reset
                )
        else:
            unmatched_field_md5s.append(md5)
    return unmatched_field_md5s


async def _update_field_lineages_and_relationships_for_skipped_fields_by_overlap(
    request: Request,
    md5_to_field: dict[str, Fields],
    md5_to_two_year_ago_field: dict[str, Fields],
    set_field_lineage: bool,
    set_field_relationship: bool,
) -> None:
    md5s = list(md5_to_field.keys())
    for md5_batch in batch_list(full_list=md5s, batch_size=BOUNDARY_ID_BATCH_SIZE):
        all_intersections = await feature_intersections(
            boundary_ids=md5_batch, comparison_boundary_ids=list(md5_to_two_year_ago_field.keys())
        )
        for md5 in md5_batch:
            field = md5_to_field[md5]
            intersections = all_intersections.feature_intersections.get(md5, [])
            for intersection in intersections:
                if intersection.percent_intersection_first >= 90:
                    intersecting_field = md5_to_two_year_ago_field[intersection.intersecting_id]
                    if set_field_relationship:
                        await create_field_relationships(
                            request=request,
                            field_relationship_requests=[
                                FieldRelationshipRequest(
                                    field_id=field.id,
                                    previous_field_id=intersecting_field.id,
                                    percent_intersection=intersection.percent_intersection_first,
                                    area_intersection=intersection.area_intersection_m2 / 10000,
                                    relationship=FieldRelationshipType.SKIP,
                                )
                            ],
                        )
                    if set_field_lineage:
                        await update_previous_field_lineage_ids(
                            request=request,
                            field_lineage_id=field.field_lineage_id,
                            previous_field_lineage_ids=[intersecting_field.field_lineage_id],
                        )
                        await update_field_lineage_id_or_status(
                            request=request, field_id=field.id, field_lineage_status=FieldLineageStatus.skipped_reset
                        )
                    break
