#!/usr/bin/env python3
"""
Field Transfer Tool

Transfers field boundaries between farms with optional event migration.

Features:
- Fetches source field geometry from boundaries service
- Adds inert point to create unique geometry
- Uploads modified field to Core API
- Copies associated events via SES (optional)

Usage examples:
  # Transfer field only
  python field_transfer.py --source-boundary-id cb3d20df9043ee2032de0a503efc1774 \
    --target-farm-id 89795 --field-only

  # Transfer field and copy events
  python field_transfer.py --source-boundary-id cb3d20df9043ee2032de0a503efc1774 \
    --target-farm-id 89795 --grpc-url localhost:50052

  # Copy events only (requires existing target field)
  python field_transfer.py --source-boundary-id cb3d20df9043ee2032de0a503efc1774 \
    --target-boundary-id cacaf08da4abf3644ec33dfcc896bf08 --events-only \
    --grpc-url localhost:50052
"""

import argparse
import asyncio
import json
import logging
import sys
import urllib.parse
import urllib.request
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple
from urllib.error import HTTPError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
)
logger = logging.getLogger(__name__)

GRPC_URL = "localhost:50052"
FIELD_EVENT_SEARCH_URL = "http://fieldevent-srvc.int.dev.regrow.cloud"  # NOSONAR
BOUNDARIES_SERVICE_URL = "http://boundaries-service-qa.int.dev.regrow.cloud"  # NOSONAR
CORE_URL = "http://fluro-core.int.dev.regrow.cloud"  # NOSONAR

PRINT_FUNC = print


# -----------------------------
# Data Models
# -----------------------------


@dataclass
class SesClientConfig:
    grpc_host_addr: str = GRPC_URL
    search_host_addr: str = FIELD_EVENT_SEARCH_URL
    boundaries_service_url: Optional[str] = BOUNDARIES_SERVICE_URL


# -----------------------------
# Geometry Utilities
# -----------------------------


class GeometryUtils:
    """Utilities for geometry manipulation and conversion."""

    @staticmethod
    def add_inert_point(geometry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Insert a point on the first line segment of the outer ring.
        This creates a unique geometry while preserving area/shape.
        """
        geom_type = geometry.get("type")
        if geom_type not in ["Polygon", "MultiPolygon"]:
            raise ValueError("Only Polygon and MultiPolygon geometries are supported")

        coords = geometry.get("coordinates")
        if not coords or not isinstance(coords, list):
            raise ValueError("Invalid geometry: missing coordinates")

        if geom_type == "Polygon":
            new_ring = GeometryUtils._add_inert_point_to_ring(coords[0])
            return {"type": "Polygon", "coordinates": [new_ring]}

        else:
            if not coords or not coords[0] or not coords[0][0]:
                raise ValueError("Invalid MultiPolygon: missing first polygon outer ring")

            new_coords = []
            for i, polygon in enumerate(coords):
                if i == 0:
                    new_outer_ring = GeometryUtils._add_inert_point_to_ring(polygon[0])
                    new_polygon = [new_outer_ring] + polygon[1:]
                    new_coords.append(new_polygon)
                else:
                    new_coords.append(polygon)

            return {"type": "MultiPolygon", "coordinates": new_coords}

    @staticmethod
    def _add_inert_point_to_ring(ring: List[List[float]]) -> List[List[float]]:
        """Helper to add an inert point to a single ring."""
        ring = list(ring)
        if len(ring) < 4:
            raise ValueError("Invalid ring: need at least 4 points (including closure)")

        # Ensure ring is closed
        if ring[0] != ring[-1]:
            ring.append(ring[0])

        # Add midpoint between first two points
        p0 = ring[0]
        p1 = ring[1]
        mid = [(p0[0] + p1[0]) / 2.0, (p0[1] + p1[1]) / 2.0]

        new_ring = [ring[0], mid] + ring[1:]

        # Ensure closure
        if new_ring[0] != new_ring[-1]:
            new_ring[-1] = new_ring[0]

        return new_ring

    @staticmethod
    def geometry_to_kml(geometry: Dict[str, Any], name: str = "Field") -> str:
        """Convert GeoJSON geometry to KML format."""
        geom_type = geometry.get("type")
        if geom_type not in ["Polygon", "MultiPolygon"]:
            raise ValueError("Only Polygon and MultiPolygon geometries can be converted to KML")

        coords = geometry.get("coordinates", [])

        kml_parts = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<kml xmlns="http://www.opengis.net/kml/2.2">',
            "<Document>",
            f"<Placemark>",
            f"<name>{name}</name>",
        ]

        if geom_type == "Polygon":
            kml_parts.append(GeometryUtils._polygon_to_kml(coords))
        elif geom_type == "MultiPolygon":
            kml_parts.append("<MultiGeometry>")
            for polygon_coords in coords:
                kml_parts.append(GeometryUtils._polygon_to_kml(polygon_coords))
            kml_parts.append("</MultiGeometry>")

        kml_parts.extend(["</Placemark>", "</Document>", "</kml>"])

        return "\n".join(kml_parts)

    @staticmethod
    def _polygon_to_kml(polygon_coords: List[List[List[float]]]) -> str:
        """Convert a single polygon's coordinates to KML Polygon element."""
        if not polygon_coords or not polygon_coords[0]:
            raise ValueError("Invalid polygon coordinates")

        kml_parts = ["<Polygon>", "<outerBoundaryIs>", "<LinearRing>", "<coordinates>"]

        # Convert coordinates: GeoJSON [lon, lat] -> KML lon,lat,alt
        coord_strings = []
        for point in polygon_coords[0]:
            if len(point) >= 2:
                coord_strings.append(f"{point[0]},{point[1]},0")

        kml_parts.append(" ".join(coord_strings))
        kml_parts.extend(["</coordinates>", "</LinearRing>", "</outerBoundaryIs>"])

        # Handle holes (inner boundaries)
        for hole_ring in polygon_coords[1:]:
            kml_parts.extend(["<innerBoundaryIs>", "<LinearRing>", "<coordinates>"])

            hole_coord_strings = []
            for point in hole_ring:
                if len(point) >= 2:
                    hole_coord_strings.append(f"{point[0]},{point[1]},0")

            kml_parts.append(" ".join(hole_coord_strings))
            kml_parts.extend(["</coordinates>", "</LinearRing>", "</innerBoundaryIs>"])

        kml_parts.append("</Polygon>")
        return "\n".join(kml_parts)


# -----------------------------
# Service Clients
# -----------------------------


class BoundariesClient:
    """Client for fetching geometry via SES client method."""

    def __init__(self, grpc_url: str, search_url: str):
        self.grpc_url = grpc_url
        self.search_url = search_url

    def fetch_geometry(self, boundary_id: str) -> Dict[str, Any]:
        """Fetch geometry for a boundary ID using SES client."""
        PRINT_FUNC(f"🔍 Fetching geometry for boundary {boundary_id}")

        try:
            # This would normally use the SES client to fetch geometry
            # For now, we'll simulate the call but use the real boundaries service
            # TODO: Replace with actual SES client call

            # Temporary direct call until SES client is integrated
            url = f"{BOUNDARIES_SERVICE_URL}/search?ids={boundary_id}"

            with urllib.request.urlopen(url, timeout=30) as response:  # nosec
                data = json.load(response)

            # Handle GeoJSON FeatureCollection format
            if isinstance(data, dict) and data.get("type") == "FeatureCollection":
                features = data.get("features", [])
                if not features:
                    raise ValueError(f"No features found for boundary {boundary_id}")

                geometry = features[0].get("geometry")
                if not geometry:
                    raise ValueError(f"No geometry found in feature for boundary {boundary_id}")

            elif isinstance(data, list) and len(data) > 0:
                # Handle array format
                geometry = data[0].get("geometry")
                if not geometry:
                    raise ValueError(f"Invalid geometry response for boundary {boundary_id}")
            else:
                raise ValueError(f"No geometry found for boundary {boundary_id}")

            geom_type = geometry.get("type")
            PRINT_FUNC(f"✅ Found {geom_type} geometry")
            return geometry

        except Exception as e:
            raise ValueError(f"Failed to fetch geometry: {e}")


class CoreApiClient:
    """Client for uploading KML to Core API."""

    def __init__(self, base_url: str = CORE_URL):
        self.base_url = base_url.rstrip("/")
        self.headers = {"x-consumer-custom-id": "13"}

    def upload_kml_field(
        self, kml_content: str, target_farm_id: str, field_name: str, source_boundary_id: str
    ) -> tuple[str, Any]:
        """Upload KML field and return new field MD5."""
        url = f"{self.base_url}/api/v1/files/kml"

        # Clean KML content
        cleaned_kml = kml_content.replace("\n", "").replace("\r", "")

        payload = [{"farm_id": int(target_farm_id), "name": field_name, "kml": cleaned_kml}]

        PRINT_FUNC(f"📤 Uploading field '{field_name}' to farm {target_farm_id}")

        try:
            req_data = json.dumps(payload).encode("utf-8")
            headers = {"Content-Type": "application/json"}
            headers.update(self.headers)
            req = urllib.request.Request(url, data=req_data, headers=headers)

            with urllib.request.urlopen(req, timeout=30) as response:  # nosec
                data = json.load(response)

        except HTTPError as e:
            error_body = e.read().decode("utf-8") if hasattr(e, "read") else str(e)
            raise ValueError(f"Core API upload failed: HTTP {e.code} - {error_body}")
        except Exception as e:
            raise ValueError(f"Core API upload failed: {e}")

        if data.get("status") != "ok":
            raise ValueError(f"Core API upload returned non-OK status: {data}")

        PRINT_FUNC("✅ Upload successful, fetching target farm fields...")

        # Fetch target farm fields to find our uploaded field
        target_farm_fields, farm_data = self.fetch_farm_fields(target_farm_id)
        self._last_farm_data = farm_data

        # Filter out source MD5 and find new field
        filtered_fields = [f for f in target_farm_fields if f.get("MD5") != source_boundary_id]

        # Find field by name
        target_field = None
        for field in filtered_fields:
            if field.get("Name") == field_name:
                target_field = field
                break

        if not target_field:
            available_fields = [f"'{f.get('Name')}' (MD5: {f.get('MD5')})" for f in filtered_fields]
            raise ValueError(f"Could not find uploaded field '{field_name}'. Available: {available_fields}")

        md5 = target_field.get("MD5")
        if not md5:
            raise ValueError(f"MD5 not found for field '{field_name}'")

        # Extract owner information from the farm data
        farm_owner_id = None
        if hasattr(self, "_last_farm_data") and self._last_farm_data:
            group = self._last_farm_data.get("result", {}).get("group", {})
            farm_owner_id = group.get("ownerId")

        PRINT_FUNC(f"✅ Field created with MD5: {md5}")
        if farm_owner_id:
            PRINT_FUNC(f"✅ Target farm owner ID: {farm_owner_id}")

        return md5, farm_owner_id

    def fetch_farm_fields(self, target_farm_id: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """Fetch all fields for a farm and return fields + full response data."""
        url = f"{self.base_url}/api/v1/kml/{target_farm_id}"

        try:
            headers = {}
            headers.update(self.headers)
            req = urllib.request.Request(url, headers=headers)

            with urllib.request.urlopen(req, timeout=30) as response:  # nosec
                data = json.load(response)

        except Exception as e:
            raise ValueError(f"Failed to fetch farm fields: {e}")

        if data.get("status") != "ok":
            raise ValueError(f"Core API returned non-OK status: {data}")

        result = data.get("result", {})
        fields = result.get("fields", [])

        return fields, data


class SesClient:
    """Real SES client for copying events."""

    def __init__(self, config: SesClientConfig, dry_run: bool = False):
        self.config = config
        self.dry_run = dry_run
        self._client = None

        try:
            from ses_client.client import Client  # type: ignore

            kwargs = {
                "grpc_host_addr": config.grpc_host_addr,
                "search_host_addr": config.search_host_addr,
            }
            if config.boundaries_service_url:
                kwargs["boundaries_service_url"] = config.boundaries_service_url
            self._client = Client(**kwargs)
            PRINT_FUNC("✅ Real SES client initialized")
        except ModuleNotFoundError:
            PRINT_FUNC("⚠️  ses_client package not available. Using dry-run mode.")
            self.dry_run = True
            self._client = None

    async def get_event_ids_for_boundary(self, boundary_id: str) -> List[str]:
        """Get event IDs associated with a boundary."""
        PRINT_FUNC(f"🔍 Finding events for boundary {boundary_id}")

        if not self._client or not hasattr(self._client, "search_client"):
            raise RuntimeError("SES search client not initialized")

        try:
            # Try to get the constant from the search client instance
            return_event_ids = getattr(self._client.search_client, "RETURN_EVENT_IDS", "event_ids")

            response = await self._client.search_client.field_events(
                field_ids=[boundary_id], search_filter=None, return_type=return_event_ids
            )

            # Extract event IDs from the response dictionary
            if isinstance(response, dict) and boundary_id in response:
                event_ids = response[boundary_id]
            else:
                event_ids = []

            PRINT_FUNC(f"✅ Found {len(event_ids)} events")
            return event_ids
        except Exception as e:
            raise ValueError(f"Failed to fetch events: {e}")

    async def bulk_copy_events(
        self, copy_pairs: List[Tuple[List[str], str]], target_owner_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Copy events to new boundary IDs."""
        if not copy_pairs:
            return {"success": 0, "failures": 0, "total": 0}

        source_events, target_boundary_id = copy_pairs[0]
        PRINT_FUNC(f"📋 Copying {len(source_events)} events to boundary {target_boundary_id}")

        if self.dry_run or not self._client:
            PRINT_FUNC("⚠️  DRY-RUN: Simulating event copy")
            # Simulate the copy process
            for event_id in source_events:
                PRINT_FUNC(f"  ✅ Would copy event {event_id}")
            return {"success": len(source_events), "failures": 0, "total": len(source_events)}

        # Real copy using SES client
        try:
            from ses_client.client import CopyEventsToFieldRequest  # type: ignore

            api_requests = [CopyEventsToFieldRequest(event_ids=source_events, field_id=target_boundary_id)]

            api_results = await self._client.bulk_copy_events(api_requests)

            success_count = 0
            failure_count = 0
            new_event_ids = []

            for result in api_results:
                if hasattr(result, "error") and result.error:
                    failure_count += 1
                    PRINT_FUNC(f"  ❌ Failed to copy event: {result.error}")
                else:
                    success_count += 1
                    new_id = getattr(result, "new_event_id", None)
                    if new_id:
                        new_event_ids.append(new_id)
                        PRINT_FUNC(f"  ✅ Copied event → {new_id}")

            PRINT_FUNC(f"✅ Event copy complete: {success_count} success, {failure_count} failures")

            return {
                "success": success_count,
                "failures": failure_count,
                "total": len(source_events),
                "new_event_ids": new_event_ids,
            }

        except Exception as e:
            raise ValueError(f"Failed to copy events: {e}")

    async def update_event_ownership_context(self, field_id: str, owner_id: str, copy_result: Dict[str, Any]) -> None:
        """Update ownership context for copied events."""
        if not self._client:
            PRINT_FUNC("⚠️  No SES client available for context update")
            return

        # We need to get the new event IDs from the copy result
        # The copy_result should contain information about the copied events
        if not copy_result or copy_result.get("success", 0) == 0:
            PRINT_FUNC("⚠️  No successful event copies to update context for")
            return

        try:
            from ses_client.client import context_pb2  # type: ignore

            # Create EventContext with the new ownership
            context = context_pb2.EventContext()

            # Set all user context values to the target owner ID for proper filtering
            context.source["regrowUserId"] = str(owner_id)
            context.creation["regrowUserId"] = str(owner_id)
            context.association["mrvOwner"] = str(owner_id)
            context.association["mrvActor"] = str(owner_id)

            # Get the new event IDs from the copy result
            new_event_ids = copy_result.get("new_event_ids", [])
            if not new_event_ids:
                PRINT_FUNC("⚠️  No new event IDs found in copy result")
                return

            PRINT_FUNC(f"🔧 Updating ownership context for {len(new_event_ids)} events")

            # Update context for each copied event
            for event_id in new_event_ids:
                try:
                    await self._client.add_field_event_context(field_id=field_id, event_id=event_id, context=context)
                    PRINT_FUNC(f"  ✅ Context updated for event {event_id}")
                except Exception as e:
                    PRINT_FUNC(f"  ❌ Failed to update context for event {event_id}: {e}")

            PRINT_FUNC(f"✅ Context update complete")

        except Exception as e:
            PRINT_FUNC(f"⚠️  Failed to update event ownership context: {e}")
            import traceback

            traceback.print_exc()


# -----------------------------
# Main Transfer Logic
# -----------------------------


class FieldTransfer:
    """Main field transfer orchestrator."""

    def __init__(
        self, boundaries_client: BoundariesClient, core_client: CoreApiClient, ses_client: Optional[SesClient] = None
    ):
        self.boundaries = boundaries_client
        self.core = core_client
        self.ses = ses_client

    async def transfer_field(
        self, source_boundary_id: str, target_farm_id: str, target_field_name: Optional[str] = None
    ) -> tuple[str, Any]:
        """Transfer field boundary and return new boundary MD5."""

        PRINT_FUNC("🚀 Starting field transfer...")

        # 1. Fetch source geometry
        geometry = self.boundaries.fetch_geometry(source_boundary_id)

        # 2. Add inert point to create unique geometry
        PRINT_FUNC("🔧 Adding inert point to geometry")
        modified_geometry = GeometryUtils.add_inert_point(geometry)

        # 3. Convert to KML
        field_name = target_field_name or f"transferred-from-{source_boundary_id}"
        PRINT_FUNC("📄 Converting geometry to KML")
        kml_content = GeometryUtils.geometry_to_kml(modified_geometry, name=field_name)

        # 4. Upload to Core API
        result = self.core.upload_kml_field(
            kml_content=kml_content,
            target_farm_id=target_farm_id,
            field_name=field_name,
            source_boundary_id=source_boundary_id,
        )

        if isinstance(result, tuple):
            new_boundary_id, farm_owner_id = result
        else:
            new_boundary_id = result
            farm_owner_id = None

        PRINT_FUNC(f"✅ Field transfer complete! New boundary ID: {new_boundary_id}")
        if farm_owner_id:
            PRINT_FUNC(f"✅ Target farm owner: {farm_owner_id}")

        return new_boundary_id, farm_owner_id

    async def copy_events(
        self, source_boundary_id: str, target_boundary_id: str, target_owner_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Copy events from source to target boundary."""

        if not self.ses:
            raise ValueError("SES client not configured for event copying")

        PRINT_FUNC("🚀 Starting event copy...")

        # If no target_owner_id provided, try to get it from the target field
        if not target_owner_id:
            target_owner_id = await self._get_field_owner(target_boundary_id)
            if not target_owner_id:
                PRINT_FUNC("⚠️  Could not determine target field owner - context will not be updated")

        # 1. Get source events
        source_events = await self.ses.get_event_ids_for_boundary(source_boundary_id)

        if not source_events:
            PRINT_FUNC("ℹ️  No events found to copy")
            return {"success": 0, "failures": 0, "total": 0}

        # 2. Copy events
        result = await self.ses.bulk_copy_events([(source_events, target_boundary_id)], target_owner_id)

        # 3. Update ownership context for copied events if target_owner_id is provided
        if target_owner_id and result.get("success", 0) > 0:
            PRINT_FUNC(f"🔧 Updating ownership context for copied events to owner {target_owner_id}")
            await self.ses.update_event_ownership_context(target_boundary_id, target_owner_id, result)

        PRINT_FUNC("✅ Event copy complete!")
        return result

    async def _get_field_owner(self, field_id: str) -> Optional[str]:
        """Get the owner of a field by looking it up via Core API."""
        try:
            # We need to find which farm this field belongs to
            # This is a bit tricky since we only have the field MD5
            # For now, return None and rely on explicit owner_id
            PRINT_FUNC("⚠️  Field owner lookup not implemented yet")
            return None
        except Exception as e:
            PRINT_FUNC(f"⚠️  Failed to get field owner: {e}")
            return None

    async def transfer_field_and_events(
        self, source_boundary_id: str, target_farm_id: str, target_field_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Transfer field and copy events in one operation."""

        # Transfer field
        result = await self.transfer_field(
            source_boundary_id=source_boundary_id, target_farm_id=target_farm_id, target_field_name=target_field_name
        )

        if isinstance(result, tuple):
            new_boundary_id, farm_owner_id = result
        else:
            new_boundary_id = result
            farm_owner_id = None

        # Copy events
        event_result = await self.copy_events(source_boundary_id, new_boundary_id, farm_owner_id)

        return {"new_boundary_id": new_boundary_id, "events": event_result}


# -----------------------------
# CLI Interface
# -----------------------------


def create_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""

    parser = argparse.ArgumentParser(
        description="Field Transfer Tool - Transfer field boundaries between farms",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Transfer field boundary only (no events)
  python field_transfer.py --source-boundary-id d3053c5483609f9b543a251f428b87d8 \\
    --target-farm-id 90074 --field-only

  # Transfer field and copy events with ownership context
  python field_transfer.py --source-boundary-id d3053c5483609f9b543a251f428b87d8 \\
    --target-farm-id 90074 --grpc-url grpc://localhost:50052

  # Custom field name and service URLs
  python field_transfer.py --source-boundary-id d3053c5483609f9b543a251f428b87d8 \\
    --target-farm-id 90074 --field-only --target-field-name "My Transferred Field" \\
    --core-url http://custom-core.example.com
        """,
    )

    # Required arguments
    parser.add_argument("--source-boundary-id", required=True, help="Source boundary ID (MD5) to transfer from")

    parser.add_argument("--target-farm-id", required=True, help="Target farm ID to transfer field to")

    # Operation mode (mutually exclusive)
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument("--field-only", action="store_true", help="Transfer field boundary only (no event copying)")
    mode_group.add_argument(
        "--grpc-url", help="gRPC URL for SES (enables field transfer + event copying). Example: grpc://localhost:50052"
    )

    # Optional arguments
    parser.add_argument(
        "--target-field-name", help="Name for target field (default: transferred-from-<source-boundary-id>)"
    )

    # Service endpoints
    parser.add_argument(
        "--core-url",
        help="Core API URL",
    )
    parser.add_argument(
        "--search-url",
        help="Search service URL for event lookup",
    )

    return parser


async def main() -> None:
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()

    # No additional validation needed since target-farm-id is now required
    # and operation mode is handled by mutually exclusive group

    try:
        # Initialize clients
        if not args.grpc_url or not args.search_url or not args.core_url:
            raise AssertionError("You must set the grpc-url, search-url, and core-url to run the script.")
        grpc_url = args.grpc_url
        boundaries_client = BoundariesClient(grpc_url, args.search_url)
        core_client = CoreApiClient(args.core_url)

        ses_client = None
        if args.grpc_url:
            grpc_host = args.grpc_url
            # Remove grpc:// prefix if present
            if grpc_host.startswith("grpc://"):
                grpc_host = grpc_host[7:]

            config = SesClientConfig(grpc_host_addr=grpc_host, search_host_addr=args.search_url)
            ses_client = SesClient(config, dry_run=False)

        transfer = FieldTransfer(boundaries_client, core_client, ses_client)

        # Execute requested operation
        if args.field_only:
            PRINT_FUNC("🎯 Mode: Field transfer only")
            transfer_field_result = await transfer.transfer_field(
                source_boundary_id=args.source_boundary_id,
                target_farm_id=args.target_farm_id,
                target_field_name=args.target_field_name,
            )
            if isinstance(transfer_field_result, tuple):
                new_boundary_id, farm_owner_id = transfer_field_result
                PRINT_FUNC(f"\n🎉 Success! New boundary ID: {new_boundary_id}")
                if farm_owner_id:
                    PRINT_FUNC(f"Target farm owner: {farm_owner_id}")
            else:
                PRINT_FUNC(f"\n🎉 Success! New boundary ID: {transfer_field_result}")

        else:  # args.grpc_url
            PRINT_FUNC("🎯 Mode: Field transfer + event copy")
            transfer_field_and_events_result = await transfer.transfer_field_and_events(
                source_boundary_id=args.source_boundary_id,
                target_farm_id=args.target_farm_id,
                target_field_name=args.target_field_name,
            )
            PRINT_FUNC(f"\n🎉 Success! New boundary ID: {transfer_field_and_events_result['new_boundary_id']}")
            PRINT_FUNC(
                f"Events copied: {transfer_field_and_events_result['events']['success']}/{transfer_field_and_events_result['events']['total']}"
            )

    except Exception as e:
        PRINT_FUNC(f"\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
