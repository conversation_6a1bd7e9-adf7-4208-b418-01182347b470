from unittest.mock import patch

from programs.enums import AccountingMethod, BaselineMethod, Protocols
from reported_outcomes.enums import OutcomeApprovalStatusType
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.measure_integration import (
    MeasureOutputIntegration,
)
from scenarios_service.generalized_integration.outcome_approval_handler import (
    _obtain_outcome_integration,
    get_existing_approved_task_for_accounting_method,
    handle_outcome_approval,
)


async def test_obtain_outcome_integration(app_request, mdl):
    program = await mdl.Programs()
    measure_task = await mdl.DndcTasks(
        program_id=program.id,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    integration = await _obtain_outcome_integration(app_request, measure_task.id)
    assert isinstance(integration, MeasureOutputIntegration)
    inventory_task = await mdl.DndcTasks(
        program_id=program.id,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
    )
    integration = await _obtain_outcome_integration(app_request, inventory_task.id)
    assert isinstance(integration, InventoryOutputIntegration)


@patch(
    "scenarios_service.generalized_integration.measure_integration.MeasureOutputIntegration.approve_outcomes_for_program"
)
async def test_handle_revoking_existing_outcome_approval_with_existing_approval(
    mock_approve_outcomes, app_request, mdl, orm_select
):
    program = await mdl.Programs(
        accounting_method=AccountingMethod.intervention,
        protocol=Protocols.GENERAL_SCOPE_3,
        baseline_method=BaselineMethod.BLENDED,
    )
    await mdl.ProgramModelingConfigurations(
        program_id=program.id,
        consumer_id="test_consumer",
    )

    task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        baseline_method=BaselineMethod.BLENDED,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    task_2 = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        baseline_method=BaselineMethod.BLENDED,
        scenarios_service_api=ScenariosServiceApi.measure_api,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=task_2.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )

    # Mock the approve_outcomes_for_program to avoid calling the real scenarios service
    mock_approve_outcomes.return_value = None

    await handle_outcome_approval(app_request, program.id, task_2.id, False, False)

    from reported_outcomes.model import OutcomeApprovalStatuses

    approvals = await orm_select(OutcomeApprovalStatuses, where=[OutcomeApprovalStatuses.program_id == program.id])
    approval_1_updated = next(a for a in approvals if a.task_id == task.id)
    approval_2_updated = next(a for a in approvals if a.task_id == task_2.id)

    # Verify that the first approval was revoked and the second remains approved
    assert approval_1_updated.outcome_approval_status == OutcomeApprovalStatusType.REVOKED
    assert approval_2_updated.outcome_approval_status == OutcomeApprovalStatusType.APPROVED

    # Verify that approve_outcomes_for_program was called
    mock_approve_outcomes.assert_called_once_with(program.id, task_2.id, False)


async def test_has_existing_approval_for_task_accounting_method(app_request, mdl):
    program = await mdl.Programs()

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.inventory,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.inventory,
    )
    result = await get_existing_approved_task_for_accounting_method(app_request, test_task.id, program.id)
    assert result.id == approved_task.id


async def test_has_existing_approval_for_task_accounting_method_revoked(app_request, mdl):
    program = await mdl.Programs()

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.inventory,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.REVOKED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.inventory,
    )
    result = await get_existing_approved_task_for_accounting_method(app_request, test_task.id, program.id)
    assert result is None


async def test_has_existing_approval_for_task_accounting_method_no_approved(app_request, mdl):
    program = await mdl.Programs()

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.biofuels,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.inventory,
    )
    result = await get_existing_approved_task_for_accounting_method(app_request, test_task.id, program.id)
    assert result is None


async def test_has_existing_approval_for_task_accounting_method_with_baseline_method(app_request, mdl):
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3, baseline_method=BaselineMethod.BLENDED)

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        protocol=program.protocol,
        baseline_method=program.baseline_method,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        protocol=program.protocol,
        baseline_method=program.baseline_method,
    )
    result = await get_existing_approved_task_for_accounting_method(app_request, test_task.id, program.id)
    assert result.id == approved_task.id


async def test_has_existing_approval_for_task_accounting_method_with_different_baseline_method(app_request, mdl):
    program = await mdl.Programs(protocol=Protocols.GENERAL_SCOPE_3, baseline_method=BaselineMethod.BLENDED)

    approved_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        protocol=program.protocol,
        baseline_method=program.baseline_method,
    )
    await mdl.OutcomeApprovalStatuses(
        program_id=program.id, task_id=approved_task.id, outcome_approval_status=OutcomeApprovalStatusType.APPROVED
    )
    test_task = await mdl.DndcTasks(
        program_id=program.id,
        accounting_method=AccountingMethod.intervention,
        protocol=program.protocol,
        baseline_method=BaselineMethod.MATCHED,
    )
    result = await get_existing_approved_task_for_accounting_method(app_request, test_task.id, program.id)
    assert result is None
