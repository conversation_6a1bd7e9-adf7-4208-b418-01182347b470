from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from unittest.mock import patch

from sqlalchemy import select

from boundaries_service.schema import FeatureIntersections, Intersection
from field_lineage.enums import FieldLineageStatus, FieldRelationshipType
from field_lineage.methods import (
    _filter_md5_to_matched_fields,
    _get_and_save_previous_fields_by_md5,
    _get_and_save_previous_fields_by_overlap,
    _get_or_create_field_lineage_for_current_field,
    _get_or_create_field_lineage_for_previous_field,
    _get_previous_field_id_to_split_field_ids,
    _set_baselines_for_returning_fields,
    _set_field_baseline_and_lineage_for_returning_reset_field,
    _set_field_baselines_and_lineage_for_exact_matched_fields,
    _set_field_baselines_and_lineage_for_high_matched_fields,
    _set_field_lineage_for_new_fields,
    _set_field_lineage_for_reset_fields,
    _update_field_relationship_requests_for_merged_fields,
    _update_field_relationships_for_split_fields,
    set_field_baselines,
)
from field_lineage.model import FieldLineage, FieldRelationship
from field_lineage.schema import FieldRelationshipRequest, MatchedFields
from fields.enums import FieldStatus
from fields.model import Fields, FieldsBaseline
from helper.helper import run_query
from projects.enums import ProjectStatus


async def test_set_field_baselines_no_previous_program(mdl, app_request, db_session_maker):
    # program w/ no previous program
    program = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=None,
    )
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)

    await set_field_baselines(
        request=app_request,
        program_id=program.id,
        project_id=project.id,
        field_ids=[field_1.id, field_2.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_1.id, field_2.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_1.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.new
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_2.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.new


async def test_set_field_baselines_no_previous_fields(mdl, app_request, db_session_maker):
    # previous program w/ no previous fields
    program_0 = await mdl.Programs()

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_1 = await mdl.Fields(parent_project_id=project_1.id)
    field_2 = await mdl.Fields(parent_project_id=project_1.id)

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_1.id, field_2.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_1.id, field_2.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_1.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_2.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_1.id, field_2.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_1.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.new
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_2.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.new


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_with_previous_program(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_2 = await mdl.Fields(
        id=2,
        parent_project_id=project_0.id,
        md5="md52",
        field_lineage_id=field_lineage_2.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # field matches to previous field by MD5
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5=field_1.md5, area=field_1.area)
    # field matches to previous field by overlap
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")
    # field w/ no previous field
    field_103 = await mdl.Fields(parent_project_id=project_1.id, md5="md5103")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_102.md5: [
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=10000,
                )
            ]
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id, field_102.id, field_103.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_101.id, field_102.id, field_103.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_102.id, baseline_year=2022, is_returning=True).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_103.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field relationships
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id.in_([field_101.id, field_102.id, field_103.id]))
            .order_by(FieldRelationship.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=Decimal(field_101.area).quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP),
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_102.id,
                previous_field_id=field_2.id,
                percent_intersection=95,
                area_intersection=1,
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id, field_102.id, field_103.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 3
        assert res[0][0].to_dict() == field_lineage_1.to_dict()
        assert res[0][1] == FieldLineageStatus.returning
        assert res[1][0].to_dict() == field_lineage_2.to_dict()
        assert res[1][1] == FieldLineageStatus.returning
        assert (
            res[2][0].to_dict()
            == FieldLineage(
                id=res[2][0].id,
                baseline_field_id=field_103.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[2][0].created_at,
            ).to_dict()
        )
        assert res[2][1] == FieldLineageStatus.new


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_multiple_overlaps(mock_feature_intersections, mdl, app_request, db_session_maker):
    user = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_lineage_3 = await mdl.FieldLineage(baseline_field_id=3, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_2 = await mdl.Fields(
        id=2,
        parent_project_id=project_0.id,
        md5="md52",
        field_lineage_id=field_lineage_2.id,
        status=FieldStatus.enrolled,
    )
    field_3 = await mdl.Fields(
        id=3,
        parent_project_id=project_0.id,
        md5="md53",
        field_lineage_id=field_lineage_3.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)
    await mdl.FieldsBaseline(field_id=field_3.id, baseline_year=2021)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # field matches to multiple previous fields
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    # field matches to one previous field
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=5,
                    percent_intersection_second=5,
                    area_intersection_m2=10000,
                ),
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=20000,
                ),
            ],
            field_102.md5: [
                Intersection(
                    intersecting_id=field_3.md5,
                    percent_intersection_first=5,
                    percent_intersection_second=5,
                    area_intersection_m2=10000,
                ),
            ],
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id, field_102.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_101.id, field_102.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2022, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_102.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field relationships
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id.in_([field_101.id, field_102.id]))
            .order_by(FieldRelationship.previous_field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=5,
                area_intersection=1,
                relationship=FieldRelationshipType.NON_MATCH,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_101.id,
                previous_field_id=field_2.id,
                percent_intersection=95,
                area_intersection=2,
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldRelationship(
                id=res[2].id,
                field_id=field_102.id,
                previous_field_id=field_3.id,
                percent_intersection=5,
                area_intersection=1,
                relationship=FieldRelationshipType.NON_MATCH,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id, field_102.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        assert res[0][0].to_dict() == field_lineage_2.to_dict()
        assert res[0][1] == FieldLineageStatus.returning
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_102.id,
                previous_lineage_ids=[field_3.field_lineage_id],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.reset


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_merge(mock_feature_intersections, mdl, app_request, db_session_maker):
    user = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md511",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_2 = await mdl.Fields(
        id=2,
        parent_project_id=project_0.id,
        md5="md512",
        field_lineage_id=field_lineage_2.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2022)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # field is merge of previous fields
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=10,
                    percent_intersection_second=100,
                    area_intersection_m2=10000,
                ),
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=90,
                    percent_intersection_second=100,
                    area_intersection_m2=90000,
                ),
            ]
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field relationships
        query = select(FieldRelationship).where(FieldRelationship.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=10,
                area_intersection=1,
                relationship=FieldRelationshipType.MERGE,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_101.id,
                previous_field_id=field_2.id,
                percent_intersection=90,
                area_intersection=9,
                relationship=FieldRelationshipType.MERGE,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 1
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_101.id,
                previous_lineage_ids=[field_1.field_lineage_id, field_2.field_lineage_id],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.reset


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_split(mock_feature_intersections, mdl, app_request, db_session_maker):
    user = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # field is split from previous field
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    # field is split from previous field
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=100,
                    percent_intersection_second=50,
                    area_intersection_m2=10000,
                ),
            ],
            field_102.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=100,
                    percent_intersection_second=50,
                    area_intersection_m2=10000,
                ),
            ],
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id, field_102.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_101.id, field_102.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_102.id, baseline_year=2021, is_returning=True).to_dict()
        )

        # verify field relationships
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id.in_([field_101.id, field_102.id]))
            .order_by(FieldRelationship.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=1,
                relationship=FieldRelationshipType.SPLIT,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_102.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=1,
                relationship=FieldRelationshipType.SPLIT,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id, field_102.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        assert res[0][0].to_dict() == field_lineage_1.to_dict()
        assert res[0][1] == FieldLineageStatus.returning
        assert res[1][0].to_dict() == field_lineage_1.to_dict()
        assert res[1][1] == FieldLineageStatus.returning


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_skip(mock_feature_intersections, mdl, app_request, db_session_maker):
    # Y1 program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_lineage_3 = await mdl.FieldLineage(baseline_field_id=3, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_2 = await mdl.Fields(
        id=2,
        parent_project_id=project_0.id,
        md5="md52",
        field_lineage_id=field_lineage_2.id,
        status=FieldStatus.enrolled,
    )
    field_3 = await mdl.Fields(
        id=3,
        parent_project_id=project_0.id,
        md5="md53",
        field_lineage_id=field_lineage_3.id,
        status=FieldStatus.enrolled,
    )

    # Y2 program
    program_1 = await mdl.Programs(previous_program_id=program_0.id)

    # Y3 program
    program_2 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_1.id,
    )
    project_2 = await mdl.Projects(program_id=program_2.id)
    # field matches to field from 2 years ago by MD5
    field_201 = await mdl.Fields(
        id=201,
        parent_project_id=project_2.id,
        md5=field_1.md5,
    )
    # field matches to field from 2 years ago by overlap
    field_202 = await mdl.Fields(
        id=202,
        parent_project_id=project_2.id,
        md5="md5202",
    )
    # field does not match to field from 2 years ago
    field_203 = await mdl.Fields(
        id=203,
        parent_project_id=project_2.id,
        md5="md5203",
    )

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_202.md5: [
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=10000,
                ),
            ],
            field_203.md5: [
                Intersection(
                    intersecting_id=field_3.md5,
                    percent_intersection_first=80,
                    percent_intersection_second=80,
                    area_intersection_m2=10000,
                )
            ],
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_2.id,
        project_id=project_2.id,
        field_ids=[field_201.id, field_202.id, field_203.id],
        set_field_lineage=True,
        set_field_relationship=True,
        set_field_baseline=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_201.id, field_202.id, field_203.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_201.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_202.id, baseline_year=2024, is_returning=False).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_203.id, baseline_year=2024, is_returning=False).to_dict()
        )

        # verify field relationships
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id.in_([field_201.id, field_202.id, field_203.id]))
            .order_by(FieldRelationship.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_201.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=Decimal(field_201.area).quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP),
                relationship=FieldRelationshipType.SKIP,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_202.id,
                previous_field_id=field_2.id,
                percent_intersection=95,
                area_intersection=1,
                relationship=FieldRelationshipType.SKIP,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_201.id, field_202.id, field_203.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 3
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_201.id,
                previous_lineage_ids=[field_lineage_1.id],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.skipped_reset
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_202.id,
                previous_lineage_ids=[field_lineage_2.id],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.skipped_reset
        assert (
            res[2][0].to_dict()
            == FieldLineage(
                id=res[2][0].id,
                baseline_field_id=field_203.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[2][0].created_at,
            ).to_dict()
        )
        assert res[2][1] == FieldLineageStatus.new


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_previous_field_from_different_user(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user_0 = await mdl.Users()
    user_1 = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    # project for user 0
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user_0.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    # project for user 1
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)
    # field matches to previous field by MD5
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5=field_1.md5, area=field_1.area)

    mock_feature_intersections.return_value = FeatureIntersections(feature_intersections={})

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )

        # verify field relationships
        query = select(FieldRelationship).where(FieldRelationship.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=Decimal(field_101.area).quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP),
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 1
        assert res[0][0].to_dict() == field_lineage_1.to_dict()
        assert res[0][1] == FieldLineageStatus.returning


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_update_field_baseline(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    user = await mdl.Users()

    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id, status=ProjectStatus.enrolled)
    await mdl.ProjectPermissions(project=project_0.id, user=user.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    # program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    await mdl.ProjectPermissions(project=project_1.id, user=user.id)
    # field matches to previous field by overlap
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    # field baseline to be updated
    await mdl.FieldsBaseline(field_id=field_101.id, baseline_year=2024, is_returning=False)

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_101.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=99,
                    percent_intersection_second=99,
                    area_intersection_m2=10000,
                ),
            ]
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id],
        set_field_lineage=True,
        set_field_relationship=True,
    )

    async with db_session_maker() as s:
        # verify field baseline
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_do_not_set(mock_feature_intersections, mdl, app_request, db_session_maker):
    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51")
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52")

    # program
    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    # field matches to previous field by MD5
    field_101 = await mdl.Fields(
        parent_project_id=project_1.id, md5=field_1.md5, field_lineage_id=None, field_lineage_status=None
    )
    # field matches to previous field by overlap
    field_102 = await mdl.Fields(
        parent_project_id=project_1.id, md5="md5102", field_lineage_id=None, field_lineage_status=None
    )
    # field w/ no previous field
    field_103 = await mdl.Fields(
        parent_project_id=project_1.id, md5="md5103", field_lineage_id=None, field_lineage_status=None
    )

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_102.md5: [
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=10000,
                )
            ]
        }
    )

    await set_field_baselines(
        request=app_request,
        program_id=program_1.id,
        project_id=project_1.id,
        field_ids=[field_101.id, field_102.id, field_103.id],
        set_field_lineage=False,
        set_field_relationship=False,
        set_field_baseline=False,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id, field_102.id, field_103.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 0

        # verify field relationships
        query = select(FieldRelationship).where(
            FieldRelationship.field_id.in_([field_101.id, field_102.id, field_103.id])
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 0

        # verify field lineage
        query = select(Fields).where(Fields.id.in_([field_101.id, field_102.id, field_103.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert res[0].field_lineage_id is None
        assert res[0].field_lineage_status is None
        assert res[1].field_lineage_id is None
        assert res[1].field_lineage_status is None
        assert res[2].field_lineage_id is None
        assert res[2].field_lineage_status is None


async def test_get_and_save_previous_fields_by_md5(mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51")
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52")

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5=field_1.md5, area=field_1.area)
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")

    matched_fields, unmatched_field_md5s = await _get_and_save_previous_fields_by_md5(
        request=app_request,
        md5_to_field={"md51": field_101, "md5102": field_102},
        md5_to_previous_field={"md51": field_1, "md52": field_2},
        set_field_relationship=True,
    )
    assert matched_fields == [
        MatchedFields(
            field_id=field_101.id,
            md5="md51",
            previous_field_id=field_1.id,
            previous_md5="md51",
            previous_field_lineage_id=field_1.field_lineage_id,
            percent_intersection_of_field_by_previous=100,
            percent_intersection_of_previous_by_field=100,
        )
    ]
    assert unmatched_field_md5s == ["md5102"]

    async with db_session_maker() as s:
        query = select(FieldRelationship).where(FieldRelationship.field_id == field_101.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=Decimal(field_101.area).quantize(Decimal("0.000001"), rounding=ROUND_HALF_UP),
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )


@patch("field_lineage.methods.feature_intersections")
async def test_get_and_save_previous_fields_by_overlap(mock_feature_intersections, mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51")
    field_2 = await mdl.Fields(parent_project_id=project_0.id, md5="md52")

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")
    field_103 = await mdl.Fields(parent_project_id=project_1.id, md5="md5103")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            "md5101": [
                Intersection(
                    intersecting_id="md51",
                    percent_intersection_first=99,
                    percent_intersection_second=99,
                    area_intersection_m2=10000,
                ),
            ],
            "md5102": [
                Intersection(
                    intersecting_id="md52",
                    percent_intersection_first=10,
                    percent_intersection_second=10,
                    area_intersection_m2=20000,
                ),
            ],
        }
    )

    md5_to_matched_fields, unmatched_field_md5s = await _get_and_save_previous_fields_by_overlap(
        request=app_request,
        md5_to_field={"md5101": field_101, "md5102": field_102, "md5103": field_103},
        md5_to_previous_field={"md51": field_1, "md52": field_2},
        set_field_relationship=True,
    )
    assert md5_to_matched_fields == {
        "md5101": [
            MatchedFields(
                field_id=field_101.id,
                md5="md5101",
                previous_field_id=field_1.id,
                previous_md5="md51",
                previous_field_lineage_id=field_1.field_lineage_id,
                percent_intersection_of_field_by_previous=99,
                percent_intersection_of_previous_by_field=99,
            )
        ],
        "md5102": [
            MatchedFields(
                field_id=field_102.id,
                md5="md5102",
                previous_field_id=field_2.id,
                previous_md5="md52",
                previous_field_lineage_id=field_2.field_lineage_id,
                percent_intersection_of_field_by_previous=10,
                percent_intersection_of_previous_by_field=10,
            )
        ],
    }
    assert unmatched_field_md5s == ["md5103"]

    async with db_session_maker() as s:
        query = select(FieldRelationship).where(
            FieldRelationship.field_id.in_([field_101.id, field_102.id, field_103.id])
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=field_101.id,
                previous_field_id=field_1.id,
                percent_intersection=99,
                area_intersection=1,
                relationship=FieldRelationshipType.MATCH,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=field_102.id,
                previous_field_id=field_2.id,
                percent_intersection=10,
                area_intersection=2,
                relationship=FieldRelationshipType.NON_MATCH,
            ).to_dict()
        )


async def test_filter_md5_to_matched_fields():
    md5_to_matched_fields = {
        # one high matched field
        "md5101": [
            MatchedFields(
                field_id=101,
                md5="md5101",
                previous_field_id=11,
                previous_md5="md511",
                previous_field_lineage_id=1,
                percent_intersection_of_field_by_previous=99,
                percent_intersection_of_previous_by_field=99,
            ),
            MatchedFields(
                field_id=101,
                md5="md5101",
                previous_field_id=12,
                previous_md5="md512",
                previous_field_lineage_id=1,
                percent_intersection_of_field_by_previous=10,
                percent_intersection_of_previous_by_field=10,
            ),
        ],
        # merged fields
        "md5102": [
            MatchedFields(
                field_id=102,
                md5="md5102",
                previous_field_id=21,
                previous_md5="md521",
                previous_field_lineage_id=2,
                percent_intersection_of_field_by_previous=90,
                percent_intersection_of_previous_by_field=100,
            ),
            MatchedFields(
                field_id=102,
                md5="md5102",
                previous_field_id=22,
                previous_md5="md522",
                previous_field_lineage_id=2,
                percent_intersection_of_field_by_previous=10,
                percent_intersection_of_previous_by_field=100,
            ),
        ],
        # one low matched field
        "md5103": [
            MatchedFields(
                field_id=103,
                md5="md5103",
                previous_field_id=31,
                previous_md5="md531",
                previous_field_lineage_id=3,
                percent_intersection_of_field_by_previous=10,
                percent_intersection_of_previous_by_field=10,
            ),
        ],
    }
    md5_to_high_matched_field, md5_to_low_matched_fields = await _filter_md5_to_matched_fields(
        md5_to_matched_fields=md5_to_matched_fields
    )
    assert md5_to_high_matched_field == {"md5101": md5_to_matched_fields["md5101"][0]}
    assert md5_to_low_matched_fields == {
        "md5102": md5_to_matched_fields["md5102"],
        "md5103": md5_to_matched_fields["md5103"],
    }


async def test_set_baselines_for_returning_fields(mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id, md5="md51", status=FieldStatus.enrolled)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2021)

    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2025, 1, 1),
        reporting_period_end_date=datetime(2025, 12, 31),
        previous_program_id=program_0.id,
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")

    await _set_baselines_for_returning_fields(
        request=app_request,
        matched_fields=[
            MatchedFields(
                field_id=field_101.id,
                md5="md5101",
                previous_field_id=field_1.id,
                previous_md5="md51",
                previous_field_lineage_id=field_1.field_lineage_id,
                percent_intersection_of_field_by_previous=99,
                percent_intersection_of_previous_by_field=99,
            )
        ],
    )

    async with db_session_maker() as s:
        query = select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_101.id]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2021, is_returning=True).to_dict()
        )


async def test_set_field_lineage_for_new_fields(mdl, app_request, db_session_maker):
    """Test internal method to set field lineage for fields w/ no previous fields."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)

    await _set_field_lineage_for_new_fields(request=app_request, field_ids=[field_1.id, field_2.id])

    async with db_session_maker() as s:
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_1.id, field_2.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_1.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.new
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_2.id,
                previous_lineage_ids=[],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.new


async def test_set_field_lineage_for_reset_fields(mdl, app_request, db_session_maker):
    """Test internal method to set field lineage for fields that insufficiently overlap previous fields."""
    # previous program
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[0])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_1 = await mdl.Fields(id=1, parent_project_id=project_0.id, md5="md51", field_lineage_id=field_lineage_1.id)
    field_2 = await mdl.Fields(id=2, parent_project_id=project_0.id, md5="md52", field_lineage_id=field_lineage_2.id)

    # program
    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    # field insufficiently overlaps previous field
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5="md5101")
    # field insufficiently overlap previous field
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5="md5102")

    await _set_field_lineage_for_reset_fields(
        request=app_request,
        md5_to_low_matched_fields={
            field_101.md5: [
                MatchedFields(
                    field_id=field_101.id,
                    md5=field_101.md5,
                    previous_field_id=field_1.id,
                    previous_md5=field_1.md5,
                    previous_field_lineage_id=field_1.field_lineage_id,
                    percent_intersection_of_field_by_previous=5,
                    percent_intersection_of_previous_by_field=5,
                )
            ],
            field_102.md5: [
                MatchedFields(
                    field_id=field_102.id,
                    md5=field_102.md5,
                    previous_field_id=field_2.id,
                    previous_md5=field_2.md5,
                    previous_field_lineage_id=field_2.field_lineage_id,
                    percent_intersection_of_field_by_previous=5,
                    percent_intersection_of_previous_by_field=5,
                )
            ],
        },
    )

    async with db_session_maker() as s:
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_101.id, field_102.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_101.id,
                previous_lineage_ids=[field_1.field_lineage_id, 0],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.reset
        assert (
            res[1][0].to_dict()
            == FieldLineage(
                id=res[1][0].id,
                baseline_field_id=field_102.id,
                previous_lineage_ids=[field_2.field_lineage_id],
                created_year=None,
                created_at=res[1][0].created_at,
            ).to_dict()
        )
        assert res[1][1] == FieldLineageStatus.reset


async def test_set_field_baselines_and_lineage_for_exact_matched_fields(mdl, app_request, db_session_maker):
    """Test internal method to set field baselines and lineage for fields that match to previous fields by MD5."""
    # previous program
    program_0 = await mdl.Programs(reporting_period_start_date=datetime(2021, 1, 1))
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_1 = await mdl.Fields(id=1, parent_project_id=project_0.id, md5="md51", field_lineage_id=field_lineage_1.id)
    field_2 = await mdl.Fields(id=2, parent_project_id=project_0.id, md5="md52", field_lineage_id=field_lineage_2.id)
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2020)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2020)

    # program
    program_1 = await mdl.Programs(reporting_period_start_date=datetime(2022, 1, 1))
    project_1 = await mdl.Projects(program_id=program_1.id)
    # field matches to previous field by MD5
    field_101 = await mdl.Fields(parent_project_id=project_1.id, md5=field_1.md5)
    # field matches to previous field by MD5
    field_102 = await mdl.Fields(parent_project_id=project_1.id, md5=field_2.md5)

    await _set_field_baselines_and_lineage_for_exact_matched_fields(
        request=app_request,
        matched_fields=[
            MatchedFields(
                field_id=field_101.id,
                md5=field_101.md5,
                previous_field_id=field_1.id,
                previous_md5=field_1.md5,
                previous_field_lineage_id=field_1.field_lineage_id,
                percent_intersection_of_field_by_previous=100,
                percent_intersection_of_previous_by_field=100,
            ),
            MatchedFields(
                field_id=field_102.id,
                md5=field_102.md5,
                previous_field_id=field_2.id,
                previous_md5=field_2.md5,
                previous_field_lineage_id=field_2.field_lineage_id,
                percent_intersection_of_field_by_previous=100,
                percent_intersection_of_previous_by_field=100,
            ),
        ],
        set_field_lineage=True,
        set_field_baseline=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_101.id, field_102.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_101.id, baseline_year=2020, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_102.id, baseline_year=2020, is_returning=True).to_dict()
        )

        # verify field lineage
        query = select(Fields).where(Fields.id.in_([field_101.id, field_102.id])).order_by(Fields.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert res[0].field_lineage_id == field_lineage_1.id
        assert res[0].field_lineage_status == FieldLineageStatus.returning
        assert res[1].field_lineage_id == field_lineage_2.id
        assert res[1].field_lineage_status == FieldLineageStatus.returning


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baselines_and_lineage_for_high_matched_fields(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    """Test internal method to set field baselines and lineage for fields that match to previous fields by overlap."""
    # Y1 program
    program_0 = await mdl.Programs(
        reporting_period_start_date=datetime(2021, 1, 1), reporting_period_end_date=datetime(2021, 12, 31)
    )
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])
    field_lineage_3 = await mdl.FieldLineage(baseline_field_id=3, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_0.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_2 = await mdl.Fields(
        id=2,
        parent_project_id=project_0.id,
        md5="md52",
        field_lineage_id=field_lineage_2.id,
        status=FieldStatus.enrolled,
    )
    field_3 = await mdl.Fields(
        id=3,
        parent_project_id=project_0.id,
        md5="md53",
        field_lineage_id=field_lineage_3.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2020)
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2020)
    await mdl.FieldsBaseline(field_id=field_3.id, baseline_year=2020)

    # Y2 program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2022, 1, 1), reporting_period_end_date=datetime(2022, 12, 31)
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(
        parent_project_id=project_1.id,
        md5=field_1.md5,
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    field_102 = await mdl.Fields(
        parent_project_id=project_1.id, md5="md5102", field_lineage_id=field_lineage_2.id, status=FieldStatus.enrolled
    )
    field_103 = await mdl.Fields(
        parent_project_id=project_1.id, md5="md5103", field_lineage_id=field_lineage_3.id, status=FieldStatus.enrolled
    )
    await mdl.FieldsBaseline(field_id=field_101.id, baseline_year=2020)
    await mdl.FieldsBaseline(field_id=field_102.id, baseline_year=2020)
    await mdl.FieldsBaseline(field_id=field_103.id, baseline_year=2020)

    # Y3 program
    program_2 = await mdl.Programs(
        reporting_period_start_date=datetime(2023, 1, 1), reporting_period_end_date=datetime(2023, 12, 31)
    )
    project_2 = await mdl.Projects(program_id=program_2.id)
    # field's previous field = baseline field
    field_201 = await mdl.Fields(parent_project_id=project_2.id, md5="md5201")
    # field sufficiently overlaps baseline field
    field_202 = await mdl.Fields(parent_project_id=project_2.id, md5="md5202")
    # field insufficiently overlaps baseline field
    field_203 = await mdl.Fields(parent_project_id=project_2.id, md5="md5203")

    mock_feature_intersections.side_effect = [
        FeatureIntersections(
            feature_intersections={
                field_201.md5: [
                    Intersection(
                        intersecting_id=field_1.md5,
                        percent_intersection_first=100,
                        percent_intersection_second=100,
                        area_intersection_m2=1,
                    ),
                ],
                field_202.md5: [
                    Intersection(
                        intersecting_id=field_2.md5,
                        percent_intersection_first=95,
                        percent_intersection_second=95,
                        area_intersection_m2=1,
                    ),
                ],
                field_203.md5: [
                    Intersection(
                        intersecting_id=field_3.md5,
                        percent_intersection_first=80,
                        percent_intersection_second=80,
                        area_intersection_m2=1,
                    ),
                ],
            }
        ),
        FeatureIntersections(
            feature_intersections={
                field_203.md5: [
                    Intersection(
                        intersecting_id=field_3.md5,
                        percent_intersection_first=80,
                        percent_intersection_second=80,
                        area_intersection_m2=1,
                    ),
                    Intersection(
                        intersecting_id=field_103.md5,
                        percent_intersection_first=95,
                        percent_intersection_second=95,
                        area_intersection_m2=1,
                    ),
                ],
            }
        ),
    ]

    await _set_field_baselines_and_lineage_for_high_matched_fields(
        request=app_request,
        matched_fields=[
            MatchedFields(
                field_id=field_201.id,
                md5=field_201.md5,
                previous_field_id=field_101.id,
                previous_md5=field_101.md5,
                previous_field_lineage_id=field_101.field_lineage_id,
                percent_intersection_of_field_by_previous=95,
                percent_intersection_of_previous_by_field=95,
            ),
            MatchedFields(
                field_id=field_202.id,
                md5=field_202.md5,
                previous_field_id=field_102.id,
                previous_md5=field_102.md5,
                previous_field_lineage_id=field_102.field_lineage_id,
                percent_intersection_of_field_by_previous=95,
                percent_intersection_of_previous_by_field=95,
            ),
            MatchedFields(
                field_id=field_203.id,
                md5=field_203.md5,
                previous_field_id=field_103.id,
                previous_md5=field_103.md5,
                previous_field_lineage_id=field_103.field_lineage_id,
                percent_intersection_of_field_by_previous=95,
                percent_intersection_of_previous_by_field=95,
            ),
        ],
        set_field_lineage=True,
        set_field_baseline=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline)
            .where(FieldsBaseline.field_id.in_([field_201.id, field_202.id, field_203.id]))
            .order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_201.id, baseline_year=2020, is_returning=True).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(id=res[1].id, field_id=field_202.id, baseline_year=2020, is_returning=True).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(id=res[2].id, field_id=field_203.id, baseline_year=2021, is_returning=True).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_201.id, field_202.id, field_203.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 3
        assert res[0][0].to_dict() == field_lineage_1.to_dict()
        assert res[0][1] == FieldLineageStatus.returning
        assert res[1][0].to_dict() == field_lineage_2.to_dict()
        assert res[1][1] == FieldLineageStatus.returning
        assert (
            res[2][0].to_dict()
            == FieldLineage(
                id=res[2][0].id,
                baseline_field_id=field_103.id,
                previous_lineage_ids=[field_lineage_3.id],
                created_year=None,
                created_at=res[2][0].created_at,
            ).to_dict()
        )
        assert res[2][1] == FieldLineageStatus.returning_reset


@patch("field_lineage.methods.feature_intersections")
async def test_set_field_baseline_and_lineage_for_returning_reset_field(
    mock_feature_intersections, mdl, app_request, db_session_maker
):
    """
    Test internal method to set field baseline and lineage for a field that matches to a previous field,
    but not to the previous field's baseline field.
    """
    # Y1 program
    program_1 = await mdl.Programs(
        reporting_period_start_date=datetime(2021, 1, 1), reporting_period_end_date=datetime(2021, 12, 31)
    )
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[])
    field_1 = await mdl.Fields(
        id=1,
        parent_project_id=project_1.id,
        md5="md51",
        field_lineage_id=field_lineage_1.id,
        status=FieldStatus.enrolled,
    )
    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2020)

    # Y2 program
    program_2 = await mdl.Programs(
        reporting_period_start_date=datetime(2022, 1, 1), reporting_period_end_date=datetime(2022, 12, 31)
    )
    project_2 = await mdl.Projects(program_id=program_2.id)
    field_2 = await mdl.Fields(
        parent_project_id=project_2.id, md5="md52", field_lineage_id=field_lineage_1.id, status=FieldStatus.enrolled
    )
    await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2020)

    # Y3 program
    program_3 = await mdl.Programs(
        reporting_period_start_date=datetime(2023, 1, 1), reporting_period_end_date=datetime(2023, 12, 31)
    )
    project_3 = await mdl.Projects(program_id=program_3.id)
    field_3 = await mdl.Fields(
        parent_project_id=project_3.id, md5="md53", field_lineage_id=field_lineage_1.id, status=FieldStatus.enrolled
    )
    await mdl.FieldsBaseline(field_id=field_3.id, baseline_year=2020)

    # Y4 program
    program_4 = await mdl.Programs(
        reporting_period_start_date=datetime(2024, 1, 1), reporting_period_end_date=datetime(2024, 12, 31)
    )
    project_4 = await mdl.Projects(program_id=program_4.id)
    # field insufficiently overlaps baseline field
    field_4 = await mdl.Fields(parent_project_id=project_4.id, md5="md54")

    mock_feature_intersections.return_value = FeatureIntersections(
        feature_intersections={
            field_4.md5: [
                Intersection(
                    intersecting_id=field_1.md5,
                    percent_intersection_first=80,
                    percent_intersection_second=80,
                    area_intersection_m2=1,
                ),
                Intersection(
                    intersecting_id=field_2.md5,
                    percent_intersection_first=90,
                    percent_intersection_second=90,
                    area_intersection_m2=1,
                ),
                Intersection(
                    intersecting_id=field_3.md5,
                    percent_intersection_first=95,
                    percent_intersection_second=95,
                    area_intersection_m2=1,
                ),
            ]
        }
    )

    await _set_field_baseline_and_lineage_for_returning_reset_field(
        request=app_request,
        matched_field=MatchedFields(
            field_id=field_4.id,
            md5=field_4.md5,
            previous_field_id=field_3.id,
            previous_md5=field_3.md5,
            previous_field_lineage_id=field_3.field_lineage_id,
            percent_intersection_of_field_by_previous=95,
            percent_intersection_of_previous_by_field=95,
        ),
        baseline_field=field_1,
        set_field_lineage=True,
        set_field_baseline=True,
    )

    async with db_session_maker() as s:
        # verify field baselines
        query = (
            select(FieldsBaseline).where(FieldsBaseline.field_id.in_([field_4.id])).order_by(FieldsBaseline.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldsBaseline(id=res[0].id, field_id=field_4.id, baseline_year=2021, is_returning=True).to_dict()
        )

        # verify field lineage
        query = (
            select(FieldLineage, Fields.field_lineage_status)
            .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
            .where(Fields.id.in_([field_4.id]))
            .order_by(Fields.id)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 1
        assert (
            res[0][0].to_dict()
            == FieldLineage(
                id=res[0][0].id,
                baseline_field_id=field_2.id,
                previous_lineage_ids=[field_1.field_lineage_id],
                created_year=None,
                created_at=res[0][0].created_at,
            ).to_dict()
        )
        assert res[0][1] == FieldLineageStatus.returning_reset


async def test_get_or_create_field_lineage_for_current_field_get(mdl, app_request, db_session_maker):
    field_lineage = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[0])

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(id=1, parent_project_id=project.id, field_lineage_id=field_lineage.id)

    # update previous lineage IDs
    field_lineage_id = await _get_or_create_field_lineage_for_current_field(
        request=app_request, field_id=field.id, immediate_previous_field_lineage_ids=[]
    )
    assert field_lineage_id == field_lineage.id

    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id == field.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res[0].previous_lineage_ids == []


async def test_get_or_create_field_lineage_for_current_field_create(mdl, app_request, db_session_maker):
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[0])
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2, previous_lineage_ids=[])

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id)

    # no previous lineage IDs
    await _get_or_create_field_lineage_for_current_field(
        request=app_request, field_id=field_101.id, immediate_previous_field_lineage_ids=[]
    )
    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id == field_101.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res[0].previous_lineage_ids == []

    # one previous lineage ID
    await _get_or_create_field_lineage_for_current_field(
        request=app_request, field_id=field_101.id, immediate_previous_field_lineage_ids=[field_lineage_1.id]
    )
    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id == field_101.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res[0].previous_lineage_ids == [field_lineage_1.id, 0]

    # multiple previous lineage IDs
    await _get_or_create_field_lineage_for_current_field(
        request=app_request,
        field_id=field_101.id,
        immediate_previous_field_lineage_ids=[field_lineage_1.id, field_lineage_2.id],
    )
    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id == field_101.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res[0].previous_lineage_ids == [field_lineage_1.id, field_lineage_2.id]


async def test_get_or_create_field_lineage_for_previous_field(mdl, app_request, db_session_maker):
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1, previous_lineage_ids=[0])

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, field_lineage_id=field_lineage_1.id)

    await _get_or_create_field_lineage_for_previous_field(request=app_request, previous_field=field_101)

    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id == field_101.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res[0].previous_lineage_ids == [field_lineage_1.id, 0]


async def test_update_field_relationship_requests_for_merged_fields():
    fields_to_field_relationship_requests = {
        ("md5101", "md511"): FieldRelationshipRequest(
            field_id=101,
            previous_field_id=11,
            percent_intersection=10,
            area_intersection=1,
            relationship=FieldRelationshipType.NON_MATCH,
        ),
        ("md5101", "md512"): FieldRelationshipRequest(
            field_id=101,
            previous_field_id=12,
            percent_intersection=90,
            area_intersection=9,
            relationship=FieldRelationshipType.MATCH,
        ),
        ("md5102", "md521"): FieldRelationshipRequest(
            field_id=102,
            previous_field_id=21,
            percent_intersection=100,
            area_intersection=1,
            relationship=FieldRelationshipType.MATCH,
        ),
    }
    await _update_field_relationship_requests_for_merged_fields(
        md5="md5101",
        merged_intersections=[
            Intersection(
                intersecting_id="md511",
                percent_intersection_first=10,
                percent_intersection_second=100,
                area_intersection_m2=10000,
            ),
            Intersection(
                intersecting_id="md512",
                percent_intersection_first=90,
                percent_intersection_second=100,
                area_intersection_m2=90000,
            ),
        ],
        fields_to_field_relationship_requests=fields_to_field_relationship_requests,
    )
    assert fields_to_field_relationship_requests == {
        ("md5101", "md511"): FieldRelationshipRequest(
            field_id=101,
            previous_field_id=11,
            percent_intersection=10,
            area_intersection=1,
            relationship=FieldRelationshipType.MERGE,
        ),
        ("md5101", "md512"): FieldRelationshipRequest(
            field_id=101,
            previous_field_id=12,
            percent_intersection=90,
            area_intersection=9,
            relationship=FieldRelationshipType.MERGE,
        ),
        ("md5102", "md521"): FieldRelationshipRequest(
            field_id=102,
            previous_field_id=21,
            percent_intersection=100,
            area_intersection=1,
            relationship=FieldRelationshipType.MATCH,
        ),
    }


async def test_get_previous_field_id_to_split_field_ids(mdl, app_request, db_session_maker):
    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(parent_project_id=project_0.id)
    field_2 = await mdl.Fields(parent_project_id=project_0.id)

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_11 = await mdl.Fields(parent_project_id=project_1.id)
    field_12 = await mdl.Fields(parent_project_id=project_1.id)
    field_13 = await mdl.Fields(parent_project_id=project_1.id)
    field_14 = await mdl.Fields(parent_project_id=project_1.id)

    other_program = await mdl.Programs()
    other_project = await mdl.Projects(program_id=other_program.id)
    other_field = await mdl.Fields(parent_project_id=other_project.id)

    # split
    await mdl.FieldRelationship(
        field_id=field_11.id,
        previous_field_id=field_1.id,
        percent_intersection=95,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )
    await mdl.FieldRelationship(
        field_id=field_12.id,
        previous_field_id=field_1.id,
        percent_intersection=95,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )

    # not split because insufficient overlap
    await mdl.FieldRelationship(
        field_id=field_13.id,
        previous_field_id=field_2.id,
        percent_intersection=80,
        area_intersection=1,
        relationship=FieldRelationshipType.NON_MATCH,
    )
    await mdl.FieldRelationship(
        field_id=field_14.id,
        previous_field_id=field_2.id,
        percent_intersection=95,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )

    # not split because not in same program
    await mdl.FieldRelationship(
        field_id=other_field.id,
        previous_field_id=field_1.id,
        percent_intersection=95,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )

    res = await _get_previous_field_id_to_split_field_ids(
        request=app_request, program_id=program_1.id, field_ids=[field_11.id, field_13.id]
    )
    assert len(res) == 1
    assert sorted(res[field_1.id]) == [field_11.id, field_12.id]


@patch("field_lineage.methods._get_previous_field_id_to_split_field_ids")
async def test_update_field_relationships_for_split_fields(
    mock_get_previous_field_id_to_split_field_ids, mdl, app_request, db_session_maker
):
    await mdl.FieldRelationship(
        field_id=1,
        previous_field_id=0,
        percent_intersection=100,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )
    await mdl.FieldRelationship(
        field_id=2,
        previous_field_id=0,
        percent_intersection=100,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )
    await mdl.FieldRelationship(
        field_id=4,
        previous_field_id=3,
        percent_intersection=100,
        area_intersection=1,
        relationship=FieldRelationshipType.MATCH,
    )

    mock_get_previous_field_id_to_split_field_ids.return_value = {0: [1, 2]}

    await _update_field_relationships_for_split_fields(request=app_request, program_id=1, field_ids=[1, 4])

    async with db_session_maker() as s:
        query = (
            select(FieldRelationship.field_id)
            .where(FieldRelationship.relationship == FieldRelationshipType.SPLIT)
            .order_by(FieldRelationship.field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert res == [1, 2]
