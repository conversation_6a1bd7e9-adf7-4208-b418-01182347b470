"""update default date

Revision ID: d06638189291
Revises: fe6f40f92b04
Create Date: 2025-07-23 11:56:23.189320

"""

from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d06638189291"
down_revision = "fe6f40f92b04"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "mrv_programs",
        "date_format",
        existing_type=mysql.VARCHAR(charset="utf8mb3", collation="utf8mb3_bin", length=10),
        server_default="yyyy-MM-dd",
        existing_nullable=False,
    )


def downgrade():
    pass
