import uuid
from unittest.mock import ANY, Mock

import pytest
from regrow.ses.event.v1.event_service_pb2 import FetchEventWithContextRequest
from ses_client.event import EventWithContext

from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate
from ses_integration.fetch_entity_events import (
    fetch_events_for_field_phase,
    MAX_FETCH_EVENT_REQUEST_BATCH_SIZE,
    reconcile_events_if_mismatch,
)
from ses_integration.schema import EventRevision


@pytest.mark.parametrize(
    "enrollment_phase_only,is_single_phase_program,expected_phase_type",
    [(False, False, PhaseTypes.MONITORING), (True, False, PhaseTypes.ENROLMENT), (False, True, PhaseTypes.ENROLMENT)],
)
async def test_fetch_events_for_field_phase(
    enrollment_phase_only,
    is_single_phase_program,
    expected_phase_type,
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=expected_phase_type, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mock_get_event_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: event_revisions},
    )
    mock_fetch_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=enrollment_phase_only,
        is_single_phase_program=is_single_phase_program,
    )
    assert res == [cropping_event, tillage_event]

    mock_get_event_revisions.assert_called_with(
        request=app_request, field_ids=[field.id], phase_type=expected_phase_type
    )
    mock_fetch_events.assert_called_with(
        fetch_event_requests=[FetchEventWithContextRequest(id=str(event_id), revision="1", merge_context=False)]
    )
    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert {ev.event.id for ev in mock_parse_ses_events.call_args.kwargs["cropping_events"][0]} == {
        sowing_activity_and_context[0].id,
        harvest_activity_and_context[0].id,
        termination_activity_and_context[0].id,
    }
    assert (
        mock_parse_ses_events.call_args.kwargs["field_practice_events"][0].event.id
        == tillage_activity_and_context[0].id
    )


async def test_fetch_events_for_field_phase_batch(
    app_request,
    mdl,
    mocker,
    tillage_activity_and_context,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    event_ids = []
    for _ in range(MAX_FETCH_EVENT_REQUEST_BATCH_SIZE * 2):
        event_ids.append(uuid.uuid4())

    event_revisions = [EventRevision(event_id=event_id, revision=1) for event_id in event_ids]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: event_revisions},
    )
    mock_fetch_event_revisions_with_context = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*tillage_activity_and_context),
        ]
        * MAX_FETCH_EVENT_REQUEST_BATCH_SIZE,
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert len(res) == MAX_FETCH_EVENT_REQUEST_BATCH_SIZE * 2

    assert mock_fetch_event_revisions_with_context.call_count == 2


async def test_fetch_events_for_field_phase_with_needed_reconciliation(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mock_get_event_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        # empty dict to simulate no revisions found, triggering reconciliation
        side_effect=[{field.id: []}, {field.id: event_revisions}],
    )
    mock_search_events = mocker.patch(
        "ses_client.client.Client.fetch_events_for_fields",
        # doesn't matter what we return here, we are mocking fetch_event_revisions_with_context
        return_value={},
    )
    mock_fetch_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=False,
        is_single_phase_program=False,
    )
    assert res == [cropping_event, tillage_event]

    assert mock_get_event_revisions.call_count == 2
    mock_search_events.assert_called_with(field_ids=[field.md5], search_filter=ANY)
    mock_get_event_revisions.assert_called_with(
        request=app_request, field_ids=[field.id], phase_type=PhaseTypes.MONITORING
    )
    mock_fetch_events.assert_called_with(
        fetch_event_requests=[FetchEventWithContextRequest(id=str(event_id), revision="1", merge_context=False)]
    )
    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id


async def test_fetch_events_for_field_phase_with_disabled_cropping_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=False)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: event_revisions},
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [tillage_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert mock_parse_ses_events.call_args.kwargs["cropping_events"] == []
    assert len(mock_parse_ses_events.call_args.kwargs["field_practice_events"]) == 1


async def test_fetch_events_for_field_phase_with_disabled_tillage_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=False)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: event_revisions},
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [cropping_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert len(mock_parse_ses_events.call_args.kwargs["cropping_events"][0]) == 3
    assert mock_parse_ses_events.call_args.kwargs["field_practice_events"] == []


async def test_fetch_events_for_field_phase_with_disabled_monitoring_tillage_stage_in_enrollment(
    app_request,
    mdl,
    mocker,
    sowing_activity_and_context,
    harvest_activity_and_context,
    termination_activity_and_context,
    tillage_activity_and_context,
    cropping_event,
    tillage_event,
):
    program = await mdl.Programs()
    enrollment_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    await mdl.Stage(phase_id=enrollment_phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=enrollment_phase.id, type_=StageTypes.TILLAGE_EVENTS, enabled=True)
    monitoring = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    await mdl.Stage(phase_id=monitoring.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=monitoring.id, type_=StageTypes.TILLAGE_EVENTS, enabled=False)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid.uuid4()

    event_revisions = [EventRevision(event_id=event_id, revision=1)]

    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: event_revisions},
    )
    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_event_revisions_with_context",
        return_value=[
            EventWithContext(*sowing_activity_and_context),
            EventWithContext(*harvest_activity_and_context),
            EventWithContext(*termination_activity_and_context),
            EventWithContext(*tillage_activity_and_context),
        ],
    )
    mock_parse_ses_events = mocker.patch(
        "ses_integration.fetch_entity_events.parse_ses_events_to_entity_events",
        return_value=[cropping_event, tillage_event],
    )

    res = await fetch_events_for_field_phase(
        request=app_request,
        field_id=field.id,
        enrollment_phase_only=True,
        is_single_phase_program=False,
    )
    assert res == [cropping_event, tillage_event]

    assert mock_parse_ses_events.call_args.kwargs["field"].id == field.id
    assert len(mock_parse_ses_events.call_args.kwargs["cropping_events"][0]) == 3
    assert len(mock_parse_ses_events.call_args.kwargs["field_practice_events"]) == 1


@pytest.mark.asyncio
async def test_reconcile_events_if_mismatch_single_field_batch(app_request, mdl, mocker):
    """Test reconciliation with a single field in batch mode (using fetch_events_for_fields)."""
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    ses_event_id = uuid.uuid4()
    mrv_event_id = uuid.uuid4()

    mocker.patch(
        "ses_integration.fetch_entity_events.get_owner_user_id_for_project",
        return_value="test_user_id",
    )

    mocker.patch(
        "ui.projects.field_events.methods.get_event_query_range_for_reporting_period",
        return_value=("2023-01-01T00:00:00Z", "2023-12-31T23:59:59Z"),
    )

    ses_event_mock = Mock()
    ses_event_mock.id = str(ses_event_id)
    ses_event_mock.revision = 2

    mock_pb_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_events_for_fields",
        return_value={field.md5: [ses_event_mock]},
    )

    mrv_revision = EventRevision(event_id=mrv_event_id, revision=1)
    mock_get_mrv_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: [mrv_revision]},
    )

    mock_reconcile = mocker.patch("ses_integration.reconcile_event_associations.reconcile_event_associations_for_field")

    await reconcile_events_if_mismatch(
        request=app_request,
        program=program,
        fields=[field],
    )

    mock_pb_events.assert_called_once()
    call_args = mock_pb_events.call_args
    assert field.md5 in call_args[1]["field_ids"]

    mock_get_mrv_revisions.assert_called_once_with(request=app_request, field_ids=[field.id], phase_type=None)

    mock_reconcile.assert_called_once_with(request=app_request, field_id=field.id)


@pytest.mark.asyncio
async def test_reconcile_events_if_mismatch_multiple_fields_batch(app_request, mdl, mocker):
    """Test reconciliation with multiple fields in batch mode."""
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    field3 = await mdl.Fields(parent_project_id=project.id)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)

    ses_event_id1 = uuid.uuid4()
    ses_event_id2 = uuid.uuid4()
    mrv_event_id1 = uuid.uuid4()

    mocker.patch(
        "ses_integration.fetch_entity_events.get_owner_user_id_for_project",
        return_value="test_user_id",
    )
    mocker.patch(
        "ui.projects.field_events.methods.get_event_query_range_for_reporting_period",
        return_value=("2023-01-01T00:00:00Z", "2023-12-31T23:59:59Z"),
    )

    ses_event_mock1 = Mock()
    ses_event_mock1.id = str(ses_event_id1)
    ses_event_mock1.revision = 1

    ses_event_mock2 = Mock()
    ses_event_mock2.id = str(ses_event_id2)
    ses_event_mock2.revision = 2

    mock_pb_events = mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_events_for_fields",
        return_value={
            field1.md5: [ses_event_mock1],
            field2.md5: [ses_event_mock2],
        },
    )

    mrv_revision1 = EventRevision(event_id=ses_event_id1, revision=1)
    mrv_revision2 = EventRevision(event_id=mrv_event_id1, revision=1)
    mrv_revision3 = EventRevision(event_id=uuid.uuid4(), revision=1)

    mock_get_mrv_revisions = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={
            field1.id: [mrv_revision1],
            field2.id: [mrv_revision2],
            field3.id: [mrv_revision3],
        },
    )

    mock_reconcile = mocker.patch("ses_integration.reconcile_event_associations.reconcile_event_associations_for_field")

    await reconcile_events_if_mismatch(
        request=app_request,
        program=program,
        fields=[field1, field2, field3],
    )

    mock_pb_events.assert_called_once()
    call_args = mock_pb_events.call_args
    field_ids_called = call_args[1]["field_ids"]
    assert field1.md5 in field_ids_called
    assert field2.md5 in field_ids_called
    assert field3.md5 in field_ids_called
    assert len(field_ids_called) == 3

    mock_get_mrv_revisions.assert_called_once_with(
        request=app_request,
        field_ids=[field1.id, field2.id, field3.id],
        phase_type=None,
    )

    assert mock_reconcile.call_count == 2
    reconciled_field_ids = [call[1]["field_id"] for call in mock_reconcile.call_args_list]
    assert field2.id in reconciled_field_ids
    assert field3.id in reconciled_field_ids
    assert field1.id not in reconciled_field_ids


@pytest.mark.asyncio
async def test_reconcile_events_if_mismatch_no_mismatch_batch(app_request, mdl, mocker):
    """Test reconciliation when SES and MRV data match (no reconciliation needed)."""
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    event_id = uuid.uuid4()

    mocker.patch(
        "ses_integration.fetch_entity_events.get_owner_user_id_for_project",
        return_value="test_user_id",
    )
    mocker.patch(
        "ui.projects.field_events.methods.get_event_query_range_for_reporting_period",
        return_value=("2023-01-01T00:00:00Z", "2023-12-31T23:59:59Z"),
    )

    ses_event_mock = Mock()
    ses_event_mock.id = str(event_id)
    ses_event_mock.revision = 1

    mocker.patch(
        "ses_integration.fetch_entity_events.ses_client.fetch_events_for_fields",
        return_value={field.md5: [ses_event_mock]},
    )

    mrv_revision = EventRevision(event_id=event_id, revision=1)
    mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase",
        return_value={field.id: [mrv_revision]},
    )

    mock_reconcile = mocker.patch("ses_integration.reconcile_event_associations.reconcile_event_associations_for_field")

    await reconcile_events_if_mismatch(
        request=app_request,
        program=program,
        fields=[field],
    )

    mock_reconcile.assert_not_called()


@pytest.mark.asyncio
async def test_reconcile_events_if_mismatch_empty_fields_batch(app_request, mdl, mocker):
    """Test reconciliation with empty fields list."""
    program = await mdl.Programs()
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    mock_pb_events = mocker.patch("ses_integration.fetch_entity_events.ses_client.fetch_events_for_fields")
    mock_get_mrv = mocker.patch(
        "ses_integration.fetch_entity_events.event_associations.get_event_revisions_for_fields_phase"
    )

    await reconcile_events_if_mismatch(
        request=app_request,
        program=program,
        fields=[],
    )

    mock_pb_events.assert_not_called()
    mock_get_mrv.assert_not_called()


@pytest.mark.asyncio
async def test_reconcile_events_if_mismatch_batch_exception_handling(app_request, mdl, mocker):
    """Test exception handling in batch reconciliation."""
    program = await mdl.Programs(program_template=ProgramTemplate.event_based)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)

    mocker.patch(
        "ses_integration.fetch_entity_events.get_owner_user_id_for_project",
        side_effect=Exception("Test exception"),
    )

    mock_logger = mocker.patch("ses_integration.fetch_entity_events.logger")

    await reconcile_events_if_mismatch(
        request=app_request,
        program=program,
        fields=[field],
    )

    mock_logger.exception.assert_called_once()
    assert "Error during event pre-check" in mock_logger.exception.call_args[0][0]
