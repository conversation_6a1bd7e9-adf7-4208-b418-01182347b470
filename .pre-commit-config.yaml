default_language_version:
  python: python3.11

repos:
- repo: local
  hooks:
  - id: refresh-mdl
    name: Refresh pytest_models_factory.py
    entry: poetry run python app/scripts/update_pytest_models_factory.py
    language: system
    # https://stackoverflow.com/questions/70778806/pre-commit-not-using-virtual-environment
    # With newer poetry `python` instead of `system` causes this step to break.
    pass_filenames: false
    files: .*model.py
  - id: update-migrations
    name: Update raw SQL migrations
    description: Updates raw sql migrations from alembic migrations when migration files change
    entry: bash -c "[[ $(echo alembic/versions/$( ls -pArt alembic/versions | grep -v / |  tail -n 1 )) -nt sql-dump/migration.sql ]] && alembic upgrade head --sql > sql-dump/migration.sql || exit 0"
    language: python
    files: alembic/versions/.*\.py
    pass_filenames: false
- repo: https://github.com/timothycrosley/isort
  rev: 6.0.0    # Update pyproject.toml when updating the version
  hooks:
  - id: isort
    additional_dependencies:
    - isort
- repo: https://github.com/ambv/black
  rev: 25.1.0    # Update pyproject.toml when updating the version
  hooks:
  - id: black
        # Line length discussion: https://regrowag.slack.com/archives/C0302UM9S1J/p1686897987363889
    args: [--line-length=120]
    # Exclude factory.py, since black output differs between precommit and internally in that file.
    exclude: app/helper/pytest_models_factory.py
- repo: https://github.com/PyCQA/bandit
  rev: 1.8.6
  hooks:
  - id: bandit
    args: [-c, pyproject.toml]
    additional_dependencies:
    - bandit[toml]
    types: [python]
- repo: https://github.com/PyCQA/flake8
  rev: 4.0.1
  hooks:
  - id: flake8
    name: Lint code (flake8)
    additional_dependencies:
    - flake8==4.0.1
    - flake8-broken-line==0.4.0
    - flake8-bugbear==22.1.11
    - flake8-builtins==1.5.3
    - flake8-comprehensions==3.8.0
    - flake8-eradicate==1.2.0
    - flake8-logging-format==0.6.0
    - flake8-mutable==1.2.0
    - flake8-pie==0.15.0
    - flake8-polyfill==1.0.2
    - flake8-quotes==3.3.1
    - flake8-string-format==0.3.0
    - flake8-tidy-imports==4.6.0
    - flake8-variables-names==0.0.4
    - pep8-naming==0.12.1
    - flake8-deprecated==1.3
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.3.0
  hooks:
  - id: check-added-large-files
    args:
    - --maxkb=3000
  - id: trailing-whitespace
    args: [--markdown-linebreak-ext=md]
    exclude: (.github/pull_request_template.md)|(.*/messages.po)
  - id: check-merge-conflict
  - id: check-case-conflict
  - id: check-symlinks
  - id: end-of-file-fixer
    exclude: (.gitignore)|(.*/messages.po)
  - id: check-json
  - id: pretty-format-json
    args: [--autofix]
  - id: check-yaml
  - id: check-toml
- repo: https://github.com/macisamuele/language-formatters-pre-commit-hooks
  rev: v2.7.0
  hooks:
  - id: pretty-format-yaml
    args: [--autofix, --indent, '2']
    exclude: .*/dependabot.yml
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v1.14.1    # Update pyproject.toml when updating the version
  hooks:
  - id: mypy
    name: Lint code (mypy)
    language: python
    types: [python]
    require_serial: true
    exclude: (^alembic/.*)|(.*/test.*)|(.*/conftest\.py)|(.*/pytest_models_factory\.py)
    args: [--config-file=pyproject.toml]
    additional_dependencies: [types-cachetools, types-python-dateutil, types-requests, types-redis, types-PyMySQL, types-pytz]
- repo: https://github.com/Riverside-Healthcare/djLint
  rev: v1.19.16
  hooks:
  - id: djlint-jinja
- repo: https://github.com/dhruvmanila/remove-print-statements
  rev: v0.5.1      # Replace with latest tag on GitHub
  hooks:
  - id: remove-print-statements
    args: [--verbose, --dry-run]           # Show all the print statements to be removed
