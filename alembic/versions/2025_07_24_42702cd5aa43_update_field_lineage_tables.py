"""Update field lineage tables

Revision ID: 42702cd5aa43
Revises: 74817d9e1f48
Create Date: 2025-07-24 13:00:54.392170

"""

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision = "42702cd5aa43"
down_revision = "74817d9e1f48"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "mrv_field_relationship",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("field_id", sa.Integer(), nullable=False),
        sa.Column("previous_field_id", sa.Integer(), nullable=False),
        sa.Column("percent_intersection", sa.DECIMAL(precision=5, scale=2), nullable=False),
        sa.Column("area_intersection", sa.DECIMAL(precision=14, scale=6), nullable=False),
        sa.Column("relationship", sa.Enum("SPLIT", "MERGE", "SKIP", name="fieldrelationshiptype"), nullable=True),
        sa.<PERSON>umn("deleted_at", sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_mrv_field_relationship_deleted_at"), "mrv_field_relationship", ["deleted_at"], unique=False
    )
    op.create_index(op.f("ix_mrv_field_relationship_id"), "mrv_field_relationship", ["id"], unique=False)
    op.add_column(
        "mrv_fields",
        sa.Column(
            "field_lineage_status",
            sa.Enum("new", "returning", "reset", "returning_reset", "skipped_reset", name="fieldlineagestatus"),
            nullable=True,
        ),
    )
    op.add_column("mrv_fields_lineage", sa.Column("baseline_field_id", sa.Integer(), nullable=True))
    op.add_column("mrv_fields_lineage", sa.Column("previous_lineage_ids", sa.JSON(), nullable=True))
    op.alter_column("mrv_fields_lineage", "created_year", existing_type=mysql.INTEGER(), nullable=True)
    op.create_unique_constraint("unique_baseline_field_id", "mrv_fields_lineage", ["baseline_field_id"])
    op.drop_table_comment(
        "mrv_fields_lineage",
        existing_comment="Stores lineage information for fields. This is used to track the history(split, merging, increase in size, decrease in size) of a field.",
        schema=None,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table_comment(
        "mrv_fields_lineage",
        "Stores lineage information for fields. This is used to track the history(split, merging, increase in size, decrease in size) of a field.",
        existing_comment=None,
        schema=None,
    )
    op.drop_constraint("unique_baseline_field_id", "mrv_fields_lineage", type_="unique")
    op.alter_column("mrv_fields_lineage", "created_year", existing_type=mysql.INTEGER(), nullable=False)
    op.drop_column("mrv_fields_lineage", "previous_lineage_ids")
    op.drop_column("mrv_fields_lineage", "baseline_field_id")
    op.drop_column("mrv_fields", "field_lineage_status")
    op.drop_index(op.f("ix_mrv_field_relationship_id"), table_name="mrv_field_relationship")
    op.drop_index(op.f("ix_mrv_field_relationship_deleted_at"), table_name="mrv_field_relationship")
    op.drop_table("mrv_field_relationship")
    # ### end Alembic commands ###
