from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest

from phases.enums import PhaseTypes
from programs.enums import AccountingMethod, BaselineMethod, Protocols
from scenarios_service.enums import DndcTaskStatusChoices, ScenariosServiceApi
from scenarios_service.generalized_integration import helpers, measure_api_methods
from scenarios_service.generalized_integration.schema import (
    ConsumerConfiguration,
)
from scenarios_service.model import DndcTasks

program_id = 123
is_dry_run = True
ss_consumer_id = "consumer-1"
protocol = Protocols.GENERAL_SCOPE_3
accounting_method = AccountingMethod.intervention
baseline_method = BaselineMethod.BLENDED


@pytest.mark.asyncio
async def test_create_dndc_task_with_dndc_version_calls_db(monkeypatch):
    # Setup
    mock_request = MagicMock()

    monkeypatch.setattr(
        measure_api_methods, "get_dndc_version_for_intervention_config", AsyncMock(return_value="v1.2.3")
    )
    monkeypatch.setattr(
        measure_api_methods.db,
        "create_dndc_task",
        AsyncMock(
            return_value=DndcTasks(
                id="task-xyz",
                program_id=program_id,
                status=DndcTaskStatusChoices.in_progress,
                scenarios_service_api=ScenariosServiceApi.measure_api,
            )
        ),
    )

    result = await measure_api_methods.create_dndc_task_with_dndc_version(
        request=mock_request,
        program_id=program_id,
        is_dry_run=is_dry_run,
        phase=PhaseTypes.MONITORING,
        accounting_method=accounting_method,
        scenarios_service_api=ScenariosServiceApi.measure_api,
        baseline_method=baseline_method,
        protocol=protocol,
        ss_consumer_id=ss_consumer_id,
        submitted_field_ids=[10],
    )

    # Assert
    assert result.id == "task-xyz"
    measure_api_methods.db.create_dndc_task.assert_awaited_once_with(
        request=mock_request,
        program_id=program_id,
        project_id=None,
        phase=PhaseTypes.MONITORING,
        is_dry_run=is_dry_run,
        scenarios_service_api=ScenariosServiceApi.measure_api,
        accounting_method=accounting_method,
        baseline_method=baseline_method,
        protocol=protocol,
        dndc_version="v1.2.3",
        submitted_field_ids=[10],
    )


@pytest.mark.asyncio
async def test_create_dndc_task_with_dndc_version_no_options(monkeypatch):
    mock_request = MagicMock()

    monkeypatch.setattr(
        measure_api_methods.db,
        "create_dndc_task",
        AsyncMock(
            return_value=DndcTasks(
                id="task-abc",
                program_id=program_id,
                status=DndcTaskStatusChoices.in_progress,
                scenarios_service_api=ScenariosServiceApi.biofuels_api,
            )
        ),
    )
    result = await measure_api_methods.create_dndc_task_with_dndc_version(
        request=mock_request,
        program_id=program_id,
        is_dry_run=is_dry_run,
        phase=PhaseTypes.MONITORING,
        accounting_method=AccountingMethod.biofuels,
        scenarios_service_api=ScenariosServiceApi.biofuels_api,
        baseline_method=None,
        ss_consumer_id=ss_consumer_id,
        submitted_field_ids=[10],
    )

    assert result.id == "task-abc"
    measure_api_methods.db.create_dndc_task.assert_awaited_once_with(
        request=mock_request,
        program_id=program_id,
        project_id=None,
        phase=PhaseTypes.MONITORING,
        is_dry_run=is_dry_run,
        scenarios_service_api=ScenariosServiceApi.biofuels_api,
        accounting_method=AccountingMethod.biofuels,
        baseline_method=None,
        protocol=None,
        dndc_version=None,
        submitted_field_ids=[10],
    )


@pytest.mark.asyncio
async def test_get_intervention_consumer_configurations_handles_http_error(monkeypatch):
    async def raise_exc(*args, **kwargs):
        raise httpx.HTTPStatusError("fail", request=None, response=httpx.Response(status_code=500, text="error"))

    monkeypatch.setattr(measure_api_methods.client, "list_intervention_consumer_configurations", raise_exc)

    result = await measure_api_methods.get_intervention_consumer_configurations("id")
    assert "error" in result


ss_protocol = helpers.convert_protocol_for_ss(Protocols.GENERAL_SCOPE_3)


@pytest.mark.asyncio
async def test_get_dndc_version_success(monkeypatch):
    consumer_config = ConsumerConfiguration(
        x_consumer_id="id",
        versions_by_protocol={
            ss_protocol: {
                "version": "v2.0",
                "dndc_version": "v2.0",
            }
        },
        created_at="2020-01-01",
        updated_at="2020-01-01",
    ).dict()
    monkeypatch.setattr(
        measure_api_methods, "get_intervention_consumer_configurations", AsyncMock(return_value=[consumer_config])
    )
    result = await measure_api_methods.get_dndc_version_for_intervention_config("id", Protocols.GENERAL_SCOPE_3)
    assert result == "v2.0"


@pytest.mark.asyncio
async def test_get_dndc_version_not_found(monkeypatch):
    monkeypatch.setattr(measure_api_methods, "get_intervention_consumer_configurations", AsyncMock(return_value=[]))

    result = await measure_api_methods.get_dndc_version_for_intervention_config("id", Protocols.GENERAL_SCOPE_3)
    assert result is None
