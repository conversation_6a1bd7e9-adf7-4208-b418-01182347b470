"""add submitted field count to dndc task

Revision ID: 042e4a6b53c4
Revises: 14b0097b3e7c
Create Date: 2025-08-06 13:23:48.243739

"""

import sqlalchemy as sa

from alembic import op

revision = "042e4a6b53c4"
down_revision = "14b0097b3e7c"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("mrv_dndc_tasks", sa.Column("submitted_field_count", sa.Integer(), nullable=True))


def downgrade():
    op.drop_column("mrv_dndc_tasks", "submitted_field_count")
