import asyncio
from datetime import datetime, UTC
from typing import Dict, List, Optional, Tuple, Type
from uuid import UUID

import elasticapm
from cachetools import TTLCache
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from regrow.ses.event.v1.event_service_pb2 import UpsertEventResponse
from ses_client.client import Client
from ses_client.crop import crop_purpose_commodity_harvest
from ses_client.event import event_type, EventWithContext, StructuredEvent
from ses_client.search import Filter
from starlette import status

from config import get_settings
from entity_events.events.constants import (
    CONTEXT_KEY_REGROW_ACTING_USER,
    CONTEXT_KEY_REGROW_OWNING_USER,
)
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from fields.schema import Field
from logger import get_logger
from phases.db import get_phase_by_id
from phases.enums import StageTypes
from phases.model import Phases
from phases.schema import StageRequestNoParentBase
from programs.db import get_program_series
from ses_integration.constants import (
    MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
    SES_EVENT_TO_ENTITY_EVENT,
    SES_EVENT_TYPE_TO_STAGE,
    STAGE_TYPE_TO_SES_EVENT,
)
from ses_integration.db import (
    _get_potentially_locked_event_ids_for_user,
    get_programs_required_stage_types,
)
from ses_integration.schema import EventIdWithStageType

logger = get_logger(__name__)

settings = get_settings()


# Event stage type cache with 1 hour TTL
_event_stage_cache: TTLCache[str, StageTypes | None] = TTLCache(maxsize=10240, ttl=60 * 60)


async def get_stage_types_for_event_ids(ses_client: Client, ses_event_ids: list[str]) -> dict[str, StageTypes | None]:
    """
    Get stage types for SES event IDs with TTL caching.
    Only fetches events from SES that aren't cached or have expired.

    Args:
        ses_client: SES client for fetching events
        ses_event_ids: List of SES event IDs (not EntityEvent IDs) to get stage types for

    Returns:
        Dictionary mapping SES event_id to stage_type (or None if no mapping exists)
    """
    result = {}
    missing_event_ids = []

    # Check cache for existing mappings
    for event_id in ses_event_ids:
        if event_id in _event_stage_cache:
            result[event_id] = _event_stage_cache[event_id]
        else:
            missing_event_ids.append(event_id)

    # Fetch missing events from SES in a single batch call (only if needed)
    if missing_event_ids:
        ses_events = await ses_client.fetch_events(event_ids=missing_event_ids)

        # Process and cache new events
        for event in ses_events:
            stage_type = SES_EVENT_TYPE_TO_STAGE.get(event_type(event))
            _event_stage_cache[event.id] = stage_type  # Cache with TTL
            result[event.id] = stage_type

        # Handle events that weren't returned by SES (cache as None)
        for event_id in missing_event_ids:
            if event_id not in result:
                _event_stage_cache[event_id] = None
                result[event_id] = None

    return result


def get_ses_event_types_to_query(
    stages: list[StageRequestNoParentBase], stage_type: StageTypes | None = None
) -> list[str]:
    ses_event_types_to_query = []
    if stage_type:
        event_types = STAGE_TYPE_TO_SES_EVENT.get(stage_type)
        if event_types:
            ses_event_types_to_query.extend(event_types)
    else:
        for stage in stages:
            event_types = STAGE_TYPE_TO_SES_EVENT.get(stage.type_)
            if event_types:
                ses_event_types_to_query.extend(event_types)
    return ses_event_types_to_query


def parse_ses_events_to_entity_events(
    field: Field,
    cropping_events: List[Tuple[EventWithContext | None, EventWithContext | None, EventWithContext | None]] | None,
    field_practice_events: List[EventWithContext] | None,
) -> List[EntityEvent]:
    entity_events = []
    if cropping_events is not None:
        for field_crop_events in cropping_events:
            entity_event = CroppingEvent.from_ses_events(events=field_crop_events, entity_id=field.id)
            entity_events.append(entity_event)

    if field_practice_events is not None:
        for ses_event in field_practice_events:
            ses_type = event_type(ses_event.event)
            entity_event_type: Type[EntityEvent] = SES_EVENT_TO_ENTITY_EVENT[ses_type]
            if ses_type is not None:
                entity_event = entity_event_type.from_ses_events(events=[ses_event], entity_id=field.id)
                entity_events.append(entity_event)
            else:
                logger.error(f"Unsupported SES event type: {ses_type}")

    return entity_events


@elasticapm.async_capture_span()
async def upsert_events_to_ses(
    field: Field,
    event: Optional[EntityEvent],
    new_events: list[StructuredEvent],
    ses_client: Client,
) -> list[UpsertEventResponse]:
    logger.debug(f"upsert_events_to_ses {field.md5=}, {new_events=}")

    created_events: list[StructuredEvent] = []

    for event_to_upsert in new_events:
        try:
            if event is None or event.id is None:
                assert event_to_upsert.pb_context.creation.get(StructuredEvent.CONTEXT_KEY_REGROW_USER_ID) is not None
                assert event_to_upsert.pb_context.association.get(CONTEXT_KEY_REGROW_OWNING_USER) is not None
                assert event_to_upsert.pb_context.association.get(CONTEXT_KEY_REGROW_ACTING_USER) is not None
            created_event = await ses_client.upsert_field_event(field_id=field.md5, structured_event=event_to_upsert)
        except Exception as e:
            logger.error(f"Error upserting event for field: {field.md5} - {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Upserting SES event for field {field.md5} returned error: {e}",
            )
        if created_event is not None:
            created_events.append(created_event)

    return created_events


async def get_cropping_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    stage_type: StageTypes | None = None,
) -> Dict[str, List[Tuple[EventWithContext, EventWithContext]]] | None:
    crop_purpose_filter = None
    # Only query for all crop types if stage type is not CROP_EVENTS, otherwise only fetch Commodity crop events
    if stage_type is not None and stage_type is not StageTypes.CROP_EVENTS:
        crop_purpose_filter = [crop_purpose_commodity_harvest()]
    return await ses_client.fetch_cropping_sequences_for_fields_with_context(
        field_ids=field_md5s,
        to_date=to_date,
        from_date=from_date,
        user_ids=[owner_user_id],
        min_overlap_percentage=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
        crop_purpose=crop_purpose_filter,
    )


async def get_field_practice_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    phase: Phases,
    stage_type: StageTypes | None = None,
) -> Dict[str, List[EventWithContext]] | None:
    ses_event_types_to_query: list[str] = [StructuredEvent.TYPE_FALLOW_PERIOD]
    event_types_for_stage = get_ses_event_types_to_query(stages=phase.stages, stage_type=stage_type)
    ses_event_types_to_query.extend(event_types_for_stage)

    if len(ses_event_types_to_query) == 0:
        return None

    return await ses_client.fetch_events_for_fields_with_context(
        field_ids=field_md5s,
        search_filter=Filter(
            event_types=ses_event_types_to_query,
            interval_from=from_date,
            interval_to=to_date,
            user_ids=[owner_user_id],
            min_overlap=MINIMUM_REQUIRED_FIELD_EVENT_OVERLAP_PERCENTAGE,
        ),
    )


async def get_ses_field_events(
    ses_client: Client,
    field_md5s: list[str],
    from_date: str,
    to_date: str,
    owner_user_id: str,
    phase: Phases,
    stage_type: StageTypes | None = None,
) -> Tuple[
    Dict[str, List[Tuple[EventWithContext, EventWithContext]]] | None,
    Dict[str, List[EventWithContext]] | None,
]:
    ses_crop_events = await get_cropping_events(
        ses_client=ses_client,
        field_md5s=field_md5s,
        from_date=from_date,
        to_date=to_date,
        owner_user_id=owner_user_id,
        stage_type=stage_type,
    )
    ses_events = await get_field_practice_events(
        ses_client=ses_client,
        field_md5s=field_md5s,
        from_date=from_date,
        to_date=to_date,
        owner_user_id=owner_user_id,
        phase=phase,
        stage_type=stage_type,
    )

    return ses_crop_events, ses_events


async def poll_for_field_change(from_time: datetime, field_md5s: list[str]) -> None:
    ses_client = Client(
        grpc_host_addr=settings.SES_INTERNAL_URL_BASE,
        search_host_addr=settings.SES_SEARCH_INTERNAL_URL_BASE,
    )
    updated_md5: set[str] = set()
    waiting_for_updates = True
    while waiting_for_updates:
        waiting_for_updates = False
        for field_md5 in field_md5s:
            if field_md5 in updated_md5:
                continue
            md5_updated_at = await ses_client.get_last_update_for_field(field_md5)
            if md5_updated_at > from_time:
                updated_md5.add(field_md5)
            else:
                waiting_for_updates = True
                break

        if waiting_for_updates:
            if (
                datetime.now(tz=UTC) - from_time
            ).seconds >= settings.SES_RECONCILIATION_POLLING_INTERVAL_TIMEOUT_SECONDS:
                waiting_for_updates = False
                break

        await asyncio.sleep(settings.SES_RECONCILIATION_POLLING_INTERVAL_SECONDS)


async def get_locked_event_ids_for_user(
    ses_client: Client,
    request: Request,
    owner_user_id: int,
    acting_phase_id: int,
    field_md5s: list[str] | None = None,
    events: list[EventIdWithStageType] | None = None,
) -> set:
    """
    Derive SES event locks based on association with a completed phase for the events a user is permitted to see.
    E Phases assume a lock based on a completed E or M Phase, whereas M Phases only assume a lock if the event was is
    associated with a completed M Phase.

    This version includes logic to check if the event type was required in program stages:
    - If locked by the acting program AND the stage is required in that program, lock it
    - Otherwise, only lock if the stage was required in a previous program

    Args:
        ses_client: SES client for fetching event data
        request: FastAPI request object
        owner_user_id: User ID that is the owner of the events
        acting_phase_id: Phase ID that the user is acting under.
        field_md5s: Optional list of field MD5s to filter by
        events: Optional list of EventIdWithStageType objects.
               If provided, skips SES fetching and ONLY returns events in the provided list.
    """

    acting_phase = await get_phase_by_id(request, acting_phase_id)

    # Get events and ALL previous programs that completed the corresponding phase
    potentially_locked_events_to_programs = await _get_potentially_locked_event_ids_for_user(
        request=request,
        owner_user_id=owner_user_id,
        acting_phase_type=acting_phase.type_,
        field_md5s=field_md5s,
    )

    if not potentially_locked_events_to_programs:
        return set()

    # Get program series and get previous programs' required stages
    program_series = await get_program_series(request, acting_phase.program_id)
    previous_required_stage_types = await get_programs_required_stage_types(
        request=request,
        phase_type=acting_phase.type_,
        program_ids=program_series[: program_series.index(acting_phase.program_id)],
    )

    # Get current program required stages
    current_required_stage_types = await get_programs_required_stage_types(
        request=request,
        phase_type=acting_phase.type_,
        program_ids=[acting_phase.program_id],
    )

    # Convert event IDs to list for SES client
    event_ids = [str(event_id) for event_id in potentially_locked_events_to_programs.keys()]

    # Create event_stage_mapping from events list or fetch from SES
    if events is None:
        event_stage_mapping = await get_stage_types_for_event_ids(ses_client, event_ids)
    else:
        event_stage_mapping = {
            str(event.event_id): event.stage_type for event in events if str(event.event_id) in event_ids
        }
        # also populate cache
        for event in events:
            if str(event.event_id) not in _event_stage_cache:
                _event_stage_cache[str(event.event_id)] = event.stage_type

    # Apply locking logic
    locked_event_ids = set()
    for event_id_str, stage_type in event_stage_mapping.items():
        if stage_type:
            event_id = UUID(event_id_str)
            locking_program_ids = potentially_locked_events_to_programs.get(event_id, set())

            # If locked by current program AND stage is required, this event should be locked
            if acting_phase.program_id in locking_program_ids and stage_type in current_required_stage_types:
                locked_event_ids.add(event_id)
            # If stage was required in any previous program, the event should be locked
            elif stage_type in previous_required_stage_types:
                locked_event_ids.add(event_id)

    return locked_event_ids
