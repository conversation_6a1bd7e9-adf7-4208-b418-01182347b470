from __future__ import annotations

from annotations import Any
from helper.datetime_helper import DatetimeHelper
from projects.migration.project_transformer import ProjectTransformer
from projects.migration.schema import (
    Field,
    ProjectMigrationCommand,
    Rule,
    RuleFunctionData,
    RuleSet,
)
from projects.migration.transformers import common_transforms


class Transform155to1665(ProjectTransformer):
    """
    Transform data from program 155 to program 1665
    """

    async def transform(self) -> ProjectMigrationCommand | None:
        if self.transform_request is None:
            return None

        self.transform_request.rulesets = self.translation_rules()
        result = await self.gather_data(transform_request=self.transform_request)
        return await self.create_data(result)

    def get_cover_crop_mix(self, src: RuleFunctionData) -> str:
        return common_transforms.get_cover_crop_mix(src)

    def get_assigned_practices(self, src: RuleFunctionData) -> dict[int, str] | None:
        return common_transforms.get_assigned_practices(src)

    async def crop_type(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type(src)

    async def crop_type_basic(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.crop_type_basic(src)

    async def practice_to_croptype(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_croptype(src)

    async def practice_to_usage(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.practice_to_usage(src)

    async def tillage_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_practice(src)

    async def tillage_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_practice_to_tillage_event(src)

    async def tillage_date_period_to_tillage_date(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_date_period_to_tillage_date(src)

    async def tillage_rule_value(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_rule_value(src)

    async def tillage_depth_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.tillage_depth_to_tillage_depth(src)

    async def assigned_practice_to_tillage_event(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_event(src)

    async def assigned_practice_to_tillage_practice(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_practice(src)

    async def assigned_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.assigned_practice_to_tillage_depth(src)

    async def spring_tillage_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.spring_tillage_practice_to_tillage_depth(src)

    async def fall_tillage_practice_to_tillage_depth(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.fall_tillage_practice_to_tillage_depth(src)

    async def rule_value(self, src: RuleFunctionData) -> Any | None:
        return await common_transforms.rule_value(src)

    async def field_allowed(self, field: Field | None = None, target_project_id: int | None = None) -> bool:
        return await super().field_allowed(field, target_project_id)

    def translation_rules(self) -> list[RuleSet]:
        """
        Returns a list of rules to translate values from one program to another.
        """

        source_program_id = 155

        # This is the prod program
        target_program_id = 1665

        # Dummy dev program for testing
        # target_program_id = 71143

        return [
            RuleSet(
                reference="1.1 - Summer Crop-Crop Type 2024",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/crop_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="crop_type",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="1.2 - Summer Crop-Crop Type 2023",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/crop_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="crop_type",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="2.1 - Summer Crop-Planting Date 2024",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROP/planting_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="planting_date",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="2.2 - Summer Crop-Planting Date 2023",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROP/planting_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="planting_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="3.1 - Summer Crop-Harvest Date 2024",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/harvest_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="harvest_date",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="3.2 - Summer Crop-Harvest Date 2023",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/harvest_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="harvest_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="3.3 - Summer Crop - crop_yield 2024",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/crop_yield",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="crop_yield",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_yield",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_yield",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="3.3 - Summer Crop - yield_rate_unit 2024",
                source=Rule(
                    action="read from MONITORING/SUMMER_CROPS/yield_rate_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="SUMMER_CROPS",
                    attribute_type="yield_rate_unit",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/yield_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="yield_rate_unit",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="4 - Winter Crop-Crop Type 2023, and move to 2024",
                source=Rule(
                    action="read from MONITORING/WINTER_CROPS/crop_type",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="WINTER_CROPS",
                    attribute_type="crop_type",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="5 - Winter Crop-Planting Date 2023, and move to 2024",
                source=Rule(
                    action="read from MONITORING/WINTER_CROPS/planting_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="WINTER_CROPS",
                    attribute_type="planting_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="6 - Winter Crop-Harvest Date 2023, and move to 2024",
                source=Rule(
                    action="read from MONITORING/WINTER_CROPS/harvest_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="WINTER_CROPS",
                    attribute_type="harvest_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        record_year_increment=1,
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="7 - Winter Crop - crop_yield 2024",
                source=Rule(
                    action="read from MONITORING/WINTER_CROPS/crop_yield",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="WINTER_CROPS",
                    attribute_type="crop_yield",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/crop_yield",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_yield",
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="8 - Winter Crop - yield_rate_unit 2024",
                source=Rule(
                    action="read from MONITORING/WINTER_CROPS/yield_rate_unit",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="WINTER_CROPS",
                    attribute_type="yield_rate_unit",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_crop_rotation/yield_rate_unit",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="yield_rate_unit",
                        row_id_style="increment",
                        row_id_offset=3,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="7.1.1 - Tillage Practice 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_practice",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_practice",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="7.1.2 - Tillage Practice to Event 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_practice",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_practice",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_TILLAGE/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="7.1.3 - Tillage Date to Date 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_date",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_TILLAGE/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_date_period_to_tillage_date",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="7.1.4 - Tillage Depth 2023",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_depth",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_depth",
                    record_years=[2023],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="tillage_depth_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=6,
                        record_years=[2023],
                    ),
                ],
            ),
            RuleSet(
                reference="7.2.1 - Tillage Practice 2024",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_practice",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_practice",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="7.2.2 - Tillage Practice to Event 2024",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_practice",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_practice",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_TILLAGE/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="7.2.3 - Tillage Date to Date 2024",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_date",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_date",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_TILLAGE/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_date_period_to_tillage_date",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="7.2.4 - Tillage Depth 2024",
                source=Rule(
                    action="read from MONITORING/TILLAGE/tillage_depth",
                    program_id=source_program_id,
                    phase_type="MONITORING",
                    stage_type="TILLAGE",
                    attribute_type="tillage_depth",
                    record_years=[2024],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="tillage_depth_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=0,
                        record_years=[2024],
                    ),
                ],
            ),
            RuleSet(
                reference="10.1 - ENROLMENT Summer Crop 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2019, 6, 15),  # June 15, 2019
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2019, 10, 1),  # October 1, 2019
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="10.2 - ENROLMENT Summer Crop 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2020, 6, 15),  # June 15, 2020
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2020, 10, 1),  # October 1, 2020
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="10.3 - ENROLMENT Summer Crop 2021, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 6, 15),  # June 15, 2020
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 10, 1),  # October 1, 2020
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="10.4 - ENROLMENT Summer Crop 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 6, 15),  # June 15, 2020
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 10, 1),  # October 1, 2020
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="10.3 - ENROLMENT Summer Crop 2021, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 6, 15),  # June 15, 2020
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 10, 1),  # October 1, 2020
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="10.4 - ENROLMENT Summer Crop 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/summer_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="summer_crop_type",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Commodity",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 6, 15),  # June 15, 2020
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 10, 1),  # October 1, 2020
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="11.1 - ENROLMENT Winter Crop 2018, move to 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/winter_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_type",
                    record_years=[2018],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2018, 10, 5),  # October 5, 2018
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2019, 4, 20),  # April 20, 2019
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="11.2 - ENROLMENT Winter Crop 2019 move to 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/winter_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_type",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2019, 10, 5),  # October 5, 2019
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2020, 4, 20),  # April 20, 2020
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="11.3 - ENROLMENT Winter Crop 2020 move to 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/winter_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_type",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2020, 10, 5),  # October 5, 2020
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 4, 20),  # April 20, 2021
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="11.4 - ENROLMENT Winter Crop 2021 move to 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/winter_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_type",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2021, 10, 5),  # October 5, 2021
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 4, 20),  # April 20, 2022
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="11.4 - ENROLMENT Winter Crop 2022 move to 2023, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/winter_crop_type",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="winter_crop_type",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_type using function crop_type",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_type",
                        value_blacklist=["fallow", "no cover"],
                        function_name="crop_type_basic",
                        row_id_style="increment",
                        row_id_offset=9,
                        record_years=[2023],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/crop_usage",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="crop_usage",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value="Cover",
                        row_id_style="increment",
                        row_id_offset=9,
                        record_years=[2023],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/planting_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="planting_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2022, 10, 5),  # October 5, 2021
                        row_id_style="increment",
                        row_id_offset=9,
                        record_years=[2023],
                        overwrite=False,
                    ),
                    Rule(
                        action="write into ENROLMENT/HISTORICAL_CROP_ROTATION/harvest_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_CROP_ROTATION",
                        attribute_type="harvest_date",
                        value_blacklist=["fallow", "no cover"],
                        function_name="rule_value",
                        value=DatetimeHelper.midnight_str(2023, 4, 20),  # April 20, 2022
                        row_id_style="increment",
                        row_id_offset=9,
                        record_years=[2023],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="12.1 - Spring Tillage Practice 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="12.2 - Spring Tillage Practice 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="12.3 - Spring Tillage Practice 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="12.4 - Spring Tillage Practice 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="13.1 - Spring Tillage Event 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="13.2 - Spring Tillage Event 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="13.3 - Spring Tillage Event 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="13.4 - Spring Tillage Event 2022",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="14.1 - Spring Tillage Date 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2019, 4, 20),  # April 20, 2019
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="14.2 - Spring Tillage Date 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2020, 4, 20),  # April 20, 2019
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="14.3 - Spring Tillage Date 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2021, 4, 20),  # April 20, 2021
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="14.4 - Spring Tillage Date 2022 for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2022, 4, 20),  # April 20, 2021
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="15.1 - Spring Tillage Practice to Depth 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="spring_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=33,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="15.2 - Spring Tillage Practice to Depth 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="spring_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=27,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="15.3 - Spring Tillage Practice to Depth 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="spring_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=21,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="15.4 - Spring Tillage Practice to Depth 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/spring_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="spring_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="spring_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=15,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="16.1 - Fall Tillage Practice 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="16.2 - Fall Tillage Practice 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="16.3 - Fall Tillage Practice 2021, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="16.4 - Fall Tillage Practice 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_practice",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_practice",
                        function_name="tillage_practice_to_tillage_practice",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="17.1 - Fall Tillage Event 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="17.2 - Fall Tillage Event 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="17.3 - Fall Tillage Event 2021, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="17.4 - Fall Tillage Event 2022, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_event",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_event",
                        function_name="tillage_practice_to_tillage_event",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="18.1 - Fall Tillage Date 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2019, 10, 5),  # October 5, 2019
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="18.2 - Fall Tillage Date 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2020, 10, 5),  # October 5, 2020
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="18.3 - Fall Tillage Date 2021",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2021, 10, 5),  # October 5, 2020
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="18.4 - Fall Tillage Date 2022",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_date",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_date",
                        function_name="tillage_rule_value",
                        value=DatetimeHelper.midnight_str(2022, 10, 5),  # October 5, 2020
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                        overwrite=False,
                    ),
                ],
            ),
            RuleSet(
                reference="19.1 - Fall Tillage Practice to Depth 2019",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2019],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="fall_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=30,
                        record_years=[2019],
                    ),
                ],
            ),
            RuleSet(
                reference="19.2 - Fall Tillage Practice to Depth 2020",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2020],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="fall_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=24,
                        record_years=[2020],
                    ),
                ],
            ),
            RuleSet(
                reference="19.3 - Fall Tillage Practice to Depth 2021, for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2021],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="fall_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=18,
                        record_years=[2021],
                    ),
                ],
            ),
            RuleSet(
                reference="19.4 - Fall Tillage Practice to Depth 2022. for possible missing data",
                source=Rule(
                    action="read from ENROLMENT/HISTORICAL_CROP_ROTATION/fall_tillage_practice",
                    program_id=source_program_id,
                    phase_type="ENROLMENT",
                    stage_type="HISTORICAL_CROP_ROTATION",
                    attribute_type="fall_tillage_practice",
                    record_years=[2022],
                ),
                destination=[
                    Rule(
                        action="write into enrolment/historical_tillage/tillage_depth",
                        program_id=target_program_id,
                        phase_type="ENROLMENT",
                        stage_type="HISTORICAL_TILLAGE",
                        attribute_type="tillage_depth",
                        function_name="fall_tillage_practice_to_tillage_depth",
                        row_id_style="increment",
                        row_id_offset=12,
                        record_years=[2022],
                    ),
                ],
            ),
        ]
