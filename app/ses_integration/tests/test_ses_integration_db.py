from datetime import datetime
from uuid import uuid4

from phases.enums import PhaseTypes, StageTypes
from root_crud import get, update
from ses_integration.db import (
    _get_potentially_locked_event_ids_for_user,
    bulk_associate_phases_with_events,
    get_field_event_associations,
    get_phase_event_associations,
    get_programs_required_stage_types,
    get_user_owned_field_event_associations,
    RawPhaseEventAssociation,
)
from ses_integration.model import PhaseEventAssociation


async def test_get_field_event_associations_by_event_ids(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id2),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=[event_id1], field_md5=None, field_ids=None
    )

    assert len(result) == 1
    assert result[0].ses_event_id == str(event_id1)


async def test_get_field_event_associations_by_field_md5(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field_md5 = "test_md5"
    field_md5_2 = "test_md52"
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id)
    field2 = await mdl.Fields(md5=field_md5_2, parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id2),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=field_md5, field_ids=None
    )

    assert len(result) == 1
    assert result[0].field_md5 == field_md5


async def test_get_field_event_associations_by_field_ids(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id2),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id]
    )

    assert len(result) == 1
    assert result[0].field_id == field.id


async def test_get_field_event_associations_filter_deleted(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id1 = uuid4()
    event_id2 = uuid4()
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id2),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
        deleted_at=datetime.now(),
    )
    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id]
    )

    assert len(result) == 1
    assert result[0].ses_event_id == str(event_id1)

    result = await get_field_event_associations(
        request=app_request, event_ids=None, field_md5=None, field_ids=[field.id], filter_deleted=False
    )

    assert len(result) == 2


async def test_get_field_event_associations_no_results(app_request, mdl):
    result = await get_field_event_associations(
        request=app_request, event_ids=[uuid4()], field_md5="non_existent_md5", field_ids=[999]
    )

    assert len(result) == 0


async def test_get_user_owned_field_event_associations(app_request, mdl) -> None:
    owner_user_id = "1"
    other_user_id = "2"
    field_md5 = "test_md5"
    event_id = uuid4()

    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, md5=field_md5)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    other_program = await mdl.Programs()
    other_project = await mdl.Projects(program_id=other_program.id)
    other_field = await mdl.Fields(parent_project_id=other_project.id, md5=field_md5)
    await mdl.ProjectPermissions(project=other_project.id, user=int(other_user_id))

    # FieldEventAssociation for owner_user_id
    field_event_association_owner = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(event_id), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    # FieldEventAssociation for other_user_id
    field_event_association_other = await mdl.FieldEventAssociation(
        field_id=other_field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=other_project.id,
        program_id=other_program.id,
    )

    # Test fetching associations for owner_user_id
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=owner_user_id, event_ids=[event_id]
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_owner.id

    # Test fetching associations for other_user_id
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=other_user_id, event_ids=[event_id]
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_other.id


async def test_get_user_owned_field_event_associations_no_event_ids(app_request, mdl) -> None:
    owner_user_id = "1"
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=int(owner_user_id))

    # FieldEventAssociation for owner_user_id
    field_event_association_owner = await mdl.FieldEventAssociation(
        field_id=field.id, ses_event_id=str(uuid4()), field_md5=field.md5, project_id=project.id, program_id=program.id
    )

    # Test fetching associations without specifying event_ids
    associations = await get_user_owned_field_event_associations(
        request=app_request, owner_user_id=owner_user_id, event_ids=None
    )
    assert len(associations) == 1
    assert associations[0].id == field_event_association_owner.id


async def test_bulk_associate_phases_with_events(app_request, mdl):
    program = await mdl.Programs()
    phase_id_1 = (await mdl.Phases(program_id=program.id)).id
    phase_id_2 = (await mdl.Phases(program_id=program.id)).id
    project = await mdl.Projects(program_id=program.id)
    field_1 = await mdl.Fields(parent_project_id=project.id)
    field_2 = await mdl.Fields(parent_project_id=project.id)
    event_id_1 = uuid4()
    event_id_2 = uuid4()

    # Create FieldEventAssociations
    field_event_association_1 = await mdl.FieldEventAssociation(
        field_id=field_1.id,
        ses_event_id=str(event_id_1),
        field_md5=field_1.md5,
        project_id=project.id,
        program_id=program.id,
    )
    field_event_association_2 = await mdl.FieldEventAssociation(
        field_id=field_2.id,
        ses_event_id=str(event_id_2),
        field_md5=field_2.md5,
        project_id=project.id,
        program_id=program.id,
    )

    phase_event_associations = [
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_1.id,
            phase_id=phase_id_1,
            revision=1,
        ),
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_1.id,
            phase_id=phase_id_2,
            revision=2,
        ),
        RawPhaseEventAssociation(
            field_event_association_id=field_event_association_2.id,
            phase_id=phase_id_1,
            revision=1,
        ),
    ]
    await bulk_associate_phases_with_events(request=app_request, phase_event_associations=phase_event_associations)

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(
                id_field=PhaseEventAssociation.field_event_association_id,
                ids=[field_event_association_1.id, field_event_association_2.id],
            ),
            get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id_1, phase_id_2]),
        ],
        order_by_cols=[PhaseEventAssociation.field_event_association_id, PhaseEventAssociation.phase_id],
    )

    assert associations[0].field_event_association_id == field_event_association_1.id
    assert associations[0].phase_id == phase_id_1
    assert associations[0].revision == 1

    assert associations[1].field_event_association_id == field_event_association_1.id
    assert associations[1].phase_id == phase_id_2
    assert associations[1].revision == 2

    assert associations[2].field_event_association_id == field_event_association_2.id
    assert associations[2].phase_id == phase_id_1
    assert associations[2].revision == 1


async def test_bulk_associate_phases_with_events_duplicate(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    phase_id_1 = (await mdl.Phases(program_id=program.id)).id
    phase_id_2 = (await mdl.Phases(program_id=program.id)).id
    revision = 1

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_1,
                revision=revision,
            ),
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_2,
                revision=revision,
            ),
        ],
    )
    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_1,
                revision=revision + 1,  # Update revision
            ),
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id_2,
                revision=revision + 1,  # Update revision
            ),
        ],
    )

    associations = await get.generic_get(
        request=app_request,
        orm_type=PhaseEventAssociation,
        filters=[
            get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id]),
            get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id_1, phase_id_2]),
        ],
        order_by_cols=[PhaseEventAssociation.field_event_association_id, PhaseEventAssociation.phase_id],
    )

    assert len(associations) == 2
    assert associations[0].field_event_association_id == field_event_association.id
    assert associations[0].phase_id == phase_id_1
    assert associations[0].revision == revision + 1

    assert associations[1].field_event_association_id == field_event_association.id
    assert associations[1].phase_id == phase_id_2
    assert associations[1].revision == revision + 1


async def test_bulk_associate_phases_with_events_deleted_phase_assoc(app_request, mdl, faker):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    revision = 1
    phase_id = (await mdl.Phases(program_id=program.id)).id

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase_id,
        revision=revision,
        deleted_at=faker.date_time(),
    )

    await bulk_associate_phases_with_events(
        request=app_request,
        phase_event_associations=[
            RawPhaseEventAssociation(
                field_event_association_id=field_event_association.id,
                phase_id=phase_id,
                revision=revision,
            )
        ],
    )

    # Make sure the association was undeleted
    association = (
        await get.generic_get(
            request=app_request,
            orm_type=PhaseEventAssociation,
            filters=[
                get.Filter(id_field=PhaseEventAssociation.field_event_association_id, ids=[field_event_association.id]),
                get.Filter(id_field=PhaseEventAssociation.phase_id, ids=[phase_id]),
            ],
        )
    )[0]

    assert association.deleted_at is None


async def test_get_phase_event_associations_by_field_event_association_id(app_request, mdl):
    program = await mdl.Programs()
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    field2 = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id),
        field_md5=field2.md5,
        project_id=project.id,
        program_id=program.id,
    )

    e_phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=e_phase.id,
        revision=1,
    )
    m_phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=m_phase.id,
        revision=2,
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 1
    assert result[0].id == e_phase_event_association.id
    assert result[0].phase_id == e_phase.id
    assert result[0].revision == 1

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.MONITORING
    )

    assert len(result) == 1
    assert result[0].id == m_phase_event_association.id
    assert result[0].phase_id == m_phase.id
    assert result[0].revision == 2


async def test_get_phase_event_associations_ignores_deleted(app_request, mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT)
    event_id = uuid4()

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )

    phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase.id,
        revision=1,
        deleted_at=datetime.now(),
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 0

    phase_event_association.deleted_at = None
    await update.update(
        request=app_request,
        type=PhaseEventAssociation,
        instances=[phase_event_association],
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 1

    phase.deleted_at = datetime.now()
    await update.update(
        request=app_request,
        type=mdl.Phases,
        instances=[phase],
    )

    result = await get_phase_event_associations(
        request=app_request, field_event_association_ids=[field_event_association.id], phase_type=PhaseTypes.ENROLMENT
    )

    assert len(result) == 0


async def test_get_programs_required_stage_types_with_required_stages(app_request, mdl):
    first_program = await mdl.Programs()
    first_phase = await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.CROP_EVENTS, required=True)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.NUTRIENT_EVENTS, required=False)

    await mdl.Programs(previous_program_id=first_program.id)

    result = await get_programs_required_stage_types(app_request, [first_program.id], PhaseTypes.ENROLMENT)

    assert result == {StageTypes.CROP_EVENTS, StageTypes.TILLAGE_EVENTS}


async def test_get_programs_required_stage_types_with_no_required_stages(app_request, mdl):
    first_program = await mdl.Programs()
    first_phase = await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.CROP_EVENTS, required=False)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.TILLAGE_EVENTS, required=False)

    await mdl.Programs(previous_program_id=first_program.id)

    result = await get_programs_required_stage_types(app_request, [first_program.id], PhaseTypes.ENROLMENT)

    assert result == set()


async def test_get_programs_required_stage_types_filters_deleted(app_request, mdl):
    first_program = await mdl.Programs()
    first_phase = await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.CROP_EVENTS, required=True)
    await mdl.Stage(
        phase_id=first_phase.id,
        type_=StageTypes.TILLAGE_EVENTS,
        required=True,
        deleted_at=datetime.now(),
    )

    await mdl.Programs(previous_program_id=first_program.id)

    result = await get_programs_required_stage_types(app_request, [first_program.id], PhaseTypes.ENROLMENT)

    assert result == {StageTypes.CROP_EVENTS}


async def test_get_programs_required_stage_types_multiple_programs(app_request, mdl):
    first_program = await mdl.Programs()
    first_phase = await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.CROP_EVENTS, required=True)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.TILLAGE_EVENTS, required=False)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.NUTRIENT_EVENTS, required=False)

    second_program = await mdl.Programs(previous_program_id=first_program.id)
    second_phase = await mdl.Phases(program_id=second_program.id, type_=PhaseTypes.MONITORING)
    await mdl.Stage(phase_id=second_phase.id, type_=StageTypes.CROP_EVENTS, required=False)
    await mdl.Stage(phase_id=second_phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True)
    await mdl.Stage(phase_id=second_phase.id, type_=StageTypes.NUTRIENT_EVENTS, required=False)

    await mdl.Programs(previous_program_id=second_program.id)

    # Test for ENROLMENT phase type
    result_enrolment = await get_programs_required_stage_types(
        app_request, [first_program.id, second_program.id], PhaseTypes.ENROLMENT
    )
    assert result_enrolment == {StageTypes.CROP_EVENTS}

    # Test for MONITORING phase type
    result_monitoring = await get_programs_required_stage_types(
        app_request, [first_program.id, second_program.id], PhaseTypes.MONITORING
    )
    assert result_monitoring == {StageTypes.TILLAGE_EVENTS}


async def test_get_potentially_locked_event_ids_returns_dict_structure(app_request, mdl):
    user_id = 1
    event_id = uuid4()

    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase.id,
        revision=1,
    )

    result = await _get_potentially_locked_event_ids_for_user(
        request=app_request,
        owner_user_id=user_id,
        acting_phase_type=PhaseTypes.ENROLMENT,
    )

    assert isinstance(result, dict)
    assert event_id in result
    assert isinstance(result[event_id], set)
    assert program.id in result[event_id]


async def test_get_potentially_locked_event_ids_multiple_programs_same_event(app_request, mdl):
    user_id = 1
    event_id = uuid4()
    field_md5 = "test_md5"

    # Create two programs with phases that both associate with the same event
    program1 = await mdl.Programs()
    phase1 = await mdl.Phases(program_id=program1.id, type_=PhaseTypes.MONITORING)
    project1 = await mdl.Projects(program_id=program1.id)
    field1 = await mdl.Fields(parent_project_id=project1.id, md5=field_md5)

    program2 = await mdl.Programs()
    phase2 = await mdl.Phases(program_id=program2.id, type_=PhaseTypes.MONITORING)
    project2 = await mdl.Projects(program_id=program2.id)
    field2 = await mdl.Fields(parent_project_id=project2.id, md5=field_md5)

    # Setup permissions and completions for both programs
    await mdl.ProjectPermissions(project=project1.id, user=user_id)
    await mdl.ProjectPermissions(project=project2.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project1.id, phase_id=phase1.id, is_completed=True)
    await mdl.ProjectPhaseCompletion(project_id=project2.id, phase_id=phase2.id, is_completed=True)

    # Create field event associations for the same event in both programs
    field_event_association1 = await mdl.FieldEventAssociation(
        field_id=field1.id,
        ses_event_id=str(event_id),
        field_md5=field_md5,
        project_id=project1.id,
        program_id=program1.id,
    )
    field_event_association2 = await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id),
        field_md5=field_md5,
        project_id=project2.id,
        program_id=program2.id,
    )

    # Associate both with their respective phases
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association1.id,
        phase_id=phase1.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association2.id,
        phase_id=phase2.id,
        revision=1,
    )

    result = await _get_potentially_locked_event_ids_for_user(
        request=app_request,
        owner_user_id=user_id,
        acting_phase_type=PhaseTypes.ENROLMENT,
    )

    assert event_id in result
    assert len(result[event_id]) == 2
    assert {program1.id, program2.id} == result[event_id]


async def test_get_potentially_locked_event_ids_with_field_md5_filter(app_request, mdl):
    user_id = 1
    event_id1 = uuid4()
    event_id2 = uuid4()
    field_md5_1 = "field_md5_1"
    field_md5_2 = "field_md5_2"

    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    project = await mdl.Projects(program_id=program.id)
    field1 = await mdl.Fields(parent_project_id=project.id, md5=field_md5_1)
    field2 = await mdl.Fields(parent_project_id=project.id, md5=field_md5_2)

    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    # Create associations for both fields
    field_event_association1 = await mdl.FieldEventAssociation(
        field_id=field1.id,
        ses_event_id=str(event_id1),
        field_md5=field_md5_1,
        project_id=project.id,
        program_id=program.id,
    )
    field_event_association2 = await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id2),
        field_md5=field_md5_2,
        project_id=project.id,
        program_id=program.id,
    )

    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association1.id,
        phase_id=phase.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association2.id,
        phase_id=phase.id,
        revision=1,
    )

    # Test with field MD5 filter
    result = await _get_potentially_locked_event_ids_for_user(
        request=app_request,
        owner_user_id=user_id,
        acting_phase_type=PhaseTypes.ENROLMENT,
        field_md5s=[field_md5_1],
    )

    assert event_id1 in result
    assert event_id2 not in result
    assert program.id in result[event_id1]


async def test_get_potentially_locked_event_ids_no_results(app_request, mdl):
    user_id = 1

    result = await _get_potentially_locked_event_ids_for_user(
        request=app_request,
        owner_user_id=user_id,
        acting_phase_type=PhaseTypes.ENROLMENT,
    )

    assert result == {}


async def test_get_potentially_locked_event_ids_filters_uncompleted_phases(app_request, mdl):
    user_id = 1
    event_id = uuid4()

    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)

    await mdl.ProjectPermissions(project=project.id, user=user_id)
    # Phase is NOT completed
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=False)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase.id,
        revision=1,
    )

    result = await _get_potentially_locked_event_ids_for_user(
        request=app_request,
        owner_user_id=user_id,
        acting_phase_type=PhaseTypes.ENROLMENT,
    )

    assert result == {}


async def test_get_programs_required_stage_types_empty_program_list(app_request, mdl):
    result = await get_programs_required_stage_types(app_request, [], PhaseTypes.ENROLMENT)
    assert result == set()


async def test_get_programs_required_stage_types_deduplication(app_request, mdl):
    program1 = await mdl.Programs()
    phase1 = await mdl.Phases(program_id=program1.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=phase1.id, type_=StageTypes.CROP_EVENTS, required=True)

    program2 = await mdl.Programs()
    phase2 = await mdl.Phases(program_id=program2.id, type_=PhaseTypes.ENROLMENT)
    # Same stage type in both programs
    await mdl.Stage(phase_id=phase2.id, type_=StageTypes.CROP_EVENTS, required=True)
    await mdl.Stage(phase_id=phase2.id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    result = await get_programs_required_stage_types(app_request, [program1.id, program2.id], PhaseTypes.ENROLMENT)

    # Should only have unique stage types
    assert result == {StageTypes.CROP_EVENTS, StageTypes.TILLAGE_EVENTS}


async def test_get_programs_required_stage_types_filters_disabled(app_request, mdl):
    first_program = await mdl.Programs()
    first_phase = await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.ENROLMENT)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.CROP_EVENTS, required=True, enabled=True)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True, enabled=False)
    await mdl.Stage(phase_id=first_phase.id, type_=StageTypes.NUTRIENT_EVENTS, required=True, enabled=True)

    result = await get_programs_required_stage_types(app_request, [first_program.id], PhaseTypes.ENROLMENT)

    assert result == {StageTypes.CROP_EVENTS, StageTypes.NUTRIENT_EVENTS}
