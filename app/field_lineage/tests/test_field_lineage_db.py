from datetime import datetime

import pytest
from sqlalchemy import select

from field_lineage.db import (
    create_field_lineages,
    create_field_relationships,
    delete_field_relationships,
    get_field_id_to_baseline_field,
    get_field_id_to_previous_field_ids,
    get_field_lineage_by_baseline_field_id,
    get_field_lineage_by_id,
    get_field_relationships,
    get_fields_by_field_lineage_id_asc,
    update_field_relationship,
    update_previous_field_lineage_ids,
)
from field_lineage.enums import FieldRelationshipType
from field_lineage.model import FieldLineage, FieldRelationship
from field_lineage.schema import (
    FieldLineageRequest,
    FieldRelationshipRequest,
    FieldRelationshipUpdate,
)
from fields.enums import FieldStatus
from helper.helper import run_query


async def test_get_field_lineage_by_id(mdl, app_request):
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1)
    await mdl.FieldLineage(baseline_field_id=2)
    field_lineage = await get_field_lineage_by_id(request=app_request, field_lineage_id=field_lineage_1.id)
    assert field_lineage.to_dict() == field_lineage_1.to_dict()


async def test_get_field_lineage_by_baseline_field_id(mdl, app_request):
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1)
    await mdl.FieldLineage(baseline_field_id=2)
    field_lineage = await get_field_lineage_by_baseline_field_id(request=app_request, baseline_field_id=1)
    assert field_lineage.to_dict() == field_lineage_1.to_dict()


async def test_create_field_lineages(app_request, db_session_maker):
    await create_field_lineages(
        request=app_request,
        field_lineage_requests=[
            FieldLineageRequest(baseline_field_id=1, previous_lineage_ids=[1, 2, 3]),
            FieldLineageRequest(baseline_field_id=2, previous_lineage_ids=[4, 5, 6]),
        ],
    )

    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.baseline_field_id.in_([1, 2]))
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldLineage(
                id=res[0].id, created_at=res[0].created_at, baseline_field_id=1, previous_lineage_ids=[1, 2, 3]
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldLineage(
                id=res[1].id, created_at=res[1].created_at, baseline_field_id=2, previous_lineage_ids=[4, 5, 6]
            ).to_dict()
        )


async def test_update_previous_field_lineage_ids(mdl, app_request, db_session_maker):
    field_lineage = await mdl.FieldLineage(previous_lineage_ids=[])

    await update_previous_field_lineage_ids(
        request=app_request, field_lineage_id=field_lineage.id, previous_field_lineage_ids=[1, 2, 3]
    )

    async with db_session_maker() as s:
        query = select(FieldLineage).where(FieldLineage.id == field_lineage.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldLineage(
                id=field_lineage.id,
                created_year=field_lineage.created_year,
                created_at=field_lineage.created_at,
                baseline_field_id=field_lineage.baseline_field_id,
                previous_lineage_ids=[1, 2, 3],
            ).to_dict()
        )


async def test_get_field_relationships(mdl, app_request):
    relationship_1 = await mdl.FieldRelationship(
        field_id=2, previous_field_id=0, percent_intersection=50, area_intersection=10, relationship=None
    )
    relationship_2 = await mdl.FieldRelationship(
        field_id=2, previous_field_id=1, percent_intersection=50, area_intersection=10, relationship=None
    )

    relationships = await get_field_relationships(request=app_request, field_id=2)
    sorted_relationship_dicts = [
        relationship.to_dict() for relationship in sorted(relationships, key=lambda x: x.previous_field_id)
    ]
    assert sorted_relationship_dicts == [relationship_1.to_dict(), relationship_2.to_dict()]


@pytest.mark.parametrize("append_only", [True, False])
async def test_create_field_relationships(append_only, mdl, app_request, db_session_maker):
    fields_relationship_0 = await mdl.FieldRelationship(
        field_id=3,
        previous_field_id=0,
        percent_intersection=50,
        area_intersection=10,
        relationship=None,
        deleted_at=None,
    )

    await create_field_relationships(
        request=app_request,
        field_relationship_requests=[
            FieldRelationshipRequest(
                field_id=3, previous_field_id=1, percent_intersection=50, area_intersection=10, relationship=None
            ),
            FieldRelationshipRequest(
                field_id=3, previous_field_id=2, percent_intersection=50, area_intersection=10, relationship=None
            ),
        ],
        append_only=append_only,
    )

    async with db_session_maker() as s:
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id == 3)
            .where(FieldRelationship.deleted_at.is_(None))
            .order_by(FieldRelationship.previous_field_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        if append_only:
            assert len(res) == 3
            assert res[0].to_dict() == fields_relationship_0.to_dict()
            assert (
                res[1].to_dict()
                == FieldRelationship(
                    id=res[1].id,
                    field_id=3,
                    previous_field_id=1,
                    percent_intersection=50,
                    area_intersection=10,
                    relationship=None,
                    deleted_at=None,
                ).to_dict()
            )
            assert (
                res[2].to_dict()
                == FieldRelationship(
                    id=res[2].id,
                    field_id=3,
                    previous_field_id=2,
                    percent_intersection=50,
                    area_intersection=10,
                    relationship=None,
                    deleted_at=None,
                ).to_dict()
            )
        else:
            assert len(res) == 2
            assert (
                res[0].to_dict()
                == FieldRelationship(
                    id=res[0].id,
                    field_id=3,
                    previous_field_id=1,
                    percent_intersection=50,
                    area_intersection=10,
                    relationship=None,
                    deleted_at=None,
                ).to_dict()
            )
            assert (
                res[1].to_dict()
                == FieldRelationship(
                    id=res[1].id,
                    field_id=3,
                    previous_field_id=2,
                    percent_intersection=50,
                    area_intersection=10,
                    relationship=None,
                    deleted_at=None,
                ).to_dict()
            )


async def test_update_field_relationship(mdl, app_request, db_session_maker):
    await mdl.FieldRelationship(
        field_id=1, previous_field_id=0, percent_intersection=100, area_intersection=10, relationship=None
    )

    await update_field_relationship(
        request=app_request,
        field_id=1,
        previous_field_id=0,
        field_relationship_update=FieldRelationshipUpdate(
            percent_intersection=50, relationship=FieldRelationshipType.MERGE
        ),
    )

    async with db_session_maker() as s:
        query = select(FieldRelationship).where(FieldRelationship.field_id == 1)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=1,
                previous_field_id=0,
                percent_intersection=50,
                area_intersection=10,
                relationship=FieldRelationshipType.MERGE,
                deleted_at=None,
            ).to_dict()
        )


async def test_delete_field_relationships(mdl, app_request, db_session_maker):
    await mdl.FieldRelationship(
        field_id=2,
        previous_field_id=0,
        percent_intersection=50,
        area_intersection=10,
        relationship=None,
        deleted_at=None,
    )
    await mdl.FieldRelationship(
        field_id=2,
        previous_field_id=1,
        percent_intersection=50,
        area_intersection=10,
        relationship=None,
        deleted_at=None,
    )
    await mdl.FieldRelationship(
        field_id=5,
        previous_field_id=3,
        percent_intersection=50,
        area_intersection=10,
        relationship=None,
        deleted_at=None,
    )
    await mdl.FieldRelationship(
        field_id=5,
        previous_field_id=4,
        percent_intersection=50,
        area_intersection=10,
        relationship=None,
        deleted_at=None,
    )

    await delete_field_relationships(request=app_request, field_ids=[2, 5])

    async with db_session_maker() as s:
        query = (
            select(FieldRelationship)
            .where(FieldRelationship.field_id.in_([2, 5]))
            .where(FieldRelationship.deleted_at.is_(None))
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 0


async def test_get_field_id_to_previous_field_ids(mdl, app_request):
    await mdl.FieldRelationship(
        field_id=2, previous_field_id=0, percent_intersection=50, area_intersection=10, relationship=None
    )
    await mdl.FieldRelationship(
        field_id=2, previous_field_id=1, percent_intersection=50, area_intersection=10, relationship=None
    )
    await mdl.FieldRelationship(
        field_id=3, previous_field_id=2, percent_intersection=100, area_intersection=10, relationship=None
    )
    assert (await get_field_id_to_previous_field_ids(request=app_request, field_ids=[1, 2, 3, 4])) == {
        2: [0, 1],
        3: [2],
    }


async def test_get_field_id_to_baseline_field(mdl, app_request):
    field_lineage_1 = await mdl.FieldLineage(baseline_field_id=1)
    field_lineage_2 = await mdl.FieldLineage(baseline_field_id=2)

    program_0 = await mdl.Programs()
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(id=1, parent_project_id=project_0.id, field_lineage_id=field_lineage_1.id)
    field_2 = await mdl.Fields(id=2, parent_project_id=project_0.id, field_lineage_id=field_lineage_2.id)

    program_1 = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(parent_project_id=project_1.id, field_lineage_id=field_lineage_1.id)
    field_102 = await mdl.Fields(parent_project_id=project_1.id, field_lineage_id=field_lineage_2.id)

    field_id_to_baseline_field = await get_field_id_to_baseline_field(
        request=app_request, field_ids=[field_101.id, field_102.id]
    )
    assert len(field_id_to_baseline_field) == 2
    assert field_id_to_baseline_field[field_101.id].id == field_1.id
    assert field_id_to_baseline_field[field_102.id].id == field_2.id


async def test_get_fields_by_field_lineage_id_asc(mdl, app_request):
    field_lineage = await mdl.FieldLineage(id=1, baseline_field_id=0, previous_lineage_ids=[])

    program_0 = await mdl.Programs(reporting_period_end_date=datetime(2020, 12, 31))
    project_0 = await mdl.Projects(program_id=program_0.id)
    field_1 = await mdl.Fields(
        parent_project_id=project_0.id,
        status=FieldStatus.enrolled,
        field_lineage_id=field_lineage.id,
        created_at=datetime(2025, 1, 1),
    )
    # program_0 field created after program_1 fields
    field_2 = await mdl.Fields(
        parent_project_id=project_0.id,
        status=FieldStatus.enrolled,
        field_lineage_id=field_lineage.id,
        created_at=datetime(2026, 1, 1),
    )

    program_1 = await mdl.Programs(reporting_period_end_date=datetime(2021, 12, 31))
    project_1 = await mdl.Projects(program_id=program_1.id)
    field_101 = await mdl.Fields(
        parent_project_id=project_1.id,
        status=FieldStatus.enrolled,
        field_lineage_id=field_lineage.id,
        created_at=datetime(2025, 1, 1),
    )
    field_102 = await mdl.Fields(
        parent_project_id=project_1.id,
        status=FieldStatus.enrolled,
        field_lineage_id=field_lineage.id,
        created_at=datetime(2025, 1, 2),
    )
    field_103 = await mdl.Fields(
        parent_project_id=project_1.id,
        status=FieldStatus.registered,
        field_lineage_id=field_lineage.id,
        created_at=datetime(2025, 1, 3),
    )

    fields = await get_fields_by_field_lineage_id_asc(
        request=app_request, field_lineage_id=field_lineage.id, enrolled_only=True
    )
    assert [field.id for field in fields] == [field_1.id, field_2.id, field_101.id, field_102.id]

    fields = await get_fields_by_field_lineage_id_asc(
        request=app_request, field_lineage_id=field_lineage.id, enrolled_only=False
    )
    assert [field.id for field in fields] == [field_1.id, field_2.id, field_101.id, field_102.id, field_103.id]
