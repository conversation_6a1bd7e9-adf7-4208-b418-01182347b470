from datetime import datetime, timezone
from unittest.mock import patch

from ses_client.client import Client as SesClient

from boundaries_service import client as boundaries_client
from entity_events.events.enums import EntityEventType
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from ses_integration.tests.mocks import (
    get_mock_crop_sequences,
    get_mock_crop_sequences_cross_year,
    get_mock_field_practice_events,
    get_mock_field_practice_events_with_fallow_period,
)
from ui.projects.field_events.router import get_phase_cultivation_cycles
from ui.projects.field_events.schema import (
    CultivationCycleResponse,
    NoPracticeObservations,
)
from ui.projects.field_events.tests.mocks import get_mock_geom

field_id = "field-id"


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences_cross_year(field_id),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_id),
)
async def test_get_phase_cultivation_cycles_by_field_id(app_request, mdl):
    start = datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2025, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        is_single_phase_data_collection=False,
        required_years_of_history=5,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_id, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, stages_=[stage])

    result = await get_phase_cultivation_cycles(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        phase_id=phase.id,
    )

    result_for_field = result[field.id]
    assert len(result_for_field) > 0

    for cult_cycle in result_for_field:
        assert isinstance(cult_cycle, CultivationCycleResponse)
        assert cult_cycle.participates_in_phase_completion is True
        assert cult_cycle.end.year < 2025
        if cult_cycle.start.year == 2020 and cult_cycle.end.year == 2021:
            assert len(cult_cycle.events) == 1
            assert cult_cycle.events[0].type == EntityEventType.CROPPING_EVENT
            assert cult_cycle.no_practice_observations == NoPracticeObservations(
                tillage_event=True,
                application_event=True,
                irrigation_event=True,
            )
        elif cult_cycle.start.year == 2021 and cult_cycle.end.year == 2022:
            assert len(cult_cycle.events) == 4
            assert cult_cycle.events[0].type == EntityEventType.CROPPING_EVENT
            assert cult_cycle.events[1].type == EntityEventType.APPLICATION_EVENT
            assert cult_cycle.events[2].type == EntityEventType.IRRIGATION_EVENT
            assert cult_cycle.events[3].type == EntityEventType.TILLAGE_EVENT
            assert cult_cycle.no_practice_observations == NoPracticeObservations()
        elif cult_cycle.start.year == 2022 and cult_cycle.end.year == 2023:
            assert len(cult_cycle.events) == 1
            assert cult_cycle.events[0].type == EntityEventType.FALLOW_PERIOD
            assert cult_cycle.no_practice_observations == NoPracticeObservations(
                tillage_event=True,
                application_event=True,
                irrigation_event=True,
            )
        else:
            assert len(cult_cycle.events) == 0
            assert cult_cycle.no_practice_observations == NoPracticeObservations()


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences_cross_year(field_id),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events(field_id),
)
async def test_get_phase_cultivation_cycles_all_fields(app_request, mdl):
    start = datetime(year=2025, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2025, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        is_single_phase_data_collection=False,
        required_years_of_history=5,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field1 = await mdl.Fields(
        id=1, md5=field_id, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled
    )
    field2 = await mdl.Fields(id=2, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    field3 = await mdl.Fields(id=3, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, stages_=[stage])

    result = await get_phase_cultivation_cycles(
        request=app_request,
        project_id=project.id,
        phase_id=phase.id,
    )
    for field in [field1, field2, field3]:
        result_for_field = result[field.id]
        assert len(result_for_field) > 0
        for cult_cycle in result_for_field:
            assert isinstance(cult_cycle, CultivationCycleResponse)
            assert cult_cycle.participates_in_phase_completion is True


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_id),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events(field_id),
)
async def test_cultivation_cycles_have_correct_participation(app_request, mdl):
    start = datetime(year=2026, month=1, day=1, tzinfo=timezone.utc)
    end = datetime(year=2026, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        is_single_phase_data_collection=False,
        required_years_of_history=3,
    )
    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_id, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    stage = await mdl.Stage(type_=StageTypes.CROP_EVENTS)
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, stages_=[stage])

    result = await get_phase_cultivation_cycles(
        request=app_request,
        project_id=project.id,
        field_id=field.id,
        phase_id=phase.id,
    )

    result_for_field = result[field.id]
    assert len(result_for_field) > 0
    found_false_cycles = False
    found_true_cycles = False
    for cult_cycle in result_for_field:
        assert isinstance(cult_cycle, CultivationCycleResponse)
        if cult_cycle.end.year < 2023:
            assert cult_cycle.participates_in_phase_completion is False
            found_false_cycles = True
        else:
            assert cult_cycle.participates_in_phase_completion is True
            found_true_cycles = True
    assert found_true_cycles
    assert found_false_cycles
