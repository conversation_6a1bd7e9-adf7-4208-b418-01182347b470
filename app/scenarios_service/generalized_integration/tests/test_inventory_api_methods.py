from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest

from phases.enums import PhaseTypes
from programs.enums import AccountingMethod
from scenarios_service.enums import DndcTaskStatusChoices, ScenariosServiceApi
from scenarios_service.generalized_integration import (
    inventory_api_methods,
    measure_api_methods,
)
from scenarios_service.model import DndcTasks


@pytest.mark.asyncio
async def test_get_inventory_consumer_configurations_success(monkeypatch):
    """Test successful retrieval of inventory consumer configurations."""
    expected_response = [
        {
            "x_consumer_id": "inventory-consumer-1",
            "versions_by_protocol": {},
            "dndc_version": "v1.8.2",
            "created_at": "2020-01-01",
            "updated_at": "2020-01-01",
        }
    ]

    mock_response = MagicMock()
    mock_response.json.return_value = expected_response

    mock_inventory_client = MagicMock()
    mock_inventory_client.list_inventory_consumer_configurations = AsyncMock(return_value=mock_response)

    monkeypatch.setattr(inventory_api_methods, "inventory_client", mock_inventory_client)

    result = await inventory_api_methods.get_inventory_consumer_configurations("inventory-consumer-1")

    assert result == expected_response
    mock_inventory_client.list_inventory_consumer_configurations.assert_awaited_once_with("inventory-consumer-1")


@pytest.mark.asyncio
async def test_get_inventory_consumer_configurations_http_error(monkeypatch):
    """Test handling of HTTP errors when retrieving inventory consumer configurations."""
    mock_response = MagicMock()
    mock_response.text = "Internal Server Error"
    mock_response.status_code = 500

    http_error = httpx.HTTPStatusError("Server Error", request=None, response=mock_response)

    mock_inventory_client = MagicMock()
    mock_inventory_client.list_inventory_consumer_configurations = AsyncMock(side_effect=http_error)

    monkeypatch.setattr(inventory_api_methods, "inventory_client", mock_inventory_client)

    result = await inventory_api_methods.get_inventory_consumer_configurations("inventory-consumer-1")

    assert "error" in result
    assert "Scenarios Service threw error: Internal Server Error with status code: 500" in result["error"]
    mock_inventory_client.list_inventory_consumer_configurations.assert_awaited_once_with("inventory-consumer-1")


@pytest.mark.asyncio
async def test_get_dndc_version_for_inventory_config_success(monkeypatch):
    """Test successful retrieval of dndc_version for inventory consumer configuration."""
    consumer_configs = [
        {
            "x_consumer_id": "inventory-consumer-1",
            "versions_by_protocol": {},
            "dndc_version": "v1.8.2",
            "created_at": "2020-01-01",
            "updated_at": "2020-01-01",
        },
        {
            "x_consumer_id": "inventory-consumer-2",
            "versions_by_protocol": {},
            "dndc_version": "v1.9.0",
            "created_at": "2020-01-01",
            "updated_at": "2020-01-01",
        },
    ]

    monkeypatch.setattr(
        inventory_api_methods, "get_inventory_consumer_configurations", AsyncMock(return_value=consumer_configs)
    )

    result = await inventory_api_methods.get_dndc_version_for_inventory_config("inventory-consumer-1")

    assert result == "v1.8.2"
    inventory_api_methods.get_inventory_consumer_configurations.assert_awaited_once_with("inventory-consumer-1")


@pytest.mark.asyncio
async def test_get_dndc_version_for_inventory_config_no_configs(monkeypatch):
    """Test handling when no inventory consumer configurations are found."""
    monkeypatch.setattr(inventory_api_methods, "get_inventory_consumer_configurations", AsyncMock(return_value=[]))

    result = await inventory_api_methods.get_dndc_version_for_inventory_config("inventory-consumer-1")

    assert result is None
    inventory_api_methods.get_inventory_consumer_configurations.assert_awaited_once_with("inventory-consumer-1")


@pytest.mark.asyncio
async def test_get_dndc_version_for_inventory_config_consumer_id_not_found(monkeypatch):
    """Test handling when consumer_id is not found in the configurations."""
    consumer_configs = [
        {
            "x_consumer_id": "different-consumer",
            "versions_by_protocol": {},
            "dndc_version": "v1.8.2",
            "created_at": "2020-01-01",
            "updated_at": "2020-01-01",
        }
    ]

    monkeypatch.setattr(
        inventory_api_methods, "get_inventory_consumer_configurations", AsyncMock(return_value=consumer_configs)
    )

    result = await inventory_api_methods.get_dndc_version_for_inventory_config("inventory-consumer-1")

    assert result is None
    inventory_api_methods.get_inventory_consumer_configurations.assert_awaited_once_with("inventory-consumer-1")


@pytest.mark.asyncio
async def test_create_dndc_task_with_dndc_version_integration_with_inventory(monkeypatch):
    """Test integration between create_dndc_task_with_dndc_version and inventory API methods."""
    # Setup
    mock_request = MagicMock()
    program_id = 123
    ss_consumer_id = "inventory-consumer-1"

    consumer_configs = [
        {
            "x_consumer_id": "inventory-consumer-1",
            "versions_by_protocol": {},
            "dndc_version": "v2.1.0",
            "created_at": "2020-01-01",
            "updated_at": "2020-01-01",
        }
    ]

    monkeypatch.setattr(
        inventory_api_methods, "get_inventory_consumer_configurations", AsyncMock(return_value=consumer_configs)
    )

    monkeypatch.setattr(
        measure_api_methods.db,
        "create_dndc_task",
        AsyncMock(
            return_value=DndcTasks(
                id="task-integration-test",
                program_id=program_id,
                status=DndcTaskStatusChoices.in_progress,
                scenarios_service_api=ScenariosServiceApi.inventory_api,
            )
        ),
    )

    result = await measure_api_methods.create_dndc_task_with_dndc_version(
        request=mock_request,
        program_id=program_id,
        is_dry_run=False,
        phase=PhaseTypes.MONITORING,
        accounting_method=AccountingMethod.inventory,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
        baseline_method=None,
        protocol=None,
        ss_consumer_id=ss_consumer_id,
        submitted_field_ids=[100, 200],
    )

    assert result.id == "task-integration-test"

    inventory_api_methods.get_inventory_consumer_configurations.assert_awaited_once_with(ss_consumer_id)
    measure_api_methods.db.create_dndc_task.assert_awaited_once_with(
        request=mock_request,
        program_id=program_id,
        project_id=None,
        phase=PhaseTypes.MONITORING,
        is_dry_run=False,
        scenarios_service_api=ScenariosServiceApi.inventory_api,
        accounting_method=AccountingMethod.inventory,
        baseline_method=None,
        protocol=None,
        dndc_version="v2.1.0",
        submitted_field_ids=[100, 200],
    )
