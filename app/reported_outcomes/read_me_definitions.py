from typing import Any

from scenarios_service.enums import MeasureProtocolResponseTypes


def get_field_outcomes_read_me(
    does_program_have_co_ops: bool, measure_protocol_response_type: MeasureProtocolResponseTypes
) -> list[dict[str, Any]]:
    lead_group = [
        ["Field Outcomes", "Field ID", "", "A unique record representing a field enrollment in a program"],
        [
            "Field Outcomes",
            "Email*",
            "",
            "The email of the producer enrolled in the program. \n*<PERSON>row recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Project ID",
            "",
            "A unique record used to represent a user’s enrolment in a program. A User can have one Project per Program.",
        ],
        [
            "Field Outcomes",
            "Farm Name*",
            "",
            "Name of the farm entered by the user.\n*<PERSON><PERSON> recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Field Name*",
            "",
            "Name of the field entered by the user. \n*<PERSON><PERSON> recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "First Name*",
            "",
            "Name of the Producer where the field is enrolled. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Last Name*",
            "",
            "Last name of the Producer where the field is enrolled. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
    ]
    if does_program_have_co_ops:
        co_op_group = [
            [
                "Field Outcomes",
                "Co-op*",
                "",
                "Name of the co-op the field enrollment is participating under. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
            ]
        ]
    else:
        co_op_group = []
    middle_group = [
        [
            "Field Outcomes",
            "Region",
            "",
            "Regions where the field is located. If fields is located accross multiple regions, the region with the largest field area overlapping will be reported.",
        ],
        ["Field Outcomes", "Country", "", "Country where the program is located"],
        ["Field Outcomes", "Mapped Area", "acres", "The size of the field quantified."],
        ["Field Outcomes", "MD5", "", "A unique record representing the field boundary."],
        ["Field Outcomes", "Enrollment Year", "", "Year where the field was first enrolled in the program."],
    ]
    # we figured out these columns weren't coming through for the first Vietnam rice program (582)
    # so we removed them for Scope 1 outcomes (at least for now)
    if measure_protocol_response_type == MeasureProtocolResponseTypes.scope_3:
        scope_specific_group = [
            [
                "Field Outcomes",
                "Credited Crop",
                "",
                "The common name of the crop for which carbon outcomes are quantified.",
            ],
            [
                "Field Outcomes",
                "Yield",
                "bushels/acre*",
                "Harvested yield of the credited crop. \n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
            ],
            [
                "Field Outcomes",
                "Baseline Practice",
                "",
                "Practices adopted in the baseline year or baseline period, derived from historcal crop management events",
            ],
            [
                "Field Outcomes",
                "Completed Practice Change",
                "",
                "Practices adopted in the reporting year, derived from reported crop management events",
            ],
        ]
    elif measure_protocol_response_type == MeasureProtocolResponseTypes.scope_1:
        scope_specific_group = []
    else:
        raise NotImplementedError(f"Did not handle measure protocol response type: {measure_protocol_response_type}")
    trailing_group = [
        [
            "Field Outcomes",
            "Eligible",
            "",
            "A binary indicator (Yes/No) showing if the field meets the program's eligibility criteria.",
        ],
        [
            "Field Outcomes",
            "Reporting Period Start Date",
            "",
            "The first day of the reporting period, This is the starting date used to quantify carbon outcomes.",
        ],
        [
            "Field Outcomes",
            "Reporting Period End Date",
            "",
            "The last day of the reporting period for the field. This is the end of the time period used to quantify carbon outcomes.",
        ],
        [
            "Field Outcomes",
            "Credit Share",
            "mtCO2e",
            "A value representing the total emissions reduction (reversible and non-reversible) attributed to the field, after uncertainty is applied. Calculated as: \nReversible Credit Share + Non Reversible Credit Share",
        ],
        [
            "Field Outcomes",
            "Reversible Credit Share",
            "mtCO2e",
            "A value representing the reversible emissions reduction attributed to the field, after uncertainty is applied. Calculated by deriving the weight of the field’s reversible `mean` in relation to other fields with the same commodity crop, and multiplying it by the total crop-level reversible emissions reduction value.",
        ],
        [
            "Field Outcomes",
            "Reversible dSOC Practice Change",
            "mtCO2e",
            "A value representing the quantity of carbon sequestration into the soil in the practice change scenario, before uncertainty is applied.",
        ],
        [
            "Field Outcomes",
            "Reversible dSOC Baseline",
            "mtCO2e",
            "A value representing the quantity of carbon sequestration into the soil in the baseline scenario, before uncertainty is applied.",
        ],
        [
            "Field Outcomes",
            "Reversible dSOC Preliminary Credit",
            "mtCO2e",
            "A value representing the modeled carbon sequestration change. Calculated as: \nReversible dSOC Practice Change and Reversible dSOC Baseline, before uncertainty is applied. \nPositive values represent increased sequestration, while negative values indicate carbon emissions resulting from SOC loss.",
        ],
        ["Field Outcomes", "Reversible dSOC Mean", "mtCO2e", "The mean of the dSOC uncertainty distribution."],
        [
            "Field Outcomes",
            "Reversible dSOC Standard Deviation",
            "mtCO2e",
            "The standard deviation of the dSOC uncertainty distribution.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Credit Share",
            "mtCO2e",
            "A value representing the non-reversible emissions reduction attributed to the field, after uncertainty is applied. Calculated by deriving the weight of the field’s reversible `mean` in relation to other fields with the same commodity crop, and multiplying it by the total crop-level non-reversible emissions reduction value.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Emission Reductions Mean",
            "mtCO2e",
            "The mean of the non-reversible uncertainty distribution. Calculated as the sum of all individual non-reversible GHG uncertainty distributions and the calculated `credit` values.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Emission Reductions Standard Deviation",
            "mtCO2e",
            "The standard deviation of the dSOC uncertainty distribution. Calculated as the sum of all individual non-reversible GHG uncertainty distributions and the calculated `credit` values.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Direct N2O Practice Change",
            "mtCO2e",
            "The modeled direct N2O emissions under the practice change scenario, before uncertainty is applied.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Direct N2O Baseline",
            "mtCO2e",
            "The modeled direct N2O emissions under the baseline scenario, before uncertainty is applied.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Direct N2O Preliminary Credit",
            "mtCO2e",
            "The modeled emissions reduction, before applying uncertainty. Calculated as:\nBaseline N2O Emissions – Practice Change N2O Emissions.\nPositive values indicate reductions, while negative values indicate increases.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Direct N2O Mean",
            "mtCO2e",
            "The mean of the direct N2O uncertainty distribution.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Direct N2O Standard Deviation",
            "mtCO2e",
            "The standard deviation of the direct N2O uncertainty distribution.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Indirect N2O Practice Change",
            "mtCO2e",
            "The modeled indirect N2O emissions under the practice change scenario.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Indirect N2O Baseline",
            "mtCO2e",
            "The modeled indirect N2O emissions under the baseline scenario.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Indirect N2O Credit",
            "mtCO2e",
            "A value representing the modeled emissions reduction. It is the difference in indirect N2O emissions. Calculated as: \nBaseline - Practice Change. ",
        ],
        [
            "Field Outcomes",
            "Non Reversible Soil CH4 Practice Change",
            "mtCO2e",
            "The modeled soil CH4 emissions under the practice change scenario.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Soil CH4 Baseline",
            "mtCO2e",
            "The modeled Soil CH4 emissions under the baseline scenario.",
        ],
        [
            "Field Outcomes",
            "Non Reversible Soil CH4 Credit",
            "mtCO2e",
            "A value representing the modeled emissions reduction. It is the difference in Soil CH4 emissions, calculated as baseline - practice change.",
        ],
    ]
    field_row_data = lead_group + co_op_group + middle_group + scope_specific_group + trailing_group
    return [{k: v for k, v in zip(_get_read_me_headers(), row)} for row in field_row_data]


def get_field_inventory_outcomes_read_me() -> list[dict[str, Any]]:
    field_row_data = [
        ["Field Outcomes", "Field ID", "", "A unique record representing a field enrollment in a program"],
        [
            "Field Outcomes",
            "Email*",
            "",
            "The email of the producer enrolled in the program. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Project ID",
            "",
            "A unique record used to represent a user’s enrolment in a program. A User can have one Project per Program.",
        ],
        [
            "Field Outcomes",
            "Farm Name*",
            "",
            "Name of the farm entered by the user.\n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Field Name*",
            "",
            "Name of the field entered by the user. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "First Name*",
            "",
            "Name of the Producer where the field is enrolled. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Last Name*",
            "",
            "Last name of the Producer where the field is enrolled. \n*Regrow recommends that customers exclude this information from any reports shared with auditors due to concerns over protecting Personal Identifiable Information (PII).",
        ],
        [
            "Field Outcomes",
            "Region",
            "",
            "Regions where the field is located. If fields is located cross multiple regions, the region with the largest field area overlapping will be reported.",
        ],
        ["Field Outcomes", "Mapped Area", "acres", "The size of the area quantified."],
        [
            "Field Outcomes",
            "Yield",
            "bushels/acre*",
            "Harvested yield of the credited crop. \n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Field Outcomes",
            "Crop Type",
            "",
            "The common name of the crop to which this field's emissions contributed.",
        ],
        [
            "Field Outcomes",
            "Net Emissions Factor Percentage",
            "%",
            "% of each field contribution to the project emissions, for a given crop.",
        ],
        [
            "Field Outcomes",
            "Net Emissions Factor",
            "mtCO2e/bushels*",
            "The total field-level emissions, combining all greenhouse gas (GHG) emissions and soil organic carbon (SOC) changes into a single factor. This includes CH4, and both direct and indirect N2O emissions.\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Field Outcomes",
            "GHG Emissions Factor",
            "mtCO2e/bushels*",
            "Direct and indirect field-level emissions including methane (CH4) and nitrous oxide (N2O). SOC changes are not included.\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Field Outcomes",
            "SOC Emissions Factor",
            "mtCO2e/bushels*",
            "The net change in soil organic carbon (SOC) due to agricultural practices, reported separately from other GHG emissions. This captures whether the soil is storing or releasing carbon over time.\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
    ]
    return [{k: v for k, v in zip(_get_read_me_headers(), row)} for row in field_row_data]


def get_crop_outcomes_read_me() -> list[dict[str, Any]]:
    crop_row_data = [
        ["Crop Outcomes", "Crop", "", "The common name of the crop for which carbon outcomes are quantified."],
        ["Crop Outcomes", "Number Of Fields", "", "The total number of fields quantified for a given crop type."],
        [
            "Crop Outcomes",
            "Mapped Area",
            "acres",
            "The total area of all fields assessed for a given crop type, determined by summing each individual field area.",
        ],
        [
            "Crop Outcomes",
            "Total Yield",
            "bushels*",
            "The total reported yield for a crop. \n* Yield units are reported in bushels for all crops, except for pumpkins, which are reported in crates (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Crop Outcomes",
            "Average Yield",
            "bushels/acre*",
            "The total reported yield for a crop divided by the total area harvested\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Crop Outcomes",
            "Reporting Period Start Date (earliest)",
            "",
            "The earliest start date of the field-level reporting period, used to quantify carbon outcomes.",
        ],
        [
            "Crop Outcomes",
            "Reporting Period End Date (latest)",
            "",
            "The latest end date of the field-level reporting period, marking the conclusion of the time used to quantify carbon outcomes.",
        ],
        [
            "Crop Outcomes",
            "Total Credits",
            "mtCO2e",
            "The sum of Total Reversible Emissions Reductions and Total Non Reversible Emissions Reductions",
        ],
        [
            "Crop Outcomes",
            "Total Credits Per Yield",
            "mtCO2e/bushel",
            "The sum of Total Reversible Emissions Reductions and Total Non Reversible Emissions Reductions divided by the Total Yield",
        ],
        [
            "Crop Outcomes",
            "Reversible Credit Mean",
            "mtCO2e",
            "The mean of the crop-level reversible uncertainty distribution.",
        ],
        [
            "Crop Outcomes",
            "Reversible Credit Standard Deviation",
            "mtCO2e",
            "The standard deviation of the crop-level reversible uncertainty distribution.",
        ],
        [
            "Crop Outcomes",
            "Total Reversible Emissions Reductions",
            "mtCO2e",
            "The reversible emissions reduction value, with an uncertainty adjustment. The value is derived from the `reporting percentile` of the crop-level reversible uncertainty distribution.",
        ],
        [
            "Crop Outcomes",
            "Non Reversible Credit Mean",
            "mtCO2e",
            "The mean of the crop-level non-reversible uncertainty distribution.",
        ],
        [
            "Crop Outcomes",
            "Non Reversible Credit Standard Deviation",
            "mtCO2e",
            "The standard deviation of the crop-level non-reversible uncertainty distribution.",
        ],
        [
            "Crop Outcomes",
            "Total Non Reversible Emissions Reductions",
            "mtCO2e",
            "The non-reversible emissions reduction value, with an uncertainty adjustment. The value is derived from the `reporting percentile` of the crop-level non-reversible uncertainty distribution.",
        ],
    ]
    return [{k: v for k, v in zip(_get_read_me_headers(), row)} for row in crop_row_data]


def get_crop_inventory_outcomes_read_me() -> list[dict[str, Any]]:
    crop_row_data = [
        ["Crop Outcomes", "Crop", "", "The common name of the crop for which carbon outcomes are quantified."],
        ["Crop Outcomes", "Number Of Fields", "", "The total number of fields quantified for a given crop type."],
        [
            "Crop Outcomes",
            "Mapped Area",
            "acres",
            "The total area of all fields assessed for a given crop type, determined by summing each individual field area.",
        ],
        [
            "Crop Outcomes",
            "Total Yield",
            "bushels*",
            "The total reported yield for a crop. \n* Yield units are reported in bushels for all crops, except for pumpkins, which are reported in crates (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Crop Outcomes",
            "Average Yield",
            "bushels/acre*",
            "The total reported yield for a crop divided by the total area harvested\n* Yield units are reported in bushels per acre for all crops, except for pumpkins, which are reported in crates per acre (1 crate = 24.6 lbs), and sugar beets, which are reported in sacks per acre (1 sack = 25 lbs), as no standard bushel conversion exists for these crops. Conversion source: USDA ERS Report.",
        ],
        [
            "Crop Outcomes",
            "Net Emissions",
            "mtCO2e/Yield Unit",
            "The net greenhouse gas (GHG) emissions associated with crop production, calculated as the sum of on-field emissions minus the change in soil carbon stocks.",
        ],
        [
            "Crop Outcomes",
            "Indirect N2O Emissions",
            "mtCO2e/Yield Unit",
            "The emissions of nitrous oxide (N2O) from nitrogen inputs that indirectly contribute to greenhouse gas effects, such as through volatilization and leaching processes.",
        ],
        [
            "Crop Outcomes",
            "Direct N2O Emissions",
            "mtCO2e/Yield Unit",
            "The direct emissions of nitrous oxide (N2O) released from soil due to applied nitrogen inputs, including fertilizers and organic matter.",
        ],
        [
            "Crop Outcomes",
            "CH4 Emissions",
            "mtCO2e/Yield Unit",
            "Methane (CH4) emissions released from agricultural practices, such as flooded conditions in rice cultivation or organic matter decomposition.",
        ],
        [
            "Crop Outcomes",
            "Land Management Removals",
            "mtCO2e/Yield Unit",
            "The removal of CO2 from the atmosphere due to changes in soil carbon stocks as a result of land management practices, such as cover cropping or reduced tillage.",
        ],
    ]
    return [{k: v for k, v in zip(_get_read_me_headers(), row)} for row in crop_row_data]


def get_program_outcomes_read_me() -> list[dict[str, Any]]:
    program_row_data = [
        ["Program Outcomes", "Number Of Fields", "", "The total number of fields quantified for this program."],
        [
            "Program Outcomes",
            "Mapped Area",
            "acres",
            "The total area of all fields assessed for this program, determined by summing each individual field area.",
        ],
        [
            "Program Outcomes",
            "Reporting Period Start Date (earliest)",
            "",
            "The earliest start date of the field-level reporting period, used to quantify carbon outcomes.",
        ],
        [
            "Program Outcomes",
            "Reporting Period End Date (latest)",
            "",
            "The latest end date of the field-level reporting period, marking the conclusion of the time used to quantify carbon outcomes.",
        ],
        [
            "Program Outcomes",
            "Total Credits",
            "mtCO2e",
            "The sum of Total Reversible Emissions Reductions and Total Non Reversible Emissions Reductions",
        ],
        ["Program Outcomes", "Total Reversible Credit", "mtCO2e", "The reversible emissions reduction value."],
        [
            "Program Outcomes",
            "Reversible Credit Mean",
            "mtCO2e",
            "The mean of the program-level reversible uncertainty distribution.",
        ],
        [
            "Program Outcomes",
            "Reversible Credit Standard Deviation",
            "mtCO2e",
            "The standard deviation of the program-level reversible uncertainty distribution.",
        ],
        [
            "Program Outcomes",
            "Total Reversible Emissions Reductions",
            "mtCO2e",
            "The reversible emissions reduction value, with an uncertainty adjustment. The value is derived from the `reporting percentile` of the program-level reversible uncertainty distribution.",
        ],
        ["Program Outcomes", "Total Non Reversible Credit", "mtCO2e", "The non-reversible emissions reduction value."],
        [
            "Program Outcomes",
            "Non Reversible Credit Mean",
            "mtCO2e",
            "The mean of the program-level non-reversible uncertainty distribution.",
        ],
        [
            "Program Outcomes",
            "Non Reversible Credit Standard Deviation",
            "mtCO2e",
            "The standard deviation of the program-level non-reversible uncertainty distribution.",
        ],
        [
            "Program Outcomes",
            "Total Non Reversible Emissions Reductions",
            "mtCO2e",
            "The non-reversible emissions reduction value, with an uncertainty adjustment. The value is derived from the `reporting percentile` of the program-level non-reversible uncertainty distribution.",
        ],
    ]
    return [{k: v for k, v in zip(_get_read_me_headers(), row)} for row in program_row_data]


def _get_read_me_headers() -> list[str]:
    return ["File", "Parameter", "Unit", "Description"]
