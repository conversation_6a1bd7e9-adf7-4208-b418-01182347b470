from datetime import datetime, timedelta, timezone
from unittest.mock import patch

from ses_client.client import Client as SesClient

from boundaries_service import client as boundaries_client
from fields.enums import FieldStatus
from permissions.enums import RoleTypes
from phases.enums import PhaseTypes, StageTypes
from programs.enums import ProgramTemplate, ProgramType
from projects.db import get_completion_percentage_from_project_stage_summary
from ses_integration.tests.mocks import (
    get_mock_crop_sequences,
    get_mock_field_practice_events_with_fallow_period,
)
from ui.projects.completion.dataclasses import StageCompletionResult
from ui.projects.completion.db import (
    get_stage_completion_for_field,
    store_stage_completion_for_field,
)
from ui.projects.completion.enums import NarrowestCompletionStatus
from ui.projects.completion.handle_bulk_field_completion_updates import (
    attempt_updating_project_completion_for_program,
    handle_multiple_fields_changing_completion,
    prepare_completion_as_necessary,
    update_completion_for_projects_in_shared_program,
)
from ui.projects.field_events.tests.mocks import get_mock_geom

field_md5 = "field-md5"


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_update_completion_for_projects_in_shared_program(mdl, mocker, app_request):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True)

    stages = []
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            stage = await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)
            stages.append(stage)

    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    f1 = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    user2 = await mdl.Users()
    project2 = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project2.id, user=user2.id)
    f2 = await mdl.Fields(md5=field_md5, parent_project_id=project2.id, deleted_at=None, status=FieldStatus.enrolled)
    f3 = await mdl.Fields(md5=field_md5, parent_project_id=project2.id, deleted_at=None, status=FieldStatus.enrolled)

    await update_completion_for_projects_in_shared_program(app_request, [project.id, project2.id])

    for field in [f1, f2, f3]:
        for stage in stages:
            stage_completion = await get_stage_completion_for_field(app_request, stage.id, field.id)
            assert stage_completion.completion_status != NarrowestCompletionStatus.undetermined


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_handle_multiple_fields_changing_completion(mdl, app_request):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True)

    stages = []
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            stage = await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)
            stages.append(stage)

    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    f1 = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    f2 = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    f3 = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    await handle_multiple_fields_changing_completion(app_request, project.id, [f1.id, f2.id, f3.id])

    for field in [f1, f2, f3]:
        for stage in stages:
            stage_completion = await get_stage_completion_for_field(app_request, stage.id, field.id)
            assert stage_completion.completion_status != NarrowestCompletionStatus.undetermined


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_prepare_completion_as_necessary(mdl, mocker, app_request):
    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
        program_type=ProgramType.live,
    )
    phase_end_date = (datetime.now() + timedelta(days=90)).date()
    e_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True, end_date=phase_end_date
    )
    m_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True, end_date=phase_end_date
    )

    stages = []
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            stage = await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)
            stages.append(stage)

    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    f1 = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    user2 = await mdl.Users()
    project2 = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project2.id, user=user2.id)
    f2 = await mdl.Fields(md5=field_md5, parent_project_id=project2.id, deleted_at=None, status=FieldStatus.enrolled)
    f3 = await mdl.Fields(md5=field_md5, parent_project_id=project2.id, deleted_at=None, status=FieldStatus.enrolled)

    await prepare_completion_as_necessary(app_request)

    for field in [f1, f2, f3]:
        for stage in stages:
            stage_completion = await get_stage_completion_for_field(app_request, stage.id, field.id)
            assert stage_completion.completion_status != NarrowestCompletionStatus.undetermined
    for project in [project, project2]:
        for stage in stages:
            existing_completion_percentage = await get_completion_percentage_from_project_stage_summary(
                app_request, project.id, stage.id
            )
        assert existing_completion_percentage is not None


@patch.object(
    boundaries_client,
    "get_geometry_for_md5",
    get_mock_geom,
)
@patch.object(
    SesClient,
    "fetch_cropping_sequences_for_fields_with_context",
    get_mock_crop_sequences(field_md5),
)
@patch.object(
    SesClient,
    "fetch_events_for_fields_with_context",
    get_mock_field_practice_events_with_fallow_period(field_md5),
)
async def test_attempt_updating_project_completion_for_program(mdl, mocker, app_request):
    producer_role = await mdl.Roles(name="Producer", role_type=RoleTypes.PRODUCER)

    start = datetime(year=2023, month=2, day=1, tzinfo=timezone.utc)
    end = datetime(year=2023, month=12, day=31, tzinfo=timezone.utc)
    program = await mdl.Programs(
        reporting_period_start_date=start,
        reporting_period_end_date=end,
        program_template=ProgramTemplate.event_based,
        required_years_of_history=5,
        is_single_phase_data_collection=False,
    )
    phase_end_date = (datetime.now() + timedelta(days=90)).date()
    e_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.ENROLMENT, deleted_at=None, enabled=True, end_date=phase_end_date
    )
    m_phase = await mdl.Phases(
        program_id=program.id, type_=PhaseTypes.MONITORING, deleted_at=None, enabled=True, end_date=phase_end_date
    )

    stages = []
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.TILLAGE_EVENTS,
        ]:
            stage = await mdl.Stage(type_=stage_type, phase_id=phase.id, deleted_at=None, enabled=True)
            stages.append(stage)

    user = await mdl.Users()
    project = await mdl.Projects(program_id=program.id)
    await mdl.ProjectPermissions(project=project.id, user=user.id)
    field = await mdl.Fields(md5=field_md5, parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    await mdl.RolesUsers(role_id=producer_role.id, program_id=program.id, fs_user_id=user.id)

    stage_completion_status = StageCompletionResult(
        completion_status=NarrowestCompletionStatus.incomplete, completed_cycle_count=1, total_cycle_count=2
    )
    for stage in stages:
        await store_stage_completion_for_field(app_request, stage.id, field.id, stage_completion_status)

    project_update_results = await attempt_updating_project_completion_for_program(app_request, program.id)
    assert len(project_update_results) == 1
    assert project_update_results[project.id]
