from collections import defaultdict

from fastapi import Request
from sqlalchemy import func, select, update as sqlalchemy_update
from sqlalchemy.orm import aliased

from field_lineage.model import FieldLineage, FieldRelationship
from field_lineage.schema import (
    FieldLineageRequest,
    FieldRelationshipRequest,
    FieldRelationshipUpdate,
)
from fields.enums import FieldStatus
from fields.model import Fields
from helper.helper import run_query
from programs.model import Programs
from projects.model import Projects
from root_crud import create, get


async def get_field_lineage_by_id(request: Request, field_lineage_id: int) -> FieldLineage | None:
    field_lineage = await get.get(
        request=request,
        orm_type=FieldLineage,
        id_field=FieldLineage.id,
        ids=[field_lineage_id],
        empty_return=True,
    )
    if field_lineage:
        return field_lineage[0]
    return None


async def get_field_lineage_by_baseline_field_id(request: Request, baseline_field_id: int) -> FieldLineage | None:
    field_lineage = await get.get(
        request=request,
        orm_type=FieldLineage,
        id_field=FieldLineage.baseline_field_id,
        ids=[baseline_field_id],
        empty_return=True,
    )
    if field_lineage:
        return field_lineage[0]
    return None


async def create_field_lineages(
    request: Request, field_lineage_requests: list[FieldLineageRequest]
) -> list[FieldLineage]:
    return await create.create(
        request=request,
        instances=field_lineage_requests,
        orm_type=FieldLineage,
        translate=True,
        return_orm=True,
    )


async def update_previous_field_lineage_ids(
    request: Request, field_lineage_id: int, previous_field_lineage_ids: list[int]
) -> None:
    stmt = (
        sqlalchemy_update(FieldLineage)
        .where(FieldLineage.id == field_lineage_id)
        .values(previous_lineage_ids=previous_field_lineage_ids)
    )
    async with request.state.sql_session.begin() as session:
        await session.execute(stmt)


async def get_field_relationships(request: Request, field_id: int) -> list[FieldRelationship]:
    return await get.get(
        request=request,
        orm_type=FieldRelationship,
        id_field=FieldRelationship.field_id,
        ids=[field_id],
        empty_return=True,
    )


async def create_field_relationships(
    request: Request, field_relationship_requests: list[FieldRelationshipRequest], append_only: bool = False
) -> None:
    if not append_only:
        await delete_field_relationships(
            request=request, field_ids=[req.field_id for req in field_relationship_requests]
        )
    return await create.create(
        request=request,
        instances=field_relationship_requests,
        orm_type=FieldRelationship,
        translate=True,
        return_orm=False,
    )


async def update_field_relationship(
    request: Request, field_id: int, previous_field_id: int, field_relationship_update: FieldRelationshipUpdate
) -> None:
    field_relationship_update_dict = field_relationship_update.dict(exclude_unset=True)
    stmt = (
        sqlalchemy_update(FieldRelationship)
        .where(FieldRelationship.field_id == field_id)
        .where(FieldRelationship.previous_field_id == previous_field_id)
        .values(field_relationship_update_dict)
    )
    async with request.state.sql_session.begin() as session:
        await session.execute(stmt)


async def delete_field_relationships(request: Request, field_ids: list[int]) -> None:
    stmt = (
        sqlalchemy_update(FieldRelationship)
        .where(FieldRelationship.deleted_at.is_(None))
        .where(FieldRelationship.field_id.in_(field_ids))
    )
    stmt = stmt.values(deleted_at=func.current_timestamp())
    async with request.state.sql_session.begin() as session:
        await session.execute(stmt)


async def get_field_id_to_previous_field_ids(request: Request, field_ids: list[int]) -> dict[int, list[int]]:
    query = (
        select(FieldRelationship)
        .where(FieldRelationship.field_id.in_(field_ids))
        .where(FieldRelationship.deleted_at.is_(None))
    )
    async with request.state.sql_session() as s:
        res = (await run_query(query=query, s=s)).scalars().all()
    field_id_to_previous_field_ids = defaultdict(list)
    for single_res in res:
        field_id_to_previous_field_ids[single_res.field_id].append(single_res.previous_field_id)
    return dict(field_id_to_previous_field_ids)


async def get_field_id_to_baseline_field(request: Request, field_ids: list[int]) -> dict[int, Fields]:
    baseline_fields = aliased(Fields)
    query = (
        select(Fields.id, baseline_fields)
        .join(FieldLineage, Fields.field_lineage_id == FieldLineage.id)
        .join(baseline_fields, FieldLineage.baseline_field_id == baseline_fields.id)
        .where(Fields.id.in_(field_ids))
    )
    async with request.state.sql_session() as s:
        res = (await run_query(query=query, s=s)).all()
    return {field_id: baseline_field for field_id, baseline_field in res}


async def get_fields_by_field_lineage_id_asc(
    request: Request, field_lineage_id: int, enrolled_only: bool
) -> list[Fields]:
    query = (
        select(Fields)
        .join(Projects, Fields.parent_project_id == Projects.id)
        .join(Programs, Projects.program_id == Programs.id)
        .where(Fields.field_lineage_id == field_lineage_id)
        .where(Fields.deleted_at.is_(None))
    )
    if enrolled_only:
        query = query.where(Fields.status == FieldStatus.enrolled)
    query = query.order_by(Programs.reporting_period_end_date, Fields.created_at)
    async with request.state.sql_session() as s:
        return (await run_query(query=query, s=s)).scalars().all()
