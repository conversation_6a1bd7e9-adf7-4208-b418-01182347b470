import itertools
from datetime import datetime, timedelta

from pydantic import ValidationError
from starlette.requests import Request

from defaults import defaults_translator
from defaults.attribute_options import CropUsage, NO_ADDITIVES_OPTION
from defaults.consts import COVER_CROP_USAGES
from entity_events.data_classes import (
    EventCreationSpecification,
    EventCreatorErrorHandling,
)
from entity_events.events.application_event import ApplicationEvent
from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.fallow_period import FallowPeriod
from entity_events.methods import get_entity_events_for_field
from fields.db import get_fields_by_program
from fields.schema import Field
from phases.enums import PhaseTypes, StageTypes
from programs import db as programs_db
from programs.enums import ProgramTemplate
from programs.model import Programs
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration import gapfilling
from scenarios_service.generalized_integration.enums import CroppingEventAdjustment
from scenarios_service.generalized_integration.schema import (
    AdjustedCroppingEvent,
    ScenariosServiceIntegrationError,
)
from scenarios_service.model import ScenariosServiceIntegrationErrorCode
from ses_integration import fetch_entity_events


async def get_events(request: Request, field: Field, scenarios_service_api: ScenariosServiceApi) -> list[EntityEvent]:
    """
    Fetches events from SES or SEF, depending on program template
    * Applies overlap adjustment logic
    * Validates some assertions about the events
    * (Legacy only) Applies gapfilling logic
    * (Legacy only) Includes intended events for Explore API
    """
    program = await programs_db.get_program_from_field_id(request=request, field_id=field.id)
    reporting_period = program.get_reporting_period()
    if program.program_template == ProgramTemplate.event_based:
        try:
            # For Measure API, we can use just M phase events since event associations do no consider event dates.
            enrollment_phase_only = bool(scenarios_service_api == ScenariosServiceApi.explore_api)
            events: list[EntityEvent] = await fetch_entity_events.fetch_events_for_field_phase(
                request=request,
                field_id=field.id,
                enrollment_phase_only=enrollment_phase_only,
                is_single_phase_program=program.is_single_phase_data_collection,
            )
            # Events before historical need not have dates, so filter these out eagerly to avoid downstream errors
            events = [
                ev for ev in events if ev.get_interval_start_or_occurred_at() and ev.get_interval_end_or_occurred_at()
            ]
            if scenarios_service_api == ScenariosServiceApi.explore_api:
                # for Explore API, we want be sure we only include events that are prior to the reporting period
                events = [
                    ev for ev in events if ev.get_interval_end_or_occurred_at().date() < reporting_period.start_date
                ]
            else:
                # Otherwise, exclude events after the reporting period since they may not be valid and complete
                assert scenarios_service_api in [
                    ScenariosServiceApi.measure_api,
                    ScenariosServiceApi.inventory_api,
                ], f"Unexpected ScenariosServiceApi: {scenarios_service_api}"
                events = [
                    ev for ev in events if ev.get_interval_end_or_occurred_at().date() <= reporting_period.end_date
                ]

        except ValidationError as validation_err:
            errors = validation_err.json()
            raise ScenariosServiceIntegrationError(
                "Unable to assemble MRV Structured Events because data failed validation: " + errors,
                ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )
        # In the event-based program, do not gapfill yet. Gapfilling has to happen after cultivation cycles have been
        # constructed to distinguish between historical and reporting period events.
        # Do not filter out intended events because they are not stored in SES.
        events, _, _ = adjust_overlapping_cropping_events(events)
        _validate_intercropping(events)
        return events
    else:
        assert program.program_template == ProgramTemplate.legacy
        phase_type = PhaseTypes.ENROLMENT if scenarios_service_api == ScenariosServiceApi.explore_api else None
        try:
            events_by_phase = await get_entity_events_for_field(request=request, field=field, phase_type=phase_type)
        except ValidationError as validation_err:
            errors = validation_err.json()
            raise ScenariosServiceIntegrationError(
                "Unable to assemble MRV Structured Events because data failed validation: " + errors,
                ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )
        # Remove any intended events. They are fetched later for Explore Integration.
        events_by_phase = {
            phase_type: [ev for ev in events if not ev.is_intended] for phase_type, events in events_by_phase.items()
        }
        # for Explore API, we want be sure we only include events that are prior to the reporting period
        if scenarios_service_api == ScenariosServiceApi.explore_api:
            events_by_phase[PhaseTypes.ENROLMENT] = [
                ev
                for ev in events_by_phase.get(PhaseTypes.ENROLMENT, [])
                if ev.get_interval_end_or_occurred_at().date() < reporting_period.start_date
            ]

        events_by_phase, _, _ = adjust_overlapping_cropping_events_by_phase(events_by_phase)
        await _validate_field_events_for_simulations(request, events_by_phase, field.id)
        # SES Events already use regrow names, so translate to regrow while still in the legacy-only event preparations
        await _translate_crop_and_product_names_from_core_to_regrow(
            request, list(itertools.chain.from_iterable(events_by_phase.values()))
        )
        events_by_phase = await gapfilling.gapfill_events_by_phase(
            request=request,
            field=field,
            events_by_phase=events_by_phase,
            raise_if_no_expected_data=scenarios_service_api != ScenariosServiceApi.explore_api,
        )
        # Flatten the events, and for Measure and Inventory API filter out intentions and include M Phase events
        events = events_by_phase.get(PhaseTypes.ENROLMENT, [])
        if scenarios_service_api in [ScenariosServiceApi.measure_api, ScenariosServiceApi.inventory_api]:
            events.extend(events_by_phase.get(PhaseTypes.MONITORING, []))
    return events


async def get_intended_commodity_crops(request: Request, field: Field) -> list[CroppingEvent]:
    """
    Use the SE Facade to fetch CroppingEvents from mrv_values for the Intended Commodity Crops stage.
    We validate that events are CroppingEvent and intended, but do not validate the crop_usage since any cover crops
    wouldn't cause trouble downstream.
    """
    crop_events = (
        await get_entity_events_for_field(
            request,
            field=field,
            phase_type=PhaseTypes.ENROLMENT,
            stage_types=[StageTypes.INTENDED_COMMODITY_CROPS],
            event_creation_specification=EventCreationSpecification(
                error_handling=EventCreatorErrorHandling.SKIP_EVENT
            ),
        )
    )[PhaseTypes.ENROLMENT]
    if not crop_events:
        raise ScenariosServiceIntegrationError(
            f"No intended commodity crops found for field {field.id}",
            ScenariosServiceIntegrationErrorCode.invalid_management_data,
        )
    assert all(ev.is_intended and isinstance(ev, CroppingEvent) for ev in crop_events)
    # Translate crop names since Intended Commodity Crops is still using core names
    await _translate_crop_and_product_names_from_core_to_regrow(request, crop_events)
    return crop_events


async def get_adjusted_cropping_events(request: Request, program_id: int) -> list[AdjustedCroppingEvent]:
    """This allows us to see how cropping events are adjusted by overlap handling before submitting fields for
    simulations."""
    orm_fields = await get_fields_by_program(request=request, program_id=program_id)
    fields = [Field.from_orm(field) for field in orm_fields]
    adjusted_cropping_events = []
    for field in fields:
        events_by_phase = await get_entity_events_for_field(
            request=request,
            field=field,
            stage_types=[
                StageTypes.HISTORICAL_CROP_ROTATION,
                StageTypes.SUMMER_CROPS,
                StageTypes.WINTER_CROPS,
                StageTypes.CROP_EVENTS,
            ],
        )
        _, deleted_events, adjusted_events = adjust_overlapping_cropping_events_by_phase(
            events_by_phase=events_by_phase
        )
        for event in deleted_events:
            adjusted_cropping_events.append(
                AdjustedCroppingEvent(
                    field_id=field.id,
                    crop_type=event.crop_type,
                    crop_usage=event.crop_usage,
                    start_date=event.get_interval_start_or_occurred_at(),
                    end_date=event.get_interval_end_or_occurred_at(),
                    adjustment=CroppingEventAdjustment.DELETED,
                )
            )
        for event in adjusted_events:
            adjusted_cropping_events.append(
                AdjustedCroppingEvent(
                    field_id=field.id,
                    crop_type=event.crop_type,
                    crop_usage=event.crop_usage,
                    start_date=event.get_interval_start_or_occurred_at(),
                    end_date=event.get_interval_end_or_occurred_at(),
                    adjustment=CroppingEventAdjustment.ADJUSTED,
                )
            )
    return adjusted_cropping_events


def adjust_overlapping_cropping_events_by_phase(
    events_by_phase: dict[PhaseTypes, list[EntityEvent]],
) -> tuple[dict[PhaseTypes, list[EntityEvent]], list[EntityEvent], list[EntityEvent]]:
    """
    Adjusts overlapping cropping events according to the rules in: https://regrow.atlassian.net/browse/MRV-4214
    """
    resulting_events, deleted_events, adjusted_events = adjust_overlapping_cropping_events(
        list(itertools.chain.from_iterable(events_by_phase.values()))
    )
    return (
        # This relies on adjust_overlapping_cropping_events mutating events in the inputs
        {phase: [event for event in events if event in resulting_events] for phase, events in events_by_phase.items()},
        deleted_events,
        adjusted_events,
    )


def adjust_overlapping_cropping_events(
    events: list[EntityEvent],
) -> tuple[list[EntityEvent], list[EntityEvent], list[EntityEvent]]:
    """
    Adjusts overlapping cropping events according to the rules in: https://regrow.atlassian.net/browse/MRV-4214
    """
    all_events = list(events)
    cropping_events = [
        event for event in all_events if isinstance(event, FallowPeriod) or (isinstance(event, CroppingEvent))
    ]
    sorted_cropping_events = sorted(cropping_events, key=lambda event: event.interval.start)

    deleted_events = []
    adjusted_events = []
    for i in range(len(sorted_cropping_events) - 1):
        event = sorted_cropping_events[i]
        # if current event is marked for deletion, then use previous event as current event
        previous_event_idx = i - 1
        while previous_event_idx >= 0 and event in deleted_events:
            event = sorted_cropping_events[previous_event_idx]
            previous_event_idx -= 1
        next_event = sorted_cropping_events[i + 1]

        # no overlap
        if next_event.interval.start.date() > event.interval.end.date():
            continue
        # if next event start date is current event end date, then increment next event start date by 1 day
        elif next_event.interval.start.date() == event.interval.end.date():
            next_event.interval.start = next_event.interval.start + timedelta(days=1)
            continue
        else:
            # if next event contained within current event, then delete one event
            # - delete current event if current event IS NOT commodity cropping event and next event IS commodity cropping event
            # - delete next event otherwise
            if next_event.interval.end.date() <= event.interval.end.date():
                if not _is_commodity_cropping_event(event) and _is_commodity_cropping_event(next_event):
                    deleted_events.append(event)
                else:
                    deleted_events.append(next_event)
            # if next event overlaps fallow period, then trim fallow period overlap to 0 days
            elif isinstance(event, FallowPeriod):
                event.interval.end = next_event.interval.start - timedelta(days=1)
                adjusted_events.append(event)
            # if next event overlaps cover cropping event, then trim one event overlap to 14 days max
            # - trim current event if next event IS commodity cropping event
            # - trim next event otherwise
            elif _is_cover_cropping_event(event):
                if _is_commodity_cropping_event(next_event):
                    adjusted_end_date = min(event.interval.end, next_event.interval.start + timedelta(days=13))
                    if adjusted_end_date == event.interval.end:
                        continue
                    event.interval.end = adjusted_end_date
                    for reduction in event.reductions:
                        reduction.occurred_at = adjusted_end_date
                    adjusted_events.append(event)
                else:
                    adjusted_start_date = max(next_event.interval.start, event.interval.end - timedelta(days=13))
                    if adjusted_start_date == next_event.interval.start:
                        continue
                    next_event.interval.start = adjusted_start_date
                    adjusted_events.append(next_event)
            # if next event overlaps commodity cropping event, then trim next event overlap to 0 days
            elif _is_commodity_cropping_event(event):
                next_event.interval.start = event.interval.end + timedelta(days=1)
                adjusted_events.append(next_event)

    return ([event for event in all_events if event not in deleted_events], deleted_events, adjusted_events)


# private


async def _validate_field_events_for_simulations(
    request: Request, events_by_phase: dict[PhaseTypes, list[EntityEvent]], field_id: int
) -> None:
    events = list(itertools.chain.from_iterable(events_by_phase.values()))
    await _validate_cover_crop_type_and_usage_compatible(events)
    _validate_intercropping(events)


def _validate_intercropping(events: list[EntityEvent]) -> None:
    """
    Intercropping is allowed ONLY if the following conditions are satisfied:
    - A crop is planted into a cover crop.
    - The 2nd cropping event is NOT contained within the 1st cropping event.
        At the moment, we do not have GI logic to handle a cropping event
        contained within another cropping event.
    """
    cropping_events = []
    for event in events:
        if isinstance(event, CroppingEvent) and event.is_intended is False:
            cropping_events.append(event)
    cropping_events = sorted(cropping_events, key=lambda event: event.interval.start)
    for i in range(len(cropping_events) - 1):
        event = cropping_events[i]
        next_event = cropping_events[i + 1]
        if next_event.interval.start >= event.interval.end:
            continue
        if next_event.interval.end <= event.interval.end:
            raise ScenariosServiceIntegrationError(
                message="A cropping event contained within another cropping event is not allowed.",
                code=ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )
        if event.crop_usage not in COVER_CROP_USAGES:
            raise ScenariosServiceIntegrationError(
                message=f"A {next_event.crop_usage} crop planted into a {event.crop_usage} crop is not allowed.",
                code=ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )


async def _validate_cover_crop_type_and_usage_compatible(events: list[EntityEvent]) -> None:
    # This is a pretty niche validation, should probably be handled at data collection by the UI.
    for event in events:
        if (
            isinstance(event, CroppingEvent)
            and event.crop_usage == CropUsage.COMMODITY
            and event.crop_type == "basic_cover crop"
        ):
            raise ScenariosServiceIntegrationError(
                f"A commodity cropping event was given the crop type 'basic_cover crop', which is invalid. " f"{event}",
                ScenariosServiceIntegrationErrorCode.invalid_management_data,
            )


def _is_cover_cropping_event(event: EntityEvent) -> bool:
    return isinstance(event, CroppingEvent) and event.crop_usage in COVER_CROP_USAGES


def _is_commodity_cropping_event(event: EntityEvent) -> bool:
    return isinstance(event, CroppingEvent) and event.crop_usage not in COVER_CROP_USAGES


async def _translate_crop_and_product_names_from_core_to_regrow(request: Request, events: list[EntityEvent]) -> None:
    """
    Converts core crop and product names to regrow names for the given events. Also applies any crop mappings that were
    in use for Legacy GI. This will allow EntityEvents pulled from the Facade / MRV Values to continue using the
    integration now that it expects regrow names.
    """
    await _convert_core_crop_names_to_regrow_names(
        request=request, crop_events=[ev for ev in events if isinstance(ev, CroppingEvent)]
    )
    await _convert_core_product_names_to_regrow_names(
        app_events=[ev for ev in events if isinstance(ev, ApplicationEvent)]
    )


async def _convert_core_crop_names_to_regrow_names(request: Request, crop_events: list[CroppingEvent]) -> None:
    for crop_ev in crop_events:
        if crop_ev.crop_type:
            crop_ev.crop_type = await translate_and_maybe_substitute_core_crop_name(
                request, crop_ev.crop_type, crop_ev.interval.start, crop_ev.entity_id
            )


async def _convert_core_product_names_to_regrow_names(app_events: list[ApplicationEvent]) -> None:
    for app_ev in app_events:
        for prod in app_ev.products or []:
            prod.product_name = (
                await defaults_translator.translate_core_product_type_to_regrow_name(prod.product_name)
                if prod.product_name != NO_ADDITIVES_OPTION
                else NO_ADDITIVES_OPTION
            )
        if app_ev.additives:
            # We need to consolidate additives into one source of truth, currently in .products and .additives
            app_ev.additives = ",".join(
                [
                    (
                        await defaults_translator.translate_core_product_type_to_regrow_name(add)
                        if add != NO_ADDITIVES_OPTION
                        else NO_ADDITIVES_OPTION
                    )
                    for add in app_ev.additives.split(",")
                ]
            )


# Crops that don't have a defaults analogue or Crops whose analogue isn't modelable have had modelable crop
# substitutions defined with the Science team (Chris) that have most-similar DNDC parameters.
# We should ALWAYS prefer using the crops in Defaults Service over adding custom Core crops and mapping them.
# Both the key and value are core crop names
CROP_SUBSTITUTIONS_BY_PROGRAM: dict[int, dict[str, str]] = {
    116: {
        "other": "wheat_winter",
        "full_cover": "rye",
    },
    117: {
        "other": "wheat_winter",
        "full_cover": "rye",
    },
    253: {
        "full_cover": "basic_cover crop",
        "chickpeas": "dry_beans",
        "beans": "dry_beans",
    },
    254: {
        "other": "wheat_winter",
        "full_cover": "basic_cover crop",
        "chickpeas": "dry_beans",
        "beans": "dry_beans",
    },
    257: {
        "full_cover": "basic_cover crop",
        "chickpeas": "dry_beans",
        "beans": "dry_beans",
    },
    258: {
        "other": "corn",
        "full_cover": "basic_cover crop",
        "chickpeas": "dry_beans",
        "beans": "dry_beans",
    },
    259: {
        "full_cover": "basic_cover crop",
        "chickpeas": "dry_beans",
        "beans": "dry_beans",
    },
    583: {"chickpeas": "dry_beans"},
    275: {"chickpeas": "dry_beans"},
    1215: {"chickpeas": "dry_beans"},
}
# TODO: consider using RangeDict.
CROP_SUBSTITUTIONS_BY_PROGRAM_AND_PLANTING_MONTH: dict[int, dict[int, dict[str, str]]] = {
    253: {
        2: {"other": "barley"},
        3: {"other": "barley"},
        4: {"other": "barley"},
        5: {"other": "barley"},
        6: {"other": "barley"},
        7: {"other": "barley"},
        8: {"other": "barley"},
        9: {"other": "wheat_winter"},
        10: {"other": "wheat_winter"},
        11: {"other": "wheat_winter"},
        12: {"other": "wheat_winter"},
        1: {"other": "wheat_winter"},
    }
}
COMMON_CROP_SUBSTITUTIONS = {"spring_barley": "barley", "winter_barley": "barley", "grass_pasture": "pasture"}


async def translate_and_maybe_substitute_core_crop_name(
    request: Request,
    core_crop_name: str,
    planting_date: datetime,
    field_id: int,
) -> str:
    program: Programs = await programs_db.get_program_from_field_id(request, field_id)
    # Program-specific substitutions override common substitutions
    crop_substitutions = {
        **COMMON_CROP_SUBSTITUTIONS,
        **CROP_SUBSTITUTIONS_BY_PROGRAM.get(program.id, {}),
        **CROP_SUBSTITUTIONS_BY_PROGRAM_AND_PLANTING_MONTH.get(program.id, {}).get(planting_date.month, {}),
    }
    if core_crop_name in crop_substitutions:
        core_crop_name = crop_substitutions[core_crop_name]
    return await defaults_translator.translate_core_crop_name_to_regrow_name(core_crop_name)
