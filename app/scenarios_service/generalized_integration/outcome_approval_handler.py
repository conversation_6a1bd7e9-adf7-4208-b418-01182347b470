from typing import Union

from fastapi import Request

from programs.enums import AccountingMethod
from reported_outcomes.db import (
    query_outcome_approvals_for_program,
    revoke_program_outcome_approval,
)
from reported_outcomes.enums import OutcomeApprovalStatusType
from reported_outcomes.handle_approvals import handle_revoking_program_outcome_approval
from scenarios_service.enums import ScenariosServiceApi
from scenarios_service.generalized_integration import tasks
from scenarios_service.generalized_integration.biofuels_integration import (
    BiofuelsOutputIntegration,
)
from scenarios_service.generalized_integration.db import (
    get_dndc_task_by_id,
    get_dndc_task_by_ids,
)
from scenarios_service.generalized_integration.inventory_integration import (
    InventoryOutputIntegration,
)
from scenarios_service.generalized_integration.measure_integration import (
    MeasureOutputIntegration,
)
from scenarios_service.generalized_integration.outcome_interfaces import (
    OutputIntegration,
)
from scenarios_service.model import DndcTasks


async def handle_outcome_approval(
    request: Request, program_id: int, task_id: str, is_test_run: bool, is_async: bool
) -> None:
    if is_async:
        tasks.approve_outcomes_for_program.delay(
            program_id=program_id,
            task_id=task_id,
            is_test_run=is_test_run,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )
    else:
        await process_outcome_approval(request, program_id, task_id, is_test_run)


async def revoke_existing_approval(
    request: Request, program_id: int, task_id: str, is_test_run: bool, is_async: bool
) -> None:
    if is_async:
        tasks.revoke_approval_for_program.delay(
            program_id=program_id,
            task_id=task_id,
            is_test_run=is_test_run,
            fs_user_id=request.state.fs_user_id,
            fs_impersonator_user_id=request.state.fs_impersonator_user_id,
        )
    else:
        await process_revoking_outcome(request, program_id, task_id, is_test_run)


async def get_existing_approved_task_for_accounting_method(
    request: Request, task_id: str, program_id: int
) -> DndcTasks | None:
    approvals = await query_outcome_approvals_for_program(request, program_id, OutcomeApprovalStatusType.APPROVED)

    if len(approvals) == 0:
        return None

    approved_tasks = await get_dndc_task_by_ids(request, [approval.task_id for approval in approvals])
    task = await get_dndc_task_by_id(request, task_id)

    tasks_with_existing_accounting_method = [
        approved_task
        for approved_task in approved_tasks
        if approved_task.accounting_method == task.accounting_method and approved_task.id != task.id
    ]

    if len(tasks_with_existing_accounting_method) == 0:
        return None

    if task.accounting_method == AccountingMethod.intervention:
        return next(
            (
                approved_task
                for approved_task in tasks_with_existing_accounting_method
                if approved_task.baseline_method == task.baseline_method and approved_task.id != task.id
            ),
            None,
        )

    return tasks_with_existing_accounting_method[0]


async def process_outcome_approval(request: Request, program_id: int, task_id: str, is_test_run: bool) -> None:
    existing_task = await get_existing_approved_task_for_accounting_method(request, task_id, program_id)
    if existing_task is not None:
        await revoke_program_outcome_approval(request, program_id, existing_task.id)
    outcome_integration = await _obtain_outcome_integration(request, task_id)
    await outcome_integration.approve_outcomes_for_program(program_id, task_id, is_test_run)


async def process_revoking_outcome(request: Request, program_id: int, task_id: str, is_test_run: bool) -> None:
    if not is_test_run:
        return await handle_revoking_program_outcome_approval(request, program_id, task_id)


AnyOutputIntegration = Union[BiofuelsOutputIntegration | InventoryOutputIntegration | MeasureOutputIntegration]


# this is separated out to have a testable unit
async def _obtain_outcome_integration(request: Request, task_id: str) -> AnyOutputIntegration:
    task = await get_dndc_task_by_id(request, task_id)
    return _select_integration_by_ssapi_type(request, task.scenarios_service_api)


def _select_integration_by_ssapi_type(
    request: Request, scenarios_service_api: ScenariosServiceApi
) -> OutputIntegration:
    if scenarios_service_api == ScenariosServiceApi.measure_api:
        return MeasureOutputIntegration(request)
    elif scenarios_service_api == ScenariosServiceApi.inventory_api:
        return InventoryOutputIntegration(request)
    elif scenarios_service_api == ScenariosServiceApi.biofuels_api:
        # to be added as part of MRV-4621
        raise NotImplementedError(
            "Outcome reporting for biofuels programs is not yet provided (see MRV-4621 for status)"
        )
        # return BiofuelsOutputIntegration(request)
    else:
        raise NotImplementedError(
            f"No output integration is available for scenarios service API: {scenarios_service_api}"
        )
