import copy
import json
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from uuid import uuid4 as uuid

from google.protobuf.json_format import MessageToDict

from logger import get_logger
from phases.enums import PhaseTypes, StageTypes
from programs.enums import AccountingMethod
from programs.export.datawarehouse import client, get_program_events
from ses_integration.tests.mocks import (
    application_activity,
    fallow_period,
    harvest_activity,
    harvest_activity1,
    irrigation_activity,
    planting_activity,
    planting_activity1,
    termination_activity,
    termination_activity1,
    tillage_activity,
)

logger = get_logger(__name__)


@patch.object(client, "query_and_wait")
async def test_get_program_events(mock_query, mdl, app_request):
    """
    Test the get_program_events function by returning a list of event entities and determine if the results match
    the expected output.
    """
    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2023, month=1, day=1),
        reporting_period_end_date=datetime(year=2023, month=12, day=31),
        is_single_phase_data_collection=False,
        accounting_method=AccountingMethod.intervention,
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.TILLAGE_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
        ]:
            await mdl.Stage(phase_id=phase.id, type_=stage_type, enabled=True)

    tillage_activity_2023 = copy.deepcopy(tillage_activity).start(
        datetime(year=2023, month=4, day=1, tzinfo=timezone.utc)
    )
    res: list[dict] = []
    pb_events = [
        planting_activity,  # year=2020, month=11, day=1 -> E Only
        termination_activity,  # 2021, 11, 1 -> E Only
        harvest_activity,  # 2021, 11, 1 -> E Only
        planting_activity1,  # 2021, 12, 1 -> E and M
        termination_activity1,  # 2022, 12, 1 -> E and M
        harvest_activity1,  # 2022, 12, 1 -> E and M
        tillage_activity.event_and_context_pb(),  # 2022, 1, 1 -> E and M
        irrigation_activity.event_and_context_pb(),  # 2022, 1, 2 -> E and M
        application_activity.event_and_context_pb(),  # 2022, 1, 3 -> E and M
        tillage_activity_2023.event_and_context_pb(),  # 2023, 4, 1 -> M Only
    ]
    for event, context in pb_events:
        event_dict = {
            "project_id": "11",
            "field_id": "1",
            "phase_type": "Enrollment",
            "event_id": str(uuid()),
            "event_data": json.dumps(MessageToDict(event)),
            "context_data": json.dumps(MessageToDict(context)),
            "deleted_at": None,  # Assuming no events are deleted in this test
        }
        res.append(event_dict)

    # Mock the BigQuery client to return the mock result
    mock_df = Mock()
    mock_df.to_dict.return_value = res
    mock_query_job = Mock()
    mock_query_job.to_dataframe.return_value = mock_df
    mock_query.return_value = mock_query_job

    events = await get_program_events(
        request=app_request, program=program, application_product_regrow_name_to_product_map={}
    )
    mock_query.assert_called()
    assert len(events) == 2
    assert len(events["ENROLLMENT"][StageTypes.CROP_EVENTS]) == 2
    assert len(events["ENROLLMENT"][StageTypes.TILLAGE_EVENTS]) == 2
    assert len(events["ENROLLMENT"][StageTypes.NUTRIENT_EVENTS]) == 2
    assert len(events["ENROLLMENT"][StageTypes.IRRIGATION_EVENTS]) == 2

    assert len(events["MONITORING"][StageTypes.CROP_EVENTS]) == 1
    assert len(events["MONITORING"][StageTypes.TILLAGE_EVENTS]) == 2
    assert len(events["MONITORING"][StageTypes.NUTRIENT_EVENTS]) == 1
    assert len(events["MONITORING"][StageTypes.IRRIGATION_EVENTS]) == 1


@patch.object(client, "query_and_wait")
async def test_get_program_events_disabled_stage(mock_query, mdl, app_request):
    """
    Test the get_program_events function by returning a list of event entities and determine if the results match
    the expected output.
    """
    program = await mdl.Programs(
        reporting_period_start_date=datetime(year=2023, month=1, day=1),
        reporting_period_end_date=datetime(year=2023, month=12, day=31),
        is_single_phase_data_collection=False,
        accounting_method=AccountingMethod.intervention,
    )
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    await mdl.Stage(phase_id=e_phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)
    await mdl.Stage(phase_id=m_phase.id, type_=StageTypes.CROP_EVENTS, enabled=True)

    res: list[dict] = []
    pb_events = [
        planting_activity,  # year=2020, month=11, day=1 -> E Only
        termination_activity,  # 2021, 11, 1 -> E Only
        harvest_activity,  # 2021, 11, 1 -> E Only
        planting_activity1,  # 2021, 12, 1 -> E and M
        termination_activity1,  # 2022, 12, 1 -> E and M
        harvest_activity1,  # 2022, 12, 1 -> E and M
        tillage_activity.event_and_context_pb(),  # 2022, 1, 1 -> E and M
    ]
    for event, context in pb_events:
        event_dict = {
            "project_id": "11",
            "field_id": "1",
            "phase_type": "Enrollment",
            "event_id": str(uuid()),
            "event_data": json.dumps(MessageToDict(event)),
            "context_data": json.dumps(MessageToDict(context)),
            "deleted_at": None,  # Assuming no events are deleted in this test
        }
        res.append(event_dict)

    # Mock the BigQuery client to return the mock result
    mock_df = Mock()
    mock_df.to_dict.return_value = res
    mock_query_job = Mock()
    mock_query_job.to_dataframe.return_value = mock_df
    mock_query.return_value = mock_query_job

    events = await get_program_events(
        request=app_request, program=program, application_product_regrow_name_to_product_map={}
    )
    mock_query.assert_called()
    assert len(events) == 2
    assert list(events["ENROLLMENT"].keys()) == [StageTypes.CROP_EVENTS]
    assert list(events["MONITORING"].keys()) == [StageTypes.CROP_EVENTS]


@patch.object(client, "query_and_wait")
async def test_get_program_events_with_fallow(mock_query, mdl, app_request):
    """
    Test the get_program_events function. By returning
    a list of event entities and determine if the results match
    the expected output.
    """
    # Call the function to test
    program = await mdl.Programs(reporting_period_start_date=datetime(year=2023, month=12, day=1))
    e_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.ENROLMENT, enabled=True)
    m_phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING, enabled=True)
    for phase in [e_phase, m_phase]:
        for stage_type in [
            StageTypes.CROP_EVENTS,
            StageTypes.TILLAGE_EVENTS,
            StageTypes.NUTRIENT_EVENTS,
            StageTypes.IRRIGATION_EVENTS,
        ]:
            await mdl.Stage(phase_id=phase.id, type_=stage_type, enabled=True)

    res: list[dict] = []
    pb_events = [fallow_period.event_and_context_pb()]
    for event, context in pb_events:
        event_dict = {
            "project_id": "11",
            "field_id": "1",
            "phase_type": "Enrollment",
            "event_id": str(uuid()),
            "event_data": json.dumps(MessageToDict(event)),
            "context_data": json.dumps(MessageToDict(context)),
            "deleted_at": None,
        }
        res.append(event_dict)

    # Mock the BigQuery client to return the mock result
    mock_df = Mock()
    mock_df.to_dict.return_value = res
    mock_query_job = Mock()
    mock_query_job.to_dataframe.return_value = mock_df
    mock_query.return_value = mock_query_job

    events = await get_program_events(
        request=app_request, program=program, application_product_regrow_name_to_product_map={}
    )
    mock_query.assert_called()
    assert len(events) == 2  # should have two phases
    assert "ENROLLMENT" in events
    assert len(events.get("ENROLLMENT")) == 4

    found_fallow_crop = False
    for crop_rec in events["ENROLLMENT"][StageTypes.CROP_EVENTS]:
        if "Crop Type" in crop_rec:
            assert crop_rec["Crop Type"] == "Fallow"
            assert crop_rec["Crop Year"] == "No Commodity Feb 2022 - Feb 2023"
            assert crop_rec["Crop Termination Year"] == "2023"
            found_fallow_crop = True
    assert found_fallow_crop
