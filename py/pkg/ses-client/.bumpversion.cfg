[bumpversion]
current_version = 1.2.10
commit = True
tag = True
tag_name = py-ses-client-v{new_version}
commit_args = --no-verify
message = chore(py-ses-client): bump version from {current_version} to {new_version}
parse = (?P<major>\d+)\.(?P<minor>\d+)\.(?P<patch>\d+)(\.(?P<dev>dev)(?P<devnum>\d+))?
serialize =
	{major}.{minor}.{patch}.dev{devnum}
	{major}.{minor}.{patch}

[bumpversion:part:dev]
optional_value = release
values =
	release
	dev

[bumpversion:part:devnum]
first_value = 1

[bumpversion:file:pyproject.toml]
search = version = "{current_version}"
replace = version = "{new_version}"
