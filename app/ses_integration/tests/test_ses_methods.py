from datetime import datetime, timezone
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch
from uuid import UUID, uuid4

import pytest
from mocks import mock_cropping_sequences, mock_field_practice_events
from ses_client.application import ApplicationActivity
from ses_client.event import StructuredEvent

from entity_events.events.cropping_event import CroppingEvent
from entity_events.events.entity_event import EntityEvent
from entity_events.events.enums import EntityEventType
from fields.enums import FieldStatus
from phases.enums import PhaseTypes, StageTypes
from projects.model import ProjectPermissions, ProjectPhaseCompletion
from root_crud import delete, update
from ses_integration.constants import ENTITY_EVENT_TYPE_TO_STAGE_TYPE
from ses_integration.methods import (
    _event_stage_cache,
    get_locked_event_ids_for_user,
    get_ses_event_types_to_query,
    get_stage_types_for_event_ids,
    parse_ses_events_to_entity_events,
)
from ses_integration.model import FieldEventAssociation, PhaseEventAssociation
from ses_integration.schema import EventIdWithStageType


async def test_parse_ses_events_to_entity_events(mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)

    entity_events = parse_ses_events_to_entity_events(field, mock_cropping_sequences, mock_field_practice_events)
    assert len(entity_events) == len(mock_field_practice_events) + len(mock_cropping_sequences)
    assert all(entity_event.entity_id == field.id for entity_event in entity_events)
    assert all(isinstance(entity_event, EntityEvent) for entity_event in entity_events)


async def test_parse_ses_events_to_entity_events_cropping_events(mdl):
    program = await mdl.Programs()
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id, deleted_at=None, status=FieldStatus.enrolled)
    entity_events = parse_ses_events_to_entity_events(field, mock_cropping_sequences, [])

    assert len(entity_events) == len(mock_cropping_sequences)
    assert all(isinstance(entity_event, CroppingEvent) for entity_event in entity_events)


async def test_get_ses_event_types_to_query_for_stages(mdl):
    stages = [
        await mdl.Stage(type_=StageTypes.CROP_EVENTS),
        await mdl.Stage(type_=StageTypes.TILLAGE_EVENTS),
        await mdl.Stage(type_=StageTypes.NUTRIENT_EVENTS),
    ]
    result = get_ses_event_types_to_query(stages=stages)
    assert result == [
        StructuredEvent.TYPE_TILLAGE_ACTIVITY,
        StructuredEvent.TYPE_APPLICATION_ACTIVITY,
    ]


async def test_get_ses_event_types_to_query_for_stage_type(mdl):
    result = get_ses_event_types_to_query(stages=[], stage_type=StageTypes.TILLAGE_EVENTS)
    assert result == [StructuredEvent.TYPE_TILLAGE_ACTIVITY]


async def test_get_ses_event_types_to_query_for_stage_type_empty():
    result = get_ses_event_types_to_query(stages=[], stage_type=StageTypes.CONFIRM_HISTORY)
    assert result == []


# Predictable event ID for testing
TEST_NUTRIENT_EVENT_ID = UUID("11111111-**************-************")

# Create SES activity for testing
NUTRIENT_APPLICATION_ACTIVITY = (
    ApplicationActivity(event_id=str(TEST_NUTRIENT_EVENT_ID), user_id="1")
    .start(datetime(year=2021, month=6, day=1, tzinfo=timezone.utc))
    .event_and_context_pb()
)


async def mock_fetch_events_func(event_ids):
    """Mock SES client fetch_events to return our test event"""
    events = []
    for eid in event_ids:
        if eid == str(TEST_NUTRIENT_EVENT_ID):
            events.append(NUTRIENT_APPLICATION_ACTIVITY[0])
    return events


@pytest.mark.parametrize(
    "past_phase_completed, acting_phase_type, acting_phase_completed, stage_required_in_acting, stage_required_in_past, expected_locked",
    [
        # Test scenarios with acting phase completion status
        # Format: (Past complete, Acting type, Acting complete, Acting required, Past required, Expected)
        # Scenario 1: Event locked by past program (stage required in past)
        (
            False,
            PhaseTypes.ENROLMENT,
            False,
            False,
            True,
            False,
        ),  # Past incomplete -> no lock
        (
            True,
            PhaseTypes.ENROLMENT,
            False,
            False,
            True,
            True,
        ),  # Past complete, past required -> LOCK
        (
            True,
            PhaseTypes.MONITORING,
            False,
            False,
            True,
            True,
        ),  # Past complete, past required -> LOCK
        # Scenario 2: Event locked by current/acting program (stage required in current)
        (
            False,
            PhaseTypes.ENROLMENT,
            False,
            True,
            False,
            False,
        ),  # Acting incomplete -> no lock
        (
            False,
            PhaseTypes.ENROLMENT,
            True,
            True,
            False,
            True,
        ),  # Acting complete, acting required -> LOCK
        (
            False,
            PhaseTypes.MONITORING,
            True,
            True,
            False,
            True,
        ),  # Acting complete, acting required -> LOCK
        # Scenario 3: Both programs have requirements
        (
            True,
            PhaseTypes.ENROLMENT,
            True,
            True,
            True,
            True,
        ),  # Both complete, both required -> LOCK
        (
            False,
            PhaseTypes.ENROLMENT,
            True,
            True,
            True,
            True,
        ),  # Only acting complete -> LOCK (current program)
        (
            True,
            PhaseTypes.ENROLMENT,
            False,
            True,
            True,
            True,
        ),  # Only past complete -> LOCK (past program)
        (
            False,
            PhaseTypes.ENROLMENT,
            False,
            True,
            True,
            False,
        ),  # Neither complete -> no lock
        # Scenario 4: No requirements
        (
            True,
            PhaseTypes.ENROLMENT,
            True,
            False,
            False,
            False,
        ),  # Both complete, no requirements -> no lock
        (
            True,
            PhaseTypes.MONITORING,
            True,
            False,
            False,
            False,
        ),  # Both complete, no requirements -> no lock
    ],
)
async def test_get_locked_event_ids_for_user_phase_logic_with_acting_completion(
    past_phase_completed,
    acting_phase_type,
    acting_phase_completed,
    stage_required_in_acting,
    stage_required_in_past,
    expected_locked,
    app_request,
    mdl,
):
    """
    Test locking scenarios including acting phase completion status.
    Tests both current program locking and past program locking.
    """
    user_id = 1
    event_id = TEST_NUTRIENT_EVENT_ID

    # Create past program with phase matching acting phase type
    past_program = await mdl.Programs()
    past_phase = await mdl.Phases(program_id=past_program.id, type_=acting_phase_type)
    await mdl.Stage(
        phase_id=past_phase.id,
        type_=StageTypes.NUTRIENT_EVENTS,
        required=stage_required_in_past,
    )

    # Create acting program and phase
    acting_program = await mdl.Programs(previous_program_id=past_program.id)
    acting_phase = await mdl.Phases(program_id=acting_program.id, type_=acting_phase_type)
    await mdl.Stage(
        phase_id=acting_phase.id,
        type_=StageTypes.NUTRIENT_EVENTS,
        required=stage_required_in_acting,
    )

    # Create projects and field
    past_project = await mdl.Projects(program_id=past_program.id)
    acting_project = await mdl.Projects(program_id=acting_program.id)
    await mdl.ProjectPermissions(project=past_project.id, user=user_id)
    await mdl.ProjectPermissions(project=acting_project.id, user=user_id)

    # Field belongs to past project (so event can be locked by past program)
    field = await mdl.Fields(parent_project_id=past_project.id, md5="test-md5")

    # Set up phase completions for past project
    await mdl.ProjectPhaseCompletion(
        project_id=past_project.id,
        phase_id=past_phase.id,
        is_completed=past_phase_completed,
        allow_post_close_edit=False,
    )

    # For acting phase completion, we need the acting project to have completed the acting phase
    # But the event is still associated with past project
    await mdl.ProjectPhaseCompletion(
        project_id=past_project.id,
        phase_id=acting_phase.id,
        is_completed=acting_phase_completed,
        allow_post_close_edit=False,
    )

    # Create event association with the past project
    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=past_project.id,
        program_id=past_program.id,
    )

    # Associate event with both phases
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=past_phase.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=acting_phase.id,
        revision=1,
    )

    # Create mock SES client
    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    # Test the locking logic
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=acting_phase.id,
    )

    # Verify result
    if expected_locked:
        assert event_id in result, f"Event should be locked but wasn't. Result: {result}"
    else:
        assert event_id not in result, f"Event should not be locked but was. Result: {result}"


@patch("ses_integration.methods.event_type")
async def test_get_locked_event_ids_for_user_different_users(mock_event_type, app_request, mdl):
    """
    The same event can be locked for one user and not locked for another if they both have access to it.
    User 1 should see the event as locked because of its completed phase, while User 2 should not see the event at all.
    """
    user1_id = 1
    user2_id = 2
    # Users have the same field md5 and SES event id
    field_md5 = "test_md5"
    event_id = uuid4()

    # Create a program series: first_program -> program1
    # This ensures program1 has previous program stages to check against
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.CROP_EVENTS)

    # User 1 setup
    program1 = await mdl.Programs(previous_program_id=first_program.id)
    project1 = await mdl.Projects(program_id=program1.id)
    field1 = await mdl.Fields(parent_project_id=project1.id, md5=field_md5)
    phase1_id = (await mdl.Phases(program_id=program1.id, type_=PhaseTypes.MONITORING)).id
    await mdl.ProjectPermissions(project=project1.id, user=user1_id)
    field_event_association1 = await mdl.FieldEventAssociation(
        field_id=field1.id,
        ses_event_id=str(event_id),
        field_md5=field1.md5,
        project_id=project1.id,
        program_id=program1.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association1.id,
        phase_id=phase1_id,
        revision=1,
    )
    await mdl.ProjectPhaseCompletion(
        project_id=project1.id,
        phase_id=phase1_id,
        is_completed=True,
        allow_post_close_edit=False,
    )

    # User 2 setup - separate program in the same series
    program2 = await mdl.Programs(previous_program_id=first_program.id)
    project2 = await mdl.Projects(program_id=program2.id)
    field2 = await mdl.Fields(parent_project_id=project2.id, md5=field_md5)
    phase2_id = (await mdl.Phases(program_id=program2.id, type_=PhaseTypes.MONITORING)).id
    await mdl.ProjectPermissions(project=project2.id, user=user2_id)
    field_event_association2 = await mdl.FieldEventAssociation(
        field_id=field2.id,
        ses_event_id=str(event_id),
        field_md5=field2.md5,
        project_id=project2.id,
        program_id=program2.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association2.id,
        phase_id=phase2_id,
        revision=1,
    )
    await mdl.ProjectPhaseCompletion(project_id=project2.id, phase_id=phase2_id, is_completed=False)

    # Mock SES client to return events and event_type to return harvest activity
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            events.append(mock_event)
        return events

    # Mock event_type to return harvest activity for all events
    mock_event_type.return_value = StructuredEvent.TYPE_HARVEST_ACTIVITY

    # Create mock client
    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result_user1 = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user1_id,
        acting_phase_id=phase1_id,
    )
    result_user2 = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user2_id,
        acting_phase_id=phase2_id,
    )

    assert event_id in result_user1
    assert event_id not in result_user2


@patch("ses_integration.methods.event_type")
async def test_get_locked_event_ids_for_user_with_deleted_entities(mock_event_type, app_request, mdl):
    """Deleting any of the entities that connect events to phase completion should unlock the event."""
    user_id = 1
    # Create a program series to ensure there are previous program stages to check against
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.CROP_EVENTS)

    program = await mdl.Programs(previous_program_id=first_program.id)
    project = await mdl.Projects(program_id=program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    event_id = uuid4()
    phase_id = (await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)).id

    # Create associations
    project_permission = await mdl.ProjectPermissions(project=project.id, user=user_id, deleted_at=None)
    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=program.id,
        deleted_at=None,
    )
    phase_event_association = await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=phase_id,
        revision=1,
        deleted_at=None,
    )
    project_phase_completion = await mdl.ProjectPhaseCompletion(
        project_id=project.id, phase_id=phase_id, is_completed=True, deleted_at=None
    )

    # Mock SES client to return events and event_type to return harvest activity
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            events.append(mock_event)
        return events

    # Mock event_type to return harvest activity for all events
    mock_event_type.return_value = StructuredEvent.TYPE_HARVEST_ACTIVITY

    # Create mock client
    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    # Event is locked initially
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=phase_id,
    )
    assert event_id in result

    # Delete ProjectPhaseCompletion, verify event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=ProjectPhaseCompletion,
        ids=[project_phase_completion.id],
        id_field=ProjectPhaseCompletion.id,
    )
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=phase_id,
    )
    assert event_id not in result

    # Recreate ProjectPhaseCompletion
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase_id, is_completed=True, deleted_at=None)

    # Delete PhaseEventAssociation and verify the event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=PhaseEventAssociation,
        ids=[phase_event_association.id],
        id_field=PhaseEventAssociation.id,
    )
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=phase_id,
    )
    assert event_id not in result

    phase_event_association.deleted_at = None
    await update.update(
        request=app_request,
        type=PhaseEventAssociation,
        instances=[phase_event_association],
    )

    # Delete FieldEventAssociation, verify the event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=FieldEventAssociation,
        ids=[field_event_association.id],
        id_field=FieldEventAssociation.id,
    )
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=phase_id,
    )
    assert event_id not in result

    # Recreate FieldEventAssociation
    field_event_association.deleted_at = None
    await update.update(
        request=app_request,
        type=FieldEventAssociation,
        instances=[field_event_association],
    )

    # Delete ProjectPermissions and verify the event is no longer locked
    await delete.soft(
        request=app_request,
        orm_type=ProjectPermissions,
        ids=[project_permission.id],
        id_field=ProjectPermissions.id,
    )
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=phase_id,
    )
    assert event_id not in result


@patch("ses_integration.methods.event_type")
async def test_get_locked_event_ids_for_user_not_locked_when_not_required_in_previous_program(
    mock_event_type, app_request, mdl
):
    event_id = uuid4()

    # Create a program series: first_program -> current_program
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    # Create a required CROP_EVENTS stage in the first program
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.CROP_EVENTS, required=True)

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id

    project = await mdl.Projects(program_id=first_program.id)
    permission = await mdl.ProjectPermissions(project=project.id)
    field = await mdl.Fields(parent_project_id=project.id, md5="test-md5")

    await mdl.ProjectPhaseCompletion(
        project_id=project.id,
        phase_id=first_phase_id,
        is_completed=True,
        allow_post_close_edit=False,
    )

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=first_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=first_phase_id,
        revision=1,
    )

    # Mock SES client to return events
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            events.append(mock_event)
        return events

    # Mock event_type to return TILLAGE_ACTIVITY (maps to TILLAGE_EVENTS stage type)
    # This event type was NOT required in the previous program (only CROP_EVENTS was required)
    mock_event_type.return_value = StructuredEvent.TYPE_TILLAGE_ACTIVITY

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=current_phase_id,
    )
    # Event should NOT be locked because TILLAGE_EVENTS stage type was not required in previous program
    assert event_id not in result


@pytest.mark.asyncio
async def test_get_stage_types_for_event_ids_basic_functionality():
    """Test basic functionality of get_stage_types_for_event_ids with various event types."""

    # Clear cache before test
    _event_stage_cache.clear()

    # Create mock events
    mock_tillage_event = Mock()
    mock_tillage_event.id = "event_1"
    mock_tillage_event.event_type = StructuredEvent.TYPE_TILLAGE_ACTIVITY

    mock_nutrient_event = Mock()
    mock_nutrient_event.id = "event_2"
    mock_nutrient_event.event_type = StructuredEvent.TYPE_APPLICATION_ACTIVITY

    mock_unknown_event = Mock()
    mock_unknown_event.id = "event_3"
    mock_unknown_event.event_type = "UNKNOWN_TYPE"

    # Mock SES client - single batch call
    mock_ses_client = Mock()
    mock_ses_client.fetch_events = AsyncMock(return_value=[mock_tillage_event, mock_nutrient_event, mock_unknown_event])

    # Mock event_type function
    with patch("ses_integration.methods.event_type") as mock_event_type:
        mock_event_type.side_effect = lambda event: event.event_type

        result = await get_stage_types_for_event_ids(mock_ses_client, ["event_1", "event_2", "event_3"])

    # Verify results
    assert result["event_1"] == StageTypes.TILLAGE_EVENTS
    assert result["event_2"] == StageTypes.NUTRIENT_EVENTS
    assert result["event_3"] is None  # Unknown event type

    # Verify SES client was called once with all event IDs (batch call)
    mock_ses_client.fetch_events.assert_called_once_with(event_ids=["event_1", "event_2", "event_3"])

    # Verify cache was populated
    assert _event_stage_cache["event_1"] == StageTypes.TILLAGE_EVENTS
    assert _event_stage_cache["event_2"] == StageTypes.NUTRIENT_EVENTS
    assert _event_stage_cache["event_3"] is None


@pytest.mark.asyncio
async def test_get_stage_types_for_event_ids_partial_caching():
    """Test that only missing event IDs are fetched from SES when some are cached."""

    # Clear cache and pre-populate with some events
    _event_stage_cache.clear()
    _event_stage_cache["cached_event_1"] = StageTypes.IRRIGATION_EVENTS
    _event_stage_cache["cached_event_2"] = StageTypes.CROP_EVENTS

    # Create mock for only the missing event
    mock_new_event = Mock()
    mock_new_event.id = "new_event"
    mock_new_event.event_type = StructuredEvent.TYPE_TILLAGE_ACTIVITY

    # Mock SES client - should only be called for missing events
    mock_ses_client = Mock()
    mock_ses_client.fetch_events = AsyncMock(return_value=[mock_new_event])

    with patch("ses_integration.methods.event_type") as mock_event_type:
        mock_event_type.return_value = StructuredEvent.TYPE_TILLAGE_ACTIVITY

        result = await get_stage_types_for_event_ids(mock_ses_client, ["cached_event_1", "cached_event_2", "new_event"])

    # Verify all results are returned correctly
    assert result["cached_event_1"] == StageTypes.IRRIGATION_EVENTS  # From cache
    assert result["cached_event_2"] == StageTypes.CROP_EVENTS  # From cache
    assert result["new_event"] == StageTypes.TILLAGE_EVENTS  # Newly fetched

    # Verify SES client was called only for the missing event
    mock_ses_client.fetch_events.assert_called_once_with(event_ids=["new_event"])


@pytest.mark.asyncio
async def test_get_stage_types_for_event_ids_full_cache_hit():
    """Test that no SES calls are made when all events are cached."""

    # Clear cache and pre-populate with all requested events
    _event_stage_cache.clear()
    _event_stage_cache["event_1"] = StageTypes.TILLAGE_EVENTS
    _event_stage_cache["event_2"] = StageTypes.NUTRIENT_EVENTS
    _event_stage_cache["event_3"] = None  # Cached as None

    # Mock SES client - should NOT be called
    mock_ses_client = Mock()
    mock_ses_client.fetch_events = AsyncMock()

    result = await get_stage_types_for_event_ids(mock_ses_client, ["event_1", "event_2", "event_3"])

    # Verify all results are returned from cache
    assert result["event_1"] == StageTypes.TILLAGE_EVENTS
    assert result["event_2"] == StageTypes.NUTRIENT_EVENTS
    assert result["event_3"] is None

    # Verify SES client was NOT called
    mock_ses_client.fetch_events.assert_not_called()


@pytest.mark.asyncio
async def test_get_stage_types_for_event_ids_handles_missing_events():
    """Test that events not returned by SES are cached as None."""

    # Clear cache
    _event_stage_cache.clear()

    # Create mock for only one event (missing_event_2 won't be in the response)
    mock_event = Mock()
    mock_event.id = "existing_event"
    mock_event.event_type = StructuredEvent.TYPE_TILLAGE_ACTIVITY

    # Mock SES client - returns only one of the two requested events
    mock_ses_client = Mock()
    mock_ses_client.fetch_events = AsyncMock(return_value=[mock_event])

    with patch("ses_integration.methods.event_type") as mock_event_type:
        mock_event_type.return_value = StructuredEvent.TYPE_TILLAGE_ACTIVITY

        result = await get_stage_types_for_event_ids(mock_ses_client, ["existing_event", "missing_event"])

    # Verify results
    assert result["existing_event"] == StageTypes.TILLAGE_EVENTS
    assert result["missing_event"] is None  # Should be None for missing event

    # Verify both events are cached (including missing as None)
    assert _event_stage_cache["existing_event"] == StageTypes.TILLAGE_EVENTS
    assert _event_stage_cache["missing_event"] is None


async def test_get_locked_event_ids_current_program_required_stage(app_request, mdl):
    """Test that events are locked when locked by current program AND stage is required in current program."""
    user_id = 1
    event_id = uuid4()

    # Create a program series: first_program -> current_program
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    # No required stages in first program
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=False)

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    # Current program has required CROP_EVENTS stage
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.CROP_EVENTS, required=True)

    project = await mdl.Projects(program_id=current_program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=current_phase_id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=current_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=current_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf event that event_type() function can process
            # Event maps to CROP_EVENTS stage which IS required in current program (harvest activity)
            mock_event.HasField = Mock(side_effect=lambda field: field == "harvest_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=current_phase_id,
    )

    # Event should be locked because it's locked by current program AND stage is required in current program
    assert event_id in result


async def test_get_locked_event_ids_current_vs_previous_program_logic(app_request, mdl):
    """Test interaction between current and previous program locking logic."""
    user_id = 1
    event_id = uuid4()

    # Create a program series: first_program -> current_program
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    # Previous program has required CROP_EVENTS stage
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.CROP_EVENTS, required=True)

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    # Current program does NOT have required CROP_EVENTS stage
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    # Event is locked by FIRST program
    first_project = await mdl.Projects(program_id=first_program.id)
    first_field = await mdl.Fields(parent_project_id=first_project.id)
    await mdl.ProjectPermissions(project=first_project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=first_project.id, phase_id=first_phase_id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=first_field.id,
        ses_event_id=str(event_id),
        field_md5=first_field.md5,
        project_id=first_project.id,
        program_id=first_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=first_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf event that event_type() function can process
            # Event maps to CROP_EVENTS stage which WAS required in previous program (harvest activity)
            mock_event.HasField = Mock(side_effect=lambda field: field == "harvest_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=current_phase_id,
    )

    # Event should be locked because stage was required in previous program
    assert event_id in result


async def test_get_locked_event_ids_stage_required_current_not_previous(app_request, mdl):
    """Test event locked by current program where stage is required in current but not previous."""
    user_id = 1
    event_id = uuid4()

    # Create a program series: first_program -> current_program
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    # Previous program does NOT have required CROP_EVENTS stage
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    # Current program HAS required CROP_EVENTS stage
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.CROP_EVENTS, required=True)

    # Event is locked by CURRENT program
    project = await mdl.Projects(program_id=current_program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=current_phase_id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=current_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=current_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf event that event_type() function can process
            # Event maps to CROP_EVENTS stage which IS required in current program (harvest activity)
            mock_event.HasField = Mock(side_effect=lambda field: field == "harvest_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=current_phase_id,
    )

    # Event should be locked because it's locked by current program AND stage is required in current program
    assert event_id in result


async def test_get_locked_event_ids_stage_not_required_anywhere(app_request, mdl):
    """Test event NOT locked when stage is not required in current or previous programs."""
    user_id = 1
    event_id = uuid4()

    # Create a program series: first_program -> current_program
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    # Previous program has different required stage
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    # Current program also has different required stage
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.NUTRIENT_EVENTS, required=True)

    # Event is locked by current program
    project = await mdl.Projects(program_id=current_program.id)
    field = await mdl.Fields(parent_project_id=project.id)
    await mdl.ProjectPermissions(project=project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=current_phase_id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=current_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=current_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf event that event_type() function can process
            # Event maps to CROP_EVENTS stage which is NOT required in any program (harvest activity)
            mock_event.HasField = Mock(side_effect=lambda field: field == "harvest_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=current_phase_id,
    )

    # Event should NOT be locked because stage is not required in current or previous programs
    assert event_id not in result


async def test_get_locked_event_ids_complex_program_series(app_request, mdl):
    """Test locking logic with complex program series (multiple previous programs)."""
    event_id = uuid4()

    # Create a program series: prog1 -> prog2 -> prog3 -> current_program
    prog1 = await mdl.Programs()
    prog1_phase_id = (await mdl.Phases(program_id=prog1.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=prog1_phase_id, type_=StageTypes.CROP_EVENTS, required=True)

    prog2 = await mdl.Programs(previous_program_id=prog1.id)
    prog2_phase_id = (await mdl.Phases(program_id=prog2.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=prog2_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    prog3 = await mdl.Programs(previous_program_id=prog2.id)
    prog3_phase_id = (await mdl.Phases(program_id=prog3.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=prog3_phase_id, type_=StageTypes.NUTRIENT_EVENTS, required=True)

    current_program = await mdl.Programs(previous_program_id=prog3.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.IRRIGATION_EVENTS, required=True)

    # Event is locked by prog2 (middle of the series)
    project = await mdl.Projects(program_id=prog2.id)
    field = await mdl.Fields(parent_project_id=project.id)
    permission = await mdl.ProjectPermissions(project=project.id)
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=prog2_phase_id, is_completed=True)

    field_event_association = await mdl.FieldEventAssociation(
        field_id=field.id,
        ses_event_id=str(event_id),
        field_md5=field.md5,
        project_id=project.id,
        program_id=prog2.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association.id,
        phase_id=prog2_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf event that event_type() function can process
            # Event maps to TILLAGE_EVENTS stage which WAS required in prog2 (a previous program)
            mock_event.HasField = Mock(side_effect=lambda field: field == "tillage_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=current_phase_id,
    )

    # Event should be locked because TILLAGE_EVENTS stage was required in prog2 (a previous program)
    assert event_id in result


async def test_get_locked_event_ids_multiple_events_mixed_logic(app_request, mdl, faker):
    """Test multiple events with different locking scenarios in one call."""
    user_id = faker.random_number(3)
    event_id1 = uuid4()  # Should be locked (current program + required)
    event_id2 = uuid4()  # Should be locked (previous program required)
    event_id3 = uuid4()  # Should NOT be locked (not required anywhere)

    # Create program series
    first_program = await mdl.Programs()
    first_phase_id = (await mdl.Phases(program_id=first_program.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=first_phase_id, type_=StageTypes.TILLAGE_EVENTS, required=True)  # For event_id2

    current_program = await mdl.Programs(previous_program_id=first_program.id)
    current_phase_id = (await mdl.Phases(program_id=current_program.id, type_=PhaseTypes.MONITORING)).id
    await mdl.Stage(phase_id=current_phase_id, type_=StageTypes.CROP_EVENTS, required=True)  # For event_id1

    # Setup projects and fields
    current_project = await mdl.Projects(program_id=current_program.id)
    current_field = await mdl.Fields(parent_project_id=current_project.id)
    await mdl.ProjectPermissions(project=current_project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=current_project.id, phase_id=current_phase_id, is_completed=True)

    first_project = await mdl.Projects(program_id=first_program.id)
    first_field = await mdl.Fields(parent_project_id=first_project.id)
    await mdl.ProjectPermissions(project=first_project.id, user=user_id)
    await mdl.ProjectPhaseCompletion(project_id=first_project.id, phase_id=first_phase_id, is_completed=True)

    # Create field event associations
    # Event 1: locked by current program
    field_event_association1 = await mdl.FieldEventAssociation(
        field_id=current_field.id,
        ses_event_id=str(event_id1),
        field_md5=current_field.md5,
        project_id=current_project.id,
        program_id=current_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association1.id,
        phase_id=current_phase_id,
        revision=1,
    )

    # Event 2: locked by previous program
    field_event_association2 = await mdl.FieldEventAssociation(
        field_id=first_field.id,
        ses_event_id=str(event_id2),
        field_md5=first_field.md5,
        project_id=first_project.id,
        program_id=first_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association2.id,
        phase_id=first_phase_id,
        revision=1,
    )

    # Event 3: locked by current program but stage not required anywhere
    field_event_association3 = await mdl.FieldEventAssociation(
        field_id=current_field.id,
        ses_event_id=str(event_id3),
        field_md5=current_field.md5,
        project_id=current_project.id,
        program_id=current_program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_association3.id,
        phase_id=current_phase_id,
        revision=1,
    )

    # Mock SES client with events that have proper protobuf-like structure
    async def mock_fetch_events_func(event_ids):
        events = []
        for eid in event_ids:
            mock_event = Mock()
            mock_event.id = str(eid)
            # Create mock protobuf events that event_type() function can process
            # Map events to different stage types
            if eid == str(event_id1):
                # Maps to CROP_EVENTS (required in current) - harvest activity
                mock_event.HasField = Mock(side_effect=lambda field: field == "harvest_activity")
            elif eid == str(event_id2):
                # Maps to TILLAGE_EVENTS (required in previous) - tillage activity
                mock_event.HasField = Mock(side_effect=lambda field: field == "tillage_activity")
            elif eid == str(event_id3):
                # Maps to NUTRIENT_EVENTS (not required anywhere) - application activity
                mock_event.HasField = Mock(side_effect=lambda field: field == "application_activity")
            events.append(mock_event)
        return events

    mock_client = Mock()
    mock_client.fetch_events = mock_fetch_events_func

    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=user_id,
        acting_phase_id=current_phase_id,
    )

    # Event 1 should be locked (current program + required stage)
    assert event_id1 in result
    # Event 2 should be locked (required in previous program)
    assert event_id2 in result
    # Event 3 should NOT be locked (stage not required anywhere)
    assert event_id3 not in result


@patch("ses_integration.methods.event_type")
async def test_get_locked_event_ids_with_prefetched_mapping_full(mock_event_type, app_request, mdl):
    """Test that passing a complete event_stage_mapping skips SES calls entirely."""
    event_id1 = uuid4()
    event_id2 = uuid4()

    # Create program series
    program1 = await mdl.Programs()
    program2 = await mdl.Programs(previous_program_id=program1.id)

    # Create phases with different types
    phase1 = await mdl.Phases(program_id=program1.id, type_=PhaseTypes.MONITORING)
    phase2 = await mdl.Phases(program_id=program2.id, type_=PhaseTypes.MONITORING)

    # Create required stages
    await mdl.Stage(phase_id=phase1.id, type_=StageTypes.TILLAGE_EVENTS, required=True, enabled=True)
    await mdl.Stage(
        phase_id=phase2.id,
        type_=StageTypes.NUTRIENT_EVENTS,
        required=True,
        enabled=True,
    )

    # Create project and permissions
    project = await mdl.Projects(program_id=program2.id)
    permission = await mdl.ProjectPermissions(project=project.id)

    # Create field and event associations
    field = await mdl.Fields()
    field_event_assoc1 = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id1),
        field_md5=field.md5,
        field_id=field.id,
        project_id=project.id,
        program_id=program2.id,
    )
    field_event_assoc2 = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id2),
        field_md5=field.md5,
        field_id=field.id,
        project_id=project.id,
        program_id=program2.id,
    )

    # Create phase event associations
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_assoc1.id,
        phase_id=phase2.id,
        revision=1,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_assoc2.id,
        phase_id=phase2.id,
        revision=1,
    )

    # Complete the phase
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase2.id, is_completed=True)

    # Pre-create the events list
    events = [
        EventIdWithStageType(event_id=event_id1, stage_type=StageTypes.TILLAGE_EVENTS),  # Was required in program1
        EventIdWithStageType(event_id=event_id2, stage_type=StageTypes.NUTRIENT_EVENTS),  # Required in current program
    ]

    # Create mock SES client - it should NOT be called
    mock_client = Mock()
    mock_client.fetch_events = Mock(side_effect=Exception("SES client should not be called!"))

    # Call with pre-fetched events
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=phase2.id,
        events=events,
    )

    # Event 1 should be locked (was required in previous program)
    assert event_id1 in result
    # Event 2 should be locked (required in current program)
    assert event_id2 in result
    # Verify SES was never called
    mock_client.fetch_events.assert_not_called()


@patch("ses_integration.methods.event_type")
async def test_get_locked_event_ids_with_partial_prefetched_mapping(mock_event_type, app_request, mdl):
    """Test that when events list is provided, only events in that list are considered for locking."""
    event_id1 = uuid4()
    event_id2 = uuid4()
    event_id3 = uuid4()

    # Create program
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True, enabled=True)

    # Create project and permissions
    project = await mdl.Projects(program_id=program.id)
    permission = await mdl.ProjectPermissions(project=project.id)

    # Create field and event associations for all three events
    field = await mdl.Fields()
    for event_id in [event_id1, event_id2, event_id3]:
        field_event_assoc = await mdl.FieldEventAssociation(
            ses_event_id=str(event_id),
            field_md5=field.md5,
            field_id=field.id,
            project_id=project.id,
            program_id=program.id,
        )
        await mdl.PhaseEventAssociation(
            field_event_association_id=field_event_assoc.id,
            phase_id=phase.id,
            revision=1,
        )

    # Complete the phase
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    # Provide events for only two events (event_id3 is not included)
    events = [
        EventIdWithStageType(event_id=event_id1, stage_type=StageTypes.TILLAGE_EVENTS),
        EventIdWithStageType(event_id=event_id2, stage_type=StageTypes.NUTRIENT_EVENTS),
        # event_id3 is intentionally NOT included in the events list
    ]

    # Create mock SES client that should NOT be called since we provide the events list
    mock_client = Mock()
    mock_client.fetch_events = Mock(side_effect=Exception("SES should not be called when events are provided"))

    # Call with events list - only these events should be considered for locking
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=phase.id,
        events=events,
    )

    # Only events from the provided list should be considered:
    # event_id1 should be locked (TILLAGE_EVENTS is required)
    assert event_id1 in result
    # event_id2 should not be locked (NUTRIENT_EVENTS is not required)
    assert event_id2 not in result
    # event_id3 should not be in result because it wasn't in the provided events list
    assert event_id3 not in result

    # Verify SES was never called since we provided the events list
    mock_client.fetch_events.assert_not_called()


async def test_get_locked_event_ids_only_returns_events_from_provided_list(app_request, mdl):
    """Test that when events list is provided, the function only returns locked events from that list."""
    event_id1 = uuid4()
    event_id2 = uuid4()
    event_id3 = uuid4()

    # Create program with required TILLAGE_EVENTS stage
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True, enabled=True)

    # Create project and permissions
    project = await mdl.Projects(program_id=program.id)
    permission = await mdl.ProjectPermissions(project=project.id)

    # Create field and event associations for all three events
    field = await mdl.Fields()
    for event_id in [event_id1, event_id2, event_id3]:
        field_event_assoc = await mdl.FieldEventAssociation(
            ses_event_id=str(event_id),
            field_md5=field.md5,
            field_id=field.id,
            project_id=project.id,
            program_id=program.id,
        )
        await mdl.PhaseEventAssociation(
            field_event_association_id=field_event_assoc.id,
            phase_id=phase.id,
            revision=1,
        )

    # Complete the phase so events would be locked
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    # Provide only event_id1 and event_id2 in the events list (both would normally be locked)
    events = [
        EventIdWithStageType(event_id=event_id1, stage_type=StageTypes.TILLAGE_EVENTS),  # Should be locked
        EventIdWithStageType(event_id=event_id2, stage_type=StageTypes.TILLAGE_EVENTS),  # Should be locked
        # event_id3 is intentionally NOT included, even though it would be locked if queried
    ]

    # Create mock SES client that should NOT be called
    mock_client = Mock()
    mock_client.fetch_events = Mock(side_effect=Exception("SES should not be called when events are provided"))

    # Call with limited events list
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=phase.id,
        events=events,
    )

    # Both provided events should be locked (TILLAGE_EVENTS is required)
    assert event_id1 in result
    assert event_id2 in result
    # event_id3 should NOT be in result because it wasn't in the provided events list
    assert event_id3 not in result

    # Verify SES was never called
    mock_client.fetch_events.assert_not_called()


async def test_get_locked_event_ids_filters_prefetched_events_to_requested_ids(app_request, mdl):
    """Test that when prefetched events contain more events than requested, only requested events are considered for locking."""
    requested_event_id1 = uuid4()
    requested_event_id2 = uuid4()
    extra_event_id3 = uuid4()  # This event is in prefetched data but NOT requested
    extra_event_id4 = uuid4()  # Another extra event in prefetched data

    # Create program with required TILLAGE_EVENTS stage
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True, enabled=True)

    # Create project and permissions
    project = await mdl.Projects(program_id=program.id)
    permission = await mdl.ProjectPermissions(project=project.id)

    # Create field and event associations for only the requested events (not the extra ones)
    field = await mdl.Fields()
    for event_id in [requested_event_id1, requested_event_id2]:
        field_event_assoc = await mdl.FieldEventAssociation(
            ses_event_id=str(event_id),
            field_md5=field.md5,
            field_id=field.id,
            project_id=project.id,
            program_id=program.id,
        )
        await mdl.PhaseEventAssociation(
            field_event_association_id=field_event_assoc.id,
            phase_id=phase.id,
            revision=1,
        )

    # Complete the phase so events would be locked
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    # Prefetched events contain MORE events than what's in the database associations (simulating a batch fetch scenario)
    prefetched_events = [
        EventIdWithStageType(event_id=requested_event_id1, stage_type=StageTypes.TILLAGE_EVENTS),
        EventIdWithStageType(event_id=requested_event_id2, stage_type=StageTypes.TILLAGE_EVENTS),
        EventIdWithStageType(event_id=extra_event_id3, stage_type=StageTypes.TILLAGE_EVENTS),  # Extra event not in DB
        EventIdWithStageType(event_id=extra_event_id4, stage_type=StageTypes.TILLAGE_EVENTS),  # Extra event not in DB
    ]

    # Mock SES client that should NOT be called since events are prefetched
    mock_client = Mock()
    mock_client.fetch_events = Mock(side_effect=Exception("SES should not be called when events are provided"))

    # Call with prefetched events that contain extra events
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=phase.id,
        events=prefetched_events,
    )

    # Only the requested events (those with associations) should be in the result
    assert requested_event_id1 in result
    assert requested_event_id2 in result
    # Extra events should NOT be in result since they don't have associations
    assert extra_event_id3 not in result
    assert extra_event_id4 not in result

    # Verify the result contains exactly the requested locked events
    assert len(result) == 2

    # Verify SES was never called since prefetched events were provided
    mock_client.fetch_events.assert_not_called()


async def test_entity_event_type_to_stage_type_mapping_optimization(app_request, mdl):
    """Test that ENTITY_EVENT_TYPE_TO_STAGE_TYPE provides correct optimization for delete operations."""

    # Test that optimization avoids SES call when stage type is known
    event_id = uuid4()

    # Create program and phase
    program = await mdl.Programs()
    phase = await mdl.Phases(program_id=program.id, type_=PhaseTypes.MONITORING)

    # Create stage that requires TILLAGE_EVENTS
    await mdl.Stage(phase_id=phase.id, type_=StageTypes.TILLAGE_EVENTS, required=True)

    # Create project and permissions
    project = await mdl.Projects(program_id=program.id)
    permission = await mdl.ProjectPermissions(project=project.id)

    # Create field and event association
    field = await mdl.Fields()
    field_event_assoc = await mdl.FieldEventAssociation(
        ses_event_id=str(event_id),
        field_md5=field.md5,
        field_id=field.id,
        project_id=project.id,
        program_id=program.id,
    )
    await mdl.PhaseEventAssociation(
        field_event_association_id=field_event_assoc.id,
        phase_id=phase.id,
        revision=1,
    )

    # Complete the phase
    await mdl.ProjectPhaseCompletion(project_id=project.id, phase_id=phase.id, is_completed=True)

    # Mock SES client that should NOT be called due to optimization
    mock_client = Mock()
    mock_client.fetch_events = Mock(
        side_effect=Exception("SES should not be called when event_stage_mapping is provided")
    )

    # Use ENTITY_EVENT_TYPE_TO_STAGE_TYPE mapping for TILLAGE_EVENT
    stage_type = ENTITY_EVENT_TYPE_TO_STAGE_TYPE.get(EntityEventType.TILLAGE_EVENT)
    events_with_stage_type = [EventIdWithStageType(event_id=event_id, stage_type=stage_type)]

    # Call with pre-computed events (simulating delete_field_event optimization)
    result = await get_locked_event_ids_for_user(
        ses_client=mock_client,
        request=app_request,
        owner_user_id=permission.user,
        acting_phase_id=phase.id,
        events=events_with_stage_type,
    )

    # Event should be locked because TILLAGE_EVENTS stage is required and phase is completed
    assert event_id in result

    # Verify that SES client's fetch_events was never called due to optimization
    mock_client.fetch_events.assert_not_called()
