from datetime import datetime

from sqlalchemy import select

from core.model import Users
from field_lineage.enums import FieldLineageStatus, FieldRelationshipType
from field_lineage.model import FieldRelationship
from fields.enums import FieldHistoryChangeReason, FieldStatus
from fields.model import Fields, FieldsBaseline, FieldsHistory
from helper.helper import run_query
from migrate_single_year_entities.methods import migrate_single_year_entities
from permissions.model import RolesUsers
from programs.enums import CustomRegInputsTypes, CustomRegInputsValidationRules
from programs.model import ProgramCustomRegInputs, ProgramPermissions
from projects.enums import ProjectStatus
from projects.farms.model import Farms
from projects.model import ProjectPermissions, Projects, ProjectValues
from user_groups.model import UserGroupProjects, UserGroups, UserGroupsAdmins


async def test_migrate_single_year_entities(mdl, app_request, db_session_maker):
    source_program = await mdl.Programs()

    program_custom_reg_input_1 = await mdl.ProgramCustomRegInputs(
        program_id=source_program.id,
        name="program_custom_reg_input_1",
        order=0,
        type_=CustomRegInputsTypes.regex,
        validation_rule=CustomRegInputsValidationRules.none,
    )
    await mdl.ProgramCustomRegInputs(
        program_id=source_program.id,
        name="program_custom_reg_input_2",
        order=1,
        type_=CustomRegInputsTypes.user_group,
        validation_rule=CustomRegInputsValidationRules.none,
    )

    project_1 = await mdl.Projects(program_id=source_program.id, status=ProjectStatus.enrolled, reporting_enabled=0)
    project_2 = await mdl.Projects(program_id=source_program.id, status=ProjectStatus.enrolled, reporting_enabled=1)
    user_1 = await mdl.Users()
    user_2 = await mdl.Users()
    project_permission_1 = await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)
    project_permission_2 = await mdl.ProjectPermissions(project=project_2.id, user=user_2.id)

    program_permission_1 = await mdl.ProgramPermissions(user_id=user_1.id, program_id=source_program.id)

    role_1 = await mdl.Roles()
    user_role_1 = await mdl.RolesUsers(fs_user_id=user_1.id, role_id=role_1.id, program_id=source_program.id)
    user_role_2 = await mdl.RolesUsers(fs_user_id=user_2.id, role_id=role_1.id, program_id=source_program.id)

    core_farm_1 = await mdl.Groups()
    core_farm_2 = await mdl.Groups()
    farm_1 = await mdl.Farms(parent_project_id=project_1.id, core_farm_group_id=core_farm_1.id)
    farm_2 = await mdl.Farms(parent_project_id=project_2.id, core_farm_group_id=core_farm_2.id)

    kml_1 = await mdl.KMLFiles()
    kml_2 = await mdl.KMLFiles()
    core_field_1 = await mdl.KMLGroups(kml_id=kml_1.id)
    core_field_2 = await mdl.KMLGroups(kml_id=kml_2.id)
    field_1 = await mdl.Fields(
        parent_project_id=project_1.id,
        fs_field_id=core_field_1.id,
        kml_id=kml_1.id,
        farm_id=core_farm_1.id,
        md5="md51",
        area=10,
    )
    field_2 = await mdl.Fields(
        parent_project_id=project_2.id,
        fs_field_id=core_field_2.id,
        kml_id=kml_2.id,
        farm_id=core_farm_2.id,
        md5="md52",
        area=20,
    )

    field_baseline_1 = await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2020, is_returning=True)
    field_baseline_2 = await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2025, is_returning=False)

    project_value_1 = await mdl.ProjectValues(
        project_id=project_1.id, type_="test", key=str(program_custom_reg_input_1.id), value="test_value"
    )
    await mdl.ProjectValues(project_id=project_1.id, type_="survey", key="test_survey_key", value="test_survey_value")
    project_value_2 = await mdl.ProjectValues(
        project_id=project_2.id, type_="test", key=program_custom_reg_input_1.id, value="test_value"
    )
    await mdl.ProjectValues(project_id=project_2.id, type_="survey", key="test_survey_key", value="test_survey_value")

    user_group_1 = await mdl.UserGroups(
        id=1, program_id=source_program.id, name="group1", parent_group_id=None, path="1"
    )
    user_group_2 = await mdl.UserGroups(
        id=2, program_id=source_program.id, name="group2", parent_group_id=1, path="1/2"
    )
    await mdl.UserGroupProjects(group_id=user_group_1.id, project_id=project_1.id)
    await mdl.UserGroupProjects(group_id=user_group_1.id, project_id=project_2.id)
    user_group_admin_1 = await mdl.UserGroupsAdmins(
        group_id=user_group_1.id, user_id=user_1.id, program_id=source_program.id
    )

    dest_program = await mdl.Programs()

    await migrate_single_year_entities(
        request=app_request, source_program_id=source_program.id, dest_program_id=dest_program.id
    )

    async with db_session_maker() as s:
        query = select(ProgramCustomRegInputs).where(ProgramCustomRegInputs.program_id == dest_program.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        dest_program_custom_reg_input_1_id = res[0].id
        assert (
            res[0].to_dict()
            == ProgramCustomRegInputs(
                id=res[0].id,
                program_id=dest_program.id,
                order=program_custom_reg_input_1.order,
                name=program_custom_reg_input_1.name,
                input_key=program_custom_reg_input_1.input_key,
                type_=program_custom_reg_input_1.type_,
                validation_rule=program_custom_reg_input_1.validation_rule,
                config=program_custom_reg_input_1.config,
                visibility=program_custom_reg_input_1.visibility,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(Projects, Users.id)
            .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
            .join(Users, ProjectPermissions.user == Users.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        dest_project_1_id = res[0][0].id
        dest_project_2_id = res[1][0].id
        assert (
            res[0][0].to_dict()
            == Projects(
                id=res[0][0].id,
                program_id=dest_program.id,
                config=project_1.config,
                send_marketing_data=project_1.send_marketing_data,
                docusign_id=None,
                docusign_status=None,
                reporting_enabled=project_1.reporting_enabled,
                contract_status=None,
                status=ProjectStatus.created,
                value_last_updated_at=None,
                show_email=project_1.show_email,
                created_at=res[0][0].created_at,
                updated_at=res[0][0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert res[0][1] == user_1.id
        assert (
            res[1][0].to_dict()
            == Projects(
                id=res[1][0].id,
                program_id=dest_program.id,
                config=project_2.config,
                send_marketing_data=project_2.send_marketing_data,
                docusign_id=None,
                docusign_status=None,
                reporting_enabled=project_2.reporting_enabled,
                contract_status=None,
                status=ProjectStatus.created,
                value_last_updated_at=None,
                show_email=project_2.show_email,
                created_at=res[1][0].created_at,
                updated_at=res[1][0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert res[1][1] == user_2.id

        query = (
            select(ProjectPermissions)
            .join(Projects, ProjectPermissions.project == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == ProjectPermissions(
                id=res[0].id,
                user=project_permission_1.user,
                project=dest_project_1_id,
                details=project_permission_1.details,
                deleted_at=None,
                deleted_at_unix=0,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProjectPermissions(
                id=res[1].id,
                user=project_permission_2.user,
                project=dest_project_2_id,
                details=project_permission_2.details,
                deleted_at=None,
                deleted_at_unix=0,
            ).to_dict()
        )

        query = select(ProgramPermissions).where(ProgramPermissions.program_id == dest_program.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == ProgramPermissions(
                id=res[0].id,
                user_id=program_permission_1.user_id,
                program_id=dest_program.id,
                details=program_permission_1.details,
                deleted_at=None,
                deleted_at_unix=0,
            ).to_dict()
        )

        query = select(RolesUsers).where(RolesUsers.program_id == dest_program.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        user_role_dicts = [single_res.to_dict() for single_res in res]
        expected_user_role_dicts = [
            RolesUsers(
                id=res[0].id,
                fs_user_id=user_role_1.fs_user_id,
                role_id=user_role_1.role_id,
                program_id=dest_program.id,
                created_at=res[0].created_at,
                deleted_at=None,
            ).to_dict(),
            RolesUsers(
                id=res[1].id,
                fs_user_id=user_role_2.fs_user_id,
                role_id=user_role_2.role_id,
                program_id=dest_program.id,
                created_at=res[1].created_at,
                deleted_at=None,
            ).to_dict(),
        ]
        for expected in expected_user_role_dicts:
            assert expected in user_role_dicts

        query = (
            select(Farms)
            .join(Projects, Farms.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == Farms(
                id=res[0].id,
                parent_project_id=dest_project_1_id,
                core_farm_group_id=core_farm_1.id,
                farm_name=farm_1.farm_name,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == Farms(
                id=res[1].id,
                parent_project_id=dest_project_2_id,
                core_farm_group_id=core_farm_2.id,
                farm_name=farm_2.farm_name,
                created_at=res[1].created_at,
                updated_at=res[1].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(Fields)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        dest_field_1_id = res[0].id
        dest_field_2_id = res[1].id
        assert (
            res[0].to_dict()
            == Fields(
                id=res[0].id,
                parent_project_id=dest_project_1_id,
                fs_field_id=core_field_1.id,
                kml_id=field_1.kml_id,
                farm_id=field_1.farm_id,
                md5=field_1.md5,
                area=field_1.area,
                measurement_eligibility=None,
                status=FieldStatus.registered,
                value_last_updated_at=None,
                core_region_id=field_1.core_region_id,
                field_lineage_id=field_1.field_lineage_id,
                field_lineage_status=FieldLineageStatus.returning,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
                deleted_at_unix=0,
                origin_date=res[0].origin_date,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == Fields(
                id=res[1].id,
                parent_project_id=dest_project_2_id,
                fs_field_id=core_field_2.id,
                kml_id=field_2.kml_id,
                farm_id=field_2.farm_id,
                md5=field_2.md5,
                area=field_2.area,
                measurement_eligibility=None,
                status=FieldStatus.registered,
                value_last_updated_at=None,
                core_region_id=field_2.core_region_id,
                field_lineage_id=field_2.field_lineage_id,
                field_lineage_status=FieldLineageStatus.returning,
                created_at=res[1].created_at,
                updated_at=res[1].updated_at,
                deleted_at=None,
                deleted_at_unix=0,
                origin_date=res[1].origin_date,
            ).to_dict()
        )

        query = (
            select(FieldsBaseline)
            .join(Fields, FieldsBaseline.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsBaseline(
                id=res[0].id,
                field_id=dest_field_1_id,
                baseline_year=field_baseline_1.baseline_year,
                is_returning=True,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(
                id=res[1].id,
                field_id=dest_field_2_id,
                baseline_year=field_baseline_2.baseline_year,
                is_returning=True,
            ).to_dict()
        )

        query = (
            select(FieldsHistory)
            .join(Fields, FieldsHistory.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldsHistory(
                id=res[0].id,
                field_id=dest_field_1_id,
                fs_field_id=field_1.fs_field_id,
                kml_id=field_1.kml_id,
                group_id=field_1.farm_id,
                md5=field_1.md5,
                area=field_1.area,
                core_region_id=field_1.core_region_id,
                reason=FieldHistoryChangeReason.api,
                field_lineage_id=field_1.field_lineage_id,
                created_at=res[0].created_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsHistory(
                id=res[1].id,
                field_id=dest_field_2_id,
                fs_field_id=field_2.fs_field_id,
                kml_id=field_2.kml_id,
                group_id=field_2.farm_id,
                md5=field_2.md5,
                area=field_2.area,
                core_region_id=field_2.core_region_id,
                reason=FieldHistoryChangeReason.api,
                field_lineage_id=field_2.field_lineage_id,
                created_at=res[1].created_at,
            ).to_dict()
        )

        query = (
            select(FieldRelationship)
            .join(Fields, FieldRelationship.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=res[0].id,
                field_id=dest_field_1_id,
                previous_field_id=field_1.id,
                percent_intersection=100,
                area_intersection=field_1.area,
                relationship=FieldRelationshipType.MATCH,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=dest_field_2_id,
                previous_field_id=field_2.id,
                percent_intersection=100,
                area_intersection=field_2.area,
                relationship=FieldRelationshipType.MATCH,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(ProjectValues)
            .join(Projects, ProjectValues.project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == ProjectValues(
                id=res[0].id,
                type_=project_value_1.type_,
                project_id=dest_project_1_id,
                stage_id=None,
                key=str(dest_program_custom_reg_input_1_id),
                value=project_value_1.value,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProjectValues(
                id=res[1].id,
                type_=project_value_2.type_,
                project_id=dest_project_2_id,
                stage_id=None,
                key=str(dest_program_custom_reg_input_1_id),
                value=project_value_2.value,
                updated_at=res[1].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = select(UserGroups).where(UserGroups.program_id == dest_program.id).order_by(UserGroups.name)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        dest_user_group_1_id = res[0].id
        assert (
            res[0].to_dict()
            == UserGroups(
                id=res[0].id,
                name=user_group_1.name,
                display_name=user_group_1.display_name,
                program_id=dest_program.id,
                parent_group_id=None,
                color_category_index=user_group_1.color_category_index,
                path=str(res[0].id),
                allow_self_assignment=user_group_1.allow_self_assignment,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == UserGroups(
                id=res[1].id,
                name=user_group_2.name,
                display_name=user_group_2.display_name,
                program_id=dest_program.id,
                parent_group_id=res[0].id,
                color_category_index=user_group_2.color_category_index,
                path=f"{res[0].id}/{res[1].id}",
                allow_self_assignment=user_group_2.allow_self_assignment,
                created_at=res[1].created_at,
                updated_at=res[1].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(UserGroupProjects)
            .join(Projects, UserGroupProjects.project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == UserGroupProjects(
                id=res[0].id,
                group_id=dest_user_group_1_id,
                project_id=dest_project_1_id,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == UserGroupProjects(
                id=res[1].id,
                group_id=dest_user_group_1_id,
                project_id=dest_project_2_id,
                created_at=res[1].created_at,
                updated_at=res[1].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = select(UserGroupsAdmins).where(UserGroupsAdmins.program_id == dest_program.id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 1
        assert (
            res[0].to_dict()
            == UserGroupsAdmins(
                id=res[0].id,
                program_id=dest_program.id,
                group_id=dest_user_group_1_id,
                user_id=user_group_admin_1.user_id,
                unrestricted_access=user_group_admin_1.unrestricted_access,
                created_at=res[0].created_at,
                updated_at=res[0].updated_at,
                deleted_at=None,
                deleted_at_unix=None,
            ).to_dict()
        )


async def test_migrate_single_year_entities_existing_project_entities(mdl, app_request, db_session_maker):
    source_program = await mdl.Programs()

    program_custom_reg_input_1 = await mdl.ProgramCustomRegInputs(
        program_id=source_program.id,
        name="program_custom_reg_input_1",
        order=0,
        type_=CustomRegInputsTypes.regex,
        validation_rule=CustomRegInputsValidationRules.none,
    )
    program_custom_reg_input_2 = await mdl.ProgramCustomRegInputs(
        program_id=source_program.id,
        name="program_custom_reg_input_2",
        order=1,
        type_=CustomRegInputsTypes.regex,
        validation_rule=CustomRegInputsValidationRules.none,
    )

    project_1 = await mdl.Projects(program_id=source_program.id, status=ProjectStatus.enrolled, reporting_enabled=0)
    project_2 = await mdl.Projects(program_id=source_program.id, status=ProjectStatus.enrolled, reporting_enabled=1)
    user_1 = await mdl.Users()
    user_2 = await mdl.Users()
    await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)
    project_permission_2 = await mdl.ProjectPermissions(project=project_2.id, user=user_2.id)

    core_farm_1 = await mdl.Groups()
    core_farm_2 = await mdl.Groups()
    await mdl.Farms(parent_project_id=project_1.id, core_farm_group_id=core_farm_1.id)
    farm_2 = await mdl.Farms(parent_project_id=project_2.id, core_farm_group_id=core_farm_2.id)

    kml_1 = await mdl.KMLFiles()
    kml_2 = await mdl.KMLFiles()
    core_field_1 = await mdl.KMLGroups(kml_id=kml_1.id)
    core_field_2 = await mdl.KMLGroups(kml_id=kml_2.id)
    field_1 = await mdl.Fields(
        parent_project_id=project_1.id,
        fs_field_id=core_field_1.id,
        kml_id=kml_1.id,
        farm_id=core_farm_1.id,
        md5="md51",
        area=10,
    )
    field_2 = await mdl.Fields(
        parent_project_id=project_2.id,
        fs_field_id=core_field_2.id,
        kml_id=kml_2.id,
        farm_id=core_farm_2.id,
        md5="md52",
        area=20,
    )

    await mdl.FieldsBaseline(field_id=field_1.id, baseline_year=2020, is_returning=True)
    field_baseline_2 = await mdl.FieldsBaseline(field_id=field_2.id, baseline_year=2025, is_returning=False)

    await mdl.ProjectValues(
        project_id=project_1.id, type_="test", key=str(program_custom_reg_input_1.id), value="test_value"
    )
    project_value_2 = await mdl.ProjectValues(
        project_id=project_2.id, type_="test", key=str(program_custom_reg_input_1.id), value="test_value"
    )

    dest_program = await mdl.Programs(created_at=datetime(2025, 1, 1, 0, 0, 0))

    dest_program_custom_reg_input_0 = await mdl.ProgramCustomRegInputs(
        program_id=dest_program.id,
        name="program_custom_reg_input_0",
        order=0,
        type_=CustomRegInputsTypes.regex,
        validation_rule=CustomRegInputsValidationRules.none,
    )
    # existing program custom reg input for program_custom_reg_input_1
    dest_program_custom_reg_input_1 = await mdl.ProgramCustomRegInputs(
        program_id=dest_program.id,
        name="program_custom_reg_input_1",
        order=1,
        type_=CustomRegInputsTypes.regex,
        validation_rule=CustomRegInputsValidationRules.none,
    )

    # existing project for project_1
    dest_project_1 = await mdl.Projects(
        program_id=dest_program.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=1,
        created_at=datetime(2025, 1, 1, 0, 0, 0),
    )
    # existing project permission for project_1
    dest_project_permission_1 = await mdl.ProjectPermissions(project=dest_project_1.id, user=user_1.id)

    dest_core_farm_0 = await mdl.Groups()
    dest_farm_0 = await mdl.Farms(
        parent_project_id=dest_project_1.id,
        core_farm_group_id=dest_core_farm_0.id,
        created_at=datetime(2025, 1, 1, 0, 0, 0),
    )
    # existing farm for farm_1
    dest_farm_1 = await mdl.Farms(
        parent_project_id=dest_project_1.id, core_farm_group_id=core_farm_1.id, created_at=datetime(2025, 1, 2, 0, 0, 0)
    )

    dest_kml_0 = await mdl.KMLFiles()
    dest_core_field_0 = await mdl.KMLGroups(kml_id=dest_kml_0.id)
    dest_field_0 = await mdl.Fields(
        parent_project_id=dest_project_1.id,
        fs_field_id=dest_core_field_0.id,
        kml_id=dest_kml_0.id,
        farm_id=dest_core_farm_0.id,
        md5="md50",
        area=1,
        created_at=datetime(2025, 1, 1, 0, 0, 0),
    )
    # existing field for field_1
    dest_field_1 = await mdl.Fields(
        parent_project_id=dest_project_1.id,
        fs_field_id=core_field_1.id,
        kml_id=kml_1.id,
        farm_id=core_farm_1.id,
        md5="md51",
        area=10,
        created_at=datetime(2025, 1, 2, 0, 0, 0),
    )

    dest_field_baseline_0 = await mdl.FieldsBaseline(field_id=dest_field_0.id, baseline_year=2025, is_returning=False)
    # existing field baseline for field_1
    dest_field_baseline_1 = await mdl.FieldsBaseline(field_id=dest_field_1.id, baseline_year=2020, is_returning=True)

    dest_field_history_0 = await mdl.FieldsHistory(
        field_id=dest_field_0.id,
        fs_field_id=dest_field_0.fs_field_id,
        kml_id=dest_field_0.kml_id,
        group_id=dest_field_0.farm_id,
        md5=dest_field_0.md5,
        area=dest_field_0.area,
        reason=FieldHistoryChangeReason.fmi,
    )
    # existing field history for field_1
    dest_field_history_1 = await mdl.FieldsHistory(
        field_id=dest_field_1.id,
        fs_field_id=dest_field_1.fs_field_id,
        kml_id=dest_field_1.kml_id,
        group_id=dest_field_1.farm_id,
        md5=dest_field_1.md5,
        area=dest_field_1.area,
        reason=FieldHistoryChangeReason.fmi,
    )
    # existing field relationship for field_1
    dest_field_relationship_1 = await mdl.FieldRelationship(
        field_id=dest_field_1.id,
        previous_field_id=field_1.id,
        percent_intersection=100,
        area_intersection=field_1.area,
        relationship=FieldRelationshipType.MATCH,
        deleted_at=None,
    )

    dest_project_value_0 = await mdl.ProjectValues(
        project_id=dest_project_1.id, type_="test", key="0", value="test_value_0"
    )
    # existing project value for project_1
    dest_project_value_1 = await mdl.ProjectValues(
        project_id=dest_project_1.id, type_="test", key=str(dest_program_custom_reg_input_1.id), value="test_value_1"
    )

    await migrate_single_year_entities(
        request=app_request, source_program_id=source_program.id, dest_program_id=dest_program.id
    )

    async with db_session_maker() as s:
        query = (
            select(ProgramCustomRegInputs)
            .where(ProgramCustomRegInputs.program_id == dest_program.id)
            .order_by(ProgramCustomRegInputs.order)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == ProgramCustomRegInputs(
                id=dest_program_custom_reg_input_0.id,
                program_id=dest_program_custom_reg_input_0.program_id,
                order=dest_program_custom_reg_input_0.order,
                name=dest_program_custom_reg_input_0.name,
                input_key=dest_program_custom_reg_input_0.input_key,
                type_=dest_program_custom_reg_input_0.type_,
                validation_rule=dest_program_custom_reg_input_0.validation_rule,
                config=dest_program_custom_reg_input_0.config,
                visibility=dest_program_custom_reg_input_0.visibility,
                created_at=dest_program_custom_reg_input_0.created_at,
                updated_at=dest_program_custom_reg_input_0.updated_at,
                deleted_at=dest_program_custom_reg_input_0.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProgramCustomRegInputs(
                id=dest_program_custom_reg_input_1.id,
                program_id=dest_program_custom_reg_input_1.program_id,
                order=dest_program_custom_reg_input_1.order,
                name=dest_program_custom_reg_input_1.name,
                input_key=dest_program_custom_reg_input_1.input_key,
                type_=dest_program_custom_reg_input_1.type_,
                validation_rule=dest_program_custom_reg_input_1.validation_rule,
                config=dest_program_custom_reg_input_1.config,
                visibility=dest_program_custom_reg_input_1.visibility,
                created_at=dest_program_custom_reg_input_1.created_at,
                updated_at=dest_program_custom_reg_input_1.updated_at,
                deleted_at=dest_program_custom_reg_input_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == ProgramCustomRegInputs(
                id=res[2].id,
                program_id=dest_program.id,
                order=3,
                name=program_custom_reg_input_2.name,
                input_key=program_custom_reg_input_2.input_key,
                type_=program_custom_reg_input_2.type_,
                validation_rule=program_custom_reg_input_2.validation_rule,
                config=program_custom_reg_input_2.config,
                visibility=program_custom_reg_input_2.visibility,
                created_at=res[2].created_at,
                updated_at=res[2].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(Projects, Users.id)
            .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
            .join(Users, ProjectPermissions.user == Users.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 2
        dest_project_2_id = res[1][0].id
        assert (
            res[0][0].to_dict()
            == Projects(
                id=dest_project_1.id,
                program_id=dest_project_1.program_id,
                config=dest_project_1.config,
                send_marketing_data=dest_project_1.send_marketing_data,
                docusign_id=dest_project_1.docusign_id,
                docusign_status=dest_project_1.docusign_status,
                reporting_enabled=dest_project_1.reporting_enabled,
                contract_status=dest_project_1.contract_status,
                status=dest_project_1.status,
                value_last_updated_at=dest_project_1.value_last_updated_at,
                show_email=dest_project_1.show_email,
                created_at=dest_project_1.created_at,
                updated_at=dest_project_1.updated_at,
                deleted_at=dest_project_1.deleted_at,
            ).to_dict()
        )
        assert res[0][1] == user_1.id
        assert (
            res[1][0].to_dict()
            == Projects(
                id=res[1][0].id,
                program_id=dest_program.id,
                config=project_2.config,
                send_marketing_data=project_2.send_marketing_data,
                docusign_id=None,
                docusign_status=None,
                reporting_enabled=project_2.reporting_enabled,
                contract_status=None,
                status=ProjectStatus.created,
                value_last_updated_at=None,
                show_email=project_2.show_email,
                created_at=res[1][0].created_at,
                updated_at=res[1][0].updated_at,
                deleted_at=None,
            ).to_dict()
        )
        assert res[1][1] == user_2.id

        query = (
            select(ProjectPermissions)
            .join(Projects, ProjectPermissions.project == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == ProjectPermissions(
                id=dest_project_permission_1.id,
                user=dest_project_permission_1.user,
                project=dest_project_permission_1.project,
                details=dest_project_permission_1.details,
                deleted_at=dest_project_permission_1.deleted_at,
                deleted_at_unix=dest_project_permission_1.deleted_at_unix,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProjectPermissions(
                id=res[1].id,
                user=project_permission_2.user,
                project=dest_project_2_id,
                details=project_permission_2.details,
                deleted_at=None,
                deleted_at_unix=0,
            ).to_dict()
        )

        query = (
            select(Farms)
            .join(Projects, Farms.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at, Farms.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == Farms(
                id=dest_farm_0.id,
                parent_project_id=dest_farm_0.parent_project_id,
                core_farm_group_id=dest_farm_0.core_farm_group_id,
                farm_name=dest_farm_0.farm_name,
                created_at=dest_farm_0.created_at,
                updated_at=dest_farm_0.updated_at,
                deleted_at=dest_farm_0.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == Farms(
                id=dest_farm_1.id,
                parent_project_id=dest_farm_1.parent_project_id,
                core_farm_group_id=dest_farm_1.core_farm_group_id,
                farm_name=dest_farm_1.farm_name,
                created_at=dest_farm_1.created_at,
                updated_at=dest_farm_1.updated_at,
                deleted_at=dest_farm_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == Farms(
                id=res[2].id,
                parent_project_id=dest_project_2_id,
                core_farm_group_id=core_farm_2.id,
                farm_name=farm_2.farm_name,
                created_at=res[2].created_at,
                updated_at=res[2].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(Fields)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at, Fields.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        dest_field_2_id = res[2].id
        assert (
            res[0].to_dict()
            == Fields(
                id=dest_field_0.id,
                parent_project_id=dest_field_0.parent_project_id,
                fs_field_id=dest_field_0.fs_field_id,
                kml_id=dest_field_0.kml_id,
                farm_id=dest_field_0.farm_id,
                md5=dest_field_0.md5,
                area=dest_field_0.area,
                measurement_eligibility=dest_field_0.measurement_eligibility,
                status=dest_field_0.status,
                value_last_updated_at=dest_field_0.value_last_updated_at,
                core_region_id=dest_field_0.core_region_id,
                field_lineage_id=dest_field_0.field_lineage_id,
                field_lineage_status=dest_field_0.field_lineage_status,
                created_at=dest_field_0.created_at,
                updated_at=dest_field_0.updated_at,
                deleted_at=dest_field_0.deleted_at,
                deleted_at_unix=dest_field_0.deleted_at_unix,
                origin_date=dest_field_0.origin_date,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == Fields(
                id=dest_field_1.id,
                parent_project_id=dest_field_1.parent_project_id,
                fs_field_id=dest_field_1.fs_field_id,
                kml_id=dest_field_1.kml_id,
                farm_id=dest_field_1.farm_id,
                md5=dest_field_1.md5,
                area=dest_field_1.area,
                measurement_eligibility=dest_field_1.measurement_eligibility,
                status=dest_field_1.status,
                value_last_updated_at=dest_field_1.value_last_updated_at,
                core_region_id=dest_field_1.core_region_id,
                field_lineage_id=dest_field_1.field_lineage_id,
                field_lineage_status=dest_field_1.field_lineage_status,
                created_at=dest_field_1.created_at,
                updated_at=dest_field_1.updated_at,
                deleted_at=dest_field_1.deleted_at,
                deleted_at_unix=dest_field_1.deleted_at_unix,
                origin_date=dest_field_1.origin_date,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == Fields(
                id=res[2].id,
                parent_project_id=dest_project_2_id,
                fs_field_id=core_field_2.id,
                kml_id=field_2.kml_id,
                farm_id=field_2.farm_id,
                md5=field_2.md5,
                area=field_2.area,
                measurement_eligibility=None,
                status=FieldStatus.registered,
                value_last_updated_at=None,
                core_region_id=field_2.core_region_id,
                field_lineage_id=field_2.field_lineage_id,
                field_lineage_status=FieldLineageStatus.returning,
                created_at=res[2].created_at,
                updated_at=res[2].updated_at,
                deleted_at=None,
                deleted_at_unix=0,
                origin_date=res[2].origin_date,
            ).to_dict()
        )

        query = (
            select(FieldsBaseline)
            .join(Fields, FieldsBaseline.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at, Fields.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsBaseline(
                id=dest_field_baseline_0.id,
                field_id=dest_field_baseline_0.field_id,
                baseline_year=dest_field_baseline_0.baseline_year,
                is_returning=dest_field_baseline_0.is_returning,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsBaseline(
                id=dest_field_baseline_1.id,
                field_id=dest_field_baseline_1.field_id,
                baseline_year=dest_field_baseline_1.baseline_year,
                is_returning=dest_field_baseline_1.is_returning,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsBaseline(
                id=res[2].id,
                field_id=dest_field_2_id,
                baseline_year=field_baseline_2.baseline_year,
                is_returning=True,
            ).to_dict()
        )

        query = (
            select(FieldsHistory)
            .join(Fields, FieldsHistory.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at, Fields.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == FieldsHistory(
                id=dest_field_history_0.id,
                field_id=dest_field_history_0.field_id,
                fs_field_id=dest_field_history_0.fs_field_id,
                kml_id=dest_field_history_0.kml_id,
                group_id=dest_field_history_0.group_id,
                md5=dest_field_history_0.md5,
                area=dest_field_history_0.area,
                core_region_id=dest_field_history_0.core_region_id,
                reason=dest_field_history_0.reason,
                field_lineage_id=dest_field_history_0.field_lineage_id,
                created_at=dest_field_history_0.created_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldsHistory(
                id=dest_field_history_1.id,
                field_id=dest_field_history_1.field_id,
                fs_field_id=dest_field_history_1.fs_field_id,
                kml_id=dest_field_history_1.kml_id,
                group_id=dest_field_history_1.group_id,
                md5=dest_field_history_1.md5,
                area=dest_field_history_1.area,
                core_region_id=dest_field_history_1.core_region_id,
                reason=dest_field_history_1.reason,
                field_lineage_id=dest_field_history_1.field_lineage_id,
                created_at=dest_field_history_1.created_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == FieldsHistory(
                id=res[2].id,
                field_id=dest_field_2_id,
                fs_field_id=field_2.fs_field_id,
                kml_id=field_2.kml_id,
                group_id=field_2.farm_id,
                md5=field_2.md5,
                area=field_2.area,
                core_region_id=field_2.core_region_id,
                reason=FieldHistoryChangeReason.api,
                field_lineage_id=field_2.field_lineage_id,
                created_at=res[2].created_at,
            ).to_dict()
        )

        query = (
            select(FieldRelationship)
            .join(Fields, FieldRelationship.field_id == Fields.id)
            .join(Projects, Fields.parent_project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at, Fields.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == FieldRelationship(
                id=dest_field_relationship_1.id,
                field_id=dest_field_relationship_1.field_id,
                previous_field_id=dest_field_relationship_1.previous_field_id,
                percent_intersection=dest_field_relationship_1.percent_intersection,
                area_intersection=dest_field_relationship_1.area_intersection,
                relationship=dest_field_relationship_1.relationship,
                deleted_at=dest_field_relationship_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == FieldRelationship(
                id=res[1].id,
                field_id=dest_field_2_id,
                previous_field_id=field_2.id,
                percent_intersection=100,
                area_intersection=field_2.area,
                relationship=FieldRelationshipType.MATCH,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(ProjectValues)
            .join(Projects, ProjectValues.project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == ProjectValues(
                id=dest_project_value_0.id,
                type_=dest_project_value_0.type_,
                project_id=dest_project_value_0.project_id,
                stage_id=dest_project_value_0.stage_id,
                key=dest_project_value_0.key,
                value=dest_project_value_0.value,
                updated_at=dest_project_value_0.updated_at,
                deleted_at=dest_project_value_0.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProjectValues(
                id=dest_project_value_1.id,
                type_=dest_project_value_1.type_,
                project_id=dest_project_value_1.project_id,
                stage_id=dest_project_value_1.stage_id,
                key=dest_project_value_1.key,
                value=dest_project_value_1.value,
                updated_at=dest_project_value_1.updated_at,
                deleted_at=dest_project_value_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == ProjectValues(
                id=res[2].id,
                type_=project_value_2.type_,
                project_id=dest_project_2_id,
                stage_id=None,
                key=str(dest_program_custom_reg_input_1.id),
                value=project_value_2.value,
                updated_at=res[2].updated_at,
                deleted_at=None,
            ).to_dict()
        )


async def test_migrate_single_year_entities_existing_group_entities(mdl, app_request, db_session_maker):
    source_program = await mdl.Programs()
    project_1 = await mdl.Projects(program_id=source_program.id, status=ProjectStatus.enrolled, reporting_enabled=0)
    user_1 = await mdl.Users()
    await mdl.ProjectPermissions(project=project_1.id, user=user_1.id)

    user_group_1 = await mdl.UserGroups(
        id=1, program_id=source_program.id, name="group1", parent_group_id=None, path="1"
    )
    user_group_2 = await mdl.UserGroups(
        id=2, program_id=source_program.id, name="group2", parent_group_id=1, path="1/2"
    )
    await mdl.UserGroupProjects(group_id=user_group_1.id, project_id=project_1.id)
    await mdl.UserGroupProjects(group_id=user_group_2.id, project_id=project_1.id)
    await mdl.UserGroupsAdmins(group_id=user_group_1.id, user_id=user_1.id, program_id=source_program.id)
    user_group_admin_2 = await mdl.UserGroupsAdmins(
        group_id=user_group_2.id, user_id=user_1.id, program_id=source_program.id
    )

    dest_program = await mdl.Programs(created_at=datetime(2025, 1, 1, 0, 0, 0))
    # existing project for project_1
    dest_project_1 = await mdl.Projects(
        program_id=dest_program.id,
        status=ProjectStatus.enrolled,
        reporting_enabled=1,
        created_at=datetime(2025, 1, 1, 0, 0, 0),
    )
    # existing project permission for project_1
    await mdl.ProjectPermissions(project=dest_project_1.id, user=user_1.id)

    dest_user_group_0 = await mdl.UserGroups(
        id=10, program_id=dest_program.id, name="group0", parent_group_id=None, path="10"
    )
    # existing user group for user_group_1
    dest_user_group_1 = await mdl.UserGroups(
        id=11, program_id=dest_program.id, name="group1", parent_group_id=None, path="11"
    )
    dest_user_group_project_0 = await mdl.UserGroupProjects(
        group_id=dest_user_group_0.id, project_id=dest_project_1.id, created_at=datetime(2025, 1, 1)
    )
    # existing user group project for user_group_1
    dest_user_group_project_1 = await mdl.UserGroupProjects(
        group_id=dest_user_group_1.id, project_id=dest_project_1.id, created_at=datetime(2025, 1, 2)
    )
    # exiting user group admin for user_group_admin_1
    dest_user_group_admin_1 = await mdl.UserGroupsAdmins(
        group_id=dest_user_group_1.id, user_id=user_1.id, program_id=dest_program.id, created_at=datetime(2025, 1, 1)
    )

    await migrate_single_year_entities(
        request=app_request, source_program_id=source_program.id, dest_program_id=dest_program.id
    )

    async with db_session_maker() as s:
        query = (
            select(Projects, Users.id)
            .join(ProjectPermissions, Projects.id == ProjectPermissions.project)
            .join(Users, ProjectPermissions.user == Users.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(Projects.created_at)
        )
        res = (await run_query(query=query, s=s)).all()
        assert len(res) == 1
        assert (
            res[0][0].to_dict()
            == Projects(
                id=dest_project_1.id,
                program_id=dest_project_1.program_id,
                config=dest_project_1.config,
                send_marketing_data=dest_project_1.send_marketing_data,
                docusign_id=dest_project_1.docusign_id,
                docusign_status=dest_project_1.docusign_status,
                reporting_enabled=dest_project_1.reporting_enabled,
                contract_status=dest_project_1.contract_status,
                status=dest_project_1.status,
                value_last_updated_at=dest_project_1.value_last_updated_at,
                show_email=dest_project_1.show_email,
                created_at=dest_project_1.created_at,
                updated_at=dest_project_1.updated_at,
                deleted_at=dest_project_1.deleted_at,
            ).to_dict()
        )

        query = select(UserGroups).where(UserGroups.program_id == dest_program.id).order_by(UserGroups.name)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        dest_user_group_2_id = res[2].id
        assert (
            res[0].to_dict()
            == UserGroups(
                id=dest_user_group_0.id,
                name=dest_user_group_0.name,
                display_name=dest_user_group_0.display_name,
                program_id=dest_user_group_0.program_id,
                parent_group_id=dest_user_group_0.parent_group_id,
                color_category_index=dest_user_group_0.color_category_index,
                path=dest_user_group_0.path,
                allow_self_assignment=dest_user_group_0.allow_self_assignment,
                created_at=dest_user_group_0.created_at,
                updated_at=dest_user_group_0.updated_at,
                deleted_at=dest_user_group_0.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == UserGroups(
                id=dest_user_group_1.id,
                name=dest_user_group_1.name,
                display_name=dest_user_group_1.display_name,
                program_id=dest_user_group_1.program_id,
                parent_group_id=dest_user_group_1.parent_group_id,
                color_category_index=dest_user_group_1.color_category_index,
                path=dest_user_group_1.path,
                allow_self_assignment=dest_user_group_1.allow_self_assignment,
                created_at=dest_user_group_1.created_at,
                updated_at=dest_user_group_1.updated_at,
                deleted_at=dest_user_group_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == UserGroups(
                id=res[2].id,
                name=user_group_2.name,
                display_name=user_group_2.display_name,
                program_id=dest_program.id,
                parent_group_id=res[1].id,
                color_category_index=user_group_2.color_category_index,
                path=f"{res[1].id}/{res[2].id}",
                allow_self_assignment=user_group_2.allow_self_assignment,
                created_at=res[2].created_at,
                updated_at=res[2].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(UserGroupProjects)
            .join(Projects, UserGroupProjects.project_id == Projects.id)
            .where(Projects.program_id == dest_program.id)
            .order_by(UserGroupProjects.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 3
        assert (
            res[0].to_dict()
            == UserGroupProjects(
                id=dest_user_group_project_0.id,
                group_id=dest_user_group_project_0.group_id,
                project_id=dest_user_group_project_0.project_id,
                created_at=dest_user_group_project_0.created_at,
                updated_at=dest_user_group_project_0.updated_at,
                deleted_at=dest_user_group_project_0.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == UserGroupProjects(
                id=dest_user_group_project_1.id,
                group_id=dest_user_group_project_1.group_id,
                project_id=dest_user_group_project_1.project_id,
                created_at=dest_user_group_project_1.created_at,
                updated_at=dest_user_group_project_1.updated_at,
                deleted_at=dest_user_group_project_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[2].to_dict()
            == UserGroupProjects(
                id=res[2].id,
                group_id=dest_user_group_2_id,
                project_id=dest_project_1.id,
                created_at=res[2].created_at,
                updated_at=res[2].updated_at,
                deleted_at=None,
            ).to_dict()
        )

        query = (
            select(UserGroupsAdmins)
            .where(UserGroupsAdmins.program_id == dest_program.id)
            .order_by(UserGroupsAdmins.created_at)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == UserGroupsAdmins(
                id=dest_user_group_admin_1.id,
                program_id=dest_user_group_admin_1.program_id,
                group_id=dest_user_group_admin_1.group_id,
                user_id=dest_user_group_admin_1.user_id,
                unrestricted_access=dest_user_group_admin_1.unrestricted_access,
                created_at=dest_user_group_admin_1.created_at,
                updated_at=dest_user_group_admin_1.updated_at,
                deleted_at=dest_user_group_admin_1.deleted_at,
                deleted_at_unix=dest_user_group_admin_1.deleted_at_unix,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == UserGroupsAdmins(
                id=res[1].id,
                program_id=dest_program.id,
                group_id=dest_user_group_2_id,
                user_id=user_group_admin_2.user_id,
                unrestricted_access=user_group_admin_2.unrestricted_access,
                created_at=res[1].created_at,
                updated_at=res[1].updated_at,
                deleted_at=None,
                deleted_at_unix=None,
            ).to_dict()
        )


async def test_migrate_single_year_entities_existing_user_entities(mdl, app_request, db_session_maker):
    source_program = await mdl.Programs()
    user_1 = await mdl.Users(id=1)
    user_2 = await mdl.Users(id=2)

    await mdl.ProgramPermissions(user_id=user_1.id, program_id=source_program.id)
    program_permission_2 = await mdl.ProgramPermissions(user_id=user_2.id, program_id=source_program.id)

    role_1 = await mdl.Roles()
    await mdl.RolesUsers(fs_user_id=user_1.id, role_id=role_1.id, program_id=source_program.id)
    user_role_2 = await mdl.RolesUsers(fs_user_id=user_2.id, role_id=role_1.id, program_id=source_program.id)

    dest_program = await mdl.Programs()

    # existing program permission for user_1
    dest_program_permission_1 = await mdl.ProgramPermissions(
        user_id=user_1.id, program_id=dest_program.id, details="{}"
    )

    role_0 = await mdl.Roles()
    # existing user role for user_1
    dest_user_role_1 = await mdl.RolesUsers(fs_user_id=user_1.id, role_id=role_0.id, program_id=dest_program.id)

    await migrate_single_year_entities(
        request=app_request, source_program_id=source_program.id, dest_program_id=dest_program.id
    )

    async with db_session_maker() as s:
        query = (
            select(ProgramPermissions)
            .where(ProgramPermissions.program_id == dest_program.id)
            .order_by(ProgramPermissions.user_id)
        )
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == ProgramPermissions(
                id=dest_program_permission_1.id,
                user_id=dest_program_permission_1.user_id,
                program_id=dest_program_permission_1.program_id,
                details=dest_program_permission_1.details,
                deleted_at=dest_program_permission_1.deleted_at,
                deleted_at_unix=dest_program_permission_1.deleted_at_unix,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == ProgramPermissions(
                id=res[1].id,
                user_id=program_permission_2.user_id,
                program_id=dest_program.id,
                details=program_permission_2.details,
                deleted_at=None,
                deleted_at_unix=0,
            ).to_dict()
        )

        query = select(RolesUsers).where(RolesUsers.program_id == dest_program.id).order_by(RolesUsers.fs_user_id)
        res = (await run_query(query=query, s=s)).scalars().all()
        assert len(res) == 2
        assert (
            res[0].to_dict()
            == RolesUsers(
                id=dest_user_role_1.id,
                fs_user_id=dest_user_role_1.fs_user_id,
                role_id=dest_user_role_1.role_id,
                program_id=dest_user_role_1.program_id,
                created_at=dest_user_role_1.created_at,
                deleted_at=dest_user_role_1.deleted_at,
            ).to_dict()
        )
        assert (
            res[1].to_dict()
            == RolesUsers(
                id=res[1].id,
                fs_user_id=user_role_2.fs_user_id,
                role_id=user_role_2.role_id,
                program_id=dest_program.id,
                created_at=res[1].created_at,
                deleted_at=None,
            ).to_dict()
        )
